define(function(require) {
	'use strict';

	var AbstractQuestionController = require('./Abstract');
	var Notes = require('i18n!../../constants/nls/en-us/Notes');
	var Classes = require('../../constants/Classes');
	var EventController = require('../Event');
	var ProductEvents = require('../../constants/ProductEvents');
	var DomEvents = require('../../constants/DomEvents');
	var bindAll = require('mout/object/bindAll');
	var inherits = require('mout/lang/inheritPrototype');
	var SessionStorage = require('../../providers/SessionStorage');
	var ActionEvents = require('../../constants/ActionEvents');
	/**
	 * @class App.Controllers.Question.Note
	 * @extends App.Controllers.Question.Abstract
	 *
	 * @constructor
	 * @param {Object} config
	 * @param {jQuery} config.view
	 * @param {Models.Ui.Question} config.model
	 */
	function NoteQuestionController(config) {
		bindAll(this,
				'retrieveImageUrl',
				'closeModalPopup',
				'onProductChange',
				'onLogoChange',
				'onEditLogoClick'
		);

		AbstractQuestionController.call(this, config);
	}

	var proto = inherits(NoteQuestionController, AbstractQuestionController);

	/**
	 * @method template
	 * @param {Object} model
	 * @return {String}
	 */
	proto.template = require('hbs!templates/question/note');

	/**
	 * @method attachEvents
	 * @chainable
	 */
	proto.attachEvents = function() {
		this.$view.on(DomEvents.CLICK, Classes.PREVIEW_BUTTON_SELECTOR, this.retrieveImageUrl);
		this.$view.on(DomEvents.CLICK, Classes.CLOSE_BUTTON_SELECTOR, this.closeModalPopup);
		this.$view.on(DomEvents.CLICK,Classes.EDIT_LOGO_SELECTOR, this.onEditLogoClick);
		EventController.on(ProductEvents.CHANGE, this.onProductChange)
					   .on(ProductEvents.CUSTOM_LOGO_CHANGE, this.onLogoChange)
		return this;
	};

	/**
	 * @method detachEvents
	 * @chainable
	 */
	proto.detachEvents = function() {
		this.$view.off(DomEvents.CLICK, Classes.PREVIEW_BUTTON_SELECTOR, this.retrieveImageUrl);
		this.$view.off(DomEvents.CLICK, Classes.CLOSE_BUTTON_SELECTOR, this.closeModalPopup);
		this.$view.off(DomEvents.CLICK,Classes.EDIT_LOGO_SELECTOR, this.onEditLogoClick);
		return this;
	};

	/**
	 * @method detachProductEvents
	 * @chainable
	 */
	proto.detachProductEvents = function() {
		EventController.off(ProductEvents.CHANGE, this.onProductChange);
		return this;
	};

	/**
	 * @method render
	 * @chainable
	 */
	proto.render = function() {
		var model = this.model;
		var showNote = false;
		if (model.id == "HIDDEN_ACCOUNT" || model.id == "HIDDEN_LOGO") {
			showNote = true;
		}
		var link = (model.id == "HIDDEN_LINK") ? true : false;
		var my_logo = (model.id == "SHOW_LOGO") ? true : false;
		var isCustom = (SessionStorage.getValue('logo') === 'CUSTOM') ? true : false;
		var isCustomLogo = (model.id == "HIDDEN_LOGO") ? true : false;
		var iconCheck;
		if(isCustom && isCustomLogo){
			iconCheck=true;
		}else{
			iconCheck=false;
		}
		var noteDisplay;
		var msgCheck = (Notes[model.id] =="Custom logos are not displayed on the product preview. A design team will best fit the image on the product.") ? true : false;
		if (iconCheck && msgCheck) {
			noteDisplay = Notes[model.id];
		} else if (!iconCheck && msgCheck) {
			noteDisplay = null;
		} else {
			noteDisplay = Notes[model.id];
		}
		let logoModel= model.productInfo.productInfo.question.filter(function(item){
			return item.id == 'logo'
		})
		if(logoModel.length === 0 && msgCheck){
			noteDisplay = null;
			iconCheck = false;
		}
		this.$view.html(this.template({
			show: showNote,
			note: noteDisplay,
			link: link,
			my_logo: my_logo,
			messageCheck: iconCheck
		}));
		return this.start();
	};

	proto.retrieveImageUrl = function()
	{

		$('#pop-up').fadeIn(300);

		// Add the mask to body
		$('body').append('<div id="mask-cart"></div>');
		$('#mask-cart').fadeIn(300);



		var orgURl = $(".js-preview-img").attr('src');
		var wid_url = orgURl.substr(0, orgURl.indexOf('&wid='));
		var img_url;
		if (wid_url == '') {
			// console.log('www', wid_url);
			var hei_url = orgURl.substr(0, orgURl.indexOf('&hei='));
			if (hei_url == '') {
				hei_url = orgURl;
			}
			img_url = hei_url + '&wid=' + 1480;
		} else {
			img_url = wid_url + '&wid=' + 1480;
		}
		// var final_url = img_url + '&wid=' + orgWidth;
		// console.log(final_url);
		var urlSplitUid = orgURl.substr(0, orgURl.indexOf('?'));
		var tempuid = urlSplitUid.split("/");
		var uid = tempuid[tempuid.length - 1];


		var urlSplitAppUrl = orgURl.substr(orgURl.indexOf('?') + 1);
		var appUrl = urlSplitAppUrl.substr(0, urlSplitAppUrl.length);
		scene7url = urlSplitUid.substr(0, urlSplitUid.lastIndexOf('/')) + '/';
		zoom.initHeroImage();
		zoom.iviewer.settings.imgUrlPrefix = scene7url;
		zoom.iviewer.productPartNum = uid;
		zoom.iviewer.appendUrl = appUrl;
		zoom.iviewer.loadImage(img_url, 'sample', imgwidth);
		$('.drag_help').fadeIn(300);
		return false;
		
		// $(".js_preview_button").removeClass("js-include-zoom");
		// $(".modal-dialog").show();
		// $('.modal-content').css('height',$( window ).height()*0.8);
		// $('.modal-content').css('width',$( window ).width()*0.8);
		// $('.modal-body').css('height',$( window ).height()*0.8);
		// $('.modal-body').css('width',$( window ).width()*0.8);
		// $(".zoomingImage").css("display","block");
		// $(".zoomContainer").show();
		// $(".zoomWindowContainer").show();
		// $("#backdrop").css("display","block");
		// document.documentElement.style.overflow = 'hidden';
		// document.body.scroll = "no";
		// document.documentElement.scrollTop = "no";
		// if($('.text-on-image').is(':visible')) {
		// 	$('.text-on-image').offset({left:$('.modal-content').offset().left + 15 + ($(".modal-content").width() - $('.text-on-image').width())/2, top:$('.modal-content').offset().top + 5 + ($(".modal-content").height() - $('.text-on-image').height())/2});
		// 	$('.text-on-image').delay(1300).fadeOut(250);
		// }
		// $(".js_image_close").offset({top:$(".modal-content").offset().top - $(".js_image_close").height(), left:($(".modal-content").offset().left+6 + $(".modal-content").width()) - $(".js_image_close").width()});
	};

	$(window).resize(function() {
		var a = $('.modal-content').offset();
		if (a) {
			var tp = a.top;
			var lt = a.left;
			$('.zoomContainer').offset({top: tp+23 ,left: lt+20});
			$(".js_image_close").offset({top:$(".modal-content").offset().top - $(".js_image_close").height() + 50, left:($(".modal-content").offset().left + $(".modal-content").width()) - $(".js_image_close").width() + 60});
		}
	});

	$(window).resize(function() {
		var modalcontent = $( ".modal-content").offset();
		if (modalcontent) {
			var modalcontent_top = modalcontent.top;
			var modalcontent_left = modalcontent.left;
			$( ".zoomContainer" ).offset({top:modalcontent_top+10,left:modalcontent_left+10});
		}
	});

	/**
     * @method onProductChange
     * @param {jQuery.Event} event
     * @param {App.Models.Product} product
     * @callback
     */
    proto.onProductChange = function(event, product) {
		//If this is the account info note, only show it if the product is applicable
		//console.log('JoeTest.Note.id', this.model.id);
		//console.log('JoeTest.Note.product', product);
		if (this.model.id == "HIDDEN_ACCOUNT") {
			var hasAccountNumber = this.hasQuestion(product, 'accountNumber');
			if (!hasAccountNumber) {
				this.$view.hide();
			}
 		} else if (this.model.id == "NUMBERING_INFORMATION") {
			var hasNumbering = this.hasQuestion(product, 'numbering');
			if (!hasNumbering) {
				this.$view.hide();
			}
		} else if (this.model.id == "SHOW_LOGO") {
			this.$view.hide();
		}
		this.detachProductEvents();
    };

    proto.hasQuestion = function(product, questionId) {
    	var hasQuestion = false;
		var questions = [].concat(product.info.productInfo.question);
		for (var i=0; i<questions.length; i++) {
			if (questions[i].id == questionId) {
				hasQuestion = true;
				break;
			}
		}
    	return hasQuestion;
    }

	/**
	 * @method onLogoChange
	 * @param {jQuery.Event} event
	 */
	proto.onLogoChange = function(event) {
		this.render();
	};

	/**
	 * @method onEditLogoClick
	 * @param {jQuery.Event} event
	 */

	proto.onEditLogoClick = function(event){
		EventController.emit(ActionEvents.GO_TO_EDIT_STEP,"Logo");
	}

	
	return NoteQuestionController;
});
