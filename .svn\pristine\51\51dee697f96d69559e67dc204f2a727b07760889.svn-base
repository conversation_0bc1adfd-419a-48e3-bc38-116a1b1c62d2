/**@license
 * mout v0.9.0 | http://moutjs.com | MIT license
 */
define(function(require){

//automatically generated, do not edit!
//run `node build` instead
return {
    'VERSION' : '0.9.0',
    'array' : require('./array'),
    'collection' : require('./collection'),
    'date' : require('./date'),
    'function' : require('./function'),
    'lang' : require('./lang'),
    'math' : require('./math'),
    'number' : require('./number'),
    'object' : require('./object'),
    'queryString' : require('./queryString'),
    'random' : require('./random'),
    'string' : require('./string'),
    'time' : require('./time'),
    'fn' : require('./function')
};

});
