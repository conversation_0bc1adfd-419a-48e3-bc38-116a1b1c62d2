.site:has(.cfg-loader) {
    max-width: 100%;
}

.cfg-loader {
    display: grid !important;
    place-content: center;
    min-height: 99vh;
    text-align: center;
}

.cfg-loader-logo a {
    text-decoration: none !important;
    background: none !important;
}

.cfg-loader-logo img {
    height: 60px;
    max-width: 136px;
    object-fit: contain;
    margin-bottom: 5px;
}

.cfg-loader h5 {
    font-weight: 600;
    color: #3d5366;
    padding-left: 20px;
}

.sub-text {
    color: #606060;
}

.spin-wrapper {
    position: relative;
    height: 135px;
    display: grid;
    place-content: center;
}

.slider {
    height: 100%;
    margin-left: 15px;
    display: flex;
    align-items: center;
    animation: slider 5s linear infinite;
    will-change: transform;
}

.spin-wrapper i::after {
    font-family: FontAwesome;
    font-size: 20px;
    color: #fff;
    padding-right: 40px;
}

.spin-wrapper i:nth-child(1)::after {
    content: "\f02d";
}

.spin-wrapper i:nth-child(2)::after {
    content: "\f093";
}

.spin-wrapper i:nth-child(3)::after {
    content: "\f044";
}

.spin-wrapper i:nth-child(4)::after {
    content: "\f0f6";
}

.spin-wrapper i:nth-child(5)::after {
    content: "\f03e";
}

.spin-wrapper i:nth-child(6)::after {
    content: "\f013";
}

.load-icons {
    width: 50px;
    height: 50px;
    overflow: hidden;
    background: #3d5366;
    border-radius: 50%;
}

@keyframes slider {
    0% {
        transform: translateX(100%);
    }

    100% {
        transform: translateX(-900%);
    }
}

/* Spinner Animation (Pulse Circle) */
@keyframes spinner-pulse-in {
    from {
        transform: scale(0.5);
        opacity: 0;
    }

    to {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes spinner-pulse-out {
    from {
        transform: scale(1);
        opacity: 1;
    }

    to {
        transform: scale(1.5);
        opacity: 0;
    }
}

.spin-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -40px 0 0 -40px;
}

.spin-circle::before,
.spin-circle::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 2px solid #6c757d;
    border-radius: 50%;
    box-sizing: border-box;
}

.spin-circle::before {
    animation: spinner-pulse-in 1s linear infinite;
}

.spin-circle::after {
    animation: spinner-pulse-out 1s linear infinite;
}

.dot-loader {
    display: flex;
    justify-content: center;
}

.dot-loader span {
    width: 4px;
    height: 4px;
    margin: 2px;
    border-radius: 50%;
    background-color: #3d5366;
    animation: dot 1.3s ease-in-out infinite;
    display: inline-block;
    position: relative;
    top: 14px;
    left: 4px;
    will-change: transform;
}

.dot-loader span:nth-of-type(2) {
    animation-delay: 0.2s;
}

.dot-loader span:nth-of-type(3) {
    animation-delay: 0.3s;
}

@keyframes dot {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(0);
    }
}