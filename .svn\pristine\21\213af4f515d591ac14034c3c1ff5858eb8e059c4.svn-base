{{#if options}}
    <h2 class="hdg hdg_h2">{{desc}}</h2> 
    <ul class="vList vList_std">
        {{#each options}}
            <li>
                <input type="radio"
                    class="radio radioExtRadio{{#is id ../default}} isChecked{{/is}}"
                    id="{{slug}}"
                    name="{{../id}}"
                    value="{{matrix.uid}}"
                    {{#is id ../default}} checked{{/is}} />

                <label id="paper-option-{{@index}}" for="{{slug}}" class="radioExtLabel">{{uiDesc}}{{#if surcharge}}<span class="radioExtPrice">(+{{qtyCharge}})</span>{{/if}}</label>
                    <span id="pcHelp_{{id}}" class="radioExtHelp js-button-action" name="{{uiDesc}}"
                    data-template='<div id="{{id}}" class="custpop popover" role="tooltip"><div class="arrow"></div><div class="popover-body"></div></div>'
                    data-container="body" data-placement="bottom"   data-content="" data-original-title="" title="">
                    <img id="pcHelp" src="{{../host_url}}webasset/w2p_mobile/{{../baseAppUrl}}assets/media/images/helpText.png" class="vMiddle pcHelpImg" alt="pcHelp"></img>
                    
                    </span>
                    <div class="radioExtImage">
                        <img src="{{imgSrc}}" id="PaperColor-Img-{{tlId}}" alt="PaperColor-Img-{{tlId}}"/>
                    </div>
                
                {{#ifEq @index 1}}
                    <div class="radioExtRecommend">
                        <div id="Matrix-Label-{{tlId}}">Our Recommended Upgrade</div>
                    </div>
                {{/ifEq}}

                <!-- Debug -->
                <!--
                <label><div class="paperSwatchDesc">-{{@index}}-</div></label>
                <label><div class="paperSwatchDesc">-{{@index}}-</div></label>
                <label><div class="paperSwatchDesc">-{{../host_url}}-</div></label>
                <label><div class="paperSwatchDesc">-{{../baseAppUrl}}-</div></label>
                <label><div class="paperSwatchDesc">-{{matrix.code}}-</div></label>
                <label><div class="paperSwatchDesc">-{{matrix.id}}-</div></label>
                <label><div class="paperSwatchDesc">-{{layoutClass}}-</div></label>
                <label><div class="paperSwatchDesc">-{{id}}-</div></label>
                <label><div class="paperSwatchDesc">-{{slug}}-</div></label>
                -->

            </li>
        {{/each}}
    </ul>
{{/if}}
