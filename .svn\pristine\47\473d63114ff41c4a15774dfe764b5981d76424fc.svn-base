{{! used in logoMix to show logos }}


{{! used in logoMix to show logos }}
<ul class="blocks blocks_4up mix-blocks">
    {{#each items}}
        <li>
            <input type="radio" class="designRadio " id="{{url}}" name="logos" value="{{url}}" />
            <label for="{{url}}"> <span  style="display:none;">mix-logobox </span>  
                <span class="designBox mix-logobox">
                    {{#if url}} 
                        <canvas id="mix-listing-canvas-{{@index}}" width="150" height="150"></canvas>
                    {{/if}}

                </span>
             <br/>
            </label>     
        </li>
    {{/each}}
</ul> {{! /blocks }}
<div class="pages_container">
    <ul class="blocks mix-pages js-mix-pages ">
        {{#each pages}}
        <li>    
            <input type="radio" class="designRadio" id="Page_{{id}}" name="page" value="{{id}}" {{#is id ../current_page}} checked{{/is}} />
            <label for="Page_{{id}}" title="{{id}}">
                <span class="designBox {{#is id ../current_page}} c-page{{/is}}" id="{{id}}">{{id}}</span>
            </label>        
        </li>
        {{/each}}
    </ul>
</div>


{{!-- <ul class="blocks blocks_4up mix-blocks">
    {{#each items}}
        <li>
            <input type="radio" class="designRadio " id="{{url}}" name="logos" value="{{url}}" />
            <label for="{{url}}"> <span  style="display:none;">mix-logobox </span>  
                <span class="designBox mix-logobox">
                    {{#if url}} 
                        <canvas id="mix-listing-canvas-{{@index}}" width="165" height="165"></canvas>
                    {{/if}}

                </span>
             <br/>
            </label>     
        </li>
    {{/each}}
</ul> {{! /blocks }}
<div class="pages_container">
    <ul class="blocks mix-pages js-mix-pages ">
        {{#each pages}}
        <li>    
            <input type="radio" class="designRadio" id="Page_{{id}}" name="page" value="{{id}}" {{#is id ../current_page}} checked{{/is}} />
            <label for="Page_{{id}}" title="{{id}}">
                <span class="designBox {{#is id ../current_page}} c-page{{/is}}" id="{{id}}">{{id}}</span>
            </label>        
        </li>
        {{/each}}
    </ul>
</div> --}}