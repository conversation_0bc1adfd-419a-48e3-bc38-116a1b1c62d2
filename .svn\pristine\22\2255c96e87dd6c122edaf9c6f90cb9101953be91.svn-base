//>>excludeStart('excludeAfterBuild', pragmas.excludeAfterBuild)
/*
I have no idea why this dependency can't be met without this, but for now it works.
Keep it updated with all of your helpers.
It will get completely removed in the build.
I think it has to do with circular dependencies, but I don't know how to fix it.

Sucks. I know.
*/





define([
  'template/helpers/yeller',
], function(){
  return {};
});
//>>excludeEnd('excludeAfterBuild')