# i18n 

An AMD loader plugin for loading internationalization/localization
string resources.

Known to work in RequireJS, but should work in other
AMD loaders that support the same loader plugin API.

## Docs

See the [RequireJS API i18n section](http://requirejs.org/docs/api.html#i18n).

## Latest release

The latest release will be available from the "latest" tag.

## License

Dual-licensed -- new BSD or MIT.

## Where are the tests?

They are in the [requirejs](https://github.com/jrburke/requirejs) and
[r.js](https://github.com/jrburke/r.js) repos.

## History

This plugin was in the [requirejs repo](https://github.com/jrburke/requirejs)
up until the requirejs 2.0 release.
