define(function(require) {
    'use strict';

    var Registry = require('util/Registry');
    var Content = require('i18n!../constants/nls/en-us/Content');
    var currency = require('util/currencyFormat');
    var Query = require('models/Query');

    /**
     * Surcharge registry namespace
     * @type {String}
     * @static
     */
    var NAMESPACE = Registry.PRICES;

    /**
     * Surcharge ID map
     * @type Object
     * @private
     */
    var _productSurchargeMap = {
        '2INKSET': 'inkColor2',
        'Z4655': 'inkColor2',
        '2INK1': 'inkColor2',
        'PRESC1': 'matrix1',
        'PRESM': 'matrix1',
        'PRESC3': 'matrix1',
        '1310' :'verse',
        '1310ED' :'verse'
    };

    var query = Query.getInstance();

    /**
     * Surcharge global registry
     *
     * @class Util.Surcharge
     * @type Object
     * @static
     */
    var Surcharge = {

        /**
         * Add surcharge data to registry
         *
         * @method add
         * @param {String} id
         * @param {Object} value
         * @chainable
         */
        add: function(id, value) {
            if (_productSurchargeMap.hasOwnProperty(id)) {
                Registry.set(NAMESPACE, _productSurchargeMap[id], value);
            }
            Registry.set(NAMESPACE, id, value);
            return this;
        },

        /**
         * Get registered surcharge data by ID
         *
         * @method get
         * @param {String} id
         * @returns {Object|null}
         */
        get: function(id) {
            return Registry.get(NAMESPACE, id);
        },

        /**
         * Get surcharge price
         *
         * @method getPrice
         * @param {String} id
         * @param {Boolean} [raw=false] Return price as number instead of formatted string
         * @returns {String|Number}
         */
        getPrice: function(id, raw, qty) {
            var priceObj = Surcharge.get(id);
            if (!priceObj) {
                return '';
            }

            var options = priceObj.option;
            if (!(options instanceof Array)) {
                options = [options];
            }

            var price,addPrice,quantity;
            //var quantity = Registry.get(Registry.PRODUCT, 'qty') || 0;
            if(! qty)
                quantity = query.qty;
            else
                quantity = qty;

            if(options[0].discountedPrice) {
                price = options[0].discountedPrice;
            
            //var quantity = Registry.get(Registry.PRODUCT, 'qty') || 0;
               
                for (var i=0; i<options.length; i++) {
                    if (options[i].qty == quantity) {
                        price = parseFloat(options[i].discountedPrice);
                        break;
                    } else if (options[i].qty > quantity) {
                        //Inbtwn pricing logic  price = ${prev_price + (($qty - $prev_qty) * (($next_price - $prev_price) / ($next_qty - $prev_qty)))
                        addPrice = (quantity - options[i-1].qty) * ((options[i].discountedPrice - options[i-1].discountedPrice) / (options[i].qty - options[i-1].qty));
                        price = parseFloat(options[i-1].discountedPrice) + parseFloat(addPrice);
                        break;
                    }
                }
            }
             else {
                price = options[0].price;
               
                for (var j=0; j<options.length; j++) {
                    if (options[j].qty == quantity) {
                        price = parseFloat(options[j].price);
                        break;
                    } else if (parseInt(options[j].qty) > parseInt(quantity)) {
                        //Inbtwn pricing logic  price = ${prev_price + (($qty - $prev_qty) * (($next_price - $prev_price) / ($next_qty - $prev_qty)))
                        if(options[j-1] && options[j-1].qty){
							addPrice = (quantity - options[j-1].qty) * ((options[j].price - options[j-1].price) / (options[j].qty - options[j-1].qty));
							price = parseFloat(options[j-1].price) + parseFloat(addPrice);
							break;
						}
                    }
                }
            }
            //console.log('Surcharge.getPrice', price);

            if (raw === true) {
                return price || 0;
            }

            return price ? Content.get('$') + currency(price, 0) : '';
        },

        getUnitPrice: function(id, raw) {
            var price = this.getPrice(id, raw);
            //var quantity = Registry.get(Registry.PRODUCT, 'qty') || 0;
            var quantity = query.qty;
            //var unitPrice = currency(Math.round(price) / quantity, 0);
            var unitPrice = currency(price / quantity, 0);
            return unitPrice;
        },

        getPriceDesc: function(id, raw) {
            var priceObj = Surcharge.get(id);
            if (!priceObj) {
                return '';
            }
            return priceObj.desc ? priceObj.desc : '';
        },

        isChargeApplied: function(surcharges, chargeId) {
            var bool = false;
            if (surcharges !== null) {
                for (var i=0; i<surcharges.length; i++) {
                    if (surcharges[i].id == chargeId) {
                        bool = true;
                        break;
                    }
                }
            }
            return bool;
        }
    };

    return Surcharge;
});