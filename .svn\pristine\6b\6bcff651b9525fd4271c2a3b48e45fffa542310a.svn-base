<!-- vim:ts=4:sts=4:sw=4:et:tw=60 -->

This library has the following policy about versions.

-   Presently, all planned versions have a major version number of 0.
-   The minor version number increases for every backward-incompatible
    change to a documented behavior.
-   The patch version number increases for every added feature,
    backward-incompatible changes to undocumented features, and
    bug-fixes.

Upon the release of a version 1.0.0, the strategy will be revised.

-   The major version will increase for any backward-incompatible
    changes.
-   The minor version will increase for added features.
-   The patch version will increase for bug-fixes.

