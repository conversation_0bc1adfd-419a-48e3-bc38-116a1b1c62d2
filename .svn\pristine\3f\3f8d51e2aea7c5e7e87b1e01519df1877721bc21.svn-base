define(function(require) {
    'use strict';
    var Helper = {
        isSameTooltipClick: function(event) {
            var visibleTooltipId = "";
            $('[data-container="body"]').each(function(){
                var data = $("#" + $(this).attr('id')).data('bs.popover');
                if(data !== undefined && data.tip.isConnected == true){
                    visibleTooltipId = $(this).attr('id');
                }
            });
            
            return visibleTooltipId == event.target.id || (event.target.parentElement.id !== "" && visibleTooltipId == event.target.parentElement.id);
        },
        parseValue: function(value) {
            if (typeof value !== 'string') {
                return ['', ''];
            }
            const regex = /\{(BP|BF)\d+(\.\d+)?\}/g;
            const matches = value.match(regex) || [];
            if (matches.length > 0) {
                const textWithoutMatches = value.replace(regex, '').trim();
                return [matches.join(''), textWithoutMatches];
            }
            return ['', value];
        },
        isAlphaNumeric: function(value){
            var alphanumericRegex = /^[a-zA-Z0-9]+$/;
            if (alphanumericRegex.test(value)) {
                return true;
            }else{
                return false
            }

        }
    }
    return Helper;
})