define(function (require) {
    'use strict';

    var AbstractModel = require('./Abstract');
    var trim = require('mout/string/trim');
    var inherits = require('mout/lang/inheritPrototype');
    var SessionStorage = require('../providers/SessionStorage');

    /**
     * @class App.Models.CustomerProfile
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} CustomerProfileData
     */
    var CustomerProfileModel = function (CustomerProfileData) {
        AbstractModel.call(this, CustomerProfileData);
    };

    var proto = inherits(CustomerProfileModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} CustomerProfileData
     * @chainable
     */
    proto.init = function(CustomerProfileData) {
        /**
         * @property profile
         * @default {null}
         * @type {object}
         */
        this.imprint = {};

        /**
         * @property logo
         * @default {null}
         * @type {object}
         */
        this.logo = {};

        /**
         * Stores a key value pair of customer data.
         * Keys match question ids, values contain the
         * data from their Deluxe online account.
         *
         * @property lines
         * @default {null}
         * @type {Object}
         */
        this.lines = {};

        // run the parent init method to parse determine the data type
        base.init.call(this, CustomerProfileData);

        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param json the original JSON data.
     * @chainable
     */
    proto.fromJSON = function(json) {
        json = this.stripInvalidFields(json);
        if (json && json.CustomText) {
            this.imprint = json.CustomText.CustomTextInfo;
            
            if (this.imprint) {
                if (SessionStorage.getValue("CI_ML")) {
                    this.imprint.businessName = "";
                }
                if (SessionStorage.getValue("CI_AD")) {
                    this.imprint.address1 = "";
                    this.imprint.address2 = "";
                }
                if (SessionStorage.getValue("CI_CI")) {
                    this.imprint.city = "";
                    this.imprint.state = "";
                    this.imprint.zipCode = "";
                }
                if (SessionStorage.getValue("CI_PH") && (SessionStorage.getValue("CA_PH") === null)) {
                    this.imprint.phone1 = "";
                    this.imprint.phone1Ext = "";
                    this.imprint.phone2 = "";
                    this.imprint.phone2Ext = "";
                    this.imprint.fax = "";
                    this.imprint.email = "";
                }
            }
        } else {
            this.imprint = undefined;
        }
    
        if (json && json.CustomText) {
            if (SessionStorage.getValue("logo") === "NO LOGO" || SessionStorage.getValue("logo") === "ADD LOGO" || !(SessionStorage.getValue("logo"))) {
                this.logo = json.CustomText.CustomLogoInfo;
            } else {
                this.logo = "";
            }
        } else {
            this.logo = undefined;
        }
    
        return this;
    }; 


    /**
     * Loop over the line usage items and replace
     * the tokens with the value from the customer's account
     * into the each line E.g.
     *
     *      city, state, zipCode
     *
     * becomes
     *
     *      Minneapolis, MN, 55431
     *
     * @method mapLineUsage
     * @param {App.Models.Collections.LineUsage} lineUsage
     * @chainable
     */
    proto.mapLineUsage = function(lineUsage, blockPrefix) {
        this.ciFlag = blockPrefix.includes("CI_PH")
        lineUsage.each(this._mapLineUsage.bind(this));
        this.cleanLines();

        return this;
    };

    /**
     * @method _mapLineUsage
     * @param {App.Models.LineUsage} line
     * @callback
     */
    proto._mapLineUsage = function(line) {
        line = line || {};

        var imprint = this.imprint;
        var lines = this.lines;
        var code = line.lineCode;
        var text = line.text;

        if (!imprint || !lines || !code || !text) {
            return;
        }
        if (SessionStorage.getValue("CI_PH") && code == 'PH' && this.ciFlag) {         
            return;
        }
        lines[code] = text.replace(/\b[a-z0-9]+\b/gi, function(key) {
            if (key in imprint) {
                return imprint[key] || '';
            }

            return key;
        });
            lines[code]=(lines[code]).replace(/,\s*$/, "");
    };

    /**
     * @method cleanLines
     * @chainable
     */
    proto.cleanLines = function() {
        var key;
        var line;

        for (key in this.lines) {
            line = trim(this.lines[key]);

            // Trim the FAX and ext. text from the
            // line value if they don't contain a FAX
            // or extension number. Only do so to the PH
            // and AP fields as we don't want to trim something
            // like COLFAX in an address.
            if (key === 'PH' || key === 'AP') {
                if (line.slice(-3) === 'FAX') {
                    line = trim(line.slice(0, line.length - 3));
                }

                if (line.slice(-4) === 'ext.') {
                    line = trim(line.slice(0, line.length - 4));
                }
            }
            this.lines[key] = line;
        }
    };

    return CustomerProfileModel;
});
