define(function(require){

//automatically generated, do not edit!
//run `node build` instead
return {
    'MAX_INT' : require('./number/MAX_INT'),
    'MAX_UINT' : require('./number/MAX_UINT'),
    'MIN_INT' : require('./number/MIN_INT'),
    'abbreviate' : require('./number/abbreviate'),
    'currencyFormat' : require('./number/currencyFormat'),
    'enforcePrecision' : require('./number/enforcePrecision'),
    'isNaN' : require('./number/isNaN'),
    'nth' : require('./number/nth'),
    'ordinal' : require('./number/ordinal'),
    'pad' : require('./number/pad'),
    'rol' : require('./number/rol'),
    'ror' : require('./number/ror'),
    'sign' : require('./number/sign'),
    'toInt' : require('./number/toInt'),
    'toUInt' : require('./number/toUInt'),
    'toUInt31' : require('./number/toUInt31')
};

});
