define(function() {
    'use strict';

    /**
     * XML constants.
     *
     * @type {Object}
     */
    var XmlNodeTypes = {
        ELEMENT: 1,
        TEXT: 3,
        TEXT_NAME: '#text'
    };

    /**
     * Matches strings that only contain whitespace.
     *
     * @type {RegExp}
     */
    var whitespaceOnly = /^\s*$/;

    /**
     * @type {Function}
     * @param {NodeList} attributes
     * @param {Object} obj
     */
    function attributesToObject(attributes, obj) {
        var attribute;
        var length = attributes.length;
        var i = 0;

        for (; i < length; i++) {
            attribute = attributes.item(i);

            // Live dangerously and allow attribute and tag collisions
            obj[attribute.nodeName] = attribute.nodeValue;
        }
    }

    /**
     * @type {Function}
     * @param {NodeList} children
     * @param {Object} obj
     */
    function childrenToObject(children, obj) {
        var child;
        var name;
        var length = children.length;
        var i = 0;

        for (; i < length; i++) {
            child = children.item(i);
            name = child.nodeName;

            // Ignore empty text nodes
            if (name === XmlNodeTypes.TEXT_NAME && whitespaceOnly.test(child.nodeValue)) {
                continue;
            }

            // First time seeing this type
            if (typeof obj[name] === 'undefined') {
                obj[name] = xmlToObject(child);
                continue;
            }

            // Second time seeing this type
            if (typeof obj[name].push === 'undefined') {
                obj[name] = [].concat(obj[name]);
            }

            // Add item to array of this type
            obj[name].push(xmlToObject(child));
        }
    }

    /**
     * Parses an XML document and converts it to data.
     * http://davidwalsh.name/convert-xml-json
     *
     * @type {Function}
     * @param {Document} xml
     * @return {Object}
     */
    function xmlToObject(xml, translate) {
        var obj = {};

        switch (xml.nodeType) {
            case XmlNodeTypes.ELEMENT:
                attributesToObject(xml.attributes, obj);
                break;

            case XmlNodeTypes.TEXT:
                obj = xml.nodeValue;
                break;
        }

        if (xml.hasChildNodes()) {
            childrenToObject(xml.childNodes, obj);
        }

        return translate ? translateJSON(obj) : obj;
    }

    /**
     * Translate json data into expeected format
     * @param  {Object} json JSON object to transform
     * @return {Object}      Transformed object
     */
    function translateJSON(json) {
        var key;
        var root = {};
        var attr = {};
        var attrLen = 0;
        var text = json['#text'];
        for (key in json) {
            if (!json.hasOwnProperty(key) || key === '#text') {
                continue;
            }
            if (Array.isArray(json[key])) {
                root[key] = json[key].map(translateJSON);
                attrLen++;
                continue;
            }
            if (typeof json[key] === 'object') {
                root[key] = translateJSON(json[key]);
                attrLen++;
                continue;
            }
            attr[key] = json[key];
            root[key] = json[key];
            attrLen++;
        }

        if (attrLen) {
            root.$ = attr;
            if (typeof text !== 'undefined') {
                root._ = text;
            }
        } else {
            root = text || '';
        }
        return root;
    }

    function xmlToObjectTranslated(json) {
        return xmlToObject(json, true);
    }

    return xmlToObjectTranslated;
});
