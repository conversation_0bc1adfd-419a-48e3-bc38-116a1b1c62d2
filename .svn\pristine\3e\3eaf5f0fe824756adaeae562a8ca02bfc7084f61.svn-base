/* ---------------------------------------------------------------------
Original Author: <PERSON>
Contributors: N/A

Target Browsers: All
Media Type: Screen, Projection
Width: All Sizes
------------------------------------------------------------------------ */
/*csslint
    adjoining-classes: false
*/

/* ---------------------------------------------------------------------
 Design Radio - for when you want a designBox to act as a radio button
------------------------------------------------------------------------ */
.designRadio {
    display: none;
}

/* mahesh - test fonts to affect in logomix - logo text size */
.designRadio + label {
    display: block;
    text-align: center;
    font-size: 12px;
    cursor: pointer;
}

/* mahesh - test fonts to affect in logomix - logo text size */
.designRadio + label + span.id {
	font-family: Arial;
	font-size: 13px;
	color: #555555;
	display: block;
    text-align: center;
}

/* adjoining classes required for IE8 :checked support. */
.designRadio.isChecked + label > .designBox:before {
    border: 2px solid #fd9600;
}
