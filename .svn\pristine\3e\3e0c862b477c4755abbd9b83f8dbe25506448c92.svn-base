#mask-cart {
  display: block;
  background: #000;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.8;
  z-index: 49;
}

.pop-up {
  display: none;
  padding: 0;
  float: left;
  position: fixed;
  width: 90%;
  max-width: 900px;
  height: 100%;
  max-height: 680px;
  top: 20px;
  left: 50%;
  transform: translate(-50%, 0);
  z-index: 99999;
  border-radius: 15px;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
}

.cart-zoom-close {
  float: right;
  font-size: 16px;
  font-weight: normal;
  margin: 6px 12px 3px 0;
  color: #000f0c;
  text-decoration: underline;
}

a.cart-zoom-close.position {
  position: absolute;
  right: 0;
  z-index: 1;
  margin: 12px 15px 0px 0px;
}

.popup-outer {
  position: relative;
  border-radius: 0px;
  background-color: #333;
  border: 1px solid rgba(30, 30, 30, 1);
  z-index: -2;
}

.popup-outer:after {
  content: '';
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  background-color: #fff;
  z-index: -1;
  top: 0;
  left: 0;
}

#primaryImgContainer {
  margin: 5px 5px 5px 5px;
}

#primaryImgContainer #Hero {
  cursor: move;
  height: 100%;
  height: 590px;
  overflow: hidden;
  width: 100%;
  max-width: 890px;
  position: relative;
  background-image: url(../../media/images/spinner.gif);
  background-repeat: no-repeat;
  background-position: center;
}
.remove-spinner {
  background-image: none !important;
}

#primaryImgContainer #Hero img {
  margin: 0 auto;
}

p#preview {
  position: absolute;
  border: 1px solid #ccc;
  background: #333;
  padding: 5px;
  display: none;
  color: #fff;
  z-index: 999;
}

.figure img {
  border: 0 !important;
}
.drag_help {
  position: absolute;
  top: 3px;
  left: 5px;
  font-size: 20px;
}
.rollover:hover {
  opacity: 0.5;
  transition-duration: 1s;
}

.rollover {
  background: url(/webasset/sd/128/images/magnify.png) no-repeat scroll center
    center;
  cursor: pointer;
  opacity: 0;
  position: absolute;
  transition-duration: 1s;
  background-size: 60px;
  width: inherit;
  top: 0;
  left: 0;
  z-index: 10;
  height: 100%;
}

.wrapper.darkgray.increased {
  font-weight: 500;
}

#zoomButtonContainer {
  position: relative;
  margin-left: 300px;
  margin-top: 11px;
  padding-bottom: 29px;
  height: 8px;
}
*:focus {
  outline: none;
}
.zoomBox {
  /*  position: relative; 
    height: 20px;
    width: 220px;*/
  margin: auto;
}

.zoomBox .iviewer_zoom_out,
.zoomBox .iviewer_zoom_out_off,
.zoomBox .iviewer_zoom_in,
.zoomBox .iviewer_zoom_in_off,
.zoomBox .iviewer_zoom_refresh,
.zoomBox .iviewer_zoom_refresh_off,
.zoomBox .iviewer_move_Left,
.zoomBox .iviewer_move_Left_off,
.zoomBox .iviewer_move_right,
.zoomBox .iviewer_move_right_off,
.zoomBox .iviewer_move_top,
.zoomBox .iviewer_move_top_off,
.zoomBox .iviewer_move_down,
.zoomBox .iviewer_move_down_off {
  background-image: url('../../media/images/sprites.png');
  float: left;
  height: 35px;
  width: 35px;
}

.iviewer_zoom_in {
  background-position: -8px 0;
  margin: 0 0 0 5px;
  cursor: pointer;
}

.iviewer_zoom_out {
  background-position: -76px 0;
  margin: 0 0 0 0px;
  cursor: pointer;
}

.iviewer_move_Left {
  background-position: -142px -36px;
  margin: 0 0 0 0px;
  cursor: pointer;
}

.iviewer_move_Left_off {
  background-position: -176px -36px;
  margin: 0 0 0 0px;
  cursor: default;
}

.iviewer_move_right {
  background-position: -71px -36px;
  margin: 0 0 3px -12px;
  cursor: pointer;
}

.iviewer_move_right_off {
  background-position: -103px -36px;
  margin: 0 0 3px -12px;
  cursor: default;
}

.iviewer_move_top {
  background-position: -286px -39px;
  cursor: pointer;
  margin: -10px 0 0 -42px;
  width: 20px !important;
}

.iviewer_move_top_off {
  background-position: -318px -39px;
  margin: -10px 0 0 -42px;
  width: 20px !important;
  cursor: default;
}

.iviewer_move_down {
  background-position: -207px -42px;
  cursor: pointer;
  height: 20px !important;
  margin: 22px 0 0 -47px;
  width: 30px !important;
}

.iviewer_move_down_off {
  background-position: -236px -43px;
  height: 20px !important;
  margin: 23px 0 0 -47px;
  width: 30px !important;
  cursor: default;
}

.iviewer_zoom_out_off {
  background-position: -109px 0;
  margin: 0 0 0 0px;
  cursor: default;
}

.iviewer_zoom_in_off {
  background-position: 164px 0;
  margin: 0 0 0 5px;
  cursor: default;
  display: none;
}

.iviewer_zoom_refresh_off {
  background-position: 96px 0;
  margin: 0 0 0 0px;
  cursor: default;
}

.iviewer_zoom_refresh {
  background-position: 130px 0;
  margin: 0 0 0 0px;

  cursor: pointer;
}

/*a.iviewer_zoom_refresh:hover{
    background-position: -363px -1486px;
    cursor: pointer;
    margin: 0 0 0 10px;
}*/
.zoomOutEn {
  display: none;
}

.zoomRefreshEn {
  display: none;
}

.moveLeftEn {
  display: block;
}

.moveRightEn {
  display: block;
}
.moveTopEn {
  display: block;
}
.moveDowntEn {
  display: block;
}

.zoomButtonContainerPDP {
  padding-bottom: 5px;
}

.zoomMessage,
.rollovermessage {
  font-size: 10px;
  color: #999999;
  position: absolute;
  height: 20px;

  margin-top: 4px;
  display: none;
  top: 4px;
  width: 100%;
  text-align: center;
}
.rollovermessage {
  font-size: 11px;
  color: #333;
}

.zoomImageBorder {
  /*outline: 1px solid #e4e4e4;*/
  outline: 0;
}

.cursorMove {
  cursor: move;
}
#img {
  border: 1px solid #b1b1b1;
}
