define(function (require) {
    'use strict';

    var $ = require('jquery');
    var AbstractProvider = require('./Abstract');
    var CartProductModel = require('../models/cart/Product');
    var Settings = require ('../constants/Settings');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Providers.Price
     * @extends App.Providers.Abstract
     *
     * @constructor
     */
    var PriceProvider = function (baseUrl, config) {
        bindAll(this, '_onResponseReceived');

        AbstractProvider.call(this, baseUrl, config);
    };

    var proto = inherits(PriceProvider, AbstractProvider);

    /**
     * @method getPrice
     *
     * @param {App.Models.Cart.Product} configuredProduct A product with custom configuration options set
     * @param {string} sessionId
     * @param {bool} flushCache
     *
     * @return {Promise}
     */
    proto.getPrice = function(configuredProduct, sessionId, flushCache) {
        if (this.promise && flushCache !== true) {
            return this.promise;
        }
        //this.setBaseUrl(Settings.SITE_HOST);
        this.setBaseUrl('');
        //console.log('Price.getPrice *******************************************************************************************');
        var data = {
                inputXml: configuredProduct.toXML ? configuredProduct.toXML() : configuredProduct,
                pg: Settings.PRICE_GROUP
            }

        //Storefront may require the X-CSRF-Token header (Tenenz.com)
        var _headers;
        var csrf_meta_value = $(Settings.X_CSRF_SELECTOR).attr('content');
        if (csrf_meta_value != undefined) {
            _headers = {'X-CSRF-TOKEN':csrf_meta_value};
        }
        var ajaxParams = {headers: _headers};

        this.promise = this
            .post(Settings.SVC_PRICE, data, 'text', ajaxParams)
            .then(this._onResponseReceived)
            .fail(this._onError);
        return this.promise;
    };

    /**
     * Handles the response from the getPrice call
     *
     * @method _onResponseReceived
     *
     * @param {HTMLDocument} data
     * @private
     */
    proto._onResponseReceived = function(data) {
        // Get it fresh next time
        this.promise = null;

        data = $.trim(data.replace(/\n+/g, '\n'));

        // success! we have the data, store and resolve the promise;
        var response = $.xml2json(data);
        var model = new CartProductModel(data);

        if (response.error) {
            throw new Error(response.error);
        }

        return model;
    };

    return new PriceProvider(Settings.SITE_HOST);
});
