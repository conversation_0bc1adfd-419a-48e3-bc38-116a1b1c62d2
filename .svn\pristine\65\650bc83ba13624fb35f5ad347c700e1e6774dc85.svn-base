define(function() {
    'use strict';

    var Handlebars = require('hbs/handlebars');

    var operators = {
        '===':        function (l, r) { return l === r; },
        '!==':        function (l, r) { return l !== r; },
        '<':          function (l, r) { return Number(l) < Number(r); },
        '>':          function (l, r) { return Number(l) > Number(r); },
        '<=':         function (l, r) { return Number(l) <= Number(r); },
        '>=':         function (l, r) { return Number(l) >= Number(r); },
        'instanceof': function (l, r) { return l instanceof r; },
        'typeof':     function (l, r) { return typeof l === r; },
        'in':         function (l, r) { return r && (r.indexOf ? r.indexOf(l) !== -1 : l in r); },
    };

    /**
     * @type {Function}
     * @param {Any} lvalue Left value to compare.
     * @param {String} operator How to compare.
     * @param {Any} rvalue Right value to compare.
     * @param {Object} options Handlebars partial options.
     * @return {String}
     */
    var is = function(lvalue, operator, rvalue, options) {
        if (arguments.length < 3) {
            throw new Error('Handlerbars Helper \'is\' needs 2 parameters');
        }

        if (options === undefined) {
            options = rvalue;
            rvalue = operator;
            operator = '===';
        }

        if (!operators[operator]) {
            throw new Error('Handlerbars Helper \'is\' doesn\'t know the operator ' + operator);
        }

        if (!operators[operator](lvalue, rvalue)) {
            return options.inverse(this);
        }

        return options.fn(this);
    };

    Handlebars.registerHelper('is', is);

    return is;
});
