<?PHP

// Script: Simple PHP Proxy: Get external HTML, JSON and more!
//
// *Version: 1.6, Last updated: 1/24/2009*
// *Modified: 2014-04-01*
//
// Project Home - http://benalman.com/projects/php-simple-proxy/
// GitHub       - http://github.com/cowboy/php-simple-proxy/
// Source       - http://github.com/cowboy/php-simple-proxy/raw/master/ba-simple-proxy.php
//
// About: License
//
// Copyright (c) 2010 "Cowboy" Ben Alman,
// Dual licensed under the MIT and GPL licenses.
// http://benalman.com/about/license/
//
// Topic: Notes
//
// * Assumes magic_quotes_gpc = Off in php.ini
//
// Topic: Configuration Options
//
// These variables can be manually edited in the PHP file if necessary.
//
//   $proxy_cache - Whether to try and retrieve files from the local cache
//     before making the real request.
//
//   $valid_url_regex - This regex is matched against the url parameter to
//     ensure that it is valid. This setting only needs to be used if either
//     $enable_jsonp or $enable_native are enabled. Defaults to '/.*/' which
//     validates all URLs.
//
// ############################################################################

// Change these configuration options if needed, see above descriptions for info.
$proxy_cache = '{{#if env.PROXY_CACHE}}1{{else}}0{{/if}}';
$proxy_scale = '{{#if env.PROXY_SCALE_IMAGES}}1{{else}}0{{/if}}';
$valid_url_regex = '/^https?:\/\/[\w\.]*(deluxe\.com|scene7\.com|akamai\.net|nebs\.com|btobsource\.com)\//';

// ############################################################################

// Parse request url.
$url = preg_replace("@^(https?:/)/?@", '$1/', substr($_SERVER['PATH_INFO'], 1));

if (strpos($url, 'ImageUpload') !== FALSE) {
    $proxy_cache = 0;
}

// Replace local hostname with approved hostname
function scrub($value) {
    global $url;

    // if (strpos($url, 'PreviewImage') && $_GET['fmt'] == 'jpg') {
    //     return str_replace('m.shopdeluxe.dev.deluxe.com', 'm.shopdeluxe.uat.deluxe.com', $value);
    // }

    return str_replace($_SERVER['HTTP_HOST'], 'm.shopdeluxe.dev.deluxe.com', $value);
}

function make_request() {
    global $url, $valid_url_regex;

    if (!$url) {

        // Passed url not specified.
        $contents = 'ERROR: url not specified';
        $status = array('http_code' => 'ERROR');

    } else if (!preg_match($valid_url_regex, $url)) {

        // Passed url doesn't match $valid_url_regex.
        $contents = 'ERROR: invalid url ' . $url;
        $status = array('http_code' => 'ERROR');

    } else {
        $ch = curl_init();

        // Query
        $query = array();
        foreach ($_GET as $key => $value) {
            $query[] = urlencode($key) . '=' . urlencode($value);
        }
        if (count($query) > 0) {
            $url .= '?' . implode('&', $query);
        }

        // Cookie
        $cookie = array();
        foreach ($_COOKIE as $key => $value) {
            $cookie[] = $key . '=' . scrub($value);
        }
        if (defined('SID')) $cookie[] = SID;
        $cookie = implode('; ', $cookie);

        // Data
        switch ($_SERVER['REQUEST_METHOD']) {
            case 'POST':
                $post_vars = array();
                foreach($_POST as $key => $value) {
                    $post_vars[] = $key.'='.$value;
                }

                $payload = file_get_contents('php://input');

                if ($payload) {
                    $post_vars = preg_replace('/<!--(.|\n)*?-->/', '', $payload);
                    $post_vars = preg_replace('/>(\s+)</', '><', $payload);
                } else {
                    $post_vars = implode('&', $post_vars);
                }

                // curl_setopt($ch, CURLOPT_HTTPHEADER, Array('application/x-www-form-urlencoded; charset=UTF-8'));
                // curl_setopt($ch, CURLOPT_HTTPHEADER, Array('text/plain'));

                curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Accept: */*',
                    'Accept-Encoding: gzip, deflate',
                    'Content-Type: text/plain;charset=ISO-8859-1',
                    'Accept-Language: en-us'
                ));

                // curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; miniProxy)');

                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $post_vars);
                break;

            case 'PUT':
                curl_setopt($ch, CURLOPT_PUT, true);
                curl_setopt($ch, CURLOPT_INFILE, fopen('php://input', 'r'));
                break;
        }

        curl_setopt($ch, CURLOPT_COOKIE, $cookie);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);

        // CURLOPT_VERBOSE: TRUE to output verbose information. Writes output to STDERR,
        // or the file specified using CURLOPT_STDERR.
        // curl_setopt($ch, CURLOPT_VERBOSE, true);
        // $verbose = fopen('php://temp', 'rw+');
        // curl_setopt($ch, CURLOPT_STDERR, $verbose);

        list($header, $contents) = preg_split('/([\r\n][\r\n])\\1/', curl_exec($ch), 2);
        $status = curl_getinfo($ch);
        curl_close($ch);

        // //Make the request.
        // $response = curl_exec($ch);
        // $responseInfo = curl_getinfo($ch);
        // $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        // curl_close($ch);

        // //Setting CURLOPT_HEADER to true above forces the response headers and body
        // //to be output together--separate them.
        // $header = substr($response, 0, $headerSize);
        // $contents = substr($response, $headerSize);
    }

    if (isset($header)) {
        // Split header text into an array.
        $header_text = preg_split('/[\r\n]+/', $header);
        $header_text = str_replace('Transfer-Encoding: chunked', '', $header_text);

        // Propagate headers to response.
        foreach ($header_text as $header) {
            if (!preg_match('/^(?:Access-Control|Location)/i', $header)) {
                header(scrub($header));
            } else {
                header('X-Original-' . $header);
            }
        }
    }

    // Force CORS support
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, OPTIONS, POST, PUT');

    header('X-PHP-Proxy-Final-Url: ' . $url);
    print $contents;
    exit;
}

function make_cache_request() {
    global $url;
    global $proxy_scale;

    // Common files
    $file = basename($url);
    $dir = 'common';

    // Product info lookup
    if ($file === 'ProductInfoWrapper.jsp' || $file === 'productInfoLookup.jsp') {
        $file = 'ProductInfoLookup.xml';
        $dir = preg_replace('@\W+@', '-', $_GET['skuId']);

        if (isset($_GET['fulfillmentId']) && $_GET['fulfillmentId'] != '' &&
            file_exists(realpath(__DIR__ . "/../test/data/$dir/ProductInfoLookup_reconfig.xml"))
        ) {
            $file = 'ProductInfoLookup_reconfig.xml';
        }
    }

    // PriceService
    if ($file === 'w2pService.jsp') {
        $file = 'w2pService.xml';
    }

    // External content lookup
    if ($file === 'customTextLookup.jsp') {
        $file = 'customTextLookup.xml';
    }

    // Deluxe does a 301 redirect to another service
    // which returns a success response. Assuming a
    // successfull submission here.
    if ($file == 'addBundleToCart.jsp') {
        $file = 'addToCart.xml';
    }

    if ($file === 'PreviewImage') {

        $file = 'renderImage.jpeg';
        $dir = preg_replace('@\W+@', '-', $_GET['skuId']);

        if (isset($_GET['fmt']) && $_GET['fmt'] == 'fxgraw') {
            $file = 'fxg.xml';
        } else {
            // Load image sized as it should be per the FXG data?
            if ($proxy_scale && file_exists(realpath(__DIR__ . "/../test/data/{$dir}/renderImage_natural.jpeg"))) {
                echo "../test/data/{$dir}/renderImage_natural.jpeg";
            } else {
                echo "../test/data/$dir/$file";
            }
            exit;
        }
    }

    // Construct path
    $path = realpath(__DIR__ . "/../test/data/$dir/$file");

    if (!file_exists($path)) {
        return;
    }

    header('X-PHP-Proxy-Cached-XML: true');
    readfile($path);
    exit;
}

if ($proxy_cache) {
    make_cache_request();
}

make_request();
