define(function (require) {
    'use strict';

    var $ = require('jquery');
    var AbstractModel = require('../../Abstract');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.Cart.Product.Matrix
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} CartProductValues
     */
    var CartProductMatrixModel = function (CartProductValues) {
        AbstractModel.call(this, CartProductValues);
    };

    var proto = inherits(CartProductMatrixModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} values
     * @chainable
     */
    proto.init = function(values) {

        /**
         * @property internalDescription
         * @default {null}
         * @type {string}
         */
        this.internalDescription = null;

        /**
         * @property matrix1OptionId
         * @default {null}
         * @type {string}
         */
        this.matrix1OptionId = null;

        /**
         * @property matrix1OptionDescription
         * @default {null}
         * @type {string}
         */
        this.matrix1OptionDescription = null;

        /**
         * @property matrix1LongDescription
         * @default {null}
         * @type {string}
         */
        this.matrix1LongDescription = null;

        /**
         * @property matrix2OptionId
         * @default {null}
         * @type {string}
         */
        this.matrix2OptionId = null;

        /**
         * @property matrix2OptionDescription
         * @default {null}
         * @type {string}
         */
        this.matrix2OptionDescription = null;

        /**
         * @property matrix2LongDescription
         * @default {null}
         * @type {string}
         */
        this.matrix2LongDescription = null;

        /**
         * @property quantity
         * @default {null}
         * @type {number}
         */
        this.quantity = null;

        base.init.call(this, values);

        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param json the original JSON data.
     */
    proto.fromJSON = function(json) {
        this.internalDescription = json.optionInternalDesc;
        this.matrix1OptionId = json.matrix1OptionId;
        this.matrix1OptionDescription = json.matrix1OptionDesc;
        this.matrix1LongDescription = json.matrix1LongDesc;
        this.matrix2OptionId = json.matrix2OptionId;
        this.matrix2OptionDescription = json.matrix2OptionDesc;
        this.matrix2LongDescription = json.matrix2LongDesc;
        this.quantity = parseInt(json.quantity, 10);

        this.availableValues = {
            matrix1: this.matrix1OptionId
        };
    };

    /**
     * Converts all data into an XML string for sending
     * to the API
     * @method toXML
     * @return {string}
     */
    proto.toXML = function() {
        var $matrix = $('<selectedMatrix/>')
            .append($('<optionInternalDesc/>').text(this.internalDescription))
            .append($('<matrix1OptionId/>').text(this.matrix1OptionId))
            .append($('<matrix1OptionDesc/>').text(this.matrix1OptionDescription))
            .append($('<matrix1LongDesc/>').text(this.matrix1LongDescription))
            .append($('<matrix2OptionId/>').text(this.matrix2OptionId))
            .append($('<matrix2OptionDesc/>').text(this.matrix2OptionDescription))
            .append($('<matrix2LongDesc/>').text(this.matrix2LongDescription))
            .append($('<quantity/>').text(this.quantity));
        var xml = $matrix[0].outerHTML;
        return xml;
    };

    /**
     *
     * @param {string} name
     * @returns {*}
     */
    proto.getValue = function(name) {
	if(typeof this.availableValues != 'undefined')
        return this.availableValues[name];
    };

    return CartProductMatrixModel;
});
