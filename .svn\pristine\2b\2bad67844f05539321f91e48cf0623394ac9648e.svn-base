define(function (require) {
    'use strict';

    var AbstractModel = require('../Abstract');
    var FieldMappings = require ('../../constants/FieldMappings');
    var ImprintOverlay = require('../../util/ImprintOverlay');
    var Settings = require('../../constants/Settings');
    var UIQuestionBlockLinesCollection = require('./collections/QuestionBlockLines');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.Ui.QuestionBlock
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} questionBlock
     */
    var UIQuestionBlockModel = function (questionBlock) {
        AbstractModel.call(this, questionBlock);
    };

    var proto = inherits(UIQuestionBlockModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} questionBlock
     * @chainable
     */
    proto.init = function(questionBlock) {
        /**
         * @property id
         * @type {string}
         * @default {null}
         */
        this.id = null;

        /**
         * @property isRequired
         * @type {Boolean}
         * @default {null}
         */
        this.isRequired = null;

        /**
         * @property requiredInk
         * @type {string}
         * @default {null}
         */
        this.requiredInk = null;

        /**
         * @property info
         * @type {Object}
         * @default {null}
         */
        this.info = null;

        // run the parent init method to parse determine the data type
        base.init.call(this, questionBlock);

        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param {object} json the original JSON data.
     */
    proto.fromJSON = function(json) {
        if (!json.id) {
            json = this.stripInvalidFields(json);

            if (!json.id && json.block !== undefined) {
                json = json.block;
            }
        }

        this.id = json.id;
        this.isRequired = (json.required === 'Y');

        return this;
    };

    /**
     * Sets internal information.
     *
     * @method setInfo
     * @param {Object} info
     * @param {Object} question
     * @param {App.Models.FXG} fxg
     * @param {Object} product
     * @param {App.Models.CustomerProfile} customer
     * @chainable
     */
    proto.setInfo = function(block, question, product, fxg, customer, isLogo) {
        var lines = block && block.line;
        var logo = block && block.logo;

        block = this._setFXG(block, fxg);

        if (lines || logo) {
            lines = new UIQuestionBlockLinesCollection([].concat(lines));
        }

        // Override isRequired based on Product Info
        if (block && block.value) {
            if (block.value === 'A') {
                this.isRequired = false;
            } else if (block.value === 'S') {
                this.isRequired = true;
            }
        }
        
        if (block && block.requiredInk) {
            this.requiredInk = block.requiredInk;
        }

        if (lines && lines.length) {
            block = block || {};
            block.isRequired = this.isRequired;
            lines.setInfo(block, question, product, fxg, customer);
        }
        this.info = block;
        this.lines = lines;
        this.hasLogo = false;
        if (block && block.surcharges) {
            this.surcharges = [].concat(block.surcharges);
        }
        if (block && (block.logo === 'Y')) {
            this.hasLogo = true;
        } else if (block && (block.logo === 'S')) {
            if (!isLogo) {
                this.hasLogo = true;
            }
        }

        return this;
    };

    /**
     * Sets the x/y coordinates on the block based on the FXG data.
     *
     * @method _setFXG
     * @param {Object} info
     * @param {App.Models.FXG} fxg
     * @return {Object} info
     */
    proto._setFXG = function(info, fxg) {
        if (info && info.id in fxg) {
            var group = fxg[info.id];
            var rotation = parseInt(group.children[0] && group.children[0].rotation, 10) || 0;
            var previewRatio = Settings.PREVIEW_WIDTH / Settings.PREVIEW_HEIGHT;
            var fxgRatio = fxg.viewWidth / fxg.viewHeight;
            var scale = 1;

            if (previewRatio < fxgRatio) {
                // preview is wide, width is full
                scale = Settings.PREVIEW_WIDTH / fxg.viewWidth;
            } else {
                // preview is tall, height is full
                scale = Settings.PREVIEW_HEIGHT / fxg.viewHeight;
            }

            //console.log('==========================================');
            //console.log('group.a: ' + JSON.stringify(group));

            var count = 0;
            group.children.forEach(function (e) {
                if (e.label.includes('_block')) {
                    count++;
                }
            });
            info.pos = {
                scale: scale,
                x: group.x * scale,
                y: group.y * scale,
                w: ImprintOverlay.getWidth(group),
                h: ImprintOverlay.getHeight(group, rotation)
            };
            if (count > 0) {
                info.pos.children = group.children;
            }
            //console.log('pos.a=' + JSON.stringify(info.pos));

            if (group.label !== info.id) {
                info.pos.master = group.label;
            }

            info.pos = ImprintOverlay.applyRotation(info.pos, rotation);
            //console.log('pos.b=' + JSON.stringify(info.pos));
            info.fxg = fxg;
        }

        return info;
    };

    /**
     * Returns an object containing all selections made by
     * the user for all contained questions
     *
     * @method getValues
     * @returns {Object}
     */
    proto.getValues = function() {
        var response;
        var lines = this.lines;

        if (lines && lines.length) {
            response = {
                id: this.id,
                hasLogo: this.hasLogo,
                lines: this.lines.getValues(),
                prefixs: this.lines.getPrefixs()
            };

            if (this.info && this.info.desc) {
                response.description = this.info.desc;
            }

            if (this.requiredInk) {
                response.requiredInk = this.requiredInk;
            }

            return response;
        }

        return false;
    };

    /**
     * Determines if values are valid, or gives a reason why not.
     *
     * @method isValid
     * @return {Boolean|String} Either `true`, or an error message.
     */
    proto.isValid = function() {
        var lines = this.lines;

        if (lines && lines.length()) {
            return lines.isValid();
        }

        return true;
    };

    return UIQuestionBlockModel;
});
