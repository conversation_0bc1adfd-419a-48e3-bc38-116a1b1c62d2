define(function (require) {
    'use strict';

    var AbstractModel = require('../Abstract');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.Collections.Abstract
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Array.<App.Models.Abstract>} items
     */
    var AbstractCollection = function (items) {
        /**
         * Current item that is being interacted with
         *
         * @property _pointer
         * @type {number}
         * @default null
         * @private
         */
        this._pointer = null;

        /**
         * Stores the items in the collection
         *
         * @property _items
         * @type {Array.<App.Models.Abstract>}
         * @default null
         * @private
         */
        this._items = null;

        /**
         * The unique ID used to find an element
         *
         * @property _id
         * @type {string}
         * @default 'id'
         * @private
         */
        this._id = 'id';

        /**
         * Stores an unmodified version of the items
         * that can be used to reset the original _items array
         *
         * @property _tempItems
         * @type {Array.<App.Models.Abstract>}
         * @default null
         * @private
         */
        this._tempItems = null;

        AbstractModel.call(this, items);
    };

    var proto = inherits(AbstractCollection, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Stores the item class for a given collection
     *
     * @type {App.Models.Abstract}
     */
    proto.itemClass = AbstractModel;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} items
     * @chainable
     */
    proto.init = function(items) {
        this._items = [];
        this._pointer = 0;

        base.init.call(this, items);

        this._tempItems = this._items;

        return this;
    };

    /**
     * Change which item key is used as the primary id.
     *
     * @method setLookupId
     * @param {String} id
     * @chainable
     */
    proto.setLookupId = function(id) {
        this._id = id;

        return this;
    };

    /**
     * @method getById
     * @param {String} id
     * @return {App.Models.Abstract|undefined}
     */
    proto.getById = function(id) {
        var item;
        var items = this._items;
        var length = items.length;
        var i = 0;

        // Ensure proper lookup
        id = String(id);

        for (; i < length; i++) {
            item = items[i];

            if (item && String(item[this._id]) === id) {
                return item;
            }
        }
    };

    /**
     * Returns the current line usage
     * @return {App.Models.Abstract}
     */
    proto.current = function() {
        return this._items[this._pointer];
    };

    /**
     * Returns the next item
     * If the pointer is already at the end of the collection, do not modify the pointer and return `null`
     *
     * @method next
     * @return {App.Models.Abstract|null}
     */
    proto.next = function() {
        if (this._pointer >= this._items.length - 1) {
            return null;
        }

        this._pointer++;

        return this.current();
    };

    /**
     * Returns the previous item
     * If the pointer is already at 0, do not modify the pointer and return `null`
     *
     * @method previous
     * @return {App.Models.Abstract|null}
     */
    proto.previous = function() {
        if (this._pointer <= 0) {
            return null;
        }

        this._pointer--;

        return this.current();
    };

    /**
     * Starts back at the beginning of the items list
     * @chainable
     */
    proto.rewind = function() {
        this._pointer = 0;
        return this;
    };

    /**
     * Starts at the end of the items list
     *
     * @method fastForward
     * @chainable
     */
    proto.fastForward = function() {
        this._pointer = Math.max(0, this._items.length - 1);
        return this;
    };

    /**
     * Runs a callback for each item in the _items list
     *
     * @method each
     * @param {function} callback
     * @chainable
     */
    proto.each = function(callback) {
        var items = this._items;
        var length = items.length;
        var i = 0;

        for (; i < length; i++) {
            if (callback(items[i], i, items) === false) {
                break;
            }
        }

        return this;
    };

    /**
     * Limit the result set with an optional offset parameter
     *
     * @param {number} limit
     * @param {number} offset
     * @chainable
     */
    proto.limit = function(limit, offset) {
        limit = limit || 10;
        offset = offset || 0;

        this._reset();
        this._items = this._tempItems.slice(offset, limit);

        return this;
    };

    /**
     * Get the collection length
     *
     * @method length
     * @return {number}
     */
    proto.length = function() {
        return this._items.length;
    };

    /**
     * Get the current pointer
     *
     * @method pointer
     * @return {Number}
     */
    proto.pointer = function() {
        return this._pointer;
    };

    /**
     * Resets the collection to the original/unmodified state
     *
     * @method _reset
     * @chainable
     * @private
     */
    proto._reset = function() {
        this._items = this._tempItems;
        return this;
    };

    /**
     * Sets up handlers, if any
     *
     * @method setupHandlers
     * @chainable
     */
    proto.setupHandlers = function() {
        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param {object|Array} items the original JSON data.
     */
    proto.fromJSON = function(items) {
        items = this.stripInvalidFields(items);

        if (!items) {
            return;
        }
        if (!items.length) {
            this.add(items);
            return;
        }
        this.addMultiple(items);
    };

    /**
     * Add a new item to the collection
     *
     * @method add
     * @param item {App.Models.Abstract|Document|object}
     * @chainable
     */
    proto.add = function(item) {
        if (item instanceof this.itemClass) {
            this._items.push(item);
        } else if (item.documentElement || item.appendChild || typeof item === 'object') {
            this._items.push(new this.itemClass(item));
        }
        return this;
    };

    /**
     * retrieves an item in the collection by its index in the collection
     *
     * @method getEntryAtIndex
     * @param {number} index
     * @return {App.Models.Abstract}
     */
    proto.getEntryAtIndex = function(index) {
        return this._items[index];
    };

    /**
     * Add multiple new items
     *
     * @method addMultiple
     * @param items {Array.<object|App.Models.Abstract>} the items to add to the collection
     * @chainable
     */
    proto.addMultiple = function(items) {
        var item;
        var i = 0;

        for (; i < items.length; i++) {
            item = items[i];
            if (!item) {
                continue;
            }
            this.add(item);
        }
        return this;
    };

    return AbstractCollection;
});
