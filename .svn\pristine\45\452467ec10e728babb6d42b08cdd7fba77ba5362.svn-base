{{#if ENVELOPE_RETURN}}


<input type="checkbox" id="{{id}}" name="{{#if blockId}}{{blockId}}_{{/if}}{{id}}" value="{{#if fieldValue }}{{fieldValue}}{{else}}{{id}}{{/if}}"
    class="checkbox {{#if fieldValue}} isChecked{{/if}}"
    {{#is id default}} checked{{/is}}{{#if fieldValue}} checked{{/if}}
/>
<label  id="{{id}}" for="{{id}}" tabindex="0" class="d-inline-flex checkbox-label-width {{#if className}}labelClass bluebleed{{/if}} {{#if ENVELOPE_RETURN}}envelopeLabel{{/if}}" >
    <div class="align-self-center"> 
		{{#if classNameN}}
			{{> question/required}}
		{{/if}}
		{{desc}}(+${{imprintingPrice}})
	</div>
</label>


{{else}}


{{#if signatures}}

	<input type="checkbox" id="{{id}}" name="{{#if blockId}}{{blockId}}_{{/if}}{{id}}" value="{{id}}"
    class="checkbox  {{#is default 'Y'}} isChecked{{/is}}"
  {{#is default 'Y'}} checked{{/is}}
/>
<label id="{{id}}" for="{{id}}" tabindex="0" class="d-inline-flex checkbox-label-width {{#if className}}labelClass bluebleed{{/if}} {{#if ENVELOPE_RETURN}}envelopeLabel{{/if}}" >
    <div class="align-self-center">
		{{#if classNameN}}
			{{> question/required}}
		{{/if}}
		{{desc}}
	</div>
</label>

{{else}}

{{#if folding}}
		<input type="checkbox" id="{{id}}" name="{{#if blockId}}{{blockId}}_{{/if}}{{id}}" value="{{id}}"
    class="checkbox  {{#is default 'Y'}} isChecked{{/is}}"
  {{#is default 'Y'}} checked{{/is}}
/>
<label  id="{{id}}" for="{{id}}" tabindex="0" class="d-inline-flex checkbox-label-width {{#if className}}labelClass bluebleed{{/if}} {{#if ENVELOPE_RETURN}}envelopeLabel{{/if}}" >
    <div class="align-self-center">
		{{#if classNameN}}
			{{> question/required}}
		{{/if}}
		{{desc}}
	</div>
</label>

{{else}}

<input type="checkbox" id="{{id}}" name="{{#if blockId}}{{blockId}}_{{/if}}{{id}}" value="{{#ifEq id 'SL'}}{{#if fieldValue }}{{fieldValue}}{{/if}}{{/ifEq}}{{#ifNotEq id 'SL'}}{{#if fieldValue}}{{fieldValue}}{{else}}{{id}}{{/if}}{{/ifNotEq}}"
    class="checkbox{{#ifEq id 'SL'}}{{#if fieldValue }} isChecked{{/if}} {{/ifEq}}{{#ifNotEq id 'SL'}}{{#is id default}} isChecked{{/is}}{{#is fieldValue default}} isChecked{{/is}}{{/ifNotEq}}"
    {{#ifEq id 'SL'}}{{#if fieldValue }} checked{{/if}} {{/ifEq}}{{#ifNotEq id 'SL'}}{{#is id default}} checked{{/is}}{{#is fieldValue default}} checked{{/is}}{{/ifNotEq}}
/>
<label id="signature-{{id}}" for="{{id}}" tabindex="0" class="d-inline-flex checkbox-label-width {{#if className}}labelClass{{/if}} {{#if ENVELOPE_RETURN}}envelopeLabel{{/if}}" >
    <div class="align-self-center">
		{{#if classNameN}}
			{{> question/required}}
		{{/if}}
		{{desc}}
	</div>
</label>

{{/if}}
{{/if}}
{{/if}}


{{#if ENVELOPE_RETURN}}
<div class ="return_help_img">
<img id="returnHelp" alt="returnHelp" src="{{host_url}}/webasset/w2p_mobile/{{baseAppUrl}}assets/media/images/helpText.png" class="js-button-action"
	data-template='<div id="returnWorks" class="custpop popover" role="tooltip"><div class="arrow"></div><div
	class="popover-body"></div></div>' data-container="body"  data-placement="bottom"   data-content="" data-original-title="" title=""></img>
</div>
<div class="address_container"> 
	<img id="returnHelpImg" alt="returnHelpImg" src="{{host_url}}/webasset/w2p_mobile/{{baseAppUrl}}assets/media/images/border.png"  title=""></img>
</div>
<div class="address_container_selected"> 
	<img id="returnHelpImg" alt="returnHelpImg" src="{{host_url}}/webasset/w2p_mobile/{{baseAppUrl}}assets/media/images/border2.png"  title=""></img>
</div>

<div id="returnPopup" style="display:none;">
	<div>
		<img src="{{host_url}}/webasset/w2p_mobile/{{baseAppUrl}}assets/media/images/tooltip-close.png" alt="foldingCloseIcon" id="foldingCloseIcon" class="close tooltip-close" style="position: absolute; top: 0; right: 7px;"/>
		<div class='titletxt'>Imprint Return Addresses</div><br/>
		<p class='othertxt'>{{ENVELOPE_IMPRINT_COPY}}</p><br/>
		{{#if ENVELOPE_IMPRINT_SHOW_GRID }}
		<div>
			<ul class="return_container">
				<li class="return_row return_heading">
					<div class="return_col return_top">Qty</div>
					<div class="return_col return_top">50</div>
					<div class="return_col return_top">100</div>
					<div class="return_col return_top">200</div>
					<div class="return_col return_top return_last">300</div>
				</li>
				<li class="return_row return_last">
					<div class="return_col">Price</div>
					<div class="return_col">$15</div>
					<div class="return_col">$20</div>
					<div class="return_col">$30</div>
					<div class="return_col return_last">$40</div>
				</li>
			</ul>
		</div>
		{{/if}}
	</div>
</div>
{{/if}}

&nbsp;{{#if noc}}<span id="NumberingHelp" class="js-button-action  help-icon-align"
	data-template='<div id="reverse" class="custpop popover" role="tooltip"><div class="arrow"></div><div class="popover-body"></div></div>'
	data-container="body" data-content="" data-original-title="" title="" data-placement="bottom"  alt="NumberingHelp"></span>{{/if}}

{{#if anotherSignature}}<span id="SignatureHelp" class="js-button-action  help-icon-align"
	data-template='<div id="signatureLine-popup" class="custpop popover" role="tooltip"><div class="arrow"></div><div class="popover-body"></div></div>'
	data-container="body" data-content="" data-original-title="" title="" data-placement="bottom" alt="SignatureHelp"></span>{{/if}}


{{#if folding}}
<br>
<div class="foldingHelpLabel">How Does This Work? </div><img id="foldingHelp" alt="foldingHelp" src="{{host_url}}/webasset/w2p_mobile/{{baseAppUrl}}assets/media/images/helpText.png" class="js-button-action foldingHelpImg"
	data-template='<div id="foldingWorks" class="custpop popover" role="tooltip"><div class="arrow"></div><div class="popover-body"></div></div>'
	data-container="body" data-placement="bottom" data-content="" data-original-title="" title=""></img>
<div id="foldingPopup" style="display:none;">
	<div>
		<img src="{{host_url}}/webasset/w2p_mobile/{{baseAppUrl}}assets/media/images/tooltip-close.png" id="foldingCloseIcon" alt="foldingCloseIcon" class="close tooltip-close" style="position:absolute; top:0; right:7px;"/>
		<div class='titletxt'>Folding Service Options</div><br/>
		<p class='othertxt'>{{FOLDING_COPY}}</p><br/>
		<div class="row">
			<div class="col text-center">
				<div class="foldSubhead">With Folding</div>
				<img src="{{host_url}}/webasset/w2p_mobile/{{baseAppUrl}}assets/media/images/u130.png" id="withFold" alt="withFold" class="withFold" style="padding-top:15px"/>
			</div>
			<div class="col text-center">
				<div class="foldSubhead">Without Folding</div>
				<img src="{{host_url}}/webasset/w2p_mobile/{{baseAppUrl}}assets/media/images/u132.png" id="withoutFold" alt="withoutFold" class="withoutFold" style="padding-left:10px"/>
			</div>
		</div>
	</div>
</div>
{{/if}}