define(function(require){

//automatically generated, do not edit!
//run `node build` instead
return {
    'clone' : require('./lang/clone'),
    'createObject' : require('./lang/createObject'),
    'ctorApply' : require('./lang/ctorApply'),
    'deepClone' : require('./lang/deepClone'),
    'defaults' : require('./lang/defaults'),
    'inheritPrototype' : require('./lang/inheritPrototype'),
    'is' : require('./lang/is'),
    'isArguments' : require('./lang/isArguments'),
    'isArray' : require('./lang/isArray'),
    'isBoolean' : require('./lang/isBoolean'),
    'isDate' : require('./lang/isDate'),
    'isEmpty' : require('./lang/isEmpty'),
    'isFinite' : require('./lang/isFinite'),
    'isFunction' : require('./lang/isFunction'),
    'isInteger' : require('./lang/isInteger'),
    'isKind' : require('./lang/isKind'),
    'isNaN' : require('./lang/isNaN'),
    'isNull' : require('./lang/isNull'),
    'isNumber' : require('./lang/isNumber'),
    'isObject' : require('./lang/isObject'),
    'isPlainObject' : require('./lang/isPlainObject'),
    'isRegExp' : require('./lang/isRegExp'),
    'isString' : require('./lang/isString'),
    'isUndefined' : require('./lang/isUndefined'),
    'isnt' : require('./lang/isnt'),
    'kindOf' : require('./lang/kindOf'),
    'toArray' : require('./lang/toArray'),
    'toNumber' : require('./lang/toNumber'),
    'toString' : require('./lang/toString')
};

});
