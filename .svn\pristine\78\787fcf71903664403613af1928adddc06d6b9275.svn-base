/* ---------------------------------------------------------------------
Original Author: <PERSON>
Contributors: N/A

Target Browsers: IE8, IE7, IE6
Media Type: Screen, Projection
Width: All Sizes
------------------------------------------------------------------------ */

.radio {
    display: inline-block;
}

.radio + label:before {
    content: none;
}

.checkbox {
    display: inline-block;
}



/* hide element without breaking change and click events */
.designRadio {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    display: block;
    position: absolute;
    left: 0;
    top: 0;
}
