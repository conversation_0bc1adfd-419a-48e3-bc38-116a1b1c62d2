/* ---------------------------------------------------------------------
Original Author: <PERSON>
Contributors: N/A

Target Browsers: All
Media Type: Screen, Projection
Width: All Sizes
------------------------------------------------------------------------ */
/*csslint
    important: false,
    compatible-vendor-prefixes: false
*/

/* ---------------------------------------------------------------------
 Utility Classes
------------------------------------------------------------------------ */
.isHidden {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    border: 0 !important;
    margin: 0 !important;
    clip: rect(1px 1px 1px 1px) !important;
    clip: rect(1px, 1px, 1px, 1px) !important;
    overflow: hidden !important;
}

.isImageReplacement {
    overflow: hidden;
    direction: ltr;
    text-indent: -999px;
}

/* Webkit selector bugfix: http://css-tricks.com/webkit-sibling-bug/ */
.isPseudoSibling {
    -webkit-animation: pseudoSiblingBugfix infinite 1s;
}

.td_underline {
  text-decoration: underline;
}

.pointer {
  cursor: pointer;
}

.mt-15 {
  margin-top: 15px !important;
}

.mb-15 {
  margin-bottom: 15px !important;
}

@-webkit-keyframes pseudoSiblingBugfix {
    from { padding: 0; }
    to   { padding: 0; }
}
