<div class="split">
    <div class="split-50-left prev_button">
        <button class="js-prev button_neutral button button_prev btn_widget_prev" id="button_prev">PREVIOUS
            STEP</button>
    </div>
    <div class="split-50-right next_button">
        <button class="js-next button button_next btn_widget_next fs12" id="{{tlIdPrefix}}Body-Next-{{tlId}}">
            <span class="js-next-label">{{{next}}}</span>
        </button>
    </div>
</div>

{{#if showSkip}}
<div class="link_Skip pt-20">
    <p align="center"><a class="js-fbt-skip" id="{{tlIdPrefix}}SkipToNextProduct">{{{skipText}}}</a></p>
</div>
{{/if}}
{{!-- <button class="js-prev link link_button link_prev" type="button" id="Header-Previous-Designs">
    « Previous
</button> --}}