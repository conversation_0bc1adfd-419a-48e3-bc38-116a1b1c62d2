/* ---------------------------------------------------------------------
Target Browsers: All
Media Type: Screen, Projection
Width: All Sizes
------------------------------------------------------------------------ */
/*csslint
    compatible-vendor-prefixes: false
*/

/* ---------------------------------------------------------------------
 Toggle Styles
------------------------------------------------------------------------ */
.toggle {
    overflow: hidden;
    height: 0;
    margin-top: 0;
    opacity: 0;
    -webkit-transition:
        margin 500ms,
        height 500ms,
        opacity 450ms;
    transition:
        margin 500ms,
        height 500ms,
        opacity 450ms;
}

.toggle_isOpen {
    height: auto;
    margin-top: 12px;
    opacity: 1;
    -webkit-transition:
        margin 500ms,
        height 500ms,
        opacity 450ms 50ms;
    transition:
        margin 500ms,
        height 500ms,
        opacity 450ms 50ms;
}

.toggle_rgb {
    width: 20px;
    height: 20px;
}
label span.toggle_rgb {
    pointer-events: none;
}

.inkRadioLabel{
    cursor: pointer;
}

.colorblocks > * {
    display: flex !important;
    flex-flow: row !important;
    margin-bottom: 0 !important;
}
.inkRadioLabel{
    display: flex ;
    flex-flow: row;
}
.colorblocks {
    column-width: 135px;
}
.inkColorBox{
    padding: 0 !important;
    margin: 0 !important;
}
.inkcolorRadio{
    margin-bottom: 6.5px;
    margin-right: 3px;
}
.inkColorLabel{
    margin-top: 6px;
    margin-left: 3px;
    font-size: 13px;
}
.mb5{
    margin-bottom: 5px !important;
}
.mb10{
    margin-bottom: 10px !important;
}
.mt10{
    margin-top: 10px !important;
}
.logoDescr{
    line-height: 1.1 !important;
}