define(function (require) {
    'use strict';

    var AbstractCollection = require('../../collections/Abstract');
    var UIQuestionOptionModel = require('../QuestionOption');
    var inherits = require('mout/lang/inheritPrototype');
    var getParam = require('mout/queryString/getParam');
    var toLookup = require('mout/array/toLookup');

    /**
     * @type {Function}
     * @param {Object} index
     * @param {Object} item
     * @return {Object}
     */
    var toMatrixLookup = function(index, item) {
        var id = item.code;
        item.uid = id;
        var list = index[id] || (index[id] = {});
        list[item.id] = item;
        return index;
    };

    /**
     * @type {Function}
     * @param {Object} index
     * @param {Object} item
     * @return {Object}
     */
    var toMatrix2Lookup = function(index, item) {
        var id = item.code + "_" + item.code2;
        item.uid = id;
        var list = index[id] || (index[id] = {});
        list[item.id] = item;
        return index;
    };

    /**
     * @type {Function}
     * @param {Object} options
     * @return {Object}
     */
    var doubleMatrixInfoLookup = function(options) {
        var infoIndex = {};
        for (var i=0; i<options.length; i++) {
            var option = options[i];
            var key = option.code + "_" + option.code2;
            //console.log("JoeTest.doubleMatrixInfoLookup.id: " + key);
            infoIndex[key] = option;
        }
        return infoIndex;
    };

    /**
     * @class App.Models.Ui.Collections.QuestionOptions
     * @extends App.Models.Collections.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} blocks
     */
    var UIQuestionOptionsCollection = function (blocks) {
        AbstractCollection.call(this, blocks);
    };

    var proto = inherits(UIQuestionOptionsCollection, AbstractCollection);

    /**
     * @property itemClass
     * @type {App.Models.Ui.QuestionOption}
     */
    proto.itemClass = UIQuestionOptionModel;

    /**
     * Recursively sets product info on all steps.
     *
     * @method setInfo
     * @param {Object} info
     * @param {Object} product
     * @param {App.Models.CustomerProfile} customer
     * @chainable
     */
    proto.setInfo = function(info, product, customer) {
        var optionInfo = info && info.option;
        var optionIndex;
        var matrixInfo = product && product.matrixInfo;
        if (matrixInfo && info.id == 'matrix1') {
            var matrixIndex;
            matrixInfo = Array.isArray(matrixInfo) ? matrixInfo : new Array(matrixInfo);

            //ToDo: Can these be consolidated to be common
            var matrixCount = info.matrixName2 ? 2 : 1;
            if (matrixCount == 1) {
                optionIndex = toLookup(optionInfo, 'id');
                matrixIndex = matrixInfo.reduce(toMatrixLookup, {});
                //matrixIndex = toLookup(matrixInfo, 'id');
            } else if (matrixCount == 2) {
                optionIndex = toLookup(optionInfo, 'id');
                matrixIndex = matrixInfo.reduce(toMatrix2Lookup, {});
                //matrixIndex = toLookup(matrixInfo, 'id');   //Joe
            }
            //console.log("JoeTest.optionIndex.length: " + Object.keys(optionIndex).length);
            //console.log("JoeTest.optionIndex: " + JSON.stringify(optionIndex, undefined, 4));
            //console.log("JoeTest.matrixInfo: " + JSON.stringify(matrixInfo, undefined, 4));
            //console.log("JoeTest.matrixIndex.length: " + Object.keys(matrixIndex).length);
            //console.log("JoeTest.matrixIndex: " + JSON.stringify(matrixIndex, undefined, 4));
            this.each(this._setMatrix.bind(this, matrixIndex));
            //console.log("JoeTest.that: " + JSON.stringify(this._items, undefined, 4));
		} else {
            optionIndex = toLookup(optionInfo, 'id');
        }
        this.each(this._setInfo.bind(this, optionIndex, info, product, customer));

        return this;
    };

    /**
     * @method _setInfo
     * @param {Object} optionIndex
     * @param {App.Models.Ui.Question} question
     * @param {Object} product
     * @param {App.Models.CustomerProfile} customer
     * @param {App.Models.Ui.QuestionOption} option
     * @callback
     */
    proto._setInfo = function(optionIndex, question, product, customer, option) {
        var info = optionIndex[option.id];
        //console.log("JoeTest.setInfo.info: " + option.id + " - " + JSON.stringify(info, undefined, 4));
        option.setInfo(info, question, product, customer);
    };

    /**
     * @method _setMatrix
     * @param {Object} matrixIndex
     * @param {App.Models.Ui.QuestionOption} option
     * @callback
     */
    proto._setMatrix = function(matrixIndex, option) {
        //console.log("JoeTest.setMatrix.option.id: " + option.id);
        //var key = getParam(location.search, 'skuId') + '_' + option.id;
        //console.log("JoeTest.setMatrix.option. skuId_option.id: " + key);
        //var matrix = matrixIndex[key];
        var matrix = matrixIndex[option.id];
        //console.log("JoeTest.setMatrix.option.matrixIndexItem: " + JSON.stringify(matrix, undefined, 4));
        option.setMatrix(matrix);
    };

    return UIQuestionOptionsCollection;
});
