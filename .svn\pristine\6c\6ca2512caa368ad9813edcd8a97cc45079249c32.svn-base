{{#if simFields.length}}
    {{#each simFields}}
        <label class="txtLarge">
           <p> {{> question/required}}

            {{#is blockId 'CI'}}
                {{desc}}
            {{else}}
            <h2 class="hdg hdg_h2 hdg_title"> {{#unless @index}}{{desc}}{{/unless}}</h2>           
             <p class="bottom_space"> <i class = "italic_desc">{{#unless @index}}{{addDesc}}{{/unless}} </i></p>
            {{/is}}

            <input class="inputBox {{piiClass}}"
                type="text"
                name="{{#if blockId}}{{blockId}}_{{/if}}{{id}}"
                id="{{#if blockId}}{{blockId}}_{{/if}}{{id}}"
                maxlength="{{maxLength}}"
                value="{{default}}" />
            <span class="error js-error"></span>
             </p>
        </label>
    {{/each}}
{{else}}
    <label class="txtLarge">
        <p class="error js-personalization-error"></p>
        {{> question/required}}
        {{desc}}
        
        <form autocomplete="off" onsubmit="return false;">
            <input class="inputBox {{piiClass}}"
                type="text"
                name="{{#if blockId}}{{blockId}}_{{/if}}{{id}}"
                id="{{#if blockId}}{{blockId}}_{{/if}}{{id}}"
                maxlength="{{maxLength}}"
                value="{{default}}" autocomplete="off" />
            <span class="error js-error"></span>
        </form>
        
    </label>
{{/if}}
