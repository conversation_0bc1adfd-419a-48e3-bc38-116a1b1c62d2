define(function (require) {
    'use strict';

    var AbstractModel = require('./Abstract');
    var ConfigurationProvider = require('../providers/Configuration');
    var ContentProvider = require('../providers/Content');
    var CustomerProfileProvider = require('../providers/CustomerProfile');
    var LineUsageProvider = require('../providers/LineUsage');
    var ProductInfoProvider = require('../providers/ProductInfo');
    var FXGProvider = require('../providers/FXG');
    var SessionIdProvider = require('../providers/SessionId');
    var Settings = require ('../constants/Settings');
    var UITypesProvider = require('../providers/UITypes');
    var addToCartTemplate = require('hbs!../../../templates/xml/addToCart');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var Query = require('models/Query');
    var q = require('q');

    // fixture data
    var config = require('text!../../../../test/fixtures/configuration.dev.xml');
    var productInfoString = require('text!../../../../test/fixtures/reconfigureProduct.xml');
    var uiTypes = require('text!../../../../test/fixtures/uiSteps.xml');
    var fxg = require('text!../../../../test/fixtures/fxg.xml');
    var content = require('text!../../../../test/fixtures/ExternalContent.xml');
    var customText = require('text!../../../../test/fixtures/customTextLookup.xml');
    var lineUsage = require('text!../../../../test/fixtures/lineUsage.xml');

    LineUsageProvider.sendRequest = function() {
        return q(lineUsage);
    };

    CustomerProfileProvider.sendRequest = function() {
        return q(customText);
    };

    ContentProvider.sendRequest = function() {
        return q(content);
    };

    FXGProvider.sendRequest = function() {
        return q(fxg);
    };

    ProductInfoProvider.sendRequest = function() {
        return q(productInfoString);
    };

    ConfigurationProvider.sendRequest = function() {
        return q(config);
    };

    UITypesProvider.sendRequest = function() {
        return q(uiTypes);
    };

    /**
     * @name App.Models.Product
     * @class App.Models.Product
     * @type App.Models.Abstract
     * @constructor
     */
    var ProductModel = function() {
        bindAll(this,
            'initInfo',
            'initModel',
            'initSession'
        );

        AbstractModel.call(this);
    };

    /**
     * ProductModel extends AbstractModel
     * @type {App.Models.Abstract}
     */
    var proto = inherits(ProductModel, AbstractModel);

    /**
     * Initializes the model
     *
     * @method init
     * @chainable
     */
    proto.init = function() {
        /**
         * @property query
         * @type {App.Models.Query}
         */
        this.query = Query.getInstance();

        /**
         * @property info
         * @default {null}
         * @type {App.Models.ProductInfo}
         */
        this.info = null;

        /**
         * @property steps
         * @default {null}
         * @type {App.Models.Ui.Collections.Steps}
         */
        this.steps = null;

        /**
         * @property content
         * @default {null}
         * @type {App.Models.Content}
         */
        this.content = null;
        /**
         *
         * @property customer
         * @default {null}
         * @type {App.Models.CustomerProfile}
         */
        this.customer = null;

        /**
         * @property questions
         * @default {null}
         * @type {App.Models.Ui.Collections.Questions|Array.<App.Models.Ui.Question>}
         */
        this.questions = null;

        /**
         * @property ready
         * @type {Promise}
         */
        this.ready = ConfigurationProvider
            .getConfiguration()
            .then(this.initSession)
            .spread(this.initInfo)
            .spread(this.initModel);

        return this;
    };

    /**
     * @method initSession
     * @return {Array}
     */
    proto.initSession = function(config) {
        var query = this.query;
        var sessionId = query.sessionId;

        if (Settings.FETCH_SESSION_ID) {
            sessionId = SessionIdProvider
                .setConfig(config)
                .getSessionId();
        }

        return [config, sessionId];
    };

    /**
     * @method initInfo
     * @return {Array}
     */
    proto.initInfo = function(config, sessionId) {
        var customer = null;
        var lineUsage = null;
        var query = this.query;
        var profileId = query.profileId;

        // Override profile ID. For dev/testing.
        if (Settings.PROFILE_ID) {
            profileId = Settings.PROFILE_ID;
        }

        var productInfoParams = {
            productId: query.productId,
            skuId: query.skuId,
            fulfillmentId: query.fulfillmentId,
        };
        // 'fromPage' parameter should be passed only if it exists and has a value.
        if (query.fromPage != null && query.fromPage != "") {
            productInfoParams.fromPage = query.fromPage;
        }

        var info = ProductInfoProvider
            .setConfig(config)
            .getProductInfo(productInfoParams);

        var types = UITypesProvider
            .setConfig(config)
            .getTypes();

        var fxg = FXGProvider
            .setConfig(config)
            .getFXG(query.productId);

        var content = ContentProvider
            .setConfig(config)
            .getContent();

        if (profileId) {
            customer = CustomerProfileProvider
                .setConfig(config)
                .getProfile(profileId, sessionId);

            lineUsage = LineUsageProvider
                .setConfig(config)
                .getLineUsage();
        }

        return [info, types, fxg, content, customer, lineUsage];
    };

    /**
     * @method initModel
     * @param {App.Models.ProductInfo} info
     * @param {App.Models.Ui.Collections.Types} types
     * @param {App.Models.FXG} fxg
     * @param {App.Models.Content} content
     * @param {App.Models.CustomerProfile} customer
     * @chainable
     */
    proto.initModel = function(info, types, fxg, content, customer, lineUsage) {
        var productInfo = info && info.productInfo;
        var type = types.getById(productInfo.group);
        var steps = type.steps;

        if (customer && lineUsage) {
            customer.mapLineUsage(lineUsage);
        }

        steps.setInfo(info, fxg, customer);

        this.info = info;
        this.steps = steps;
        this.content = content;
        this.customer = customer;
        this.questions = steps.getQuestions();

        return this;
    };

    /**
     * @method getProductDescription
     * @return {String}
     */
    proto.getProductDescription = function() {
        var productId = this.questions.getById('productId');
        var part = productId && productId.options.getById(productId.getValue());

        return part && part.desc;
    };

    /**
     * @method getQuantityValue
     * @return {String}
     */
    proto.getQuantityValue = function() {
        var quantity = this.questions.getById('quantity');

        return quantity && quantity.getValue();
    };

    /**
     * Generates <addToCart> xml using the addToCart.hbs template
     *
     * @method toXML
     * @return {string}
     */
    proto.toXML = function() {
        var query = this.query;
        var info = this.info;
        var data = this.steps.getValues();

        var productId = data.values.productId.value;
        var quantity = data.values.quantity.value;

        var pricing =  this._getPricing(productId, quantity, data.surcharges, info.priceInfo);

        return addToCartTemplate({
            query: query,
            info: info && info.productInfo,
            header: info,
            data: data,
            pricing: pricing
        }).replace(/\n\s*\n/g, '\n');
    };

    /**
     * Retrieves applicable pricing data from productInfo base
     * on user selections
     *
     * @method _getPricing
     * @param {string} productId
     * @param {string} quantity
     * @param {Array} surcharges
     * @returns {{base: number, surcharges: number, total: number}}
     * @private
     */
    proto._getPricing = function(productId, quantity, surcharges) {
        var response = {
            base: this.info.getBasePrice(productId, quantity),
            surcharges: this.info.applySurcharges(surcharges),
            total: 0
        };
        response.total = response.base + response.surcharges;
        return response;
    };

    /**
     * Retrieves applicable pricing data from productInfo base
     * on user selections
     *
     * @method getPricing
     * @returns {{base: number, surcharges: number, total: number}}
     * @private
     */
    proto.getPricing = function() {
        var data = this.steps.getValues();
        var productId = data.values.productId.value;
        var quantity = data.values.quantity.value;
        var surcharges = data.surcharges;

        var response = {
            base: this.info.getBasePrice(productId, quantity),
            surcharges: this.info.applySurcharges(surcharges),
            total: 0
        };

        response.total = response.base + response.surcharges;

        return response;
    };


    return ProductModel;
});
