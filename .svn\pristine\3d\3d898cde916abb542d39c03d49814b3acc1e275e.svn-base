define(function (require) {
    'use strict';

    var AbstractModel = require('./Abstract');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.Font
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} FontValues
     */
    var RegionModel = function (RegionValues) {
        AbstractModel.call(this, RegionValues);
    };

    var proto = inherits(RegionModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} RegionValues
     * @chainable
     */
    proto.init = function(RegionValues) {

        /**
         * @property id
         * @default {null}
         * @type {string}
         */
        this.id = null;

        /**
         * @property desc
         * @default {null}
         * @type {string}
         */
        this.desc = null; 

        // run the parent init method to parse determine the data type
        base.init.call(this, RegionValues);

        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param json the original JSON data.
     */
    proto.fromJSON = function(json) {
        if (!json.id) {
            json = this.stripInvalidFields(json);
            if (!json.id && (json.option !== undefined || json.OPTION !== undefined)) {
                json = json.option || json.OPTION;
            }
        }   
        this.id = json.id;
        this.desc = json.desc;    
    };

    return RegionModel;
});
