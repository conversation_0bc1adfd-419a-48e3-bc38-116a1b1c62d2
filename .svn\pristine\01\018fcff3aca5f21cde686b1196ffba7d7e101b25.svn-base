define(function(require) { // jshint ignore:line
    'use strict';
    try {
        var StorageObjectModel = require('../models/StorageObject');
        var Settings = require('../constants/Settings');
        var RegEx = require('../constants/RegEx');
        var bindAll = require('mout/object/bindAll');
        var Query = require('models/Query');

        var DOMAIN_STORAGE_PREFIX = 'deluxe.sessionStorage.';
        var SET_DELAY = 500;

        /**
         * Key/value pair of items waiting to be set in localStorage
         * If we request the value before it's set, we can get the
         * future value from this object.
         *
         * @type Object
         * @private
         */
        var _queuedItems = {};

        /**
         * Get storage value
         *
         *
         * @param {String} storageKey
         * @returns {*}
         * @private
         */
        var _getValue = function(storageKey) {
            var storageObject = new StorageObjectModel();
            try {
                if (!storageObject.fromString(sessionStorage.getItem(storageKey))) {
                    return false;
                }
            } catch (e) {} finally {
                return storageObject;
            }

        };

        /**
         * Get all data for a sku ID
         *
         * @param {String}sku
         * @returns {Object}
         * @private
         */
        var _getSkuData = function(sku) {
            var prefix = DOMAIN_STORAGE_PREFIX + sku + '.';
            var key;
            var value;
            var data = {};
            try {
                for (key in sessionStorage) {
                    if (sessionStorage.hasOwnProperty(key)) {// && key.indexOf(prefix) === 0) {
                        value = _getValue(key);
                        if (value) {
                            data[key.replace(prefix, '')] = value.getValue();
                        }
                    }
                }
            } catch (e) {} finally {
                return data;
            }

        };

        /**
         * @class App.Providers.SessionStorage
         *
         * @constructor
         */
        var SessionStorageProvider = function() {
            bindAll(this, 'onQueryChange');

            /**
             * @property query
             * @type {App.Models.Query}
             */
            this.query = Query.getInstance();

            /**
             * @property _timers
             * @type Object
             * @private
             */
            this._timers = {};

            this._setSku();
            this._setProfileId();
            this._setPrefix();

            // Capture query changes
            this.query.on(this.query.EVENT_CHANGE, this.onQueryChange);
        };

        var proto = SessionStorageProvider.prototype;

        /**
         * Stores a value in localStorage, tagged with an expiration date based
         * off of the HOURS_UNTIL_EXPIRATION constant
         *
         * @method storeValue
         * @param {string} key
         * @param {string|object} value
         * @chainable
         */
        proto.storeValue = function(key, value) {
            // Don't store some fields for security reasons.
            try {
                if (key && $.inArray(key, Settings.STORAGE_EXCLUDED_FIELDS) !== -1) {
                    return this;
                }
                // Changes made for DCOM-17735
                // Asterisk Numbers Allowed in Session Storage
                // if( (RegEx.ASTERISK_NUMERIC_MUST.test(value)) && ( (key == 'routingNumber') || (key == 'accountNumber') || (key == 'BI_NO') || (key == 'CI_NO')   )) {
                //     return this;
                // }

                var storageObject = new StorageObjectModel(value);
                //var storageKey = this.storagePrefix + key;
                var storageKey = key;
 
                // DCOM-11969 SD = CFG - Changes so that a deposit slip saves data for a later stamp in same session
 
                for(var ind in Settings.STORAGE_SIMILAR_FIELDS) {
                    var value = Settings.STORAGE_SIMILAR_FIELDS[ind];   
                    // console.log('jjj', value);
                    if(storageKey == ind) {
                        this._queueSetItem(value, storageObject);
                    }             
                }
                this._queueSetItem(storageKey, storageObject);
 
                // if(storageKey && ($.inArray(key, Settings.STORAGE_SIMILAR_FIELDS) !== -1) && storageKey == 'accountNumber') {
                //     this._queueSetItem(storageKey, storageObject);
                //     this._queueSetItem('CI_NO', storageObject);
                // } else if(storageKey && ($.inArray(key, Settings.STORAGE_SIMILAR_FIELDS) !== -1) && storageKey == 'CI_NO') {
                //     this._queueSetItem(storageKey, storageObject);
                //     this._queueSetItem('accountNumber', storageObject);
                // } else if(storageKey && ($.inArray(key, Settings.STORAGE_SIMILAR_FIELDS) !== -1) && storageKey == 'routingNumber') {
                //     this._queueSetItem(storageKey, storageObject);
                //     this._queueSetItem('BI_NO', storageObject);
                // } else if(storageKey && ($.inArray(key, Settings.STORAGE_SIMILAR_FIELDS) !== -1) && storageKey == 'BI_NO') {
                //     this._queueSetItem(storageKey, storageObject);
                //     this._queueSetItem('routingNumber', storageObject);
                // } else {
                //     this._queueSetItem(storageKey, storageObject);
                // }
            } catch (e) {} finally {
                return this;
            }

        };

        /**
         * Returns non-expired stored values from localStorage, or false
         *
         * @method getValue
         * @param {string} key
         *
         * @returns {*}
         */
        proto.getValue = function(key) {
            var storageObject;
            try {
                //var storageKey = this.storagePrefix + key;
                var storageKey = key;

                // Get value if item is currently queued to be written
                if (_queuedItems[storageKey] instanceof StorageObjectModel) {
                    return _queuedItems[storageKey].getValue();
                }

                storageObject = _getValue(storageKey);

                // checks whether the stored object is a valid stored object
                // and is unexpired
                if (!storageObject) {
                    sessionStorage.removeItem(storageKey);
                    return false;
                }

                // refresh expiration on each use.
                this._queueSetItem(storageKey, storageObject);

                return storageObject.getValue();
            } catch (e) {} finally {}
        };

        /**
         * Remove item from localStorage
         *
         * @method removeValue
         * @param {String} key
         * @chainable
         */
        proto.removeValue = function(key) {
            try {
                //localStorage.removeItem(this.storagePrefix + key);
                sessionStorage.removeItem(key);
            } catch (e) {} finally {
                return this;
            }

        };

        /**
         * Set sku
         *
         * @method _setSku
         * @private
         */
        proto._setSku = function() {

            /**
             * Cached sku
             *
             * @property _sku
             * @type String
             * @private
             */
            this._sku = this.query.skuId;

            /**
             * @property sku
             * @type {String}
             */
            this.sku = this.query.skuId + '.' || '';
        };

        proto._setProfileId = function() {
            /**
             * Cached profileId
             *
             * @property _profileId
             * @type {String}
             * @private
             */
            this._profileId = this.query.profileId;

            /**
             * @property profileid
             * @type {String}
             */
            this.profileId = this.query.profileId && this.query.profileId + '.' || '';
        };

        /**
         * @method _setPrefix
         * @private
         */
        proto._setPrefix = function() {
            this.storagePrefix = DOMAIN_STORAGE_PREFIX + this.profileId + this.sku;
        };

        /**
         * @method _queueSetItem
         * @param {String} key
         * @param {StorageObjectModel} val
         * @private
         */
        proto._queueSetItem = function(key, val) {
            try {
                var timers = this._timers;
                if (timers.hasOwnProperty(key)) {
                    clearTimeout(timers[key]);
                }

                // Store the value so we can access it with `getValue`
                // before it is actually written to localStorage
                _queuedItems[key] = val;
                if(Settings.PERSIST_IMPRINT){
                    timers[key] = setTimeout(function() {
                        // DCOM-17925 dnd save edit from cart routing and account number
                        if(key && Settings.STORAGE_SIMILAR_FIELDS.hasOwnProperty(key) && val.toString().includes('*')) {
                            return;
                        }
                        _queuedItems[key] = null;
                        sessionStorage.setItem(key, val.toString());
                    }, SET_DELAY);
                }

            } catch (e) {} finally {}
        };

        /**
         * Reset all current sku data
         *
         * @method _reset
         * @param {Object} [data]
         * @private
         */
        proto._reset = function(data) {
            data = data || _getSkuData(this.profileId + this._sku);
            var key;

            for (key in data) {
                if (data.hasOwnProperty(key)) {
                    this.removeValue(key);
                }
            }
        };

        /**
         * If the sku has changed, pull all data from the old sku
         * and save it with the new sku id
         *
         * @method onQueryChange
         */
        proto.onQueryChange = function() {
            if (this._sku !== this.query.skuId) {
                var data = _getSkuData(this.profileId + this._sku);
                var key;

                // Remove all data from old sku
                this._reset(data);

                // Set new sku ID
                this._setSku();

                // Reset the prefix
                this._setPrefix();

                // Clear all existing data from new sku
                this._reset();

                // copy all data from old sku to new sku
                for (key in data) {
                    if (data.hasOwnProperty(key)) {
                        this.storeValue(key, data[key]);
                    }
                }

            }
        };

        return new SessionStorageProvider();
    } catch (e) {} finally {}
});