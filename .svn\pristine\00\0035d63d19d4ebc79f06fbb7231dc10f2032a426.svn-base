define(function (require) {
    'use strict';

    var AbstractProvider = require('./Abstract');
    var Configuration = require('../models/Configuration');
    var Settings = require ('../constants/Settings');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var Query = require('../models/Query');

    /**
     * @class App.Providers.Configuration
     * @extends App.Providers.Abstract
     *
     * @constructor
     */
    var ConfigurationProvider = function () {
        bindAll(this, '_onResponseReceived');

        AbstractProvider.apply(this, arguments);
    };

    var proto = inherits(ConfigurationProvider, AbstractProvider);

    /**
     * Makes a call for the configuration values, or returns the stored
     * values via promise
     *
     * @method getConfiguration
     * @return {Promise}
     */
    proto.getConfiguration = function() {
        if (this.promise) {
            // data will be cached after the first call
            return this.promise;
        }

        this.promise = this
            .getConfig()
            .then(this._onResponseReceived)
            .fail(this._onError);

        return this.promise;
    };

    /**
     * Handles the response from the configuration call
     *
     * @method _onResponseReceived
     * @param {HTMLDocument} data
     * @private
     */
    proto._onResponseReceived = function(data) {
        return new Configuration(data);
    };

    return new ConfigurationProvider();
});
