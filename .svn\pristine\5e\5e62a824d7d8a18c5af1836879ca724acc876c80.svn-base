define(function (require) {
    'use strict';

    var AbstractModel = require('../Abstract');
    var ActionEvents = require('../../constants/ActionEvents');
    var Query = require('../Query');
    var Settings = require('../../constants/Settings');
    var Content = require('i18n!../../constants/nls/en-us/Content');
    var SessionStorage = require('../../providers/SessionStorage');
    var Surcharge = require('util/Surcharge');
    var inherits = require('mout/lang/inheritPrototype');
    var getParam = require('mout/queryString/getParam');
    var underscore = require('mout/string/underscore');
    var editflow;
    if (window.location.href.indexOf("fromPage") > -1) {
        editflow = true;
    } else {
        editflow = false;
    }
    /**
     * @class App.Models.Ui.QuestionOption
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} questionOption
     */
    var UIQuestionOptionModel = function (questionOption) {
        AbstractModel.call(this, questionOption);
    };

    var proto = inherits(UIQuestionOptionModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} questionOption
     * @chainable
     */
    proto.init = function(questionOption) {

         /**
         * @property query
         * @type {App.Models.Query}
         */
         this.query = Query.getInstance();
        /**
         * @property id
         * @type {String}
         * @default {null}
         */
        this.id = null;

        /**
         * @property change
         * @type {Boolean}
         * @default {null}
         */
        this.change = null;

        /**
         * @property id
         * @type {Object}
         * @default {null}
         */
        this.surcharge = null;

        /**
         * @property info
         * @type {Object}
         * @default {null}
         */
        this.info = null;

        /**
         * @property desc
         * @type {String}
         * @default {null}
         */
        this.desc = null;

        /**
         * @property action
         * @type {Object}
         * @default {null}
         */
        this.action = null;

        /**
         * @property type
         * @type {String}
         * @default {''}
         */
        this.type = '';
        // this.isNoLogo = true;
        // this.isAddLogo = false;
        this.isCustomLogo = false;
        this.isLogoMix = false;
        // this.isStandardLogo = false;

        // run the parent init method to parse determine the data type
        base.init.call(this, questionOption);

        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param {Object} json the original JSON data.
     * @chainable
     */
    proto.fromJSON = function(json) {
        this.id = json.id;
        this.desc = json.desc;
        this.change = json.chg === 'Y';
        this.surcharge = json.surcharge;

        return this;
    };

    /**
     * Sets internal information.
     *
     * @method setInfo
     * @param {Object} info
     * @param {App.Models.Ui.Question} question
     * @param {Object} product
     * @param {App.Models.CustomerProfile} customer
     * @chainable
     */
    proto.setInfo = function(info, question) {
        var id = this.id;
        var desc = this.desc;
        var slug = underscore(question.id + '_' + id).toUpperCase();

        this.info = info;
        this.slug = slug;

        var optionSurcharge = Surcharge.getPrice(this.surcharge ? this.surcharge.id : null);
        if (question.id == 'logo' && id == 'CUSTOM' && optionSurcharge !== null) {
            if (Settings.ULTIMATE_PERSONALIZATION === 'false' || Surcharge.get(Settings.PERSONALIZATION_SURCHARGEID) === undefined) {
                if(optionSurcharge != '') {
                    optionSurcharge = '(+' + optionSurcharge + ')';
                }
            } else {
                optionSurcharge = '';
            }
            this.reorderDesc = Content.get('REORDER_CUSTOM', desc, {
                value: optionSurcharge
            });
        }
        if (question.id == 'logo' && id == 'STANDARD' ) {
            this.reorderDesc = Content.get('REORDER_STANDARD', desc, {
                value: optionSurcharge
            });
        }
        this.desc = Content.get(id, desc, {
            value: optionSurcharge
        });


        this.action = {
            event: ActionEvents[slug],
            desc: Content.get(slug)
        };

        return this;
    };

    /**
     * Sets internal information.
     *
     * @method setMatrix
     * @param {Object} matrix
     * @chainable
     */
    proto.setMatrix = function(matrix) {
        this.matrix = matrix;
        //console.log('JoeTest.swatch.setMatrix.key: ' + JSON.stringify(this.matrix, undefined, 4));
        return this;
    };

    /**
     * @method getMatrix
     * @return {Object}
     */
    proto.getMatrix = function() {
        var matrix = this.matrix;
        let pId = typeof SessionStorage.getValue('productId') !== 'undefined' ? SessionStorage.getValue('productId') :this.query.skuId;
        var key = pId + '_' + this.id;
        //console.log('JoeTest.swatch.getMatrix.key: ' + key + ' - ' + JSON.stringify((matrix && matrix[key]), undefined, 4));
        //console.log('JoeTest.swatch.getMatrix.key: ' + key + ' - ' + JSON.stringify(matrix, undefined, 4));

        return matrix && matrix[key];
    };

    /**
     * Sets the current value and saves it to our Session
     *
     * @method setValue
     * @param {String} value
     * @chainable
     */
    proto.setValue = function(value) {
        SessionStorage.storeValue(this.id, value);

        this.value = value;

        return this;
    };

    /**
     * Gets a value from the Session, if it does not exist
     * it will return the current value or null
     *
     * @method getValue
     * @return {String}
     */
    proto.getValue = function() {
        if (editflow && Settings.PERSIST_IMPRINT)
            return this.value || null;
        else
            return SessionStorage.getValue(this.id) || this.value || null;
    };

    /**
     * @method getValues
     * @return {Object}
     */
    proto.getValues = function() {
        var matrix = this.getMatrix();
        var values = {
            description: this.desc
        };

        if (matrix && matrix.sku) {
            values.sku = matrix.sku;
            values.matrix1OptionLongDesc = matrix.desc;
            if (matrix.desc2) {
                values.matrix1OptionLongDesc = matrix.desc + "/" + matrix.desc2;
            }
        }

        if (this.type) {
            values.type = this.type;
        }

        return values;
    };

    return UIQuestionOptionModel;
});
