define(function (require) {
    'use strict';

    var AbstractModel = require('./Abstract');
    var FbtModel = require('./Fbt');
    var ConfigurationProvider = require('../providers/Configuration');
    var FbtMicroProvider = require('../providers/FbtMicro');
    var KitMicroProvider = require('../providers/KitMicro');
    var CustomerProfileProvider = require('../providers/CustomerProfile');
    var LineUsageProvider = require('../providers/LineUsage');
    var ProductInfoProvider = require('../providers/ProductInfo');
    var FXGProvider = require('../providers/FXG');
    var RegionProvider = require('../providers/Region');
    var RegEx = require('../constants/RegEx');
    var SessionIdProvider = require('../providers/SessionId');
    var SessionStorage = require('../providers/SessionStorage');
    var Settings = require ('../constants/Settings');
    var UITypesProvider = require('../providers/UITypes');
    var addToCartTemplate = require('hbs!../../../templates/xml/addToCart');
    var kitAddToCartTemplate = require('hbs!../../../templates/xml/kitAddToCart');
    var priceTemplate = require('hbs!../../../templates/xml/price');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var Query = require('models/Query');
    var Content = require('i18n!../constants/nls/en-us/Content');
    var Surcharge = require('util/Surcharge');
    var currency = require('util/currencyFormat');
    var Registry = require('util/Registry');
    var EventController = require('controllers/Event');
    var ProductEvents = require('constants/ProductEvents');
    var CartProductModel = require('models/cart/Product');
    var Helper = require('util/helper');

    /**
     * @name App.Models.Product
     * @class App.Models.Product
     * @type App.Models.Abstract
     * @constructor
     */
    var ProductModel = function() {
        bindAll(this,
            'initInfo',
            'initModel',
            'initSession',
            'initKit',
            'initFbt',
            'onPriceChange',
            'getCopiesValue',
            'formatRecIds',
            'addQtyToArray'
        );
        AbstractModel.call(this);
    };

    /**
     * ProductModel extends AbstractModel
     * @type {App.Models.Abstract}
     */
    var proto = inherits(ProductModel, AbstractModel);

    /**
     * Initializes the model
     *
     * @method init
     * @chainable
     */
    proto.init = function() {
        /**
         * @property query
         * @type {App.Models.Query}
         */
        this.query = Query.getInstance();
        this.cartProductModel = new CartProductModel();
        this.attachEvents();
        /**
         * @property info
         * @default {null}
         * @type {App.Models.ProductInfo}
         */
        this.info = null;

        /**
         * @property steps
         * @default {null}
         * @type {App.Models.Ui.Collections.Steps}
         */
        this.steps = null;

        /**
         *
         * @property customer
         * @default {null}
         * @type {App.Models.CustomerProfile}
         */
        this.customer = null;

        /**
         * @property questions
         * @default {null}
         * @type {App.Models.Ui.Collections.Questions|Array.<App.Models.Ui.Question>}
         */
        this.questions = null;


        /**
         * @property priceQuestions
         * @default {null}
         * @type {App.Models.Ui.Collections.Questions|Array.<App.Models.Ui.Question>}
         */
        this.priceQuestions = null;

        /**
         * @property logo
         * @default {null}
         * @type {Object}
         */
        this.logo = null;

        /**
         * @property logoBlock
         * @default {null}
         * @type {Object}
         */
        this.logoBlock = null;

        /**
         * @property verse
         * @default {null}
         * @type {Object}
         */
        this.verse = null;
        
        /**
         * @property folding
         * @default {null}
         * @type {Object}
         */
        this.folding = null;

        /**
         * @property signature
         * @default {null}
         * @type {Object}
         */
        this.signature = null;

         /**
         * @property envelopeReturn
         * @default {null}
         * @type {Object}
         */
        this.envelopeReturn = null;

        /**
         * @property peelAndSeal
         * @default {null}
         * @type {Object}
         */
        this.peelAndSeal = null;

        /**
         * @property basePrice
         * @default 0
         * @type Number
         */
        this.basePrice = 0;

        /**
         * @property surchargePrice
         * @default 0
         * @type Number
         */
        this.surchargePrice = 0;

        /**
         * @property totalPrice
         * @default 0
         * @type Number
         */
        this.totalPrice = 0;

        this.surcharges = [];

        this.locationSurcharge = {
            flag: false,
            questionIds: [],
            applicable: false,
        }

        this.fbtProducts = '';

        this.prefixs = {};
        
         /**
         * @property totalUISteps
         * @default {null}
         * @type {string}
         * @private
         */this.totalUISteps = null;

        /**
         * @property ready
         * @type {Promise}
         */
        this.ready = ConfigurationProvider
            .getConfiguration()
            .then(this.initSession)
            .spread(this.initKit)
            .spread(this.initFbt)
            .spread(this.initInfo)
            .spread(this.initModel);

        return this;
    };

    /**
         * DCOM - 14567 Function to trigger Callback function onPriceChange when ProductEvents.SUBTOTAL_CHANGE event is trigged
         */
    /**
     * @method attachEvents
     * @chainable 
     */
     proto.attachEvents = function() {
        EventController.on(ProductEvents.SUBTOTAL_CHANGE, this.onPriceChange);
        return this;
    }

    /**
    * DCOM-14567 CallBack Function triggered on ProductEvents.SUBTOTAL_CHANGE event
    */
    /**
     * @method onPriceChange
     * @param {JQuery.Event} event
     * @callback
     */
    proto.onPriceChange = function(event, pricing){
        this.totalPrice = pricing.total;
        this.basePrice = pricing.base;
    }

    proto.addQtyToArray = function(firstArray, secondArray)  {
        return secondArray.map(function(item) {
          const matchingItem = firstArray.find(
            function(firstItem){ return firstItem.recId === item.pc}
          );

        const newItem = Object.assign({}, item);
        newItem.qty = matchingItem ? matchingItem.qty : "0";
        return newItem;  
        //   return {
        //     ...item,
        //     qty: matchingItem ? matchingItem.qty : "0",
        //   };

        
        });
      };

    /**
     * @method initSession
     * @return {Array}
     */
    proto.initSession = function(config) {
        var query = this.query;
        var sessionId = query.sessionId;

        if (Settings.FETCH_SESSION_ID) {
            sessionId = SessionIdProvider
                .setConfig(config)
                .getSessionId();
        }
        return [config, sessionId];
    };

      /**
     * @method initKit
     * @return {Array}
     */
      proto.initKit = function(config, sessionId) {
        var query = this.query;
        var kitParams = {
            productId: query.productId,
            skuId: query.skuId
        };
        //   console.log('FbtModel.cartItems', FbtModel.cartItems)
        // Exit with already called KIT micro
        if (typeof FbtModel.kitProducts != 'undefined') {
            return [config, sessionId, FbtModel.kitProducts];
        }
        let kitDetails=  KitMicroProvider.setConfig(config)
            .getKitDetails(kitParams);
       

        return [config, sessionId, kitDetails];
      }

      proto.formatRecIds = function(input) {
        // const recIds = input.map(item => item.recId).join(',');
        const recIds = input.map(function(item) { return item.recId }).join(',')  ;
        const recSkuIds = input.map(function(item) {return item.recSkuId}).join(',');
        
        return {
          rec: recIds,
          recSkuId: recSkuIds
        };
      };
    /**
     * @method initFBT
     * @return {Array}
     */
    proto.initFbt = function(config, sessionId, kitDetails) {
        var query = this.query;
        var kitProducts = kitDetails.optionIds
        if (typeof kitProducts != 'undefined'  && kitProducts.length !== 0) {
            let ids = this.formatRecIds(kitProducts)
            
            this.query.rec = ids.rec
            this.query.recSkuId = ids.recSkuId

            this.query.kitId =  this.query.productId;
            this.query.kitSkuId =  this.query.skuId;

            // overrite url id with first product
            this.query.productId =  kitProducts[0].recId;
            this.query.skuId = kitProducts[0].recSkuId;

            FbtModel.kitId = this.query.kitId;
            FbtModel.kitSkuId = kitDetails.sku;
            FbtModel.kitDesc = kitDetails.desc
            FbtModel.kitBasePrice = kitDetails.price;
            FbtModel.kitSurcharges = kitDetails.surchargeInfo.map(function(item) {
                // return
                return {
                    id: item.$.id,
                    desc: item.$.desc,
                    inc: item.$.inc,
                    option: item.option,
                    type: item.$.type,
                };
            });
          
          
            // this.query.kitId = 
        }
       
        
        var rec = query.rec;
        var recSkuId = query.recSkuId;
        var prodId = query.productId;
        var skuId = query.skuId;
        var fbtProducts;

        // Exit with already called FBT micro
        if (typeof FbtModel.fbtProducts != 'undefined') {
            if (Object.keys(FbtModel.fbtProducts).length > 0) {
               fbtProducts = FbtModel.fbtProducts;
               return [config, sessionId, fbtProducts, kitProducts];
            };
        }

        if (typeof rec != 'undefined' && typeof recSkuId != 'undefined') {
            if (rec !== '' && recSkuId !== '' && rec != null) {
                var partsRec = rec;
                if(rec.toString().indexOf(",") > -1){
                    partsRec = rec.split(',');
                
                // check the product Id also part of rec if not add that
                if(partsRec[0] != prodId) {
                    rec = prodId + ',' + rec;
                } 
                }else{
                    if(rec === prodId){
                        rec = '';
                    } else{
                         rec = prodId + ',' + rec;
                    }
                }

                var partsRecSku = recSkuId;
                if(recSkuId.toString().indexOf(",") > -1){
                    partsRecSku = recSkuId.split(',');
                
                if(partsRecSku[0] != skuId) {
                    recSkuId = skuId + ',' + recSkuId;
                }
                } else{
                    if(recSkuId === skuId){
                        recSkuId = '';
                    } else{
                         recSkuId = skuId + ',' + recSkuId;
                    }
                }
                var fbtParams = {
                    sb: '001',
                    rec: rec,
                    recSkuId: recSkuId
                };
                fbtProducts =  FbtMicroProvider.setConfig(config)
                    .getFbtDetails(fbtParams);
            }
            
        } else {
            fbtProducts = {};
        }
       
        return [config, sessionId, fbtProducts, kitProducts];
    };

    

    /**
     * @method initInfo
     * @return {Array}
     */
    proto.initInfo = function(config, sessionId, fbtProducts, kitProducts) {
        if (typeof kitProducts != 'undefined' && kitProducts.length !== 0) {
            fbtProducts = this.addQtyToArray(kitProducts, fbtProducts)
            FbtModel.kitProducts = kitProducts;

        } else {
             FbtModel.kitProducts = [];
        }
        FbtModel.fbtProducts = fbtProducts;
        // console.log('FbtModel', FbtModel.kitBasePrice)
       
        var customer = null;
        var lineUsage = null;
        var regionOption = null;
        var query = this.query;
        var profileId = query.profileId;
        var endUserProfileId = query.endUserProfileId;
        var productId = query.productId;
        var fulfillmentId = query.fulfillmentId;
        var fromPage = query.fromPage;


        // Override profile ID. For dev/testing.
        if (Settings.PROFILE_ID) {
            profileId = Settings.PROFILE_ID;
        }
        if (Settings.PROFILE_ID_ENDUSER) {
            endUserProfileId = Settings.PROFILE_ID_ENDUSER;
        }

        var productInfoParams = {
            productId: productId,
            skuId: query.skuId,
            fulfillmentId: query.fulfillmentId,
            orderId: query.orderId,
            atgOrderId: query.atgOrderId,
            qty: query.qty
        };
        if (typeof fbtProducts != 'undefined') {
            if (Object.keys(fbtProducts).length > 0) {
                if(FbtModel.currentFbtProductIndex == null) {
                    productInfoParams.productId = fbtProducts[0].pc;
                    productInfoParams.skuId = fbtProducts[0].pid;
                    // FbtModel.currentFbtProductIndex = 0;
                    FbtModel.pdpPdt = this.query.productId;
                } else {
                    productInfoParams.productId = fbtProducts[FbtModel.currentFbtProductIndex].pc;
                    productInfoParams.skuId = fbtProducts[FbtModel.currentFbtProductIndex].pid;
                }
            }
        }
        
        if (typeof query.configId !== 'undefined' && query.configId !== '') {
            productInfoParams.configId = query.configId;
        }
        
        // 'fromPage' parameter should be passed only if it exists and has a value.
        if (query.fromPage !== null && query.fromPage !== "") {
            productInfoParams.fromPage = query.fromPage;
        }
        
        
        // Call new Pricing service
        var newPrice = {};
        if (Settings.SVC_NEW_PRICE_FLAG) {
            var priceInfoParams = {
                prodId: productInfoParams.productId,
                all: 'Y'
            };
            newPrice = ProductInfoProvider
                .setConfig(config)
                .getPriceInfo(priceInfoParams);
        } else {
            newPrice = {};
        }

        var info = ProductInfoProvider
            .setConfig(config)
            .getProductInfo(productInfoParams);

        var types = UITypesProvider
            .setConfig(config)
            .getTypes();

        var fxg = FXGProvider
            .setConfig(config)
            .getFXG(productInfoParams.productId);

        if(Settings.CUSTOM_VALIDATION_PRODUCTS.indexOf(productInfoParams.productId) >= 0) {
            regionOption = RegionProvider
                        .setConfig(config)
                        .getRegions();
        }
        if (Settings.SVC_PROFILE_ENABLE && (profileId || endUserProfileId)) {
            if (endUserProfileId) {
                customer = CustomerProfileProvider
                    .setConfig(config)
                    .getProfile(endUserProfileId, sessionId);
            } else if (profileId) {
                customer = CustomerProfileProvider
                    .setConfig(config)
                    .getProfile(profileId, sessionId);
            }
            lineUsage = LineUsageProvider
                .setConfig(config)
                .getLineUsage();
        }

        return [newPrice, info, types, fxg, customer, lineUsage, regionOption];
    };

    /**
     * @method initModel
     * @param {App.Models.ProductInfo} info
     * @param {App.Models.Ui.Collections.Types} types
     * @param {App.Models.FXG} fxg
     * @param {App.Models.CustomerProfile} customer
     * @chainable
     */
    proto.initModel = function(newPrice, info, types, fxg, customer, lineUsage, regionOption) {
       
        if(info.productId == '' || info.productId == "null") {
            for(let i= 0; i < info.productInfo.question.length; i++) {
                if(info.productInfo.question[i].id == 'productId') {
                    info.productId = info.productInfo.question[i].default;
                    break;
                }
            }            
        }       
        FbtModel.types = types;
        var i = 0;
        if(typeof FbtModel.fbtProducts != 'undefined' && typeof FbtModel.currentFbtProductIndex == 'undefined' && Object.keys(FbtModel.fbtProducts).length > 0) {
            if(this.query.productId != FbtModel.fbtProducts[0].pc) {
                for (i=0; i<info.productInfo.question.length; i++) {
                if(info.productInfo.question[i].id == 'quantity') {
                   // console.log('ggg', info.productInfo.question[i]); 
                    if (typeof info.productInfo.question[i].option[0] != 'undefined') {
                        this.query.qty = this.query.quantity = info.productInfo.question[i].option[0].id;
                    }
                    break;
                }
                }
            }
        }
            if(typeof FbtModel.kitSkuId != 'undefined') {
                
                info.priceInfo = FbtModel.kitSurcharges
            }

        // console.log('priceInfo', info.priceInfo)

        for (i=0; i<info.productInfo.question.length; i++) {
            if(info.productInfo.question[i].id == 'quantity') {
                    info.productInfo.question[i].infooptions = info.priceInfo;
             }       
         }
             
        for (i=0; i<info.productInfo.question.length; i++) {
            if (info.productInfo.question[i].id == 'layout') {
                var question = info.productInfo.question[i];
                var layouts = [].concat(question.option);
                for (var ii=0; ii<layouts.length; ii++) {
                    var layout = layouts[ii];
                    var locations = [].concat(layout.location);
                    for (var iii=0; iii<locations.length; iii++) {
                        var location = locations[iii];
                        if(location.surcharge && location.id === "FRONT") {
                            this.locationSurcharge.flag = true;
                        }
                        var blocks = [].concat(location.block);
                        for (var iiii=0; iiii<blocks.length; iiii++) {
                            var block = blocks[iiii];
                            if (block.logo === 'Y' ) {
                               this.logoBlock = block.id;
                            } else if(block.id === 'LO' && !(this.logoBlock)) {
                                this.logoBlock = block.id;
                            } else if (block.logo === 'S' && !(this.logoBlock)){
                                this.logoBlock = block.id;
                            }
                            if (this.locationSurcharge.flag){
                                for (var j=0; j<block.line.length; j++) {
                                    this.locationSurcharge.questionIds.push(block.id + '_' + block.line[j].id );
                                }
                            }                            
                        }
                    }
                }
            }
            if(info.productInfo.group.indexOf('UI_H') > -1 && info.productInfo.question[i].id == 'matrix1' && Settings.HOLIDAY_PREMIUM_PAPER ){
                info.productInfo.question[i].default = 'PREMIUM WHITE';
            }
            if(info.productCode == this.query.prodId  &&  info.productInfo.question[i].id == 'matrix1' && this.query.m1){
                info.productInfo.question[i].default = this.query.m1;
            }
            if(info.productInfo && info.productInfo.group == 'UI_10' && customer && customer.imprint){
                customer.imprint = null;
            }   
         }

        //console.log('Models.Product.info', info);
        // Update the pricing with new Prices
        if (Settings.SVC_NEW_PRICE_FLAG) {
            for (i=0; i<info.priceInfo.length; i++) {
                var sid = info.priceInfo[i].id;
                var sdata = newPrice[sid];
                if (typeof sdata != 'undefined') {
                    if (info.priceInfo[i].type == 'ONE') {
                        if (typeof sdata.option != 'undefined') {
                            info.priceInfo[i].option.discountPrice = sdata.option.discountPrice;
                            if(info.priceInfo[i].option.length > 0) {
                                for(let j = 0; j < info.priceInfo[i].option.length; j++) {
                                    info.priceInfo[i].option[j].price = sdata.option.price;
                                }
                            } else {                                
                                info.priceInfo[i].option.price = sdata.option.price;
                            }
                        }
                    } else {
                        info.priceInfo[i].option = sdata.option;
                    }
                }
            }
            
            // console.log('Models.Product.qty', info);

            // DCOM-14969 - Commented following block of code as new code is overwriting the data changed with this code
            // Old code - Start
            // Update the quantity with new quantity for Tenenz
            // var newQuantity = [];
            // for(i=0; i<info.productInfo.question.length; i++) {
            //     if(info.productInfo.question[i].id == 'quantity') {
            //         if((typeof newPrice[info.productId] != 'undefined') && (info.productInfo.question[i].option.length > newPrice[info.productId].option.length)) {
            //             for(var qtyOld = 0; qtyOld < info.productInfo.question[i].option.length; qtyOld++) {
            //                 for(var qtyNew = 0; qtyNew < newPrice[info.productId].option.length; qtyNew++) {
            //                     if( (info.productInfo.question[i].option[qtyOld].id)==(newPrice[info.productId].option[qtyNew].qty)) {                                    
            //                         newQuantity.push(info.productInfo.question[i].option[qtyOld]);
            //                     }
            //                 }
            //             }
            //             if(newQuantity.length) info.productInfo.question[i].option = newQuantity;
            //         }
            //         break;
            //     }
            // }
            // Old code - End

            // DCOM-14969 - Start - Added following to override the quantity/price when SVC_NEW_PRICE_FLAG is true
            // Override Quantity
            var newQuantity = [];
            if( newPrice && newPrice[info.productId] ) {
                for( i=0; i < info.productInfo.question.length; i++) {
                    if(info.productInfo.question[i].id === 'quantity') {                       
                        var productInfoQuantity = info.productInfo.question[i];
                        var sampleObject = productInfoQuantity.option[0]
                        for(var qtyNew = 0; qtyNew < newPrice[info.productId].option.length; qtyNew++) {
                            var newQuantityObj =  JSON.parse(JSON.stringify(sampleObject))  ;
                            newQuantityObj.id = newPrice[info.productId].option[qtyNew].qty;
                            newQuantity.push(newQuantityObj);
                        }
                        if(newQuantity.length) info.productInfo.question[i].option = newQuantity;
                    }
                }
            }
            // DCOM-14969 - End
        }
        
        var productInfo = info && info.productInfo;
        var type = types.getById(productInfo.group);
        var steps = type.steps;

        // If editing from cart or re-ordering, purge any localStorage
        // because its about to get re-created from the incoming xml.
        if (this.query.fulfillmentId || this.query.orderId) {
            localStorage.clear();
        }
        
        // Does the customer have pre-configured logo on their account?
        if (customer && lineUsage && !info.productConfiguration) {
            // customer.mapLineUsage(lineUsage);
            if (this.query.recSkuId && SessionStorage.getValue('logo')){
                this.logo = SessionStorage.getValue('logo_'+SessionStorage.getValue('logo'));
            } else {
            this.logo = this.setLogo(customer.logo, true);
        }
        }

        // If editing or re-ordering get the logo from the order data.
        var blocks = info.productConfiguration && info.productConfiguration.getBlocks();       
        if (info && blocks && blocks.logo) {
            this.logo = this.setLogo(blocks.logo, true);
        }

        
        steps.setInfo(info, fxg, customer);
       

        this.info = info;
        this.steps = steps;
        this.customer = customer;
        this.questions = steps.getQuestions();
        this.priceQuestions = steps.getPriceQuestions();
        this.fbtProducts = FbtModel.fbtProducts ? FbtModel.fbtProducts : [];

        var data = steps.getValues();
        var baseBlocks = data.blocks;
        var blockPrefix = [];
        baseBlocks.forEach(function (block) {
            block.prefixs.forEach(function (prefix) {
                var prefixValue = prefix.prefix[prefix.id];
                blockPrefix.push(prefix.id)
                if (typeof prefixValue == "undefined") {
                    Object.keys(prefix.prefix).forEach(function (key) {
                        prefixValue = prefix.prefix[key];
                        this.prefixs[key] = prefixValue;
                    }.bind(this));
                } else {
                    this.prefixs[prefix.id] = prefixValue;
                }
            }.bind(this));
        }.bind(this));

        if (customer && lineUsage && !info.productConfiguration) {
            customer.mapLineUsage(lineUsage, blockPrefix);
        }
        var productId = data.values.productId.value;
        var quantity = data.values.quantity.value;
        //console.log('Product.initModel', this);
        //console.log('Product.initModel.this.surcharges', this.surcharges);
        // DCOM-17994 save productID to session for holidaycards
        SessionStorage.storeValue('productId', productId);

        if (typeof fxg.DIECUT != 'undefined') {
            for (i=0; i<this.steps._items[2].questions._items[0].blocks._items.length; i++) {
                if (typeof this.steps._items[2].questions._items[0].blocks._items[i].lines != 'undefined' &&
                    this.steps._items[2].questions._items[0].blocks._items[i].lines._items[0]) {
                    if (this.steps._items[2].questions._items[0].blocks._items[i].lines._items[0].blockId == 'CI') {
                        this.steps._items[2].questions._items[0].blocks._items[i].lines._items[0].desc = Content['DIECUT_DESC'];
                        this.steps._items[2].questions._items[0].blocks._items[i].lines._items[0].additionaldesc = Content['DIECUT_DETAILS'];
                    }
                }
            }
        }

        var laychg = false;
        var per_amount;
        if (Settings.ULTIMATE_PERSONALIZATION === 'true'){
            // Find the value of personalization surcharge
            for (i=0; i<info.priceInfo.length; i++) {
                laychg = false;
                if (info.priceInfo[i].id === Settings.PERSONALIZATION_SURCHARGEID) {
                    per_amount = info.priceInfo[i].option.price;
                    laychg = true;
                    break;
                }
            }

            // Assign personalization surcharge to model
            for (i=0; i<this.steps._items.length; i++) {
                for (var j=0; j<this.steps._items[i].questions._items.length; j++) {
                    if (this.steps._items[i].questions._items[j].control === 'model') {
                         this.steps._items[i].questions._items[j].value = per_amount;
                    }

                    if (this.steps._items[i].questions._items[j].control === 'radio') {
                         this.steps._items[i].questions._items[j].person = laychg;
                         this.steps._items[i].questions._items[j].per_value = per_amount;
                    }
                }
            }
        }

        var pricing = this.cartProductModel.getPricing();
        if (pricing !== null) {
            this.basePrice = pricing.base;
            this.totalPrice = pricing.total;
        }

        if(info.productInfo.group == 'UI_HCARD1') {
            this.setQuestionDescription(this.getStepItems(this.steps,'TextColor'),'inkColor2', "");
        }   else if(['UI_HCARD2', 'UI_HCARD3', 'UI_ZCARD1', 'UI_PCARD1', 'UI_PCARD2'].indexOf(info.productInfo.group) > -1){
            this.setQuestionDescription(this.getStepItems(this.steps,'TextColor'),'inkColor2', "");
        }
        if(info.productInfo.group == 'UI_10') {
            this.steps._items["0"].questions._items[1].blocks._items[10].lines._items[5].desc = 'State';
            this.steps._items["0"].questions._items[1].blocks._items[10].lines._items[5].ECHKSD = true;
            this.steps._items["0"].questions._items[1].blocks._items[10].lines._items[5].regionOption = regionOption;
        }

        this.updateQuantity();
        this.getPlDesc();
        //Register new price surcharges
        if(Settings.SVC_NEW_PRICE_FLAG) {
            info._registerPrices();
        }    

        return this;
    };



    /**
     * @method getStepItems
     * @return {array}
     */
    proto.getStepItems = function(steps, stepItemId) {
        for(var i=0; i<steps._items.length; i++) {
            if(steps._items[i].tlId == stepItemId) {
                var items = steps._items[i];
                break;
            }
        }
        return items;
    };    

    /**
     * @method setQuestionDescription
     * @return {}
     */
    proto.setQuestionDescription = function(stepItems, questionItemId, value) {
        for(var i=0; i<stepItems.questions._items.length; i++) {
            if(stepItems.questions._items[i].id == questionItemId) {
                stepItems.questions._items[i].desc = value;
                break;
            }
        }
        return this;
    };

    /**
     * @method getProductDescription
     * @return {String}
     */
    proto.getProductDescription = function() {
        var productId = this.questions.getById('productId');
        var part = productId && productId.options.getById(productId.getValue());

        return part && part.desc;
    };

    /**
     * @method getPLMFPF
     * @return {String}
     */
    proto.getPLMFPF = function() {
        //console.log("PLMFPF -" + this.info.productInfo.plmfpf + "-");
        if(this.info)
            return this.info.productInfo.plmfpf;
    };
    
    /**
     * @method getConstruction
     * @return {String}
     */
    proto.getConstruction= function() {
        if(this.info)
            return this.info.productInfo.construction;
    };
    
    /**
     * @method getRequiredInk
     * @return {String}
     */
    proto.getRequiredInkAny = function() {
        var requiredInkCode = null;
        var layoutQuestion = this.questions.getById('layout');
        var locations = [].concat(layoutQuestion.info.option.location);
        for (var i=0; i<locations.length; i++) {
            var location = locations[i];
            var blocks = [].concat(location.block);
            for (var ii=0; ii<blocks.length; ii++) {
                var block = blocks[ii];
                if (block.requiredInk && block.requiredInk !== null) {
                    requiredInkCode = block.requiredInk;
                    break;
                }
            }
        }
        return requiredInkCode;
    };
    
    /**
     * @method getRequiredInkForLocation
     * @return {String}
     */
    proto.getRequiredInkForLocation = function(locationCode) {
        var requiredInkCode = null;
        var layoutQuestion = this.questions.getById('layout');
        if(layoutQuestion) {
            var locations = [].concat(layoutQuestion.info.option.location);
            for (var i=0; i<locations.length; i++) {
                var location = locations[i];
                var blocks = [].concat(location.block);
                for (var ii=0; ii<blocks.length; ii++) {
                    var block = blocks[ii];
                    if (location.id == locationCode && block.requiredInk && block.requiredInk !== null) {
                        requiredInkCode = block.requiredInk;
                        break;
                    }
                }
            }
        }
        return requiredInkCode;
    };
    
    /**
     * @method getRequiredInkForBlock
     * @return {String}
     */
    proto.getRequiredInkForBlock = function(blockCode) {
        var requiredInkCode = null;
        var layoutQuestion = this.questions.getById('layout');
        if(layoutQuestion) {
            var locations = [].concat(layoutQuestion.info.option.location);
            for (var i=0; i<locations.length; i++) {
                var location = locations[i];
                var blocks = [].concat(location.block);
                for (var ii=0; ii<blocks.length; ii++) {
                    var block = blocks[ii];
                    if (block.id === blockCode) {
                        requiredInkCode = block.requiredInk;
                        break;
                    }
                }
            }
        }
        return requiredInkCode;
    };

    proto.getBlock = function(blockCode) {
        var block = null;
        var data = this.steps.getValues();
        for (var i=0; i<data.blocks.length; i++) {
            if (data.blocks[i].id === blockCode) {
                block = data.blocks[i];
                break;
            }
        }
        return block;
    };
    
    /**
     * @method getVerseOptions
     * @return {String}
     */
    proto.getVerseOptions= function() {
        var verseQuestion = this.questions.getById('verse');
        var verseIds = [];
        if(verseQuestion){
            if(Array.isArray(verseQuestion.info.option)){
                for(var i=0 ; i< verseQuestion.info.option.length;i++){
                    verseIds.push(verseQuestion.info.option[i]);
                }
            }
            else
                verseIds.push(verseQuestion.info.option);
        }
        //this.verse = verseQuestion.info.option;
        return verseIds;
    };

    /**
     * @method getRMOptions
     * @return {array}
     */
    proto.getRMOptions= function() {
        var layoutQuestion = this.questions.getById('layout');
        let rmOptions = [];       
        if((typeof layoutQuestion != 'undefined') && (Array.isArray(layoutQuestion.info.option.location.block))) {
            layoutQuestion.info.option.location.block.forEach(function(item) {            
                    if(item.id == 'RM' && item.line.length !=0) {
                        if(Array.isArray(item.line)){
                            rmOptions = typeof item.line[0].option !='undefined' ? item.line[0].option : [];
                        }                   
                    }
                })
        }
        return rmOptions;
    };

    /**
     * @method getDefaultRM
     * @return {String}
     */
    proto.getDefaultRM= function() {
        var rmDefault = null;
        var data = this.steps.getValues();
        var block = this.getBlock('RM')
        if (block && block !== null) {
            block.lines.forEach(
                function(line) {
                    if(line.id == 'BX') {
                        rmDefault = line.input[0]
                    }
                   
                }
            );
        }
        return rmDefault;
    };

    /**
     * @method getVerseOrientation
     * @return {String}
     */
    proto.getVerseOrientation= function() {
        var verseQuestion = this.questions.getById('verse');
        var verseOrientation = null;
        if(verseQuestion)
            verseOrientation = verseQuestion.info.orientation;
        return verseOrientation;
    };

    /**
     * @method getDefaultVerse
     * @return {String}
     */
    proto.getDefaultVerse= function() {
        var verseQuestion = this.questions.getById('verse');
        var verseDefault = null;
        if(this.cartProductModel && this.cartProductModel.getVerseCode())
            verseDefault = this.cartProductModel.getVerseCode();
        else if(verseQuestion)
            verseDefault = verseQuestion.info.default;
        
        return verseDefault;
    };

    /**
     * @method getPlDesc
     * @return {String}
     */
    proto.getPlDesc = function() {
        if(this.info.productInfo.plDesc){
            var productPldesc =  this.info.productInfo.plDesc;
            Settings.productPldesc = productPldesc.substr(productPldesc.indexOf(' ')+1);
        }
    };
    /**
     * @method getQuantityValue
     * @return {String}
     */
    proto.getQuantityValue = function() {
        var quantity = this.questions.getById('quantity');
        return quantity && quantity.getValue();
    };

    /**
     * @method updateQuantity
     */
    proto.updateQuantity = function() {
        var current = parseInt(this.getQuantityValue(), 10);
        var previous = Registry.get(Registry.PRODUCT, 'qty');

        if (current !== previous) {
            Registry.set(Registry.PRODUCT, 'qty', current);
            EventController.emit(ProductEvents.QTY_CHANGE, current);
        }
    };

    proto.getQuestion = function(questions, quesitonId) {
        var question = null;
        if (questions != null) {
            for (var i=0; i<questions.length; i++) {
                if (questions[i].id == quesitonId) {
                    question = questions[i];
                    break;
                }
            }
        }
        return question;
    }

    proto.lookupSurcharge = function(surchargeId) {
        var surcharge = null;
        var surchargeData = Surcharge.get(surchargeId);
        if (surchargeData) {
            surcharge = {};
            surcharge.id = surchargeData.id;
            surcharge.description = surchargeData.desc;
            surcharge.desc = surchargeData.desc;
            surcharge.type =  surchargeData.type;
            surcharge.addToBase = false;    //Not available in W2P, only used with apparel
            if (surcharge.type === 'ONE') {
                surcharge.qty = 1;
            } else {
                surcharge.qty = this.getQuantityValue() || query.qty || query.quantity;
            }
            surcharge.price = parseFloat(Surcharge.getPrice(surchargeId, true, parseInt(surcharge.qty))).toFixed(2);
            surcharge.priceFormatted = currency(surcharge.price, 2).replace('.00', '');
        }
        return surcharge;
    };

    /**
     * Generates <addToCart> xml using the addToCart.hbs template
     *
     * @method toXML
     * @return {string}
     */
    proto.toXML = function(isAddToCart, isConfig) {
        isConfig = isConfig || false;
        var info = this.info;
        var imagesrc = '';
        var routingQuestionFlag = false;
        var accountQuestionFlag = false;
        var numberingQuestionFlag = false;
        var reverseNumberingFlag = false;
        if(window.url) {
            imagesrc = window.url;
        }
        if (isAddToCart === null || isAddToCart === undefined) {
            isAddToCart = false;
        }
        /*if (typeof FbtModel.fbtProducts != 'undefined') {
            if (Object.keys(FbtModel.fbtProducts).length > 0) { 
                if(FbtModel.currentFbtProductIndex === null) {
                    FbtModel.pdpPdt = this.query.productId;
                    this.query.productId = FbtModel.fbtProducts[0].pc;
                    this.query.skuId = FbtModel.fbtProducts[0].pid; 
                } else {
                  this.query.productId = FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].pc;
                  this.query.skuId = FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].pid;
                }
               
            }
        }*/
        if (typeof FbtModel.fbtProducts != 'undefined') {
            if (Object.keys(FbtModel.fbtProducts).length > 0) { 
                this.query.productId = this.info.productCode;
                if(this.questions.getById('productId') && this.questions.getById('productId').options.length() > 1) {
                    this.query.skuId =  this.steps.getValues().values.productId.value;
                } else {
                    this.query.skuId = this.info.productId;
                }
            }
        }
        else {
            if(typeof FbtModel.fbtRequiredFields != 'undefined') {
                this.query.productId = this.info.productCode;
                if(this.questions.getById('productId') && this.questions.getById('productId').options.length() > 1) {
                    this.query.skuId =  this.steps.getValues().values.productId.value;
                } else {
                    this.query.skuId = this.info.productId;
                }
            }
        }
        var data = this.steps.getValues();
        
        // Account for mismatch of parameters. Edit/Reorder uses productId.
        //this.query.productId = this.query.productId;
        if (typeof FbtModel.fbtProducts != 'undefined') {
            if (Object.keys(FbtModel.fbtProducts).length > 0) {
                data.values.productId.value = this.query.skuId;
            }
        }
        else {
            if(typeof FbtModel.fbtRequiredFields != 'undefined') {
                data.values.productId.value = this.query.skuId;
            }
        }
        //console.log('JoeTest.data', data);
        //console.log('JoeTest.data.values', data.values);
        //console.log('JoeTest.locations.1', data.values.layout);
        if (this.questions.getById("productId")) {
            var isStockAndCustomProduct = this.questions.getById("productId").info.imprintType;
            var isStockProduct = this.getProductType("Stock");
            // var isCustomProduct = this.getProductType('C');

            if (isStockAndCustomProduct && isStockProduct) {
                var propertiesToSetUndefined = [
                    "layout",
                    "typestyle",
                    "inkColor1",
                    "inkColor2",
                ];

                propertiesToSetUndefined.forEach(function (property) {
                    if (data.values[property]) {
                        data.values[property] = undefined;
                    }
                });
            }
        }

        if (data.values.layout) {
            var selectedLayout = data.values.layout;
            selectedLayout.location = [].concat(selectedLayout.location);
            for (var i = 0; i < selectedLayout.location.length; i++) {
                selectedLayout.location[i].block = [].concat(selectedLayout.location[i].block);
                for (var ii = 0; ii < selectedLayout.location[i].block.length; ii++) {
                    var block = this.getBlock(
                        selectedLayout.location[i].block[ii].id
                    );
                    if (block && block !== null) {
                        selectedLayout.location[i].block[ii].line = [].concat(
                            selectedLayout.location[i].block[ii].line
                        );
                        if (isAddToCart) {
                            var blockId = selectedLayout.location[i].block[ii].id;
                            block.lines.forEach(
                                function(line) {
                                    var id = blockId + "_" + line.id;
                                    if (line.input.length > 1) {
                                        for (
                                            let index = 0; index < line.input.length; index++
                                        ) {
                                            var keyid = id + "_" + (index + 1);
                                            if (typeof this.prefixs[keyid] !== "undefined") {
                                                line.input[index] =
                                                    this.prefixs[keyid] + line.input[index];
                                            }
                                        }
                                    } else {
                                        if (typeof this.prefixs[id] !== "undefined") {
                                            line.input[0] = this.prefixs[id] + line.input[0];
                                        }
                                    }
                                }.bind(this)
                            );
                            selectedLayout.location[i].block[ii].lines = block.lines;
                        } else {
                            selectedLayout.location[i].block[ii].lines = block.lines;
                        }
                        //selectedLayout.location[i].block[ii].hasLogo = block.hasLogo;
                        //console.log('JoeTest.block', block);
                    }
                }
            }
        }
        if (data.values.typestyle === undefined) {
            var defaultTypestyle = '';
            var questions = [].concat(this.info.productInfo.question);
            for (var iii=0; iii<questions.length; iii++) {
                if (questions[iii].id == 'typestyle') {
                    defaultTypestyle = questions[iii].default;
                    break;
                }
            }
            data.values.typestyle = {value:defaultTypestyle};
        }
        if (data.values.inkColor1 === undefined) {
            var defaultInkColor1 = '';
            var questions = [].concat(this.info.productInfo.question);
            for (var iii=0; iii<questions.length; iii++) {
                if (questions[iii].id == 'inkColor1') {
                    defaultInkColor1 = questions[iii].default;
                    break;
                }
            }
            data.values.inkColor1 = {value:defaultInkColor1};
        }

        //ECHKSD adapter:  
        //[1] 'inkColor1' question is part of the product xml but not part of the UI/UX steps.  If charge is
        //    present on inkColor1, we should auto apply it.  Counter-test: Product 59000HZ has inkColor1 and has the step.
        //[2] numbering value is required on back end.
        var surcharge =  this.lookupSurcharge(Settings.EZ_SHIELD_INCLUDED_SURCHARGEID);
        if (surcharge !== null) {
            if (!Surcharge.isChargeApplied(this.surcharges, Settings.EZ_SHIELD_INCLUDED_SURCHARGEID)) {
                this.surcharges.push(surcharge);
            }
            if (data.values.numbering == undefined) {
                var numberingQuestion = this.getQuestion(info.productInfo.question, 'numbering')
                if (numberingQuestion != null) {
                    data.values.numbering = {id:numberingQuestion.id,label:numberingQuestion.desc,value:numberingQuestion.numbering};
                }
            }
        }

        data.surcharges = this.surcharges;
        //console.log('Product.toXML.this', this);
        //console.log('Product.toXML.var.surcharges', surcharges);

        if(this.query.skuId == undefined || this.query.skuId == ''){
            for(i = 0; i < info.productInfo.question.length; i++ ) {
                if(info.productInfo.question[i].id == "productId") {
                    if(info.productInfo.question[i].option.length != undefined){
                        this.query.skuId = info.productInfo.question[i].option[0].id;
                    }
                    else{
                         this.query.skuId = info.productInfo.question[i].option.id;
                    }
                    break;
                }
            }
        }

        var wysiwygUrl = Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE + this.query.productId +'?fmt=jpg&imageRes=150&wid=500&hei=500';
        if (Settings.PREVIEW_URL_TO_CART == true)
        {
            wysiwygUrl = imagesrc;
        }

        for(i = 0; i < info.productInfo.question.length; i++ ) {
            if((info.productInfo.question[i].id == 'routingNumber')) {
                routingQuestionFlag = true;
            }
            if((info.productInfo.question[i].id == 'accountNumber')) {
                accountQuestionFlag = true;
            }
            if((info.productInfo.question[i].id == 'numbering')) {
                numberingQuestionFlag = true;
            }
            if((info.productInfo.question[i].id == 'reverseNumbering')) {
                reverseNumberingFlag = true;
            }
        }
        if(!routingQuestionFlag) {
            if(data.values.routingNumber) {
                data.values.routingNumber.value = null;
            }
        }
        if(!accountQuestionFlag) {
            if(data.values.accountNumber) {
                data.values.accountNumber.value = null;
            }            
        }
        if(!numberingQuestionFlag) {
            if(data.values.numbering) {
                data.values.numbering.value = null;
            }            
        }

        if(!reverseNumberingFlag) {
            data.values['reverseNumbering'] = { value: null }
        } else {
            if(data.values.reverseNumbering.value === 'true') {
                data.values.reverseNumbering.value = 'reverseNumbering';
            }
        }

        //convert array of objects to json for the reorder and editcart
        var logoDataInfo = {};
        if(this.logo && this.logo.data) {
            for (var i = 0; i < this.logo.data.item.length; i++) {
                logoDataInfo[this.logo.data.item[i].key] = this.logo.data.item[i].value;
            }
        }

        // remove logo value if product has no logo question and logo added from session storage
        var modelQuestions = info.productInfo.question;
        var modelHasLogo = false;
        modelQuestions.forEach( function(e){
            if(e.id === 'logo') {
                modelHasLogo = true;
            }
        })
        if(!modelHasLogo){
            this.logo = undefined;
        }
        var addToCartTemplateObject = {
            query: this.query,
            isConfig: isConfig,
            info: info && info.productInfo,
            header: info,
            data: data,
            selectedLayout: selectedLayout,
            logo: this.logo,
            logoDataInfo: logoDataInfo,
            logoBlock: this.logoBlock,
            verse: this.verse,
            folding: this.folding,
            signature: this.signature,
            envelopeReturn: this.envelopeReturn,
            peelAndSeal: this.peelAndSeal,
            wizWigUrl: wysiwygUrl,
            pricing: this.getPricing(),
            doLineItemComments: Settings.LINEITEM_COMMENT
        };
        var xml = addToCartTemplate(addToCartTemplateObject);
        return xml;
    };

    /*
     * @method toXML
     * @return {string}
     */
    proto.kitToXML = function(isConfig) {
        var querydata = Query.getInstance();
        var wizWigUrl = Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE + FbtModel.kitId +'?fmt=jpg&imageRes=150&wid=500&hei=500';
        
        var addToCartTemplateObject = {
            query: querydata,
            isConfig: isConfig,
            items:  FbtModel.cartItems,  
            pricing: this.getPricing(),
            desc: FbtModel.kitDesc,
            skuId: FbtModel.kitSkuId,   
            wizWigUrl: wizWigUrl   
        };        
        var xml = kitAddToCartTemplate(addToCartTemplateObject);
        return xml;

    }

     /*
     * @method kitToPriceXML
     * @return {string}
     */
    proto.kitToPriceXML = function(isConfig, cartItems) {
        var querydata = Query.getInstance();
        var wizWigUrl = Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE + FbtModel.kitId +'?fmt=jpg&imageRes=150&wid=500&hei=500';
        var addToCartTemplateObject = {
            query: querydata,
            isConfig: isConfig,
            items:  cartItems,  
            pricing: this.getPricing(),
            desc: FbtModel.kitDesc,
            skuId: FbtModel.kitSkuId,    
            wizWigUrl: wizWigUrl     
            // pricing: this.getPricing(),
            // doLineItemComments: Settings.LINEITEM_COMMENT
        };

       return kitAddToCartTemplate(addToCartTemplateObject);
    }
        
    /**
     * Apply surcharge info to surchag data
     *
     * @method applySurcharges
     * @returns {Number}
     */
    proto.applySurcharges = function() {
        var quantity = this.getQuantityValue() || this.query.qty || this.query.quantity;
        // This is need to merge pricing data for all surcharges
        return this.info.applySurcharges(this.surcharges, quantity);
    };

    /**
     * Generate price XML for checking if any price dependent values have changed
     *
     * @method toPriceXML
     * @returns {String}
     */
    proto.toPriceXML = function() {
        var xml = priceTemplate({
            options: this.priceQuestions._items,
            logo: this.logo
        });
        return xml.replace(/\n\s*\n/g, '\n');
    };

    /**
     * Generate price JSON for checking if any price dependent values have changed
     *
     * @method toPriceJSON
     * @returns {String}
     */
    proto.toPriceKey = function() {
        var priceKey = {};
        var partQuestion = this.questions.getById('productId');
        if (partQuestion) {
            priceKey.productId = partQuestion.value;
        }
        priceKey.quantity = this.getQuantityValue();
        return priceKey;
    };

    /**
     * Retrieves applicable pricing data from productInfo base
     * on user selections
     *
     * @method getPricing
     * @return {{base: number, surcharges: number, total: number}}
     * @private
     */
    proto.getPricing = function() {
        return {
            base: parseFloat(this.basePrice).toFixed(2),
            total: parseFloat(this.totalPrice).toFixed(2),
            surcharges: parseFloat(this.surchargePrice).toFixed(2)
        };
    };

    /**
     * Fetch the logo from the user's profile if it exists
     * and is not currently in the user's session.
     *
     * @method mapLogo
     * @return {object|undefined}
     */
    proto.setLogo = function(logo, ignoreSession) {
        if ( !logo) {
            return null;
        }

        // Ignore possiblity of existing session data.
        // Force the logo save to session.
        if (ignoreSession) {
            logo.logoCode = logo.logoCode.replace(RegEx.FILE_NAME_NO_EXT, '$1');
            SessionStorage.storeValue('logo', logo.logoType);
            logo.logoCode = Helper.isAlphaNumeric(logo.logoCode) ? logo.logoCode : "";
            if(logo.logoCode && !logo.logoCode.startsWith('U')) {
                logo.logoUrl = Settings.SVC_MSET_LOGO + logo.logoCode
            }
            else {
                logo.logoUrl = Settings.SVC_UPLOAD_DOMAIN + '?UID=' + logo.logoCode + '&Custom=hei:100,wid:100,fit:constrain';
            }
            SessionStorage.storeValue('logo_' + logo.logoType, logo);

            return logo;
        }

        var logoCustom = SessionStorage.getValue('logo_CUSTOM');
        var logoStandard = SessionStorage.getValue('logo_STANDARD');

        // If we have no user profile logo data, or we have an existing
        // logo in the user session return. We don't need to update anything.

        //mahesh 
        // test commenting out following "return" statement 
        // to enable next button while reloading
        // if ( !logo || logoCustom || logoStandard) {

        if ( !logo || logoCustom ) {
            return;
        }

        logo.logoCode.replace(RegEx.FILE_NAME_NO_EXT, '$1');
        SessionStorage.storeValue('logo_' + logo.logoType, logo);

        var selectedValue = SessionStorage.getValue('logo');

        // If no logo has been set for this product,
        // set it to the user's profile default
        if (!selectedValue) {
            SessionStorage.storeValue('logo', logo.logoType);
        }

        return logo;
    };


    proto.getProductType = function (type) {
      var copiesObj = this.questions.getById("productId");
      if (copiesObj !== undefined) {
        var selectedProductId = copiesObj.getValue();
        var imprint;

        if (Array.isArray(copiesObj.info.option)) {
          var matchingOption = copiesObj.info.option.filter(function (_option) {
            return _option.id == selectedProductId;
          })[0];
          imprint = matchingOption ? matchingOption.imprint : undefined;
        } else {
          imprint = copiesObj.info.option.imprint;
        }

        if (
          (type == "Stock" && imprint === "S") ||
          (type == "Custom" && imprint === "C")
        ) {
          return true;
        }
      }
      return false;
    };
    

    /**
     * Fetch the Selected Copies Type.
     *
     * @method getCopiesValue
     * @return {string}
     */
    proto.getCopiesValue = function () {
        var copiesObj = this.questions.getById('productId');
        if (copiesObj !== undefined) {
            var copiesValue = "";
            if (Array.isArray(copiesObj.info.option)) {
                copiesObj.info.option.forEach(function (object) {
                    if (object.id === copiesObj.getValue()) {
                        copiesValue = object.desc;
                    }
                });
            }
            return copiesValue;
        }else {
            return "";
        }
    }

    return ProductModel;
});