define(function (require) {
    'use strict';

    var AbstractModel = require('../Abstract');
    var ClipArtLogos = require('./collections/Logos');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.ClipArt.Subcategory
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} subcategory
     */
    var ClipArtSubcategoryModel = function (subcategory) {
        AbstractModel.call(this, subcategory);
    };

    var proto = inherits(ClipArtSubcategoryModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} input
     * @chainable
     */
    proto.init = function(input) {

        /**
         * @property id
         * @default {null}
         * @type {string}
         */
        this.id = null;

        /**
         * @property description
         * @default {null}
         * @type {string}
         */
        this.description = null;

        /**
         * @property logos
         * @default {null}
         * @type {ClipArtLogosCollection}
         */
        this.logos = null;

        this.createChildren();

        // run the parent init method to parse determine the data type
        base.init.call(this, input);

        return this;
    };

    /**
     * @method createChildren
     * @chainable
     */
    proto.createChildren = function() {
        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param {object} json the original JSON data.
     */
    proto.fromJSON = function(json) {
        if (!json.id) {
            json = this.stripInvalidFields(json);
            if (!json.id && json.sub !== undefined) {
                json = json.sub;
            }
        }
        this.id = json.id;
        this.description = json.d;

        this.logos = new ClipArtLogos(json.logos);
    };

    return ClipArtSubcategoryModel;
});
