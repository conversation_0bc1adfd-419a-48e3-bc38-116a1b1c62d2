<div class="proof-model-window">
	<div class="calcelbox js-proofClose"> Close </div>
	<div class="gridd">
		<div class="grid-col grid-col_10of10">
			{{#if heading}}
			<h2 class="hdg hdg_2 js-proofmodalHeading">
				{{heading}}
			</h2>
			{{/if}}
			<div class="js-proofmodalContainer">
				<div class="js-proofmodalImager">
					<img src="{{host_url}}/webasset/w2p_mobile/{{baseAppUrl}}assets/media/images/tip.gif" alt="tip">
				</div>
				<div class="js-proofmodalContent">
					<div class ="js-proofmodalContentBold">
						{{msg1}}
					</div>
					<div class="js-proofmodalContentList">
						{{msg2}}
						<ul>
							<li>Add to Cart, then</li>
							<li>Save Order for Later</li>
						</ul>
					</div>
				</div>
			</div>
			<div class="txtStd js-proofmodalContent3">	    
				<input type="checkbox" aria-label="checkProof" id="checkProof" name="checkProof" value="checkProof" class="checkbox js-proofCheck">      
				{{#if msg3}}
					<label for="checkProof">
						<p>{{msg3}}</p>
					</label>
				{{/if}}
			</div>
		</div>
	</div>
	{{!-- <form action="{{proofDownload}}" method="post" name="myForm" id="myForm">
		<input name="imgUrl" type="hidden" id="imageLocation" value="{{loc}}">
		<!--<input type="submit" value="download">-->
		<!--<a ng-click="downloadProof()">download me</a>-->
	</form> --}}
	<div class="proof-actions">
		<div class="grid-col grid-col_10of10">
			<form action="{{proofDownload}}" method="POST">
				<input type="hidden" id="imgUrl" name="imgUrl" value="{{loc}}" required>
				<input type="hidden" id="fileName" name="fileName" value="{{fname}}" required>
				<div class="download-box">
					<button class="button button_next button_grey btn-download link-download"
						type="submit">{{actions.download.text}}</button>
					{{!-- <a
						class="button button_next button_grey btn-download link-download js-proofDownload">{{baseAppUrl.download.text}}</a>
					--}}
				</div>
	
				<div class="cancel-box">
					{{#if actions.cancel.text}}
					<div class="cancelbtn js-proofClose">{{actions.cancel.text}}</div>
					{{/if}}
				</div>
			</form>
	
		</div>
	</div>

</div>