
define(function(require) {
	'use strict';
	require('jquery-zoom');
	var ConfigurationProvider = require('../providers/Configuration');
	var Content = require('i18n!../constants/nls/en-us/Content');
	var Classes = require('../constants/Classes');
	var Controller = require('./Controller');
	var EventController = require('./Event');
	var ProductEvents = require('../constants/ProductEvents');
	var DomEvents = require('../constants/DomEvents');
	var RenderImageProvider = require('../providers/RenderImage');
	var Settings = require('../constants/Settings');
	var bindAll = require('mout/object/bindAll');
	var inherits = require('mout/lang/inheritPrototype');
	var $ = require('jquery');
	var prevUrl='';
	

	/**
	 * Regular expression to strip excluded tags
	 * @return {RegExp} Expression matching excluded tags
	 */
	var excludeTagsExp = (function() {
		var tags = Settings.PREVIEW_EXCLUDED_TAGS;
		var parts = tags.map(function(tag) {
			return '(' + tag + ')';
		});
		var exc = '(' + parts.join('|') + ')';
		return new RegExp('<' + exc + '>.*</\\1>', 'g');
	}());

	/**
	 * @class App.Controllers.Preview
	 * @extends App.Controllers.Controller
	 *
	 * @constructor
	 * @param {Object} config
	 */
	function PreviewController(config) {
		bindAll(this,
				'fetchImage',
				'fetchPrice',
				'update',
				'updateImage',
				'onProductChange',
				'retrieveImageUrl',
				'closeModalPopup',
				'hideTextOnImage',
				'closePopupMobile'
		);

		this.initLoad = true;

		Controller.call(this, config);
	}

	var proto = inherits(PreviewController, Controller);

	/**
	 * @property registry
	 * @type {Object.<String,App.Controllers.Controller>}
	 */
	proto.registry = {
			ZoomboxController: require('./Zoombox')
	};

	// -- Accessors ------------------------------------------------------------

	// -- Methods --------------------------------------------------------------

	/**
	 * @method template
	 * @param {Object} model
	 * @return {String}
	 */
	proto.template = require('hbs!templates/preview');

	/**
	 * @method attachEvents
	 * @chainable
	 */
	proto.attachEvents = function() {
		EventController.on(ProductEvents.CHANGE, this.onProductChange);
		this.$view.on(DomEvents.CLICK,Classes.PREVIEW_BUTTON_SELECTOR, this.retrieveImageUrl);
		this.$view.on(DomEvents.CLICK,Classes.CLOSE_BUTTON_SELECTOR, this.closeModalPopup);
		this.$view.on(DomEvents.CLICK,Classes.CLOSE_BUTTON_MOBILE_SELECTOR, this.closePopupMobile);
		this.$view.on(DomEvents.MOUSE_DOWN,Classes.TEXT_ON_IMAGE_SELECTOR, this.hideTextOnImage);
		return this;
	};

	/**
	 * @method detachEvents
	 * @chainable
	 */
	proto.detachEvents = function() {
		EventController.off(ProductEvents.CHANGE, this.onProductChange);
		this.$view.off(DomEvents.CLICK,Classes.PREVIEW_BUTTON_SELECTOR, this.retrieveImageUrl);
		this.$view.off(DomEvents.CLICK,Classes.CLOSE_BUTTON_SELECTOR, this.closeModalPopup);
		this.$view.off(DomEvents.CLICK,Classes.CLOSE_BUTTON_MOBILE_SELECTOR, this.closeModalPopup);
		this.$view.off(DomEvents.MOUSE_DOWN,Classes.TEXT_ON_IMAGE_SELECTOR, this.hideTextOnImage);
		return this;
	};

	/**
	 * @method update
	 * @param {App.Models.Product} product
	 * @chainable
	 */
	proto.update = function(product) {
		var xml = product.toXML(false);
		this.productCode =  product.info.productCode;

		//console.log('Preview.product', xml.replace(excludeTagsExp, ''));
		//if (this.productXml) {
		//	console.log('Preview.productXml', this.productXml.replace(excludeTagsExp, ''));
		//}

		if (this.productXml && xml && 
			this.productXml.replace(excludeTagsExp, '') === xml.replace(excludeTagsExp, '')) {
			return this;
		}

		this.productXml = xml;

		ConfigurationProvider
			.getConfiguration()
			.then(this.fetchImage)
			.then(this.updateImage);

		return this;
	};

	/**
	 * @method fetchImage
	 * @param {App.Models.Configuration} config
	 * @return {Promise|this}
	 */
	proto.fetchImage = function(config) {
		return RenderImageProvider
			.setConfig(config)
			.getImageUrl(this.productXml);
		//return "https://raptor.scene7.com/is/image/raptor/H16606?fmt=jpg&hei=425";
	};

	/**
	 * @method updateImage
	 * @param {String} url
	 * @return {String}
	 */
	proto.updateImage = function(url) {
		var $previewButton = this.$view.find(Classes.PREVIEW_BUTTON_SELECTOR);
		var $zoombox = this.$view.find(Classes.ZOOMBOX_TARGET_SELECTOR);
		var $img = this.$view.find(Classes.PREVIEW_IMG_SELECTOR);
		var $img1  = this.$view.find(Classes.PREVIEW_ZOOM_SELECTOR);
		var $img2  = this.$view.find(Classes.PREVIEW_ZOOM_CONTAIN_SELECTOR);
		var default_url = RenderImageProvider.getdefaultImageUrl(this.productCode);		

		// Showing the catalog image onload
		if(this.initLoad) {
			$zoombox.css({
				"background-image": "url(" + default_url + ")",
				"background-repeat": "no-repeat",
			  });
			this.initLoad = false;
		}
		
		//var timestamp = Date.now();
		//var key = (url.indexOf('?') !== -1) ? '&' : '?';
		//url = url + key + 'timestamp=' + timestamp;
		if (prevUrl.substring(0, prevUrl.lastIndexOf('&')) != url.substring(0, url.lastIndexOf('&'))) {
			this.$view.find(Classes.ZOOMBOX_TARGET_SELECTOR).addClass('loading');
		}
		prevUrl = url;
		var url1 =  url;
        window.url = url;
		/*if(url1.indexOf("hei=") < 0)
		{
			url1 =  url1 +("&hei=752");
		}
		if(url1.indexOf("wid=") < 0) {
			url1 =  url1 +("&wid=589");
		}*/
		$img.one('load error abort', function() {
			$zoombox.css({
				"background-image": "none",
				"background-repeat": "no-repeat",
			  });
			  $previewButton.css({
				"pointer-events": "auto"
			  })
			$zoombox.removeClass('loading');		
			zoom.init();
		});
		$img.attr('src', url).attr('alt', Content.get('Product Preview'));
		$img1.attr('src', url1).attr('alt', Content.get('Product Preview'));
		$img2.attr('src', url1).attr('alt', Content.get('Product Preview'));
		var loc = $("#mainImg").prop("src") ;
		$("#img1_tumb").prop("src",loc);
		$("#img1_tumb").prop('alt', Content.get('Product Preview'));
		
		if(loc.indexOf("wid=") > -1) {
			var n = loc.substring(loc.indexOf("wid="),(loc.indexOf("wid=")) + 7);
			loc = loc.replace(n,"wid=1200");
		}

		window.loc = loc;
		$("#img1_img").prop("src", loc);
		$("#img1_img").prop('alt', Content.get('Product Preview'));
		$("#img1_img").css({top: -40, left: -40});

		/* Including a constant for blueoverlay */
		if(Settings.BLUEOVERLAY === false){
			document.getElementById("blueOverlay").innerHTML = "";
			$('.js-zoombox-target').removeClass('js-zoombox-target').addClass('js-zoomLarge-image');
		}
		

	};

	proto.render = function() {
		Controller.prototype.render.call(this);
		var model = this.model;
		//console.log('Preview.js.model', model);
		model.baseAppUrl = baseAppUrl;
		//console.log('Preview.js.host_url', host_url);
		model.host_url = host_url;
		this.$view.html(this.template(model));
		return this.start();
	};

	// -- Event Handlers -------------------------------------------------------

	/**
	 * @method onProductChange
	 * @param {jQuery.Event} event
	 * @callback
	 */
	proto.onProductChange = function(event, product) {
		this.update(product);
	};
	// proto.getUrlParam = function(loc, name) {
	// 	var results = new RegExp('[\?&]' + name + '=([^&#]*)').exec(loc);
	//     if (results==null) {
	//        return null;
	//     }
	//     return decodeURI(results[1]) || 0;
	// }

	proto.retrieveImageUrl =  function()
	{
		// Removes any Popups that are still present in page
		if($("div[class*='custpop']").is(':visible')) {
			$('.close.tooltip-close').trigger("click");
		}

		// Aditya: Fix for Defect DCOM-15869
		if($("#Copy.popover").is(':visible')) {
			$("#Copy.popover").remove()
		}

        $('#pop-up').fadeIn(300);
        
        // Add the mask to body
        $('body').append('<div id="mask-cart"></div>');
		$('#mask-cart').fadeIn(300);
		
		
    
        var orgURl = $(".js-preview-img").attr('src');
		var wid_url = orgURl.substr(0,orgURl.indexOf('&wid='));
		var img_url;
		if(wid_url == '') {
			// console.log('www', wid_url);
			var hei_url = orgURl.substr(0,orgURl.indexOf('&hei='));
			if (hei_url == '') {
				hei_url = orgURl;
			}
			img_url = hei_url + '&wid=' + 1480;
		} else {
			img_url = wid_url + '&wid=' + 1480;	
		}
		// var final_url = img_url + '&wid=' + orgWidth;
				// console.log(final_url);
		var urlSplitUid =  orgURl.substr(0,orgURl.indexOf('?'));
		var tempuid = urlSplitUid.split("/");
		var uid = tempuid[tempuid.length-1];
	
	
		var urlSplitAppUrl = orgURl.substr(orgURl.indexOf('?')+1); 
		var appUrl =  urlSplitAppUrl.substr(0,urlSplitAppUrl.length);
		scene7url = urlSplitUid.substr(0,urlSplitUid.lastIndexOf('/')) +'/';
	
		zoom.initHeroImage();
		zoom.iviewer.settings.imgUrlPrefix =  scene7url; 
		zoom.iviewer.productPartNum = uid ;
		zoom.iviewer.appendUrl = appUrl ;
		zoom.iviewer.loadImage(img_url, 'sample',imgwidth); 
		$('.drag_help').fadeIn(300);
		$('#Hero').removeClass("remove-spinner");
		return false;
	};
	proto.closeModalPopup = function()
	{
		$("#backdrop").css("display","none");
		$(".js_preview_button").addClass("js-include-zoom");
		$(".modal-dialog").hide();
		$(".zoomWindowContainer").hide();
		$(".zoomContainer").hide();
		document.documentElement.style.overflow = 'auto';
		document.body.scroll = "yes";
		document.documentElement.scrollTop = "yes";
		if(window.box){
			for (var i = 0; i<window.box.length; i++) {
				window.box[i].$el.toggleClass(Classes.ZOOMBOX_ENABLED, false);
				window.box[i].$overlay.toggleClass(Classes.ZOOMBOX_HIGHLIGHT, false);
			}
		}
	};
	proto.hideTextOnImage = function() {
		$('.text-on-image').css('display','none');
	};
	$(window ).resize(function() {
		var a = $('.modal-content').offset();
		if (a) {
			var tp = a.top;
			var lt = a.left;
			$('.zoomContainer').offset({top: tp+23 ,left: lt+20});
	        $(".js_image_close").offset({top:$(".modal-content").offset().top - $(".js_image_close").height() + 50, left:($(".modal-content").offset().left + $(".modal-content").width()) - $(".js_image_close").width() + 60});
	        //$('.text-on-image').offset({top:36%, left: 11vw});
	    }
	});
	$( window ).resize(function() {
		var modalcontent = $( ".modal-content" ).offset();
		if (modalcontent) {
			var modalcontent_top = modalcontent.top;
			var modalcontent_left = modalcontent.left;
			$( ".zoomContainer" ).offset({top:modalcontent_top+10,left:modalcontent_left+10});
			$('.modal-content').css('height',$( window ).height()*0.8);
			$('.modal-content').css('width',$( window ).width()*0.8);
			$('.modal-body').css('height',$( window ).height()*0.8);
			$('.modal-body').css('width',$( window ).width()*0.8);
			$(".js_image_close").offset({top:$(".modal-content").offset().top - $(".js_image_close").height() + 50, left:($(".modal-content").offset().left + $(".modal-content").width()) - $(".js_image_close").width() + 60});
		}
	});

	proto.closePopupMobile = function () {
		$('.cart-zoom-close').trigger('click');
	}

	return PreviewController;
});


