define(function(require) {
    'use strict';

    var $ = require('jquery');
    var ActionEvents = require('../constants/ActionEvents');
    var Classes = require('../constants/Classes');
    var ConfigurationProvider = require('../providers/Configuration');
    var Errors = require('i18n!../constants/nls/en-us/Errors');
    var Content = require('i18n!../constants/nls/en-us/Content');
    var Controller = require('./Controller');
    var DomEvents = require('../constants/DomEvents');
    var EventController = require('./Event');
    var InkColorsProvider = require('../providers/InkColors');
    var ProductEvents = require('../constants/ProductEvents');
    var Query = require('../models/Query');
    var RegEx = require('../constants/RegEx');
    var TrackEvents = require('../constants/TrackEvents');
    var SessionStorage = require('../providers/SessionStorage');
    var Settings = require('../constants/Settings');
    var StateEvents = require('../constants/StateEvents');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var q = require('q');

    /**
     * @class App.Controllers.LogoUpload
     * @extends App.Controllers.Controller
     *
     * @constructor
     * @param {Object} config
     */
    function LogoUploadController(config) {
        bindAll(this,
            'initConfig',
            'initModels',
            'setContent',
            'showSuccess',
            'showUpload',
            'showUploading',
            'updateInk',
            'updatePreview',
            'updateTitle',
            'onChange',
            'onDifferentClick',
            'uploadDataReady',
            'uploadDataResponse',
            'onHideAction',
            'onNextClick',
            'onNoLogoClick',
            'onPrevClick',
            'onProductChange',
            'onShowAction',
            'onStateChange'
        );

        /**
         * @property logo
         * @type {Object}
         */
        this.logo = SessionStorage.getValue('logo_CUSTOM');

        /**
         * @property query
         * @type {App.Models.Query}
         */
        this.query = Query.getInstance();

        /**
         * @property isUploading
         * @type {Boolean}
         */
        //this.isUploading = false;

        Controller.call(this, config);

        // Start hidden
        this.$view.hide();
        
        /**
         * Omniture site prefix
         *
         * @type {String}
         */
        this.site_prefix = Settings.OMNITURE_PREFIX;

        /**
         * @property ready
         * @type {Promise}
         */
        /*this.ready = q
            .when(this.ready)
            .then(this.showUpload)
            .fail(this.onError);*/
    }

    var proto = inherits(LogoUploadController, Controller);
    var progressStatus = false;
    var progressVal = 0;
    var uploadResponse = null;
    var filename = '';
    /**
     * @method init
     * @return {Promise}
     */
    proto.init = function() {
        return ConfigurationProvider
            .getConfiguration()
            .then(this.initConfig)
            .spread(this.initModels)
            .fail(this.onError);
    };

    /**
     * @method initConfig
     * @return {Promise}
     */
    proto.initConfig = function(config) {

    	var colors = InkColorsProvider
            .setConfig(config)
            .getColors();

        this.productConfig = config;

        return [colors];
    };

    /**
     * @method initModels
     * @param {App.Models.Collections.Colors} colors
     * @chainable
     */
    proto.initModels = function(colors) {
        this.colors = colors;
        return this;
    };

    // -- Accessors ------------------------------------------------------------

    // -- Methods --------------------------------------------------------------

    /**
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = require('hbs!templates/logoUpload');

    /**
     * @method titleTemplate
     * @param {Object} model
     * @return {String}
     */
    proto.titleTemplate = require('hbs!templates/product/title');

    /**
     * @method uploadTemplate
     * @param {Object} model
     * @return {String}
     */
    proto.uploadTemplate = require('hbs!templates/logo/upload');

    /**
     * @method successTemplate
     * @param {Object} model
     * @return {String}
     */
    proto.successTemplate = require('hbs!templates/logo/success');

    /**
     * @method render
     * @chainable
     */
    proto.render = function() {
        var productConfig = this.productConfig;

        this.$view
            .html(this.template({
                next: Content.get('Next Step'),
                prev: Content.get('Previous Step'),
                brandLogoUrl: Settings.BRAND_LOGO_URL,
                siteCss: Settings.SITE_CSS,
                brandLogo: Settings.BRAND_LOGO,
                notDisplay: Content.get('customLogoNotDisplay'),
                disclaimer: Content.get('LogoLegalDisclaimerText'),
                /*instructions: Content.get('uploadLogo', {
                    uploadLabel: Content.get('Browse for a file'),
                    uploadUrl: Settings.SVC_UPLOAD
                })*/
            }));

        return this;
    };

    /**
     * @method cacheElements
     * @chainable
     */
    proto.cacheElements = function() {
        var $view = this.$view;

       /* this.$controls = $view.find(Classes.LOGO_CONTROLS_SELECTOR);
        this.$details = $view.find(Classes.LOGO_DETAILS_SELECTOR);
        this.$file = $view.find(Classes.UPLOAD_FILE_SELECTOR);
        this.$next = $view.find(Classes.NEXT_SELECTOR);
        this.$nextLabel = $view.find(Classes.NEXT_LABEL_SELECTOR);
        this.$prev = $view.find(Classes.PREV_SELECTOR);
        this.$preview = $view.find(Classes.LOGO_PREVIEW_SELECTOR);
        this.$title = $view.find(Classes.PRODUCT_TITLE_SELECTOR);
        this.$upload = $view.find(Classes.LOGO_UPLOAD_SELECTOR);
        this.$uploading = $view.find(Classes.LOGO_UPLOADING_SELECTOR);*/

        return this;
    };

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        this.$view
            .on(DomEvents.CLICK, Classes.NEXT_SELECTOR, this.onNextClick)
            .on(DomEvents.CLICK, Classes.PREV_SELECTOR, this.onPrevClick)
            .on(DomEvents.CLICK, Classes.LOGO_DIFFERENT_SELECTOR, this.onDifferentClick)
            .on(DomEvents.CLICK, Classes.LOGO_NO_LOGO_SELECTOR, this.onNoLogoClick)
            .on(DomEvents.CHANGE, Classes.UPLOAD_FILE_SELECTOR, this.onFileSelected);

        EventController
            .on(ActionEvents.HIDE_ALL, this.onHideAction)
            .on(ActionEvents.LOGO_CUSTOM, this.onShowAction)
            .on(ActionEvents.LOGO_UPLOADING, this.showUploading)
            .on(ActionEvents.LOGO_UPLOAD_COMPLETE, this.uploadDataResponse)
            .on(ProductEvents.CHANGE, this.onProductChange)
            .on(StateEvents.CHANGE, this.onStateChange);

        return this;
    };

    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        this.$view
            .off(DomEvents.CLICK, Classes.NEXT_SELECTOR, this.onNextClick)
            .off(DomEvents.CLICK, Classes.PREV_SELECTOR, this.onPrevClick)
            .off(DomEvents.CLICK, Classes.LOGO_DIFFERENT_SELECTOR, this.onDifferentClick)
            .off(DomEvents.CLICK, Classes.LOGO_NO_LOGO_SELECTOR, this.onNoLogoClick)
            .off(DomEvents.CHANGE, Classes.UPLOAD_FILE_SELECTOR, this.onFileSelected);

        EventController
            .off(ActionEvents.HIDE_ALL, this.onHideAction)
            .off(ActionEvents.LOGO_CUSTOM, this.onShowAction)
            .off(ActionEvents.LOGO_UPLOADING, this.showUploading)
            .off(ActionEvents.LOGO_UPLOAD_COMPLETE, this.uploadDataResponse)
            .off(ProductEvents.CHANGE, this.onProductChange)
            .off(StateEvents.CHANGE, this.onStateChange);

        return this;
    };

    /**
     * @method showUpload
     * @chainable
     */
    proto.showUpload = function() {
        var productConfig = this.productConfig;
        var uploadUrl = productConfig && Settings.SVC_UPLOAD;
        var inkColor = this.inkColor || {};

        this
            .updateNext()
            .updatePreview();

        this.$uploading.hide();
        this.$details.show();
        this.$next.show();
        this.$prev.show();
        this.$upload.show();
		    
        this.$controls
            .html(this.uploadTemplate({
                error: this.error,
                uploadLabel: Content.get('Browse for a file'),
                uploadUrl: uploadUrl,
                inkDesc: inkColor.description,
                inkColor: inkColor.redLevel + ',' + inkColor.greenLevel + ',' + inkColor.blueLevel
            }))
            .show();

        return this;
    };

    /**
     * @method showUploading
     * @chainable
     */
    proto.showUploading = function() {
      /*progressVal = 0;
        progressStatus = false;
        this.$controls.hide();
        this.$details.hide();
        this.$next.hide();
        this.$prev.hide();
        this.$upload.hide();
        this.$uploading.show();
        progress(0);
        return this;*/
    };


    function progress(value) {
      progressVal =  value;
      $(".progress").css("width", progressVal+"%" );
      progressVal = progressVal + 1 ;
      if(progressStatus == true ){
        return;
      }

      if ( progressVal <= 97 ) {
        setTimeout(function(){ progress(progressVal) }, 200); 
      }
    }


     function progress1(value) {
      progressVal =  value;
      $(".progress").css("width", progressVal+"%" );
      progressVal = progressVal + 1 ;
      //console.log(val);
      if ( progressVal <= 97 ) {
        setTimeout(function(){ progress1(progressVal) },200); 
      }
    }

    /**
     * @method showSuccess
     * @chainable
     */
    proto.showSuccess = function() {
      if(progressVal < 97)
      {
          for (var progressVal = progressVal + 1; progressVal >= 100; progressVal++) {
              $(".progress").css("width", progressVal+"%" );
          };
          setTimeout(function(){ 
          var inkColor = that.inkColor;   
          that
          .updateNext()
          .updatePreview();

          that.$details.hide();
          that.$uploading.hide();
          that.$next.show();
          that.$prev.show();
          that.$upload.show();

          that.$controls
              .html(that.successTemplate({
                  inkDesc: inkColor.description,
                  inkColor: inkColor.redLevel + ',' + inkColor.greenLevel + ',' + inkColor.blueLevel
              }))
          .show();
          return that;

          }, 200); 

      } else {
        progressStatus = true;
        progress1(progressVal);
        var that = this;
        setTimeout(function(){ 
            var inkColor = that.inkColor;   
            that
            .updateNext()
            .updatePreview();

            that.$details.hide();
            that.$uploading.hide();
            that.$next.show();
            that.$prev.show();
            that.$upload.show();

            that.$controls
                .html(that.successTemplate({
                    inkDesc: inkColor.description,
                    inkColor: inkColor.redLevel + ',' + inkColor.greenLevel + ',' + inkColor.blueLevel
                }))
            .show();
            return that;
          }, 800);
      }    
    };

    /**
     * @method updateInk
     * @param {Object} product
     * @chainable
     */
    proto.updateInk = function(product) {
          if(product.steps){
        var values = product.steps.getValues().values;
        var color = values.inkColor2 || {};

        if (!color.value) {
            color = values.inkColor1 || {};
        }

        this.inkColor = this.colors.getById(color.value);
          }
        return this;
    };

    /**
     * @method updateNext
     * @chainable
     */
    proto.updateNext = function() {
     /*if (this.logo) {
            this.$nextLabel.html(Content.get('Next Step'));
            this.$next
                .removeClass(Classes.BUTTON_GREY)
                .removeClass(Classes.BUTTON_NEUTRAL);
            this.$next.attr("id", "Header-Next-UploadLogoNextButton");
            this.$nextLabel.attr("id", "Header-Next-UploadLogoNextButtonLabel");
        } else {
            this.$nextLabel.html(Content.get('Proceed Without a Logo'));
            this.$next.addClass(Classes.BUTTON_NEUTRAL);
            this.$next.attr("id", "Header-Next-UploadLogoProceedButton");
            this.$nextLabel.attr("id", "Header-Next-UploadLogoProceedButtonLabel");
        }
        this.$prev.attr("id", "Header-Previous-UploadLogo");
        return this;*/
    };

    /**
     * @method updatePreview
     * @chainable
     */
    proto.updatePreview = function() {
       /* var $preview = this.$preview;
        var error = this.error;
        var logo = this.logo;

        // On edit/re-order of a product this controller
        // is instantiated before the logo data is fetched
        // from the order and save to session. In this case
        // the session value is set in ProductModel.setLogo()
        if ( !logo) {
            logo = SessionStorage.getValue('logo_CUSTOM');
        }

        // Use the full image path if its available.
        var image = (logo && logo.logoUrl) || (logo && logo.logoImageName);

        $preview.toggleClass('logoUpload_error', !!error);

        if (error) {
            $preview.empty();
        }

        if (image) {
            $preview.html('<img src="' + image + '" />');
        }

        return this;*/
    };

    /**
     * @method updateTitle
     * @chainable
     */
    proto.updateTitle = function() {
        var state = this.state;

        if (!state) {
            return this;
        }

       /* this.$title.html(this.titleTemplate({
            id: state.id,
            description: state.description
        }));*/

        return this;
    };

    // -- Event Handlers -------------------------------------------------------

    /**
     * @method onChange
     * @param {jQuery.Event} event
     * @callback
     */
    //proto.onChange = function(event) {
      proto.onFileSelected = function(event) {
        EventController.emit(ActionEvents.LOGO_UPLOADING);

        var file = event.currentTarget.files[0];
        filename = file.name;
        //console.log("LogoUpload.file.name -" + file.name + "-");
        var formData = new FormData();
        formData.append('source', '');
        formData.append('noScale', 'false');
        formData.append('file', file);
        $.ajax({
            url: Settings.SVC_UPLOAD,
            type: 'POST',
            data: formData,
            cache: false,
            dataType: 'json',
            processData: false, // Don't process the files
            contentType: false, // Set content type to false as jQuery will tell the server its a query string request
            headers: {'Content-Type': undefined},
            xhrFields: {
                withCredentials: false
            },
            success: function(data, textStatus, jqXHR) {
                // console.log("LogoUpload.response: " + data);
                uploadResponse = data;
                EventController.emit(ActionEvents.LOGO_UPLOAD_COMPLETE);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                // console.log("LogoUpload.error: " + textStatus);
            }
        });
    };
    
    
    /**
     * @method uploadDataReady
     * @param {jQuery.Event} event
     * @callback
     */
    proto.uploadDataResponse = function(event) {
    	
    	if(uploadResponse == null) {
            return;
        }
    	
    	//var toks = uploadResponse.split('<');
        
    	// var xmlDoc = $.parseXML(uploadResponse);
        // var $xml = $( xmlDoc );
        // var uid = $xml.find( "UID" ).text();
        // var message = $xml.find( "message" ).text();
        // this.message = message;
        // var status = $xml.find( "status" ).text();
        // var URL = $xml.find( "URL" ).text() + '&Custom=hei:100,wid:100,fit:constrain';
        // var code = $xml.find( "error_code" ).text();
        var uid = uploadResponse.UID;
        var message = uploadResponse.message;
        this.message = message;
        var status = uploadResponse.status;
        var URL = uploadResponse.URL + '&Custom=hei:100,wid:100,fit:constrain';
        var code = uploadResponse.error_code;
        
        
        if (status == 'TRUE') {
        	  var logo = {
        	            logoType: 'CUSTOM',
        	            logoCode: uid,
        	            logoImageName: URL,
                      logoUrl: URL,
        	           // logoHeight: 555,
        	            //logoWidth: 500
        	        };

        	       // console.log('kkk', logo);
        	        if (!logo.logoImageName) {
        	            this.logo = null;
        	            this.error = Errors.get(108);
        	            this.showUpload();
        	            return;
        	        }

        	        this.error = null;
        	        this.logo = logo;

        	        this.track({
        	            pageName:  this.site_prefix + ': W2P : Logo Upload Success',
        	            //eventName: 'event13'
        	        }).showSuccess();
                  //console.log('URL'+URL);
                  //EventController.emit(TrackEvents.TRACK_LOGOUPLOAD, {
                  //    status: 'SUCCESS',
                  //    file: URL
                  //});
		} else {
			
			this.logo = null;
            this.error = Errors.get(code, message);
            // console.log(this.error);
            SessionStorage.removeValue("logo_CUSTOM");
            //this.showUpload();
            //EventController.emit(TrackEvents.TRACK_LOGOUPLOAD, {
            //    status: this.error,
            //    file: filename
            //});
            return;
		}
    };

    /**
     * @method uploadDataReady
     * @param {jQuery.Event} event
     * @callback
     */
    proto.uploadDataReady = function(event) {
        var toks = uploadResponse.split(',');

        if (!toks.length) {
            this.logo = null;
            this.error = Errors.get(-1);
            this.showUpload();
            return;
        }

        var code = toks[0].replace(RegEx.QUERY_VALUE, '');
        var message = toks[1].replace(RegEx.QUERY_VALUE, '');

        if (code && code !== '0') {
            this.logo = null;
            this.error = Errors.get(code, message);
            this.showUpload();
            return;
        }

        var logo = {
            logoType: 'CUSTOM',
            logoCode: toks[6].replace(RegEx.FILE_NAME, '$1').replace(RegEx.IMAGE_EXT, ''),
            logoImageName: toks[6].replace(RegEx.QUERY_VALUE, ''),
            logoHeight: toks[2].replace(RegEx.QUERY_VALUE, ''),
            logoWidth: toks[3].replace(RegEx.QUERY_VALUE, '')
        };

        // console.log('kkk', logo);
        if (!logo.logoImageName) {
            this.logo = null;
            this.error = Errors.get(108);
            this.showUpload();
            return;
        }

        this.error = null;
        this.logo = logo;

        this.track({
            pageName:  this.site_prefix + ': W2P : Logo Upload Success',
            //eventName: 'event13'
        })
        .showSuccess();
    };

    /**
     * @method onHideAction
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onHideAction = function() {
        this.$view.hide();
    };

    /**
     * @method onShowAction
     * @param {jQuery.Event} event
     * @param {Function(App.Models.ClipArt.Logo)} callback
     * @callback
     */
   /* proto.onShowAction = function(event, done) {
        this.callback = done;

        this.track({
            pageName:  this.site_prefix + ': W2P : Logo Upload',
            eventName: false
        });

        // On edit/re-order of a product this controller
        // is instantiated before the logo data is fetched
        // from the order and save to session.
        if ( !this.logo) {
            this.logo = SessionStorage.getValue('logo_CUSTOM');
        }

        if (this.logo) {
            this.showSuccess();
        } else {
            this.showUpload();
        }

        //this.$view.show();
    };*/

    /**
     * @method onDifferentClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onDifferentClick = function(event) {
        event.preventDefault();

        this.showUpload();
    };


    /**
     * @method onNoLogoClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onNoLogoClick = function(event) {
        event.preventDefault();

        var logo = { logoType: 'NO LOGO' };
        var callback = this.callback;

        if (typeof callback === 'function') {
            callback(logo);
        }

        delete this.callback;

        EventController
            .emit(ProductEvents.LOGO_CHANGE, logo)
            .emit(ActionEvents.HIDE_ALL)
            .emit(ActionEvents.PRODUCT_STEP, true);
    };

    /**
     * @method onNextClick
     * @param {jQuery.Event} event
     * @callback
     */
    /*proto.onNextClick = function(event) {
        event.preventDefault();

        var logo = this.logo || { logoType: 'NO LOGO' };
        var callback = this.callback;
        if (typeof callback === 'function') {
            callback(logo);
        }
        this.ownlogoCode = this.logo ? this.logo.logoCode : undefined;
        delete this.callback;

        EventController
            .emit(ProductEvents.LOGO_CHANGE, logo)
            .emit(ActionEvents.HIDE_ALL)
            .emit(ActionEvents.PRODUCT_STEP, true);
    };*/

    /**
     * @method onPrevClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onPrevClick = function(event) {
        event.preventDefault();

        if(this.ownlogoCode == undefined && this.logo !=  undefined){

          var logo = this.logo || { logoType: 'NO LOGO' };
          var callback = this.callback;
          if (typeof callback === 'function') {
              callback(logo);
          }
          this.ownlogoCode = this.logo.logoCode ;
          delete this.callback;

          EventController
              .emit(ActionEvents.HIDE_ALL)
              .emit(ActionEvents.PRODUCT_STEP);
          $(".button_next").removeClass("button_grey");

        }else{
          delete this.callback;
          EventController
              .emit(ActionEvents.HIDE_ALL)
              .emit(ActionEvents.PRODUCT_STEP);
        }
        
    };

    /**
     * @method onProductChange
     * @param {jQuery.Event} event
     * @param {App.Models.Product} product
     * @callback
     */
    proto.onProductChange = function(event, product) {
        this.updateInk(product);
    };

    /**
     * @method onStateChange
     * @callback
     */
    proto.onStateChange = function(event, state) {
        this.state = state;
        this.updateTitle();
    };

    return LogoUploadController;
});
