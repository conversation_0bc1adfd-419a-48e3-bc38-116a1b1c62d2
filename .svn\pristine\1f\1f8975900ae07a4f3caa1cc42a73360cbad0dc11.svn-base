define(function () {
    'use strict';

    /**
     * @class App.Constants.ActionEvents
     * @static
     */
    var ActionEvents = {
        /**
         * @event HIDE_ALL
         */
        HIDE_ALL: 'actionHideAll',

        /**
         * @event HIDE_HYBRID
         */
        HIDE_HYBRID: 'hideHybrid',

        /**
        * @event HIDE_PRODUCT
        */
        HIDE_PRODUCT: 'actionHideProduct',

        /**
         * @event LOGO_CUSTOM
         */
        LOGO_CUSTOM: 'actionLogoCustom',

        /**
         * @event LOGO_STANDARD
         */
        LOGO_STANDARD: 'actionLogoStandard',
        LOGO_LOGOMX: 'actionLogoMix',
        UPDATELOGORESULTS: 'updateLogoResults',
        CHANGELOGOMIXPAGE: 'changeLogoMixPage',
        REORDERACTION: 'reorderaction',


        /**
         * @event LOGO_ADDLOGO
         */
        LOGO_ADDLOGO: 'actionAddLogo',

        /**
         * @event LOGO_UPLOADLOGO
         */
        LOGO_UPLOADLOGO: 'actionUploadLogo',

        /**
         * @event FBT_INDEX
         */
        FBT_INDEX: 'actionFbtIndex',
        FBT_SUMMARY: 'actionFbtSummary',
        FBT_NEXT: 'actionFbtNext',
        FBT_HYBRID: 'actionFbtHybrid',
        FBT_REVIEWCART: 'actionReviewCart',

        /**
         * @event LOGO_UPLOADING
         */
        LOGO_UPLOADING: 'actionLogoUploading',

        /**
         * @event LOGO_UPLOAD_COMPLETE
         */
        LOGO_UPLOAD_COMPLETE: 'actionLogoUploadComplete',

        /**
        * @event LOGO_UPLOAD_COMPLETE
        */
        MIX_LOGO_UPLOAD_COMPLETE: 'actionMixLogoUploadComplete',

        STANDARD_VERSE: 'actionStandardVerse',

        /**
         * @event PRODUCT_STEP
         */
        PRODUCT_STEP: 'actionProductStep',

        /**
         * @event CHANGE_RM
         */
        CHANGE_RM: 'actionChangeRM',

        /**
         * @event SHOW_PRODUCT
        */
        SHOW_PRODUCT: 'actionShowProduct',

        /**
         * @event MODAL_OPEN
         */
        MODAL_OPEN: 'actionModalOpen',

        /**
         * @event MODAL_CLOSE
         */
        MODAL_CLOSE: 'actionModalClose',

        /**
         * @event PROOF_OPEN
         */
        PROOF_OPEN: 'proofModalOpen',

        /**
         * @event CLIPART_CAT_CHANGE
         */
        CLIPART_CAT_CHANGE: 'clipartCatChange',

        /**
         * @event CLIPART_SUBCAT_CHANGE
         */
        CLIPART_SUBCAT_CHANGE: 'clipartSubCatChange',

        /**
         * @event CLIPART_SELECTION
         */
        CLIPART_SELECTION: 'clipartSelection',

        /**
         * @event LOGOMIX_SELECTION
         */
        LOGOMIX_SELECTION: 'logomixSelection',

        /**
         * @event LOGO_CANCEL
         */
        LOGO_CANCEL: 'logoCancel',

        /**
        * @event LOGO_RENEW
        */
        LOGO_RENEW: 'logoRenew',

        /**
         * @event LOGO_COLOR
         */
        LOGO_COLOR: 'logoColor',

        /**
         * @event LOGO_INFO
         */
        LOGO_INFO: 'logoInfo',

        /**
         * @event CHANGE_QUANTITY_VALUE
         */
        CHANGE_QUANTITY_VALUE: 'changeQuantityValues',

        /**
         * @event RELOAD_ADDTOCART_REVIEW_CHECKBOX
         */
        RELOAD_ADDTOCART_REVIEW_TEMPLATE: 'reviewAddToCartCheckbox',

        /**
         * @event GO_TO_EDIT_STEP
         */
        GO_TO_EDIT_STEP: 'goToEditStep',

        /**
         * @event GO_TO_FBT_EDIT_STEP
         */
        GO_TO_FBT_EDIT_STEP: 'goToFBTEditStep',

        /**
         * @event LOGO_STATE_REP
         */
        LOGO_STATE_REP: 'logoStateRepresentation',

        /**
        * @event REQUEST_LOGO_STATE_REP
        */
        REQUEST_LOGO_STATE_REP: 'requestLogoStateRepresentation',

        /**
        * @event LOGO_DESC_UPDATE
        */
        LOGO_DESC_UPDATE: 'logoDescriptionUpdated',

    };

    return ActionEvents;
});
