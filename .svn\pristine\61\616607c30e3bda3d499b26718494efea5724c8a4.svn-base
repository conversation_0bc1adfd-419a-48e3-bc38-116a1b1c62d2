define(function (require) {
    'use strict';

    var $ = require('jquery');
    var AbstractProvider = require('./Abstract');
    var ProductInfoModel = require('../models/ProductInfo');
    var Settings = require ('../constants/Settings');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var Query = require('models/Query');

    /**
     * @class App.Providers.ProductInfo
     * @extends App.Providers.Abstract
     *
     * @constructor
     */
    var ProductInfoProvider = function () {
    	 bindAll(this, '_onResponseReceived',
         		'_onPriceResponseReceived',
         		'_processNewPrice');

        /**
         * @property promises
         * @type {Array.<Promise>}
         */
        this.promises = {};

        AbstractProvider.apply(this, arguments);
    };

    var proto = inherits(ProductInfoProvider, AbstractProvider);

    /**
     * @method getProductInfo
     * @param {object} params
     * @param {bool} flushCache
     * @return {Promise}
     */
    proto.getProductInfo = function(params, flushCache) {
        this.setBaseUrl('');

        if (this.promises[params.productId] && flushCache !== true) {
            // data will be cached after the first call
            return this.promises[params.productId];
        }

        // In case of missing parameters, return early with a new promise
        // that doesn't created this.promises (so we can try again)
        if (!params.productId ) {
            throw new Error('missing required parameters');
        }

        var query = Query.getInstance();
        params.cfg = 'W2P';
        params.pg = Settings.PRICE_GROUP;        
        params.sb = Settings.PRICE_BRAND;

        if (query.profileId) {
            params.profileId = query.profileId;
        }
        // use the parent "get" method, which returns an ajax promise
        this.promises[params.productId] = this
            .get(Settings.SVC_PRODUCT, params)
            .then(this._onResponseReceived)
            .fail(this._onError);

        return this.promises[params.productId];
    };

    /**
     * Handles the response from the ajax call
     *
     * @method _onResponseReceived
     *
     * @param {HTMLDocument} data
     * @private
     */
    proto._onResponseReceived = function(data) {
        var response = $.xml2json(data);

        if (response.error) {
            throw new Error(response.error);
        }

        return new ProductInfoModel(data);
    };

    /**
     * Get new Pricing service
     */
    proto.getPriceInfo = function(params, flushCache) {
    	
    	 //this.setBaseUrl(Settings.SITE_HOST);       
         params.cfg = 'W2P';
         // use the parent "get" method, which returns an ajax promise
         this.promises['pricedata'] = this
             .get(Settings.SVC_NEW_PRICE, params)
             .then(this._onPriceResponseReceived)
             .fail(this._onError);

         return this.promises['pricedata'];
    }
    
    
    
    /**
     * Handles the pricing response
     */
    proto._onPriceResponseReceived = function(data) {
        var response = $.xml2json(data);
        if (response.error) {
            throw new Error(response.error);
        }
        var kkk = new ProductInfoModel();
        var newPriceResponse = this._processNewPrice(response.PricingServiceResponse.priceInfo.price);
        return newPriceResponse;
    };
    
    /**
     * Process the pricing and create key value pairs for easy access
     * 
     */
    proto._processNewPrice = function(data) {
        //console.log('ProductInfo._processNewPrice.data:', data);
		var result = {};
		for(var i = 0; i < data.length; i++) {    			
			if(data[i].$.type =='ONE') {
				var opt = {};
				if (typeof data[i].option.length == 'undefined') {
					opt.price = data[i].option.$.price;
					opt.qty = data[i].option.$.qty; 
					if (typeof data[i].option.$.discountPrice != 'undefined') {
						opt.price = data[i].option.$.discountPrice; 
					}
				} else {
					opt.price = data[i].option[0].$.price;
					opt.qty = data[i].option[0].$.qty; 
					if (typeof data[i].option[0].$.discountPrice != 'undefined') {
						opt.price = data[i].option[0].$.discountPrice; 
					}
				}
				data[i].option = opt;
			} else {
				var opt = [];
				for(var j = 0; j < data[i].option.length; j++) {
					opt[j] = data[i].option[j].$;
					if (typeof opt[j].discountPrice != 'undefined') {
						opt[j].price = opt[j].discountPrice;
					}
					//console.log('ProductInfo._processNewPrice.opt.ADDING:', opt);
				}
				data[i].option = opt;
                //console.log('ProductInfo._processNewPrice.opt:', opt);
			}
			result[data[i].$.id] = data[i];
		}
        //console.log('ProductInfo._processNewPrice:', result);
		return result;		   
    }

    return new ProductInfoProvider();
});
