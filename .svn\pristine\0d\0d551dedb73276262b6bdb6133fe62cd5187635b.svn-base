/*jshint camelcase:false*/
//needs to use many camelcase properties
define(function(require) {
    'use strict';

    //require('lib/omniture/s_code');

    var $ = require('jquery');
    var EventController = require('../controllers/Event');
    var Settings = require('../constants/Settings');
    var Content = require('i18n!../constants/nls/en-us/Content');
    var StateEvents = require('../constants/StateEvents');
    var TrackEvents = require('../constants/TrackEvents');
    var ActionEvents = require('../constants/ActionEvents');
    var bindAll = require('mout/object/bindAll');
    var objectKeys = require('mout/object/keys');
    var Query = require('models/Query');
    var FbtModel = require('models/Fbt');
    var ProductModel = require('models/Product');
    var SessionStorage = require('../providers/SessionStorage');

    var x, y = null;
    var reviewLogo = { 'NO LOGO': "Logo:No Logo", 'STANDARD': "Logo:Library Logo", 'CUSTOM': "Logo:Custom Logo" };
    var reviewLogoHC = { 'NO LOGO': "Logo:No Logo", 'STANDARD': "Logo:Logo from Deluxe Library", 'CUSTOM': "Logo:Upload Your Own Logo" };
    var isFbt = false;
    var trackedFbtKeys = [];

    /**
     * @class App.Controllers.Omniture
     *
     * @constructor
     */
    function Omniture() {
        bindAll(this,
            'autoTrack',
            'onStateChange',
            'onTrackEvent'
        );
        this.init();
    }

    var proto = Omniture.prototype;
    var uiGroup = '';    
    /**
     * @method init
     * @chainable
     */
    proto.init = function() {

        this.initLoad = true;
        /**
         * @property query
         * @type {App.Models.Query}
         */
        this.query = Query.getInstance();

        if (this.query.rec) {
            isFbt = true;
        }

        /**
         * Event namespace
         *
         * @type  {String}
         */
        this.eventNamespace = '.tracking';

        /**
         * Data Attributes
         *
         * @type  {Object}
         */
        this.dataAttr = {
            linkName: 'link-name',
            linkType: 'link-type',
            linkVars: 'link-vars',
            linkEvents: 'link-events'
        };

        /**
         * String used to split/join Omniture parameters
         *
         * @type {String}
         */
        this.varSplitter = ',';

        /**
         * Omniture s_account
         *
         * @type {String}
         */
        this.s_account = Settings.OMNITURE_ACCT;

        /**
         * Omniture site prefix
         *
         * @type {String}
         */
        this.site_prefix = Settings.OMNITURE_PREFIX;

        //this.omniture_trackedGroupStep = '';

        return this.attachEvents();
    };

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        EventController
            .on(StateEvents.CHANGE, this.onStateChange)
            .on(TrackEvents.CHANGE, this.onTrackEvent)
            //.on(TrackEvents.TRACK_TEALEAF, this.onTrackTealeaf)
            //.on(TrackEvents.TRACK_LOGOUPLOAD, this.onTrackLogoUpload)
            .on(ActionEvents.FBT_INDEX, this.onFbtIndex)
            .on(ActionEvents.FBT_NEXT, this.onFbtHybridProof)
            .on(ActionEvents.FBT_SUMMARY, this.onFbtSummary)
            .on(ActionEvents.FBT_REVIEWCART, this.onFbtReviewCart);
        return this;
    };

    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        EventController
            .off(StateEvents.CHANGE, this.onStateChange)
            .off(TrackEvents.CHANGE, this.onTrackEvent)
            //.off(TrackEvents.TRACK_TEALEAF, this.onTrackTealeaf)
            //.on(TrackEvents.TRACK_LOGOUPLOAD, this.onTrackLogoUpload)
            .off(ActionEvents.FBT_INDEX, this.onFbtIndex)
            .off(ActionEvents.FBT_NEXT, this.onFbtHybridProof)
            .off(ActionEvents.FBT_SUMMARY, this.onFbtSummary)
            .off(ActionEvents.FBT_REVIEWCART, this.onFbtReviewCart);
        return this;
    };

    /**
     * Click event handler
     *
     * @method onTrackEvent
     * @param {object} event
     * @param {object} linkOptions
     * @callback
     */
    proto.onTrackEvent = function(event, linkOptions) {
        if (Settings.OMNITURE_ACCT) {
            this.trackEvent(linkOptions);
        }
    };

    /**
     * State change event handler
     *
     * @method onStateChange
     * @param {object} event
     * @param {object} linkOptions
     * @callback
     */
    var matrixColor = "";
    var fontColor = "";
    var fontList = "";
    var FontsList = "";
    
    proto.onStateChange = function(event, state) {
        //console.log('Omniture.onStateChange', state);
        // The html/jsp page containing the application has
        // its own tracking event, which is more difficult
        // to disable. To prevent duplicate calls, just prevent
        // the call on step 1.
        // if (!state || state.id === 1) {
        //     return;
        // }

        x = x ? x : $('input[name=productId]').val();
        y = y ? y : $('#QuantityCombobox').val();

        if (!state) {
            return;
        }
        var model = (state.controller && state.controller.model) || state;
        uiGroup = model.trackGroup;
        var trackEvent = model.trackEvent && model.trackEvent.omniture || '';
        var omniturePageName = model.description;
        if (omniturePageName.indexOf("Design") > -1) {
            omniturePageName = omniturePageName.replace("Design", "Design Background");
        } else if (omniturePageName.indexOf("Company Information") > -1) {
            omniturePageName = omniturePageName.replace("Company Information", "Your Info");
        } else if (omniturePageName.indexOf("Optional Information") > -1) {
            omniturePageName = omniturePageName.replace("Optional Information", "Additional Information");
        } else if (omniturePageName.indexOf("Front of Card - Text Imprint") > -1) {
            omniturePageName = omniturePageName.replace("Front of Card - Text Imprint", "Front of Card");
        } else if (omniturePageName.indexOf("Inside of Card - Text Color") > -1) {
            omniturePageName = omniturePageName.replace("Inside of Card - Text Color", "Inside of Card:Text Color");
        } else if (omniturePageName.indexOf("Inside of Card - Verse") > -1) {
            omniturePageName = omniturePageName.replace("Inside of Card - Verse", "Inside of Card:Verse");
        } else if (omniturePageName.indexOf("Inside of Card - Standard Verse") > -1) {
            omniturePageName = omniturePageName.replace("Inside of Card - Standard Verse", "Inside of Card:Standard Verse");
        } else if (omniturePageName.indexOf("Inside of Card - Text Imprint") > -1) {
            omniturePageName = omniturePageName.replace("Inside of Card - Text Imprint", "Inside of Card:Text Imprint");
        } else if (omniturePageName.indexOf("Inside of Card - Logo") > -1) {
            omniturePageName = omniturePageName.replace("Inside of Card - Logo", "Inside of Card:Logo");
        }

        var prodId = model.questions._items[0].productInfo.productId ? model.questions._items[0].productInfo.productId : this.query.skuId;

        var linkOptions = null;
        
        Settings.OMNITURE_PAGENAME = omniturePageName;
        this.appUI_Group = model.trackGroup;
        //console.log("this.appUI_Group: " + this.appUI_Group);
        this.step = "Step-"+Settings.stateModel.states.length;
        //console.log("this.step: " + this.step);
        linkOptions = {
            linkName: omniturePageName,
            linkEvents: trackEvent,
            linkVars: {
                products: ';' + prodId,
                pageName: this.site_prefix + ': W2P: ' + omniturePageName,
                prop56: model.trackGroup
            }
        };

        //this.doTrackQuantumGroupStep(state);
        if (state.hash == "/step1") {
            if (isFbt) {
                if ($('[data-controller="FbtIndexController"]').is(":visible") ||
                    $('[data-controller="FbtHybridProofController"]').is(":visible")) {
                    //Do nothing
                } else {
                    this.trackEvent(linkOptions);
                }
            } else {
                this.trackEvent(linkOptions);
            }
        }
        // For reorder with single companion product
        let recStatus = typeof this.query.recSkuId == 'undefined' || this.query.recSkuId == '' ? true : false
        if(this.query.allComps == true && state.hash == '#step/1' && this.initLoad && recStatus) {
            linkOptions.linkEvents += ',event57'
            linkOptions.linkVars.eVar12 = prodId;
            this.initLoad = false;
        }

        if (Settings.stateModel.current > 1 && window.matrixCodeValue) {
            for (var ff = 0; ff < window.matrixCodeValue.length; ff++) {
                if (window.matrixCodeValue[ff] == Settings.stateModel.states[Settings.stateModel.current - 2].controller.model.questions._items[0].value) {
                    var checkStyle = window.matrixTitleVal[ff];
                    if (checkStyle.indexOf("deluxehsecurity") > -1) {
                        matrixColor = ("Standard Designs:" + checkStyle.replace("deluxehsecurity", ""));
                    } else {
                        matrixColor = ("Premium Designs:" + checkStyle);
                    }
                }
            }
        }

        this.matrixColor = matrixColor;
        Settings.MATRIX_COLOR = this.matrixColor;

        //Last step, trying to add to cart.

        var optInfoArray = [];
        var additionalInfo1 = "";
        var additionalInfo2 = "";
        var additionalInfo3 = "";
        if ($('#ST_ST').val() !== undefined && $('#ST_ST').val() !== '') {
            additionalInfo1 = "Text on voucher1";
        }
        if ($('#SH_SH').val() !== undefined && $('#SH_SH').val() !== '') {
            additionalInfo2 = "Text on voucher2";
        }
        if ($('#LS_LS').val() !== undefined && $('#LS_LS').val() !== '') {
            additionalInfo3 = "Text above signature line";
        }
        optInfoArray.push(additionalInfo1, additionalInfo2, additionalInfo3);

        optInfoArray = $.grep(optInfoArray, function(n) {
            return (n);
        });
        this.optional_Info = 'OptInfo:' + optInfoArray;
        Settings.OPTINFO = this.optional_Info;
        fontList = ($('.inputBox_select-input.js-dropdown-trigger img').attr('alt'));
        if (Settings.EXTRACOLOR !== null) {
            Settings.EXTRACOLOR = Settings.EXTRACOLOR.replace('_', ' ').replace(/[0-9]/g, '').replace(/\w\S*/g, function(txt) {
                return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase(); });
            fontColor = Settings.EXTRACOLOR;
        }
        if (Settings.DEFAULTCOLOR !== null && Settings.EXTRACOLOR === null) {
            Settings.DEFAULTCOLOR = Settings.DEFAULTCOLOR.replace('_', ' ').replace(/[0-9]/g, '').replace(/\w\S*/g, function(txt) {
                return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase(); });
            fontColor = Settings.DEFAULTCOLOR;
        }
        this.FontsList = 'Font:' + fontList + ',color:' + fontColor;
        Settings.FONT_SELECTED = this.FontsList;

        /*
        if ($('#signatures').hasClass('isChecked')) {
                    additionOptions = Settings.productPldesc +":Additional:Add Signature";
            if ($('#folding').hasClass('isChecked')) {
                        additionOptions = additionOptions + ','+ Settings.productPldesc +":Additional:Add Folding Services";
            }
        } else if ($('#folding').hasClass('isChecked')) {
                    additionOptions = Settings.productPldesc +":Additional:Add Folding Services";
        }
        */
        
        if (Settings.stateModel.current > 1 || Settings.onPreviousClick) {
            linkOptions = {
                linkName: omniturePageName,
                linkEvents: Settings.onPreviousClick ? trackEvent + ',event38' : trackEvent,
                linkVars: {
                    products: ';' + prodId,
                    pageName: this.site_prefix + ': W2P: ' + omniturePageName,
                    prop56: model.trackGroup,
                }
            };
            var envType ="";
            var envImprint ="";
            if ($('input[name=envelope].isChecked').val() && window.envCodeVal) {
                for (var f = 0; f < window.envCodeVal.length; f++) { 
                    if (window.envCodeVal[f] == $('input[name=envelope].isChecked').val()) {
                        envType = window.envTitleVal[f];
                    }
                }
            }  
            if ($('#ENVELOPE_RETURN').hasClass('isChecked')) {
                envImprint = ":Envelope:Imprint:";
            } else {
                envImprint = ":Envelope:No Imprint:";
            }

            if (Settings.stateModel.states[Settings.stateModel.current - 1].description == 'Review Proof') {
                if (Settings.stateModel.states[Settings.stateModel.current - 2].description != 'Logo') {
                    linkOptions.linkVars.list1 = 'Logo:Not Available on Product';
                    if (Settings.stateModel.states[Settings.stateModel.current - 2].description == "Envelope Options") {
                        linkOptions.linkVars.list1 = Settings.productPldesc + envImprint +envType;
                    }
                } else {
                    linkOptions.linkVars.list1 = reviewLogo[Settings.stateModel.states[Settings.stateModel.current - 2].controller.model.questions._items[0].value];
                }
            } else if (Settings.stateModel.states[Settings.stateModel.current - 1].description == 'Additional Options') {
                if (Settings.stateModel.states[Settings.stateModel.current - 2].description != 'Inside of Card - Logo') {
                    linkOptions.linkVars.list1 = Settings.productPldesc + 'Logo:Not Available on Product';
                } else { 
                    linkOptions.linkVars.list1 = Settings.productPldesc +':'+ reviewLogoHC[Settings.stateModel.states[Settings.stateModel.current - 2].controller.model.questions._items[0].value];
                }
                //additionOptions = linkOptions.linkVars.list1;
            } else if ((!Settings.onPreviousClick && Settings.stateModel.states[Settings.stateModel.current - 2].description == "Optional Information") ||
                (Settings.stateModel.states[Settings.stateModel.current - 1].description == "Optional Information" && Settings.onPreviousClick)) {
                linkOptions.linkVars.list1 = this.optional_Info;
                if($('#LOGO_LOGOMX'). length){
                    linkOptions.linkEvents = trackEvent + ",event209";
                }
            } else if ((!Settings.onPreviousClick && Settings.stateModel.states[Settings.stateModel.current - 2].description == "Designs") ||
                (Settings.stateModel.states[Settings.stateModel.current - 1].description == "Designs" && Settings.onPreviousClick)) {
                linkOptions.linkVars.list1 = this.matrixColor;
            } else if ((!Settings.onPreviousClick && Settings.stateModel.states[Settings.stateModel.current - 2].description == "Font & Text Colors") ||
                (Settings.stateModel.states[Settings.stateModel.current - 1].description == "Font & Text Colors" && Settings.onPreviousClick)) {
                linkOptions.linkVars.list1 = this.FontsList;
                
            } else if ((!Settings.onPreviousClick && Settings.stateModel.states[Settings.stateModel.current - 2].description == "Logo") ||
                (Settings.stateModel.states[Settings.stateModel.current - 1].description == "Logo" && Settings.onPreviousClick)) {
                    let value = SessionStorage.getValue('logo');
                    if (value === 'LOGOMX') {
                        linkOptions.linkEvents = trackEvent + ",event210";
                        linkOptions.linkVars.eVar95='Logo Mix Option';
                    } else if($('#LOGO_NO_LOGO').hasClass('isChecked')) {
                        linkOptions.linkEvents = trackEvent + ",event78";
                    } else if(value === 'STANDARD') {
                        linkOptions.linkEvents = trackEvent + ",event77";
                    } else if(value === 'CUSTOM') {
                        linkOptions.linkEvents = trackEvent + ",event13";
                    }
            } else if ((!Settings.onPreviousClick && Settings.stateModel.states[Settings.stateModel.current - 2].description == "Inside of Card - Verse") ||
                (Settings.stateModel.states[Settings.stateModel.current - 1].description == "Inside of Card - Verse" && Settings.onPreviousClick)) {
                linkOptions.linkVars.list1 = Settings.productPldesc +":Verse:"+$('.radio.verse.isChecked').val();
            } else if ((!Settings.onPreviousClick && Settings.stateModel.states[Settings.stateModel.current - 2].description == "Inside of Card - Text Color") ||
                (Settings.stateModel.states[Settings.stateModel.current - 1].description == "Inside of Card - Text Color" && Settings.onPreviousClick)) {
                linkOptions.linkVars.list1 = Settings.productPldesc +":Text Color:"+$('.designRadio.isChecked').val();
            } else if ((!Settings.onPreviousClick && Settings.stateModel.states[Settings.stateModel.current - 2].description == "Additional Options")) {
                var additionOptions = "";
                if ($('#signatures').hasClass('isChecked')) {
                    additionOptions = Settings.productPldesc +":Additional:Add Signature";
                    if ($('#folding').hasClass('isChecked')) {
                        additionOptions = additionOptions + ','+ Settings.productPldesc +":Additional:Add Folding Services";
                    }
                } else if ($('#folding').hasClass('isChecked')) {
                    additionOptions = Settings.productPldesc +":Additional:Add Folding Services";
                }
                linkOptions.linkVars.list1 = additionOptions;
            } else if ((!Settings.onPreviousClick && Settings.stateModel.states[Settings.stateModel.current - 2].description == "Envelope Options") ||
                (Settings.stateModel.states[Settings.stateModel.current - 1].description == "Envelope Options" && Settings.onPreviousClick)) {
                linkOptions.linkVars.list1 = Settings.productPldesc + envImprint +envType;
            } else if (Settings.stateModel.current > 1 && Settings.stateModel.states[Settings.stateModel.current - 2].description == "Copies & Quantity") {
                linkOptions = {
                    linkName: omniturePageName,
                    linkEvents: trackEvent,
                    linkVars: {
                        products: ';' + prodId,
                        pageName: this.site_prefix + ': W2P: ' + omniturePageName,
                        prop56: model.trackGroup
                    }
                };
                if (x != $('input[name=productId].isChecked').val()) {
                    x = $('input[name=productId].isChecked').val();
                    Settings.RADIO_CHANGE = true;
                    linkOptions.linkEvents = trackEvent + ",event80";
                } else {
                    Settings.RADIO_CHANGE = false;
                }
                if (y != $('#QuantityCombobox').val()) {
                    y = $('#QuantityCombobox').val();
                    this.changeInQtyDropdown = true;
                    Settings.QUANTITY_CHANGE = true;
                    linkOptions.linkEvents = trackEvent + ",event37";
                } else {
                    Settings.QUANTITY_CHANGE = false;
                }
                if (Settings.QUANTITY_CHANGE && Settings.RADIO_CHANGE) {
                    linkOptions.linkEvents = trackEvent + ",event37" + ",event80";
                }
            } else {
                linkOptions.linkVars.list1 = "";
            }
        } else {
            linkOptions.linkVars.list1 = "";
        }
        if ($.inArray(prodId, Settings.CUSTOM_VALIDATION_PRODUCTS) > -1 || model.trackGroup === 'UI_10' ) {
            linkOptions.linkVars.eVar86='E';
        }
        if (isFbt) {
             if (($('[data-controller="FbtIndexController"]').is(":visible") ||
                $('[data-controller="FbtHybridProofController"]').is(":visible"))) {
            } else {
                this.trackEvent(linkOptions);
            }
        } else {
            this.trackEvent(linkOptions);
        }       
    };

    /**
     * Perform Omniture tracking event
     *
     * @method trackEvent
     * @param {Object} linkOptions
     * @chainable
     */
    proto.trackEvent = function(linkOptions) {
        // console.log('Omniture.trackEvent', linkOptions);

        this.targetViewHook(window.location.hash);

        if (Settings.OMNITURE_ACCT) {
            if (!linkOptions) {
                return;
            }

            // Get an instance of the Omniture tracking object
            // s_account is set in env.js
            if (window.s_gi) {
                var s = window.s_gi(this.s_account);

                // Set parameters
                var linkVarKey;
                var linkType = linkOptions.linkType || 'o';
                var linkName = linkOptions.linkName || '';
                var linkEvents = linkOptions.linkEvents;
                var linkVars = linkOptions.linkVars || {};
                var linkVarKeys = objectKeys(linkVars);
                var linkVarString = linkVarKeys.join(this.varSplitter);

                if (linkEvents !== undefined) {
                    if (linkVarString === '') {
                        linkVarString = 'events';
                    } else {
                        linkVarString = linkVarString + this.varSplitter + 'events';
                    }
                    s.linkTrackEvents = linkEvents;
                    s.events = linkEvents;
                }

                if (linkVarString !== '') {
                    s.linkTrackVars = linkVarString;
                }

                for (linkVarKey in linkVars) {
                    if (linkVars.hasOwnProperty(linkVarKey)) {
                        s[linkVarKey] = linkVars[linkVarKey];
                    }
                }
                if(s.events == 'event57') {
                    s.products = '';
                }
                // console.log("Satellite obj", s);
                // console.log('Omniture.Omniture.trackEvent: ' + linkType + ", " + linkName, linkOptions);
                s.t(true, linkType, linkName);

                //Allow Tealeaf to piggyback on Omniture tracking
                //var locationHash = location.hash;

                //Use 'MPC-' prefix on all FBT products.
                //if (isFbt) {
                //    locationHash = 'MPC-' + locationHash;
                //}
                //this.trackTealeaf(locationHash, linkOptions.linkVars.prop56, window.location.pathname);

                //Track the Review Proof step for each FBT product.  The first product in the
                // FBT flow does not show the Hybrid Proof so let's track the regular proof.
                //if (isFbt && linkOptions.linkName === 'Review Proof') {
                //    var tealeafProofStepID = 'Step 2 - SubStep ' + FbtModel.currentFbtProductIndex;
                //    this.trackTealeaf("MPC", tealeafProofStepID, window.location.pathname);
                //}
                //End Tealeaf
            }
            return this;
        }
    };


    //QuantumMetrics - Track the 'Group,Step' only once per product experience.
    /*
    proto.doTrackQuantumGroupStep = function(state) {
        if (window.QuantumMetricAPI && this.omniture_trackedGroupStep != state.productId) {
            var stepId = this.appUI_Group.concat(',').concat(this.step);
            console.log('Omniture.stepId', stepId);
            window.QuantumMetricAPI.sendEvent(345, 0, stepId);
            this.omniture_trackedGroupStep = state.productId;
        }
    }
    */

    //proto.onTrackTealeaf = function(event, input) {
    //    //console.log('Omniture.onTrackTealeaf', input);
    //    //proto.trackTealeaf(input.name, input.step, window.location.pathname);
    //};
    //proto.onTrackLogoUpload = function(event, input) {
    //    //console.log('Omniture.onTrackTealeaf', input);
    //    proto.trackLogo(input.status, input.file);
    //};
    //proto.trackLogo = function(status, file) {
    //    //console.log('Omniture.ontraklogoupload'+ 'LOGO_'+uiGroup+ status+ file);
    //     if (Settings.TEALEAF_ACTIVE && typeof TLT !== 'undefined') {
    //          TLT.logScreenviewLoad('LOGO_'+uiGroup, status, file);
    //     }
    //};

    /*
    proto.trackTealeaf = function(name, step, pathName) {
        //console.log('Omniture.trackTealeaf');
        if (Settings.TEALEAF_ACTIVE && typeof TLT !== 'undefined') {
            if (name && name !== null && name.length > 0 && name !== 'undefined' &&
                step && step !== null && step.length > 0 && step !== 'undefined') {

                //Prevent Step1 from logging since it loaded under the FbtIndex or HybriProof.
                //Manually trigger Step1
                if (isFbt && name === 'MPC-#step/1' &&
                    (($('.fbt-wrapper') && $('.fbt-wrapper').is(':visible')) ||
                    ($('#hybridProofWrapper') && $('#hybridProofWrapper').is(':visible')) ||
                    ($('.fbt-next-wrapper') && $('.fbt-next-wrapper').is(':visible')) )) {
                    //console.log('Omniture.Tealeaf.SKIP: ' + name);
                    return;
                }

                if (Settings.stateModel && Settings.stateModel.states.length != ProductModel.totalUISteps) {
                    TLT.logScreenviewLoad(name, step+"-"+Settings.stateModel.states.length, pathName);
                } else {
                    TLT.logScreenviewLoad(name, step, pathName);
                }
            } else {
                //param1 and/or param2 are null, empty or undefined
                //console.log("Omniture.Tealeaf.trackTealeaf.warning: " + name + ", " + step, ", " + pathName);
            }
        }
    };
    */

    //A/B testing requires the targetView function to be called when view changes
    proto.targetViewHook = function(viewName) {
        //console.log('Omniture.targetView()');
        if (typeof adobe != 'undefined' && adobe.target && typeof adobe.target.triggerView === 'function') {
            viewName = viewName || '#unknown';
            //console.log('AdobeTarget.targetViewHook.viewName', viewName);
            adobe.target.triggerView(viewName);
        }
        //else
        //{
        //    console.log('Omniture.targetView.Not_Found!');
        //}
    };

    //START MultiConfig FBT Tracking
    //Only track each step once
    proto.onFbtIndex = function(event, input) {
        //console.log("Omniture.onFbtIndex");
        //var key = 'Step 1 - SubStep 0';
        //if ($.inArray(key, trackedFbtKeys) == -1) {
        //    proto.trackTealeaf("MPC", key, window.location.pathname);
        //    trackedFbtKeys.push(key);
        //}
        proto.targetViewHook('#step/FBT-index');
    };

    proto.onFbtHybridProof = function(event, input) {
        //console.log("Omniture.onFbtHybridProof");
        //var key = 'Step 2 - SubStep ' + (FbtModel.currentFbtProductIndex+1);
        //if ($.inArray(key, trackedFbtKeys) == -1) {
        //    proto.trackTealeaf("MPC", key, window.location.pathname);
        //    trackedFbtKeys.push(key);
        //}
        proto.targetViewHook('#step/FBT-proof');
    };

    proto.onFbtSummary = function(event, input) {
        //console.log("Omniture.onFbtSummary");
        //var key = 'Step 3 - SubStep 0';
        //if ($.inArray(key, trackedFbtKeys) == -1) {
        //    proto.trackTealeaf("MPC", key, window.location.pathname);
        //    trackedFbtKeys.push(key);
        //}
        proto.targetViewHook('#step/FBT-summary');
    };

    proto.onFbtReviewCart = function(event, input) {
        //console.log("Omniture.onFbtReviewCart");
        //var key = 'Step 4 - SubStep 0';
        //if ($.inArray(key, trackedFbtKeys) == -1) {
        //    proto.trackTealeaf("MPC", key, window.location.pathname);
        //    trackedFbtKeys.push(key);
        //}
        proto.targetViewHook('#step/FBT-reviewCart');
    };
    //END MultiConfig FBT Tracking

    /**
     * Autoload click trackers using data attr
     *
     * @method autoTrack
     * @param {string} selector
     * @chainable
     */
    proto.autoTrack = function(selector) {
        //console.log("Omniture.autoTrack");
        var $trackedLinks = $(selector);
        $.each($trackedLinks, this.setAutoTracker.bind(this));

        return this;
    };

    /**
     * Set autotracking of elements
     *
     * @method setAutoTracker
     * @param {integer} index
     * @param {DOM Object} object
     * @chainable
     */
    proto.setAutoTracker = function(index, object) {
        var $object = $(object);
        var olinkName = $object.data(this.dataAttr.linkName);
        var olinkType = $object.data(this.dataAttr.linkType);
        var olinkVars = $object.data(this.dataAttr.linkVars); // {"prop1":"some value","eVar1":"some other value"}
        var olinkEvents = $object.data(this.dataAttr.linkEvents); // event1,event2

        // If its a string, which it will be coming from a link, e.g.
        // data-link-vars="{'prop1':'fizz', 'prop2':'cupcakes'}"
        if (typeof olinkVars === 'string') {
            // Replace single quotes with double so parseJSON works.
            // We don't want to use single quotes in our HTML markup.
            olinkVars = olinkVars.replace(/'/g, '"');
            olinkVars = $.parseJSON(olinkVars);
        }

        this.addClickTracker(
            object, {
                linkName: olinkName,
                linkType: olinkType,
                linkVars: olinkVars,
                linkEvents: olinkEvents
            }
        );

        return this;
    };

    /**
     * Attack a click tracker to an element
     *
     * @method addClickTracker
     * @param {string} selector jQuery selector
     * @param {object} linkOptions
     * @chainable
     */
    proto.addClickTracker = function(selector, linkOptions, mode) {
        var bindMode = mode || 'single';

        switch (bindMode) {
            case 'multiple':
                $(document).on(
                    'click' + this.eventNamespace,
                    selector,
                    this.onTrackEvent.bind(this, linkOptions)
                );
                break;
            default:
                $(selector).one(
                    'click' + this.eventNamespace,
                    this.onTrackEvent.bind(this, null, linkOptions)
                );
                break;
        }
        return this;
    };

    return Omniture;
});
