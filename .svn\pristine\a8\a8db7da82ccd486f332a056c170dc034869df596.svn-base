define(function (require) {
    'use strict';

    var AbstractModel = require('../Abstract');
    var UIStepsCollection = require('./collections/Steps');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.Ui.Type
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} UITypeValues
     */
    var UITypeModel = function (UITypeValues) {
        AbstractModel.call(this, UITypeValues);
    };

    var proto = inherits(UITypeModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} UITypeValues
     * @chainable
     */
    proto.init = function(UITypeValues) {

        /**
         * @property id
         * @default {null}
         * @type {string}
         */
        this.id = null;

        /**
         * @property steps
         * @default {null}
         * @type {App.Models.Ui.Collections.Steps}
         */
        this.steps = null;

        this.createChildren();

        // run the parent init method to parse determine the data type
        base.init.call(this, UITypeValues);

        return this;
    };

    /**
     * @method createChildren
     * @return {UITypeModel}
     * @chainable
     */
    proto.createChildren = function() {
        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param {object} json the original JSON data.
     */
    proto.fromJSON = function(json) {
        if (!json.id) {
            json = this.stripInvalidFields(json);
            if (!json.id && json.type !== undefined) {
                json = json.type;
            }
        }
        this.id = json.id;

        this.steps= new UIStepsCollection(json.step);
    };

    return UITypeModel;
});
