/* ---------------------------------------------------------------------
Original Author: <PERSON>
Contributors: N/A

Target Browsers: All
Media Type: Screen, Projection
Width: All Sizes
------------------------------------------------------------------------ */
/*csslint
    box-sizing: false,
    universal-selector: false
*/

/* ---------------------------------------------------------------------
 Blocks (flexible width, fixed gutters)
------------------------------------------------------------------------ */
.blocks {
    display: block;
    clear: both;
    margin-left: -18px;
    position: relative;
    font-size: 0;
}

.blocks > * {
    display: inline-block;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    margin-bottom: 5px;
    padding-left: 18px; /* positive equivalent of .blocks negative margin-left */
    font-size: 14px;
    vertical-align: top;
}

.blocks_2up > * { width: 50%; }
.blocks_3up > * { width: 33.33%; }
.blocks_4up > * { width: 25%; }
.blocks_5up > * { width: 20%; }
.blocks_6up > * { width: 16.666666%; }

.blocks_29p > * { width: 29%; }

.blocks-title {
    text-align: center;
}

span#ExtraColorHelp:after,
span#routingNumberHelp:after,
span#accountNumberHelp:after,
span#routingNameHelp:after,
span#accountNameHelp:after,
span#otaccountNameHelp:after,
span#signatureTextHelp:after,
span#sideMarginHelp:after,
span#voucherHelp:after,
span#NumberingHelp:after,
span#CopiesHelp:after,
span#fbtHelp:after,
span#FbtCopiesHelp:after,
span#primaryColorHelp:after,
span#SignatureHelp:after,
span#bankNameHelp:after,
span#paymentTermsHelp:after {
    font-family: fontAwesome;
    content: "\f059";
    color: #0070c9;
    font-size: 16px;
    background-color: white;
}

.js-logos ul.mix-blocks {
    padding-left: 0;
    padding:5px;
}