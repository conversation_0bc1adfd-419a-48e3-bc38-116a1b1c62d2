/*!
 * deluxe-configurator v0.0.1 (dev)
 * Web-to-Print Configurator for Deluxe.
 * Build Date: 2018-05-01
 */

body {
  background-color: #fff;
}
.radio + label {
  color: #259cda;
  font-size: 15px;
}
.radio + label:before {
  background-position: 0 0;
}
.radio.isChecked + label:before {
  background-position: 0 -24px;
}
.button,
.button:hover {
  background: #f90 url(../media/images/button-bg.png) repeat-x 0 -64px;
  background: -webkit-gradient(linear, 0 0, 0 100%, from(#f90), to(#e77401));
  background: -webkit-linear-gradient(top, #f90, #e77401);
  background: -moz-linear-gradient(top, #f90, #e77401);
  background: -ms-linear-gradient(top, #f90, #e77401);
  background: -o-linear-gradient(top, #f90, #e77401);
  background: linear-gradient(to bottom, #ffd65e, #febf04);
  font-family: 'Source Sans Pro', sans-serif;
  text-transform: uppercase;
  height: 31px;
  font-size: 12px;
  border-radius: 3px;
  padding: 7px;
}
.button:focus {
  background: #e77401 url(../media/images/button-bg.png) repeat-x 0 -96px;
  background: -webkit-gradient(linear, 0 0, 0 100%, from(#f90), to(#e77401));
  background: -webkit-linear-gradient(top, #e77401, #f90);
  background: -moz-linear-gradient(top, #e77401, #f90);
  background: -ms-linear-gradient(top, #e77401, #f90);
  background: -o-linear-gradient(top, #e77401, #f90);
  background: linear-gradient(to bottom, #febf04, #ffd65e);
  text-decoration: none;
}
@media (hover: hover) {
  .button:hover {
    background: #e77401 url(../media/images/button-bg.png) repeat-x 0 -96px;
    background: -webkit-gradient(linear, 0 0, 0 100%, from(#f90), to(#e77401));
    background: -webkit-linear-gradient(top, #e77401, #f90);
    background: -moz-linear-gradient(top, #e77401, #f90);
    background: -ms-linear-gradient(top, #e77401, #f90);
    background: -o-linear-gradient(top, #e77401, #f90);
    background: linear-gradient(to bottom, #febf04, #ffd65e);
    text-decoration: none;
  }
}
@media (hover: none) {
  .button:active {
    background: #e77401 url(../media/images/button-bg.png) repeat-x 0 -96px;
    background: -webkit-gradient(linear, 0 0, 0 100%, from(#f90), to(#e77401));
    background: -webkit-linear-gradient(top, #e77401, #f90);
    background: -moz-linear-gradient(top, #e77401, #f90);
    background: -ms-linear-gradient(top, #e77401, #f90);
    background: -o-linear-gradient(top, #e77401, #f90);
    background: linear-gradient(to bottom, #febf04, #ffd65e);
    text-decoration: none;
  }
}
.button_neutral {
  background: #f0ede7 url(../media/images/button-bg.png) repeat-x 0 -128px;
  background: -webkit-gradient(linear, 0 0, 0 100%, from(#d6d1c1), to(#f0ede7));
  background: -webkit-linear-gradient(top, #f0ede7, #d6d1c1);
  background: -moz-linear-gradient(top, #f0ede7, #d6d1c1);
  background: -ms-linear-gradient(top, #f0ede7, #d6d1c1);
  background: -o-linear-gradient(top, #f0ede7, #d6d1c1);
  background: linear-gradient(to bottom, #f0ede7, #d6d1c1);
  color: #777;
}
.button_neutral:focus,
.button_neutral:hover {
  background: #d6d1c1 url(../media/images/button-bg.png) repeat-x 0 -160px;
  background: -webkit-gradient(linear, 0 0, 0 100%, from(#f0ede7), to(#d6d1c1));
  background: -webkit-linear-gradient(top, #d6d1c1, #f0ede7);
  background: -moz-linear-gradient(top, #d6d1c1, #f0ede7);
  background: -ms-linear-gradient(top, #d6d1c1, #f0ede7);
  background: -o-linear-gradient(top, #d6d1c1, #f0ede7);
  background: linear-gradient(to bottom, #d6d1c1, #f0ede7);
}
.button_grey {
  background: linear-gradient(to bottom, #a7a7a5, #898987) !important;
}
.button_file_browse {
  width: 150px;
  height: 15px;
}
.padding_4px {
  padding-top: 4px;
}
/*.vList_loose ul{margin-top:19px}*/
h2 {
  line-height: 1.3846153846153846em;
}

.designRadio.isChecked + label > .designSwatchImage {
  border: 2px solid #febf04;
}
.designBox_active::before,
.designBox_active:hover::before {
  border: 4px solid #febf04 !important;
}
.designRadio.isChecked + label > .designBox:before {
  border: 4px solid #febf04;
}
.error {
  margin-bottom: 4px;
  font-size: 16px;
  line-height: 1.075;
  color: #b30000;
}
.error-head {
  color: #b30000;
  font-family: arial;
  font-size: 16px;
  line-height: 1.075;
  margin-bottom: 10px;
}
.hdg {
  color: #333;
  margin-bottom: 8px;
  font-weight: 700;
}
.hdg_h2 {
  font-size: 17px;
}
.hdg_sub_h2 {
  font-size: 16px;
  margin-bottom: 8px;
}
.mix-hdg_caps_temp {
  text-transform: uppercase;
}
.hdg_h4 {
  margin-bottom: 4px !important;
}
.mix-hdg_red {
  color: #b30000;
}
.mix-hdg_blue {
  color: #259cda !important;
}
.font-size {
  font-size: 20px !important;
}
.mix-hdg_caps_temp h1 {
  margin-bottom: 5px !important;
}
.link {
  text-decoration: none;
  color: #259cda;
  cursor: pointer;
}
.link_button {
  display: inline-block;
  padding: 7px 16px;
  border: none;
  background: 0 0;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  vertical-align: middle;
}
.siteTitle {
  padding-top: 20px;
}
.site-hd-right {
  padding: 28px 15px 0px 0px;
}
.cfgSiteLogo {
  float: left;
  margin-right: 54px;
  padding-top: 13px;
}
.error-subtext {
  float: left;
}
.deluxe-file {
  font-size: 10px !important;
}
.step {
  line-height: 0.6;
  font-weight: 700;
  text-transform: uppercase;
  margin-bottom: 5px !important;
  color: #febf04;
}
.popover {
  width: 432px;
  background-color: #aedfe8;
  border: 4px solid #fff;
}
.popover .arrow,
.popover .arrow:after {
  background-color: #aedfe8;
}
#CVHelp,
#CopiesHelp,
/* #NumberingHelp  */
{
  padding-left: 5px;
  cursor: pointer;
  margin-bottom: -3px;
}

.progressbar {
  background: #e9e9e9 none repeat scroll 0 0;
  border-radius: 11px;
  height: 13px;
  padding-left: 3px;
  padding-right: 3px;
  padding-top: 3px;
  width: 100%;
}
.progress {
  background: #c34924;
  border-radius: 11px;
  height: 10px;
}

.txtLarge {
  font-size: 16px;
  cursor: auto !important;
}
.blocks {
  margin-bottom: 10px;
}
.txt_style {
  font-weight: 700;
}
.detailBox_active,
.detailBox_active .detailBox-header,
.zoombox_connector {
  background-color: #febf04;
  border-color: #febf04;
}
.zoombox {
  background-color: rgba(255, 229, 153, 0.5);
}
.inputBox {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 16px;
}
.commentCopy {
  background-color: #ffc;
  padding: 10px 12px;
  margin-bottom: 10px;
  font-size: 14px;
}
.commentLabel {
  color: #333;
}
.commentTextArea {
  width: 98%;
  height: 60px;
  resize: none;
  -moz-user-select: text;
  -webkit-user-select: text;
  -ms-user-select: text;
}
