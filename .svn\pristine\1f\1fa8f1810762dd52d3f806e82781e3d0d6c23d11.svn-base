define(function (require) {
    'use strict';

    var AbstractProvider = require('./Abstract');
    var Settings = require ('../constants/Settings');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var mixIn = require('mout/object/mixIn');
    var Query = require('models/Query');

    /**
     * @class App.Providers.RenderImage
     * @extends App.Providers.Abstract
     *
     * @constructor
     */
    var RenderImageProvider = function (baseUrl) {
        bindAll(this, '_onResponseReceived');

        AbstractProvider.call(this, baseUrl);
    };

    var proto = inherits(RenderImageProvider, AbstractProvider);
    var productCode = '';


     /**
     * Get Default Image URL
     *
     * @method getdefaultImageUrl
     * @param {string} product
     * @param {object} params
     * @return {string}
     */
    proto.getdefaultImageUrl = function(productCode) {

        var url = 'https://raptor.scene7.com/is/image/DeluxeForms/' + productCode +
                '?wid=' + Settings.PREVIEW_WIDTH +
                '&fmt=gif&qlt=90,1&op_sharpen=1&quantize=adaptive,diffuse,255,&resMode=sharp2&op_usm=0,0,0,0&iccEmbed=0';
        return url;
    };


    /**
     * Get Image URL
     *
     * @method getImageUrl
     * @param {string} product
     * @param {object} params
     * @return {string}
     */
    proto.getImageUrl = function(product, params) {

        if (!product) {
            throw new Error();
        }

        //Stock products should show the catalog image, not an FXG template.
        var $productXML = $($.parseXML(product));
        var productType = $productXML.find("productType").text();
        productCode = $productXML.find("productCode").text();
        if (productType == 'N') {
            
            var url = 'https://raptor.scene7.com/is/image/DeluxeForms/' + productCode +
                '?wid=' + Settings.PREVIEW_WIDTH +
                '&fmt=gif&qlt=90,1&op_sharpen=1&quantize=adaptive,diffuse,255,&resMode=sharp2&op_usm=0,0,0,0&iccEmbed=0';
            return url;
        }

        /**
         * @property query
         * @type {App.Models.Query}
         */
        this.query = Query.getInstance();

        var defaultParams = {
            wid: Settings.PREVIEW_WIDTH,
            hei: Settings.PREVIEW_HEIGHT,
            imageRes: Settings.PREVIEW_RESOLUTION,
            fmt: Settings.PREVIEW_FORMAT,
            rawImg: Settings.PREVIEW_RAW_IMG,
            sb: Settings.V2_SALES_BRAND
        };

        // For testing with local cache
        if (this.query && this.query.skuId) {
            defaultParams.skuId = this.query.skuId.toString();
        }

        params = mixIn({}, defaultParams, params);

        var endPoint =
            Settings.SVC_PREVIEW + 
            this.serializeParams(params);

        this.promise = this
            .post(endPoint, product, 'text', {
                processData: false,
                contentType: 'multipart/form-data'
            })
            .fail(this._onErrorSetCatalog);

        return this.promise;
    };



    /**
     * Handles the response from the getImageUrl call
     *
     * @method _onResponseReceived
     * @param {HTMLDocument} data
     * @private
     */
    proto._onResponseReceived = function(data) {
        if (!data) {
            throw new Error(data);
        }

        return data;
    };

     proto._onErrorSetCatalog = function(data) {
        var url = 'https://raptor.scene7.com/is/image/DeluxeForms/' + productCode +
                '?wid=' + Settings.PREVIEW_WIDTH +
                '&fmt=gif&qlt=90,1&op_sharpen=1&quantize=adaptive,diffuse,255,&resMode=sharp2&op_usm=0,0,0,0&iccEmbed=0';
                // console.log('_onResponseReceived url '+ url);
        return url;
    };

    return new RenderImageProvider('');
});
