define(function(require) {
    'use strict';

    var Content = require('i18n!../constants/nls/en-us/Content');
    var Controller = require('./Controller');
    var inherits = require('mout/lang/inheritPrototype');
    var FbtModel = require('../models/Fbt');

    /**
     * @class App.Controllers.Step
     * @extends App.Controllers.Controller
     *
     * @constructor
     * @param {Object} config
     * @param {jQuery} config.view
     * @param {Models.Ui.Step} config.model
     */
    function StepController(config) {
        Controller.call(this, config);
    }

    var proto = inherits(<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller);

    /**
     * @property registry
     * @type {Object.<String,App.Controllers.Controller>}
     */
    proto.registry = {
        QuestionController: require('./Question'),
        TabController: require('./Tab')
    };

    /**
     * @method init
     * @chainable
     */
    proto.init = function() {
        var model = this.model;

        this.index = {
            QuestionController: model.questions,
            TabController: model.tabs
        };

        return this;
    };

    // -- Accessors ------------------------------------------------------------

    /**
     * Gets state object for step, or for all tabs.
     *
     * @method getStates
     * @return {Array|Object}
     */
    proto.getStates = function() {
        var model = this.model;
        var tabs = model && model.tabs;

        // Defer to tabs
        if (tabs && tabs.length()) {
            return this.started
                .reduce(this._getState, []);
        }

        var questions = model && model.questions;

        // Whether we should skip this step
        if (!questions || !questions.hasImportant()) {
            return;
        }
         //console.log('state', model.next);
        
            if (typeof FbtModel.fbtProducts != 'undefined') {
                if(Object.keys(FbtModel.fbtProducts).length > 0){
                    if (model.next == 'Add To Cart') {
                        model.next =  'FBT Add To Cart';
                    }
                    if((model.next == 'Add To Cart' || model.next == 'FBT Add To Cart') && FbtModel.currentFbtProductIndex === Object.keys(FbtModel.fbtProducts).length-1){
                        model.next = 'FBT Summary';
                    }               }
            }          
        
        return {
            controller: this,
            description: model.description,
            progressTitle: model.progressTitle,
            tlId: model.tlId,
            next: Content.get(model.next),
            prev: Content.get(model.prev)
        };
    };

    /**
     * @method _getStates
     * @return {Array}
     */
    proto._getState = function(states, component) {
        if (typeof component.getState !== 'function') {
            return states;
        }

        return states.concat(component.getState());
    };

    /**
     * @method isValid
     * @return {Boolean}
     */
    proto.isValid = function() {
        return this.started.every(this._isItemValid);
    };

    /**
     * Check if single item is valid
     * @method _isItemValid
     * @return {Boolean}
     */
    proto._isItemValid = function(item) {
        return item.isValid() === true;
    };

    // -- Methods --------------------------------------------------------------

    /**
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = require('hbs!templates/step');

    /**
     * @method render
     * @chainable
     */
    proto.render = function() {
        var $view = this.$view;
        var model = this.model;

        $view.html(this.template({
            id: model.id,
            stepId: model.stepId,
            description: model.description,
            questions: model.questions.getDisplayable(),
            tabs: model.tabs._items,
            prev: model.prev,
            next: model.next
        }));

        if (!model.tabs.length()) {
            $view.hide();
        }

        return this.start();
    };

    /**
     * @method updateErrors
     * @return {Promise}
     */
    proto.updateErrors = function() {
        var controller;
        var started = this.started;
        var length = started.length;
        var i = 0;

        for (; i < length; i++) {
            controller = started[i];

            if (typeof controller.updateErrors === 'function') {
                controller.updateErrors();
            }
        }

        return this;
    };

    /**
     * @method show
     * @chainable
     */
    proto.show = function() {
        var cart = this._getAddToCart(this.started);

        if (cart) {
            cart.$view.show();
        }

        this.$view.show();

        return this;
    };

    /**
     * @method hide
     * @chainable
     */
    proto.hide = function() {
        var cart = this._getAddToCart(this.started);

        if (cart) {
            cart.$view.hide();
        }

        this.$view.hide();

        return this;
    };

    /**
     * @method _getAddtoCart
     * @return {Array}
     */
    proto._getAddToCart = function(started) {
        if (!started) {
            return null;
        }
        var carts = started.filter(function(question) {
            return question.model.id === 'addToCart';
        });
        return carts.length ? carts[0] : null;
    };

    return StepController;
});
