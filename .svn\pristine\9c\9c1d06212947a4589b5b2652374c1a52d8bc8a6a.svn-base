{{#if hasBlocks}}
    <ul class="vList vList_tight">
    	{{#each blocks}}
			{{#is id 'SH'}}<li data-controller="TextBlockQuestionController" data-id="{{id}}" data-mobile-src="/webasset/w2p_mobile/assets/media/images/{{id}}.png" ></li>
			{{else}}
			{{#is id 'ST'}}<li data-controller="TextBlockQuestionController" data-id="{{id}}" data-mobile-src="/webasset/w2p_mobile/assets/media/images/{{id}}.png" ></li>
            {{else}}
            {{#is id 'LS'}}<li data-controller="TextBlockQuestionController" data-id="{{id}}" data-mobile-src="/webasset/w2p_mobile/assets/media/images/{{id}}.png" ></li>
            {{else}}
            {{#is id 'VS'}}<li data-controller="TextBlockQuestionController" data-id="{{id}}" class="vsBlock"></li>
            {{else}}
            <li data-controller="TextBlockQuestionController" data-id="{{id}}"></li>{{/is}}{{/is}}{{/is}}{{/is}}
        {{/each}}

    </ul>
{{else}}
    <label class="txtLarge">
        {{> question/required}}
        {{desc}}
        {{#is id 'routingNumber'}}<span id="routingNumberHelp" class="js-button-route-action" 
        data-template='<div id="routing-popup" class="custpop popover" role="tooltip"><div class="arrow"></div><div class="popover-body"></div></div>'
	    data-container="body" data-content="" data-original-title="" title="" data-placement="bottom"  alt="routingNumberHelp"></span>{{/is}}
         {{#is id 'accountNumber'}}<span id="accountNumberHelp" class="js-button-account-action" 
        data-template='<div id="account-popup" class="custpop popover" role="tooltip"><div class="arrow"></div><div class="popover-body"></div></div>'
	    data-container="body" data-content="" data-original-title="" title="" data-placement="bottom"  alt="accountNumberHelp"></span>{{/is}}
        <input class="inputBox {{piiClass}}" type="{{inputType}}"
            name="{{id}}"
            id="{{id}}"
            maxlength="{{maxLength}}" value="{{default}}" {{#if hotjarMaskInput}} data-hj-masked{{/if}}/>
            {{#is id 'accountNumber' }}
                <div class="inputBox-note">
                    <span class="inputBox-note-Bold">Important</span>: Please include preceding 0’s in your account number if applicable
                </div>
            {{/is}}
            {{#is id 'routingNumber'}}
                <div class="inputBox-note">
                    <span class="inputBox-note-Bold">Important</span>: Please include preceding 0’s in your routing number if applicable.
                </div>
            {{/is}}
        <span class="error js-error"></span>
    </label>
{{/if}}
