![mout](http://moutjs.com/logo.png "Modular JavaScript Utilties")

http://moutjs.com/

[![Build Status](https://travis-ci.org/mout/mout.png?branch=master)](https://travis-ci.org/mout/mout)

All code is library agnostic and consist mostly of helper methods that aren't
directly related with the DOM, the purpose of this library isn't to replace
Dojo, jQuery, YUI, Mootools, etc, but to provide modular solutions for common
problems that aren't solved by most of them. Consider it as a crossbrowser
JavaScript standard library.



## Main goals ##

 - increase code reuse;
 - be clear (code should be clean/readable);
 - be easy to debug;
 - be easy to maintain;
 - follow best practices;
 - follow standards when possible;
 - **don't convert JavaScript into another language!**
 - be compatible with other frameworks;
 - be modular;
 - have unit tests for all modules;
 - work on multiple environments (IE7+, modern browsers, node.js);



## What shouldn't be here ##

 - UI components;
 - CSS selector engine;
 - Event system - pub/sub;
 - Template engine;
 - Anything that isn't generic enough to be on a standard library;
 - Anything that could be a separate library and/or isn't a modular utility...



## API Documentation ##

Online documentation can be found at http://moutjs.com/ or inside the
`doc` folder.



## FAQ / Wiki / IRC ##

For more info about project structure, design decisions, tips, how to
contribute, build system, etc, please check the [project
wiki](https://github.com/mout/mout/wiki).

We also have an IRC channel [#moutjs on
irc.freenode.net](http://webchat.freenode.net/?channels=moutjs)



## License ##

Released under the [MIT License](http://www.opensource.org/licenses/mit-license.php).

