<!DOCTYPE html>
<html class="no-js" lang="en-us">
<head>
    <!-- META DATA -->
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <!--[if IE]><meta http-equiv="cleartype" content="on" /><![endif]-->

    <title>{{#block "title"}}DELUXE{{/block}}</title>

    <!-- STYLESHEETS -->
    <!--[if lte IE 9]>
        <link rel="stylesheet" media="screen, projection" href="{{env.URL_BASE}}assets/styles/ie9.css" />
    <![endif]-->
    <!--[if lte IE 8]>
        <link rel="stylesheet" media="screen, projection" href="{{env.URL_BASE}}assets/styles/ie8.css" />
        <script src="{{env.URL_BASE}}assets/vendor/es5-shim/es5-shim.js"></script>
        <script src="{{env.URL_BASE}}assets/vendor/es5-shim/es5-sham.js"></script>
    <![endif]-->

    <!-- Tealeaf -->
    <!--<script type="text/javascript" src="/webasset/sd/js/tealeaf.js"></script>-->

    <script language="JavaScript" type="text/javascript">
        var s_account = 'deluxeshopdev,deluxeglobaldev';
        var Overrides;
        var sfCSS = 'style.min.css';
        var host_name = window.location.hostname;
        if (typeof envId !== 'undefined') {
            host_name = envId;
        }
        if (typeof window !== 'undefined') {
            if (host_name == 'localhost' || host_name == 'test.localhost.com') {
                Overrides = 'LOCAL_OVERRIDES';
                baseAppUrl = "Release_120.0/";
            } else if (host_name == 'sd.localhost.com') {
                Overrides = 'SD_LOCAL_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'deluxe.css';
            } else if (host_name == 'sf-dev.deluxe.com') {
                Overrides = 'SD_DEV_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'deluxe.css';
            } else if (host_name == 'sf-stage.deluxe.com') {
                 Overrides = 'SD_STAGE_OVERRIDES';
                 baseAppUrl = "Release_120.0/";
                 sfCSS = 'deluxe.css';
            } else if (host_name == 'www.deluxe.com' || host_name == 'origin-sf-prod.deluxe.com' || host_name == 'www-dr.deluxe.com') {
                Overrides = 'SD_PROD_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'deluxe.css';
            } else if (host_name == 'dfs.localhost.com') {
                Overrides = 'B2B_LOCAL_OVERRIDES'; 
                baseAppUrl = "Release_120.0/";
                sfCSS = 'dfs.css';
            } else if (host_name == 'b2b-dev.dfsonline.com') {
                Overrides = 'B2B_DEV_OVERRIDES';  
                baseAppUrl = "Release_120.0/";
                sfCSS = 'dfs.css';
            } else if (host_name == 'b2b-stage.dfsonline.com') {
                Overrides = 'B2B_STAGE_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'dfs.css';
            } else if (host_name == 'www.dfsonline.com' || host_name == 'origin-www.dfsonline.com' || host_name == 'www-dr.dfsonline.com') {
                Overrides = 'B2B_PROD_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'dfs.css';
            } else if (host_name == 'staplescustomprinting.dev.btobsource.com') {
                Overrides = 'ST_DEV_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'staples.css';
            } else if (host_name == 'staplescustomprinting.qa.btobsource.com') {
                Overrides = 'ST_QA_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'staples.css';
            } else if (host_name == 'staplescustomprinting.btobsource.com') {
                Overrides = 'ST_PROD_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'staples.css';
            } else if (host_name == 'staplescustomprintingretail.dev.btobsource.com') {
                Overrides = 'ST_R_DEV_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'staples.css';
            } else if (host_name == 'staplescustomprintingretail.qa.btobsource.com') {
                Overrides = 'ST_R_QA_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'staples.css';
            } else if (host_name == 'staplescustomprintingretail.btobsource.com') {
                Overrides = 'ST_R_PROD_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'staples.css';
            } else if (host_name == 'msbc.dev.btobsource.com') {
                Overrides = 'MSBC_DEV_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'msbc.css';
            } else if (host_name == 'msbc.qa.btobsource.com') {
                Overrides = 'MSBC_QA_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'msbc.css';
            } else if (host_name == 'www.mysoftwarechecks.com') {
                Overrides = 'MSBC_PROD_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'msbc.css';
            } else if (host_name == 'www.microsoftbusinesschecks.com') {
                Overrides = 'MSBC_PROD_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'msbc.css';
            } else if (host_name == 'dev.printez.com') {
                Overrides = 'EZP_DEV_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'pez.min.css';
            } else if (host_name == 'staging.printez.com') {
                Overrides = 'EZP_QA_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'pez.min.css';
            } else if (host_name == 'www.printez.com') {
                Overrides = 'EZP_PROD_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'pez.min.css';
            } else if (host_name == 'dev.smartresolution.com' ) {
                Overrides = 'SR_DEV_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'dfs.css';
            } else if (host_name == 'staging.smartresolution.com' ) {
                Overrides = 'SR_QA_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'dfs.css';
            } else if (host_name == 'www.smartresolution.com' ) {
                Overrides = 'SR_PROD_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'dfs.css';
            } else if (host_name == 'staging-www.holidaycardwebsite.com') {
                Overrides = 'BCHCW_QA_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'dfs.css';
            } else if (host_name == 'staging-www.4printing.com') {
                Overrides = 'BC4PRINTING_QA_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'bc4.css';
            } else if (host_name.indexOf('.4printing.com') != -1) {
                Overrides = 'BC4PRINTING_PROD_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'bc4.css';
            } else if (host_name == 'tenenz.localhost.com') {
                Overrides = 'TENENZ_LOCAL_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'deluxe.css';
            } else if (host_name == 'tenenz.test') {
                Overrides = 'TENENZ_TEST_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'deluxe.css';
            } else if (host_name == 'www.tenenz.com') {
                Overrides = 'TENENZ_PROD_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'deluxe.css';
            } else if (host_name == 'caas.dev.btobsource.com') {
                Overrides = 'CAAS_DEV_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'deluxe.css';
            } else if (host_name == 'caas.qa.btobsource.com') {
                Overrides = 'CAAS_QA_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'deluxe.css';
            } else if (host_name == 'caas.btobsource.com') {
                Overrides = 'CAAS_PROD_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'deluxe.css';
            } else if (host_name == 'pol-sbx') {
                Overrides = 'POL_DEV_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'pol.css';
            } else if (host_name == 'pol-prd') {
                Overrides = 'POL_PRD_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'pol.css';
            } else if (host_name == 'sf-pol-sbx') {
                Overrides = 'SF_POL_SBX_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'pol.css';
            } else if (host_name == 'sf-pol-prd') {
                Overrides = 'SF_POL_PROD_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'pol.css';
            } else if (host_name == 'otis-sbx') {
                Overrides = 'OTIS_SBX_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'pol.css';
            } else if (host_name == 'otis-prd') {
                Overrides = 'OTIS_PROD_OVERRIDES';
                baseAppUrl = "Release_120.0/";
                sfCSS = 'pol.css';
            }
            document.write('<link rel="shortcut icon" href="/webasset/w2p_mobile/'+baseAppUrl+'assets/media/images/favicon.ico"/>');
            document.write('<link rel="apple-touch-icon" href="/webasset/w2p_mobile/'+baseAppUrl+'assets/media/images/apple-touch-icon.png"/>');
            document.write('<link rel="stylesheet" media="screen, projection" href="/webasset/w2p_mobile/'+baseAppUrl+'assets/styles/screen.css"/>');
            document.write('<link rel="stylesheet" media="screen, projection" href="/webasset/w2p_mobile/'+baseAppUrl+'assets/styles/'+sfCSS+'"/>');
            document.write('<script type="text/javascript" src="/webasset/w2p_mobile/'+baseAppUrl+'assets/scripts/lib/omniture/s_code.js"/></'+'script>');
        }
    </script>
</head>
<body id="{{#block 'body-tag'}}{{/block}}">
    <!-- CONTENT -->
    <div class="site">
        {{#block "body"}}
        {{/block}}

        <div class="site-ft" role="contentinfo">
            {{#block "footer"}}
                <div class="isHidden">
                    <p><small>&copy; {{year}}</small></p>
                </div>
            {{/block}}
        </div> {{! /site-ft}}
    </div> {{! /site}}

    {{#block "scripts"}}
        <script language="JavaScript" type="text/javascript">
            var s_account = "";
            document.write('<script type="text/javascript" src="/webasset/w2p_mobile/'+baseAppUrl+'assets/vendor/requirejs/require.js"/></'+'script>');
            document.write('<script type="text/javascript" src="/webasset/w2p_mobile/'+baseAppUrl+'assets/scripts/config.js"/></'+'script>');
        </script>
    {{/block}}
</body>
</html>
