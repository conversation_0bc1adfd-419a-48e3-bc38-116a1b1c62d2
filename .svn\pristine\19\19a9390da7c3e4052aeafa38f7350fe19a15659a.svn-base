define(function (require) {
    'use strict';

    var $ = require('jquery');
    var AbstractModel = require('./Abstract');
    var CartProductModel = require('./cart/Product');
    var find = require('mout/array/find');
    var inherits = require('mout/lang/inheritPrototype');
    var Surcharge = require('util/Surcharge');

    /**
     * @class App.Models.ProductInfo
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} ProductInfoValues
     */
    var FbtModel = function (ProductInfoValues) {
        AbstractModel.call(this, ProductInfoValues);
    };

    /**
     * ProductInfoModel extends AbstractModel
     * @type {AbstractModel}
     */
    var proto = inherits(FbtModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} ProductInfoValues
     * @chainable
     */
    proto.init = function(ProductInfoValues) {
        /**
         * @property fbtProducts
         * @default {null}
         * @type {string}
         */
        this.fbtProducts = null; 
        this.currentFbtProductIndex = null;    
        this.resetStep = null; 
        this.pdpPdt = null;
        this.kitProducts = null; 
        this.cartItems = [];
        this.kitId = null;
        this.kitSkuId = null;
        this.kitDesc = null;
        this.kitBasePrice = null;
        this.kitPriceXml = [];
        this.kitSurcharges = [];

        return this;
    };

    
    return FbtModel;
});
