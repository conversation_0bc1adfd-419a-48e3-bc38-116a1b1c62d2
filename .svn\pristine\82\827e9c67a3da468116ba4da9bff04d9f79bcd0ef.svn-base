 window.NREUM||(NREUM={}),__nr_require=function(t,e,n){function r(n){if(!e[n]){var o=e[n]={exports:{}};t[n][0].call(o.exports,function(e){var o=t[n][1][e];return r(o||e)},o,o.exports)}return e[n].exports}if("function"==typeof __nr_require)return __nr_require;for(var o=0;o<n.length;o++)r(n[o]);return r}({QJf3ax:[function(t,e){function n(){}function r(t){function e(t){return t&&t instanceof n?t:t?a(t,i,o):o()}function s(n,r,o){t&&t(n,r,o);for(var i=e(o),a=f(n),s=a.length,c=0;s>c;c++)a[c].apply(i,r);return i}function c(t,e){d[t]=f(t).concat(e)}function f(t){return d[t]||[]}function u(){return r(s)}var d={};return{on:c,emit:s,create:u,listeners:f,context:e,_events:d}}function o(){return new n}var i="nr@context",a=t("gos");e.exports=r()},{gos:"7eSDFh"}],ee:[function(t,e){e.exports=t("QJf3ax")},{}],3:[function(t){function e(t){try{i.console&&console.log(t)}catch(e){}}var n,r=t("ee"),o=t(1),i={};try{n=localStorage.getItem("__nr_flags").split(","),console&&"function"==typeof console.log&&(i.console=!0,-1!==n.indexOf("dev")&&(i.dev=!0),-1!==n.indexOf("nr_dev")&&(i.nrDev=!0))}catch(a){}i.nrDev&&r.on("internal-error",function(t){e(t.stack)}),i.dev&&r.on("fn-err",function(t,n,r){e(r.stack)}),i.dev&&(e("NR AGENT IN DEVELOPMENT MODE"),e("flags: "+o(i,function(t){return t}).join(", ")))},{1:23,ee:"QJf3ax"}],4:[function(t){function e(t,e,n,i,a){try{f?f-=1:r("err",[a||new UncaughtException(t,e,n)])}catch(c){try{r("ierr",[c,(new Date).getTime(),!0])}catch(u){}}return"function"==typeof s?s.apply(this,o(arguments)):!1}function UncaughtException(t,e,n){this.message=t||"Uncaught error with no additional information",this.sourceURL=e,this.line=n}function n(t){r("err",[t,(new Date).getTime()])}var r=t("handle"),o=t(6),i=t("ee"),a=t("loader"),s=window.onerror,c=!1,f=0;a.features.err=!0,t(5),window.onerror=e;try{throw new Error}catch(u){"stack"in u&&(t(1),t(2),"addEventListener"in window&&t(3),a.xhrWrappable&&t(4),c=!0)}i.on("fn-start",function(){c&&(f+=1)}),i.on("fn-err",function(t,e,r){c&&(this.thrown=!0,n(r))}),i.on("fn-end",function(){c&&!this.thrown&&f>0&&(f-=1)}),i.on("internal-error",function(t){r("ierr",[t,(new Date).getTime(),!0])})},{1:9,2:8,3:6,4:10,5:3,6:24,ee:"QJf3ax",handle:"D5DuLP",loader:"G9z0Bl"}],5:[function(t){function e(){}if(window.performance&&window.performance.timing&&window.performance.getEntriesByType){var n=t("ee"),r=t("handle"),o=t(1),i=t(2);t("loader").features.stn=!0,t(3);var a=Event;n.on("fn-start",function(t){var e=t[0];e instanceof a&&(this.bstStart=Date.now())}),n.on("fn-end",function(t,e){var n=t[0];n instanceof a&&r("bst",[n,e,this.bstStart,Date.now()])}),o.on("fn-start",function(t,e,n){this.bstStart=Date.now(),this.bstType=n}),o.on("fn-end",function(t,e){r("bstTimer",[e,this.bstStart,Date.now(),this.bstType])}),i.on("fn-start",function(){this.bstStart=Date.now()}),i.on("fn-end",function(t,e){r("bstTimer",[e,this.bstStart,Date.now(),"requestAnimationFrame"])}),n.on("pushState-start",function(){this.time=Date.now(),this.startPath=location.pathname+location.hash}),n.on("pushState-end",function(){r("bstHist",[location.pathname+location.hash,this.startPath,this.time])}),"addEventListener"in window.performance&&(window.performance.clearResourceTimings?window.performance.addEventListener("resourcetimingbufferfull",function(){r("bstResource",[window.performance.getEntriesByType("resource")]),window.performance.clearResourceTimings()},!1):window.performance.addEventListener("webkitresourcetimingbufferfull",function(){r("bstResource",[window.performance.getEntriesByType("resource")]),window.performance.webkitClearResourceTimings()},!1)),document.addEventListener("scroll",e,!1),document.addEventListener("keypress",e,!1),document.addEventListener("click",e,!1)}},{1:9,2:8,3:7,ee:"QJf3ax",handle:"D5DuLP",loader:"G9z0Bl"}],6:[function(t,e){function n(t){for(var e=t;e&&!e.hasOwnProperty("addEventListener");)e=Object.getPrototypeOf(e);e&&r(e)}function r(t){a.inPlace(t,["addEventListener","removeEventListener"],"-",o)}function o(t){return t[1]}var i=t("ee").create(),a=t(1)(i),s=t("gos");e.exports=i,r(window),"getPrototypeOf"in Object?(n(document),n(XMLHttpRequest.prototype)):XMLHttpRequest.prototype.hasOwnProperty("addEventListener")&&r(XMLHttpRequest.prototype),i.on("addEventListener-start",function(t){if(t[1]){var e=t[1];if("function"==typeof e){var n=s(e,"nr@wrapped",function(){return a(e,"fn-",null,e.name||"anonymous")});this.wrapped=t[1]=n}else"function"==typeof e.handleEvent&&a.inPlace(e,["handleEvent"],"fn-")}}),i.on("removeEventListener-start",function(t){var e=this.wrapped;e&&(t[1]=e)})},{1:25,ee:"QJf3ax",gos:"7eSDFh"}],7:[function(t,e){var n=t("ee").create(),r=t(1)(n);e.exports=n,r.inPlace(window.history,["pushState","replaceState"],"-")},{1:25,ee:"QJf3ax"}],8:[function(t,e){var n=t("ee").create(),r=t(1)(n);e.exports=n,r.inPlace(window,["requestAnimationFrame","mozRequestAnimationFrame","webkitRequestAnimationFrame","msRequestAnimationFrame"],"raf-"),n.on("raf-start",function(t){t[0]=r(t[0],"fn-")})},{1:25,ee:"QJf3ax"}],9:[function(t,e){function n(t,e,n){t[0]=i(t[0],"fn-",null,n)}function r(t,e,n){this.method=n,this.timerDuration="number"==typeof t[1]?t[1]:0,t[0]=i(t[0],"fn-",this,n)}var o=t("ee").create(),i=t(1)(o);e.exports=o,i.inPlace(window,["setTimeout","setImmediate"],"setTimer-"),i.inPlace(window,["setInterval"],"setInterval-"),i.inPlace(window,["clearTimeout","clearImmediate"],"clearTimeout-"),o.on("setInterval-start",n),o.on("setTimer-start",r)},{1:25,ee:"QJf3ax"}],10:[function(t,e){function n(){f.inPlace(this,p,"fn-",o)}function r(t,e){f.inPlace(e,["onreadystatechange"],"fn-",o)}function o(t,e){return e}function i(t,e){for(var n in t)e[n]=t[n];return e}var a=t("ee").create(),s=t(1),c=t(2),f=c(a),u=c(s),d=window.XMLHttpRequest,p=["onload","onerror","onabort","onloadstart","onloadend","onprogress","ontimeout"];e.exports=a,window.XMLHttpRequest=function(t){var e=new d(t);try{a.emit("new-xhr",[e],e),e.hasOwnProperty("addEventListener")&&u.inPlace(e,["addEventListener","removeEventListener"],"-",o),e.addEventListener("readystatechange",n,!1)}catch(r){try{a.emit("internal-error",[r])}catch(i){}}return e},i(d,XMLHttpRequest),XMLHttpRequest.prototype=d.prototype,f.inPlace(XMLHttpRequest.prototype,["open","send"],"-xhr-",o),a.on("send-xhr-start",r),a.on("open-xhr-start",r)},{1:6,2:25,ee:"QJf3ax"}],11:[function(t){function e(t){var e=this.params,r=this.metrics;if(!this.ended){this.ended=!0;for(var o=0;u>o;o++)t.removeEventListener(f[o],this.listener,!1);if(!e.aborted){if(r.duration=(new Date).getTime()-this.startTime,4===t.readyState){e.status=t.status;var i=this.lastSize||n(t);if(i&&(r.rxSize=i),this.sameOrigin){var s=t.getResponseHeader("X-NewRelic-App-Data");s&&(e.cat=s.split(", ").pop())}}else e.status=0;r.cbTime=this.cbTime,c.emit("xhr-done",[t],t),a("xhr",[e,r,this.startTime])}}}function n(t){var e=t.responseType,n="arraybuffer"===e||"blob"===e||"json"===e?t.response:t.responseText;return r(n)}function r(t){if("string"==typeof t&&t.length)return t.length;if("object"!=typeof t)return void 0;if("undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer&&t.byteLength)return t.byteLength;if("undefined"!=typeof Blob&&t instanceof Blob&&t.size)return t.size;if("undefined"!=typeof FormData&&t instanceof FormData)return void 0;try{return JSON.stringify(t).length}catch(e){return void 0}}function o(t,e){var n=s(e),r=t.params;r.host=n.hostname+":"+n.port,r.pathname=n.pathname,t.sameOrigin=n.sameOrigin}var i=t("loader");if(i.xhrWrappable){var a=t("handle"),s=t(2),c=t("ee"),f=["load","error","abort","timeout"],u=f.length,d=t(1),p=t(3),l=window.XMLHttpRequest;i.features.xhr=!0,t(5),t(4),c.on("new-xhr",function(t){var n=this;n.totalCbs=0,n.called=0,n.cbTime=0,n.end=e,n.ended=!1,n.xhrGuids={},n.lastSize=0,p&&(p>34||10>p)||window.opera||t.addEventListener("progress",function(t){n.lastSize=t.loaded},!1)}),c.on("open-xhr-start",function(t){this.params={method:t[0]},o(this,t[1]),this.metrics={}}),c.on("open-xhr-end",function(t,e){"loader_config"in NREUM&&"xpid"in NREUM.loader_config&&this.sameOrigin&&e.setRequestHeader("X-NewRelic-ID",NREUM.loader_config.xpid)}),c.on("send-xhr-start",function(t,e){var n=this.metrics,o=t[0],i=this;if(n&&o){var a=r(o);a&&(n.txSize=a)}this.startTime=(new Date).getTime(),this.listener=function(t){try{"abort"===t.type&&(i.params.aborted=!0),("load"!==t.type||i.called===i.totalCbs&&(i.onloadCalled||"function"!=typeof e.onload))&&i.end(e)}catch(n){try{c.emit("internal-error",[n])}catch(r){}}};for(var s=0;u>s;s++)e.addEventListener(f[s],this.listener,!1)}),c.on("xhr-cb-time",function(t,e,n){this.cbTime+=t,e?this.onloadCalled=!0:this.called+=1,this.called!==this.totalCbs||!this.onloadCalled&&"function"==typeof n.onload||this.end(n)}),c.on("xhr-load-added",function(t,e){var n=""+d(t)+!!e;this.xhrGuids&&!this.xhrGuids[n]&&(this.xhrGuids[n]=!0,this.totalCbs+=1)}),c.on("xhr-load-removed",function(t,e){var n=""+d(t)+!!e;this.xhrGuids&&this.xhrGuids[n]&&(delete this.xhrGuids[n],this.totalCbs-=1)}),c.on("addEventListener-end",function(t,e){e instanceof l&&"load"===t[0]&&c.emit("xhr-load-added",[t[1],t[2]],e)}),c.on("removeEventListener-end",function(t,e){e instanceof l&&"load"===t[0]&&c.emit("xhr-load-removed",[t[1],t[2]],e)}),c.on("fn-start",function(t,e,n){e instanceof l&&("onload"===n&&(this.onload=!0),("load"===(t[0]&&t[0].type)||this.onload)&&(this.xhrCbStart=(new Date).getTime()))}),c.on("fn-end",function(t,e){this.xhrCbStart&&c.emit("xhr-cb-time",[(new Date).getTime()-this.xhrCbStart,this.onload,e],e)})}},{1:"XL7HBI",2:12,3:14,4:10,5:6,ee:"QJf3ax",handle:"D5DuLP",loader:"G9z0Bl"}],12:[function(t,e){e.exports=function(t){var e=document.createElement("a"),n=window.location,r={};e.href=t,r.port=e.port;var o=e.href.split("://");!r.port&&o[1]&&(r.port=o[1].split("/")[0].split("@").pop().split(":")[1]),r.port&&"0"!==r.port||(r.port="https"===o[0]?"443":"80"),r.hostname=e.hostname||n.hostname,r.pathname=e.pathname,r.protocol=o[0],"/"!==r.pathname.charAt(0)&&(r.pathname="/"+r.pathname);var i=!e.protocol||":"===e.protocol||e.protocol===n.protocol,a=e.hostname===document.domain&&e.port===n.port;return r.sameOrigin=i&&(!e.hostname||a),r}},{}],13:[function(t,e){function n(t){return function(){r(t,[(new Date).getTime()].concat(i(arguments)))}}var r=t("handle"),o=t(1),i=t(2);"undefined"==typeof window.newrelic&&(newrelic=window.NREUM);var a=["setPageViewName","addPageAction","setCustomAttribute","finished","addToTrace","inlineHit","noticeError"];o(a,function(t,e){window.NREUM[e]=n("api-"+e)}),e.exports=window.NREUM},{1:23,2:24,handle:"D5DuLP"}],14:[function(t,e){var n=0,r=navigator.userAgent.match(/Firefox[\/\s](\d+\.\d+)/);r&&(n=+r[1]),e.exports=n},{}],gos:[function(t,e){e.exports=t("7eSDFh")},{}],"7eSDFh":[function(t,e){function n(t,e,n){if(r.call(t,e))return t[e];var o=n();if(Object.defineProperty&&Object.keys)try{return Object.defineProperty(t,e,{value:o,writable:!0,enumerable:!1}),o}catch(i){}return t[e]=o,o}var r=Object.prototype.hasOwnProperty;e.exports=n},{}],D5DuLP:[function(t,e){function n(t,e,n){return r.listeners(t).length?r.emit(t,e,n):void(r.q&&(r.q[t]||(r.q[t]=[]),r.q[t].push(e)))}var r=t("ee").create();e.exports=n,n.ee=r,r.q={}},{ee:"QJf3ax"}],handle:[function(t,e){e.exports=t("D5DuLP")},{}],XL7HBI:[function(t,e){function n(t){var e=typeof t;return!t||"object"!==e&&"function"!==e?-1:t===window?0:i(t,o,function(){return r++})}var r=1,o="nr@id",i=t("gos");e.exports=n},{gos:"7eSDFh"}],id:[function(t,e){e.exports=t("XL7HBI")},{}],G9z0Bl:[function(t,e){function n(){if(!h++){var t=l.info=NREUM.info,e=f.getElementsByTagName("script")[0];if(t&&t.licenseKey&&t.applicationID&&e){s(d,function(e,n){t[e]||(t[e]=n)});var n="https"===u.split(":")[0]||t.sslForHttp;l.proto=n?"https://":"http://",a("mark",["onload",i()]);var r=f.createElement("script");r.src=l.proto+t.agent,e.parentNode.insertBefore(r,e)}}}function r(){"complete"===f.readyState&&o()}function o(){a("mark",["domContent",i()])}function i(){return(new Date).getTime()}var a=t("handle"),s=t(1),c=window,f=c.document;t(2);var u=(""+location).split("?")[0],d={beacon:"bam.nr-data.net",errorBeacon:"bam.nr-data.net",agent:"js-agent.newrelic.com/nr-852.min.js"},p=window.XMLHttpRequest&&XMLHttpRequest.prototype&&XMLHttpRequest.prototype.addEventListener&&!/CriOS/.test(navigator.userAgent),l=e.exports={offset:i(),origin:u,features:{},xhrWrappable:p};f.addEventListener?(f.addEventListener("DOMContentLoaded",o,!1),c.addEventListener("load",n,!1)):(f.attachEvent("onreadystatechange",r),c.attachEvent("onload",n)),a("mark",["firstbyte",i()]);var h=0},{1:23,2:13,handle:"D5DuLP"}],loader:[function(t,e){e.exports=t("G9z0Bl")},{}],23:[function(t,e){function n(t,e){var n=[],o="",i=0;for(o in t)r.call(t,o)&&(n[i]=e(o,t[o]),i+=1);return n}var r=Object.prototype.hasOwnProperty;e.exports=n},{}],24:[function(t,e){function n(t,e,n){e||(e=0),"undefined"==typeof n&&(n=t?t.length:0);for(var r=-1,o=n-e||0,i=Array(0>o?0:o);++r<o;)i[r]=t[e+r];return i}e.exports=n},{}],25:[function(t,e){function n(t){return!(t&&"function"==typeof t&&t.apply&&!t[i])}var r=t("ee"),o=t(1),i="nr@original",a=Object.prototype.hasOwnProperty,s=!1;e.exports=function(t){function e(t,e,r,a){function nrWrapper(){var n,i,s,c;try{i=this,n=o(arguments),s="function"==typeof r?r(n,i):r||{}}catch(u){d([u,"",[n,i,a],s])}f(e+"start",[n,i,a],s);try{return c=t.apply(i,n)}catch(p){throw f(e+"err",[n,i,p],s),p}finally{f(e+"end",[n,i,c],s)}}return n(t)?t:(e||(e=""),nrWrapper[i]=t,u(t,nrWrapper),nrWrapper)}function c(t,r,o,i){o||(o="");var a,s,c,f="-"===o.charAt(0);for(c=0;c<r.length;c++)s=r[c],a=t[s],n(a)||(t[s]=e(a,f?s+o:o,i,s))}function f(e,n,r){if(!s){s=!0;try{t.emit(e,n,r)}catch(o){d([o,e,n,r])}s=!1}}function u(t,e){if(Object.defineProperty&&Object.keys)try{var n=Object.keys(t);return n.forEach(function(n){Object.defineProperty(e,n,{get:function(){return t[n]},set:function(e){return t[n]=e,e}})}),e}catch(r){d([r])}for(var o in t)a.call(t,o)&&(e[o]=t[o]);return e}function d(e){try{t.emit("internal-error",e)}catch(n){}}return t||(t=r),e.inPlace=c,e.flag=i,e}},{1:24,ee:"QJf3ax"}]},{},["G9z0Bl",4,11,5]);
;NREUM.info={beacon:"bam.nr-data.net",errorBeacon:"bam.nr-data.net",licenseKey:"7cca37407f",applicationID:"15315142",sa:1,agent:"js-agent.newrelic.com/nr-852.min.js"}

var host_name = window.location.hostname;
if (typeof envId !== 'undefined') {
    host_name = envId;
}
var store_url = 'http://' + host_name + '/';
var host_url = 'http://localhost:8010/';
var Overrides = 'LOCAL_OVERRIDES';
var sfCSS = 'deluxe.css';
var logo = '<a href="/shopdeluxe/home/<USER>"><img src="" alt=""></a>';

//Domain settings
if (typeof window !== 'undefined') {
    if (host_name == 'localhost' || host_name == 'deluxe.amazecodes.com') {
        Overrides = 'LOCAL_OVERRIDES';
        sfCSS = 'deluxe.css';
        host_url = 'http://localhost:8080/';
    } else if (host_name == 'test.localhost.com') {
        Overrides = 'LOCAL_OVERRIDES';
        sfCSS = 'style.min.css';
        host_url = 'https://sf-dev.deluxe.com/';
    } else if (host_name == 'sd.localhost.com') {
        Overrides = 'SD_LOCAL_OVERRIDES';
        sfCSS = 'deluxe.css';
        host_url = 'http://sd.localhost.com:8080/';
    } else if (host_name == 'sf-dev.deluxe.com') {
        Overrides = 'SD_DEV_OVERRIDES';
        sfCSS = 'deluxe.css';
        host_url = 'https://sf-dev.deluxe.com/';
        logo = '<a href="/shopdeluxe/home/<USER>"><img src="/webasset/sd/128/images/deluxe_logo_cfg.png" alt="Deluxe"></a>';
    } else if (host_name == 'sf-stage.deluxe.com') {
        Overrides = 'SD_STAGE_OVERRIDES';
        sfCSS = 'deluxe.css';
        host_url = 'https://sf-stage.deluxe.com/';
        logo = '<a href="/shopdeluxe/home/<USER>"><img src="/webasset/sd/128/images/deluxe_logo_cfg.png" alt="Deluxe"></a>';
    } else if (host_name == 'www.deluxe.com') {
        Overrides = 'SD_PROD_OVERRIDES';
        sfCSS = 'deluxe.css';
        host_url = 'https://www.deluxe.com/';
        logo = '<a href="/shopdeluxe/home/<USER>"><img src="/webasset/sd/128/images/deluxe_logo_cfg.png" alt="Deluxe"></a>';
    } else if (host_name == 'origin-sf-prod.deluxe.com') {
        Overrides = 'SD_PROD_OVERRIDES';
        sfCSS = 'deluxe.css';
        host_url = 'https://origin-sf-prod.deluxe.com/';
        logo = '<a href="/shopdeluxe/home/<USER>"><img src="/webasset/sd/128/images/deluxe_logo_cfg.png" alt="Deluxe"></a>';
    } else if (host_name == 'www-dr.deluxe.com') {
        Overrides = 'SD_PROD_OVERRIDES';
        sfCSS = 'deluxe.css';
        host_url = 'https://www-dr.deluxe.com/';
        logo = '<a href="/shopdeluxe/home/<USER>"><img src="/webasset/sd/128/images/deluxe_logo_cfg.png" alt="Deluxe"></a>';
    } else if (host_name == 'dfs.localhost.com') {
        Overrides = 'B2B_LOCAL_OVERRIDES'; 
        sfCSS = 'dfs.css';
        host_url = 'http://dfs.localhost.com:8080/';
    } else if (host_name == 'b2b-dev.dfsonline.com') {
        Overrides = 'B2B_DEV_OVERRIDES';  
        sfCSS = 'dfs.css';
        host_url = 'https://sf-dev.deluxe.com/';
        logo = '<a href="/dfs/home/"><img src="/webasset/b2b/203/images/dfs_logo.png" alt="DFS"></a>';
    } else if (host_name == 'b2b-stage.dfsonline.com') {
        Overrides = 'B2B_STAGE_OVERRIDES';
        sfCSS = 'dfs.css';
        host_url = 'https://sf-stage.deluxe.com/';
        logo = '<a href="/dfs/home/"><img src="/webasset/b2b/203/images/dfs_logo.png" alt="DFS"></a>';
    } else if (host_name == 'www.dfsonline.com') {
        Overrides = 'B2B_PROD_OVERRIDES';
        sfCSS = 'dfs.css';
        host_url = 'https://www.deluxe.com/';
        logo = '<a href="/dfs/home/"><img src="/webasset/b2b/203/images/dfs_logo.png" alt="DFS"></a>';
    } else if (host_name == 'origin-www.dfsonline.com') {
        Overrides = 'B2B_PROD_OVERRIDES';
        sfCSS = 'dfs.css';
        host_url = 'https://origin-sf-prod.deluxe.com/';
        logo = '<a href="/dfs/home/"><img src="/webasset/b2b/203/images/dfs_logo.png" alt="DFS"></a>';
    } else if (host_name == 'www-dr.dfsonline.com') {
        Overrides = 'B2B_PROD_OVERRIDES';
        sfCSS = 'dfs.css';
        host_url = 'https://www-dr.deluxe.com/';
        logo = '<a href="/dfs/home/"><img src="/webasset/b2b/203/images/dfs_logo.png" alt="DFS"></a>';
    } else if (host_name == 'staplescustomprinting.dev.btobsource.com') {
        Overrides = 'ST_DEV_OVERRIDES';
        sfCSS = 'staples.css';
        host_url = 'https://sf-dev.deluxe.com/';
    } else if (host_name == 'staplescustomprinting.qa.btobsource.com') {
        Overrides = 'ST_QA_OVERRIDES';
        sfCSS = 'staples.css';
        host_url = 'https://sf-stage.deluxe.com/';
    } else if (host_name == 'staplescustomprinting.btobsource.com') {
        Overrides = 'ST_PROD_OVERRIDES';
        sfCSS = 'staples.css';
        host_url = 'https://www.deluxe.com/';
    } else if (host_name == 'staplescustomprintingretail.dev.btobsource.com') {
        Overrides = 'ST_R_DEV_OVERRIDES';
        sfCSS = 'staples.css';
        host_url = 'https://sf-dev.deluxe.com/';
    } else if (host_name == 'staplescustomprintingretail.qa.btobsource.com') {
        Overrides = 'ST_R_QA_OVERRIDES';
        sfCSS = 'staples.css';
        host_url = 'https://sf-stage.deluxe.com/';
    } else if (host_name == 'staplescustomprintingretail.btobsource.com') {
        Overrides = 'ST_R_PROD_OVERRIDES';
        sfCSS = 'staples.css';
        host_url = 'https://www.deluxe.com/';
    } else if (host_name == 'dev.printez.com') {
        Overrides = 'EZP_DEV_OVERRIDES';
        sfCSS = 'pez.css';
        host_url = 'https://www.deluxe.com/';
    } else if (host_name == 'staging.printez.com') {
        Overrides = 'EZP_QA_OVERRIDES';
        sfCSS = 'pez.css';
        host_url = 'https://www.deluxe.com/';
    } else if (host_name == 'www.printez.com') {
        Overrides = 'EZP_PROD_OVERRIDES';
        sfCSS = 'pez.css';
        host_url = 'https://www.deluxe.com/';
    } else if (host_name == 'dev.smartresolution.com') {
        Overrides = 'SR_DEV_OVERRIDES';
        sfCSS = 'pez.min.css';
        host_url = 'https://sf-stage.deluxe.com/';
    } else if (host_name == 'staging.smartresolution.com') {
        Overrides = 'SR_QA_OVERRIDES';
        sfCSS = 'style.min.css';
        host_url = 'https://www.deluxe.com/';
    } else if (host_name == 'www.smartresolution.com') {
        Overrides = 'SR_PROD_OVERRIDES';
        sfCSS = 'style.min.css';
        host_url = 'https://www.deluxe.com/';
    } else if (host_name == 'tenenz.localhost.com') {
        Overrides = 'TENENZ_LOCAL_OVERRIDES';
        sfCSS = 'deluxe.css';
        host_url = 'http://tenenz.localhost.com:8080/';
    } else if (host_name == 'tenenz.test') {
        Overrides = 'TENENZ_TEST_OVERRIDES';
        sfCSS = 'deluxe.css';
        host_url = 'https://sf-stage.deluxe.com/';
    } else if (host_name == 'www.tenenz.com') {
        Overrides = 'TENENZ_PROD_OVERRIDES';
        sfCSS = 'deluxe.css';
        host_url = 'https://www.deluxe.com/';
    } else if (host_name == 'msbc.dev.btobsource.com') {
        Overrides = 'MSBC_DEV_OVERRIDES';
        sfCSS = 'msbc.css';
        host_url = 'https://sf-dev.deluxe.com/';
    } else if (host_name == 'msbc.qa.btobsource.com') {
        Overrides = 'MSBC_QA_OVERRIDES';
        sfCSS = 'msbc.css';
        host_url = 'https://sf-stage.deluxe.com/';
    } else if (host_name == 'www.mysoftwarechecks.com' || host_name == 'mysoftwarechecks.com' || host_name == 'www.microsoftbusinesschecks.com' || host_name == 'microsoftbusinesschecks.com' || host_name == 'msbc.btobsource.com') {
        Overrides = 'MSBC_PROD_OVERRIDES';
        sfCSS = 'msbc.css';
        host_url = 'https://www.deluxe.com/';
    } else if (host_name == 'staging-www.holidaycardwebsite.com') {
        Overrides = 'BCHCW_QA_OVERRIDES';
        sfCSS = 'dfs.css';
        host_url = 'https://sf-stage.deluxe.com/';
    } else if (host_name == 'staging-www.4printing.com') {
        Overrides = 'BC4PRINTING_QA_OVERRIDES';
        sfCSS = 'bc4.css';
        host_url = 'https://sf-stage.deluxe.com/';
    } else if (host_name.indexOf('.4printing.com') != -1) {
        Overrides = 'BC4PRINTING_PROD_OVERRIDES';
        sfCSS = 'bc4.css';
        host_url = 'https://www.deluxe.com/';
    } else if (host_name == 'caas.dev.btobsource.com') {
        Overrides = 'CAAS_DEV_OVERRIDES';
        sfCSS = 'style.min.css';
        host_url = 'https://sf-dev.deluxe.com/';
    } else if (host_name == 'caas.qa.btobsource.com') {
        Overrides = 'CAAS_QA_OVERRIDES';
        sfCSS = 'style.min.css';
        host_url = 'https://sf-stage.deluxe.com/';
    } else if (host_name == 'caas.btobsource.com') {
        Overrides = 'CAAS_PROD_OVERRIDES';
        sfCSS = 'style.min.css';
        host_url = 'https://www.deluxe.com/';
    } else if (host_name == 'pol-sbx') {
        Overrides = 'POL_DEV_OVERRIDES';
        sfCSS = 'pol.css';
        host_url = 'https://sf-stage.deluxe.com/';
    } else if (host_name == 'pol-prd') {
        Overrides = 'POL_PRD_OVERRIDES';
        sfCSS = 'pol.css';
        host_url = 'https://www.deluxe.com/';
    } else if (host_name == 'sf-pol-sbx') {
        Overrides = 'SF_POL_SBX_OVERRIDES';
        sfCSS = 'pol.css';
        host_url = 'https://sf-stage.deluxe.com/';
    } else if (host_name == 'sf-pol-prd') {
        Overrides = 'SF_POL_PROD_OVERRIDES';
        sfCSS = 'pol.css';
        host_url = 'https://www.deluxe.com/';
    } else if (host_name == 'otis-sbx') {
        Overrides = 'OTIS_SBX_OVERRIDES';
        sfCSS = 'deluxe.css';
        host_url = 'https://sf-stage.deluxe.com/';
    } else if (host_name == 'otis-prd') {
        Overrides = 'OTIS_PROD_OVERRIDES';
        sfCSS = 'deluxe.css';
        host_url = 'https://www.deluxe.com/';
    }
    // document.write('<link rel="apple-touch-icon" href="'+host_url+'webasset/w2p_mobile/'+baseAppUrl+'assets/media/images/apple-touch-icon.png"/>');
    // document.write('<link rel="stylesheet" media="screen, projection" href="'+host_url+'webasset/w2p_mobile/'+baseAppUrl+'assets/styles/screen.css"/>');
    // document.write('<link rel="stylesheet" media="screen, projection" href="'+host_url+'webasset/w2p_mobile/'+baseAppUrl+'assets/styles/'+sfCSS+'"/>');

    // dynamic script creation for apple-touch-iconvar 
    var appleTouchIcon = document.createElement('link');
    appleTouchIcon.rel = 'apple-touch-icon';
    appleTouchIcon.href = host_url + 'webasset/w2p_mobile/' + baseAppUrl + 'assets/media/images/apple-touch-icon.png';    
    document.body.appendChild(appleTouchIcon);

    // dynamic script creation for screen.css
    var screenCSS = document.createElement('link');  
    screenCSS.rel = 'stylesheet';    
    screenCSS.type = 'text/css';    
    screenCSS.media = 'screen, projection';  
    screenCSS.href = host_url + 'webasset/w2p_mobile/' + baseAppUrl + 'assets/styles/screen.css';    
    document.body.appendChild(screenCSS); 

    // dynamic script creation for deluxe.css
    var deluxeCSS = document.createElement('link'); 
    deluxeCSS.rel = 'stylesheet'; 
    deluxeCSS.type = 'text/css';
    deluxeCSS.media = 'screen, projection'; 
    deluxeCSS.href = host_url + 'webasset/w2p_mobile/' + baseAppUrl + 'assets/styles/' + sfCSS; 
    document.body.appendChild(deluxeCSS);

    //document.write('<script type="text/javascript" src="'+host_url+'webasset/w2p_mobile/'+baseAppUrl+'assets/scripts/lib/omniture/s_code.js"/></'+'script>');
}


var extHTML =
    '<div class="site">' + 
    '<div class= "product-container">'+
        '<div data-controller="ProductController">' +

            '<div class="cfg-loader" style="display:none;">'+
                '<div class="cfg-loader-logo">'+logo +'</div>' +
                `<div class="spin-wrapper">
                    <div class="load-icons">
                        <div class="slider">
                            <i></i><i></i><i></i><i></i><i></i><i></i>                           
                        </div>
                    </div>
                    <div class="spin-circle"></div>
                </div>`+                
                 `<div class="dot-loader">
                    <h5>Processing</h5>
                    <span></span><span></span><span></span>
                </div>`+
                '<div class="sub-text">Personalizing your product</div>'+
            '</div>'+

        '</div>' +
        '</div>' +
        // '<div data-controller="LogoBrowseController"></div>' +
        // '<div data-controller="LogoMixController"></div>' +
        '<div data-controller="LogoUploadController"></div>' +
        '<div data-controller="StandardVerseController" style="display:none;"></div>' +
        '<div data-controller="RMBlockController"></div>' +
        '<div data-controller="FbtIndexController"></div>' +
        '<div data-controller="FbtSummaryController"></div>' +
        '<div data-controller="FbtNextController"></div>' +
        '<div class="site-ft" role="contentinfo">' +
            //'<div class="isHidden"><p><small>&copy; 2022</small></p></div>' +
        '</div>' +
    '</div>';

document.getElementById("config-container").innerHTML=extHTML;


//$('config-container').innerHTML = extHTML;
var s_account = 'deluxeshopdev,deluxeglobaldev';

// document.write('<script type="text/javascript" src="'+host_url+'webasset/w2p_mobile/'+baseAppUrl+'assets/vendor/requirejs/require.js"/></'+'script>');
// document.write('<script type="text/javascript" src="'+host_url+'webasset/w2p_mobile/'+baseAppUrl+'assets/scripts/config.js"/></'+'script>');
// document.onreadystatechange = function () {
//     var state = document.readyState;
//     if (state == 'complete') {
//       require(['main']);
//     } 
// }

var requireJS = document.createElement('script');
requireJS.type = 'text/javascript';
requireJS.src = host_url + 'webasset/w2p_mobile/' + baseAppUrl + 'assets/vendor/requirejs/require.js'; 

requireJS.onload = function() {
    loadConfig();
};
 
requireJS.onerror = function() {
    console.error('Failed to load require.js');
};

document.head.appendChild(requireJS);
 
// Load config.js after require.js is loaded
function loadConfig() {
    var configJS = document.createElement('script');
    configJS.type = 'text/javascript';
    configJS.src = host_url + 'webasset/w2p_mobile/' + baseAppUrl + 'assets/scripts/config.js';

    configJS.onload = function() {
        // After config.js is loaded, require the 'main' module
        require(['main'], function(main) {
        });
    };

    configJS.onerror = function() {
        console.error('Failed to load config.js');
    };

    document.head.appendChild(configJS);
}