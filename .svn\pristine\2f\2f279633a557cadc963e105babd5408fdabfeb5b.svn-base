<div class="grid">

    <div class="grid-col grid-col_7of10">

        <div class="design">

            <div class="design-hd">

                <div class="detailBox detailBox_1of1Left">
                    <div class="detailBox-header">
                        <span class="hdg hdg_h3">Your Information</span>
                    </div>
                    <div class="detailBox-body detailBox-body_check mix-detailBox-body_textCenter">

                        <div class="vList vList_tiny">

                            <div>
                                <!-- business name -->
                                <span class="hdg hdg_h3">&nbsp;</span>
                            </div>

                            <div>
                                <!-- slogan -->
                                <span class="hdg hdg_h5">&nbsp;</span>
                            </div>

                            <div>
                                <!-- address line 1 -->
                                <span class="hdg hdg_h5">&nbsp;</span>
                            </div>

                            <div>
                                <!-- address line 2 -->
                                <span class="hdg hdg_h5"></span>
                            </div>

                            <div>
                                <!-- city -->
                                <span class="hdg hdg_h5">&nbsp;</span>

                                <!-- state -->
                                <span class="hdg hdg_h5">&nbsp;</span>

                                <!-- zip -->
                                <span class="hdg hdg_h5">&nbsp;</span>
                            </div>

                            <div>
                                <!-- phone, fax or email -->
                                <span class="hdg hdg_h5">&nbsp;</span>
                            </div>

                        </div> {{! /vList }}

                    </div> {{! /detailBox-body }}
                </div> {{! /detailBox }}

            </div> {{! /design-hd }}

            <div class="design-bd">

            </div>

        </div> {{! /design }}

    </div> {{! /grid-col }}

    <div class="grid-col grid-col_3of10">

        <div class="options">

            <p class="txtLarge">
                <span class="hdg hdg_h2">Note: </span>
                Blank lines will not print
            </p>

            <ul class="vList vList_tight">

                <li>
                    <label class="txtLarge">
                        Business Name
                        <input type="text" class="inputBox" />
                    </label>
                </li>

                <li>
                    <label class="txtLarge">
                        Slogan (ex: 25 Years of Service)
                        <input type="text" class="inputBox" />
                    </label>
                </li>

                <li>
                    <label class="txtLarge">
                        Address Line 1
                        <input type="text" class="inputBox" />
                    </label>
                </li>

                <li>
                    <label class="txtLarge">
                        Address Line 2
                        <input type="text" class="inputBox" />
                    </label>
                </li>

                <li>
                    <label class="txtLarge">
                        City
                        <input type="text" class="inputBox" />
                    </label>
                </li>

                <li>
                    <label class="txtLarge">
                        State
                        <span class="inputBox inputBox_select">
                            <select class="inputBox_select-input">
                                <option>--select--</option>
                                <option>state 1</option>
                                <option>state 2</option>
                                <option>state 3</option>
                                <option>state 4</option>
                                <option>state 5</option>
                            </select>
                        </span>
                    </label>
                </li>

                <li>
                    <label class="txtLarge">
                        Zip Code
                        <input type="text" class="inputBox" />
                    </label>
                </li>

                <li>
                    <label class="txtLarge">
                        Phone, Fax or Email
                        <input type="text" class="inputBox" />
                    </label>
                </li>

            </ul>

        </div> {{! /options }}

        {{> subtotal}}

    </div> {{! /grid-col }}

</div> {{! /grid }}
