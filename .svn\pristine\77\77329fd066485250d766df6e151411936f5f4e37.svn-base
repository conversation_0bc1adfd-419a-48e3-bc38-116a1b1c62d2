define(function (require) { // jshint ignore:line
    'use strict';

    var AbstractCollection = require('../../collections/Abstract');
    var ClipArtSubcategoryModel = require('../Subcategory');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.ClipArt.Collections.Subcategories
     * @extends App.Models.Collections.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} ClipArtData
     */
    var ClipArtSubcategoriesCollection = function (ClipArtData) {
        AbstractCollection.call(this, ClipArtData);
    };

    var proto = inherits(ClipArtSubcategoriesCollection, AbstractCollection);

    /**
     * @property itemClass
     * @type {App.Models.ClipArt.Subcategory}
     */
    proto.itemClass = ClipArtSubcategoryModel;

    return ClipArtSubcategoriesCollection;
});
