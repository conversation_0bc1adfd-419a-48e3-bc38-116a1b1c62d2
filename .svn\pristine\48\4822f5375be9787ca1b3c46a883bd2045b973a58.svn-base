<?xml version="1.0" encoding="UTF-8"?>
<addToCart>
    {{!-- Comment Test --}}
    <HeaderInfo>
        {{tag 'profileId' query.profileId}}
        {{tag 'productId' skuId}}
        {{tag 'sessionId' query.sessionId}}
        {{tag 'orderId' query.orderId}}
        <lineItemId></lineItemId>
        {{tag 'fulfillmentId' query.fulfillmentId}}
        {{tag 'totalPrice' pricing.total}}
        <customTextExistsInProfile></customTextExistsInProfile>
        {{#if isConfig}}
             {{tag 'configId' query.configId}}
        {{else}}
             {{tag 'configId' query.cartConfigId}}
        {{/if}}     
    </HeaderInfo>
    <productDetails>
        {{tag 'productCode' query.kitId}}
        {{tag 'productCodeName' desc}}
        {{tag 'productTypeDesc' desc}}
        {{tag 'selectedProductId' skuId}}
        <selectedProductIdDesc>Original</selectedProductIdDesc>
        {{tag 'wizWigUrl' wizWigUrl}}
        <peelAndSeal></peelAndSeal>
        <folding></folding>
        <signature></signature>
        
        <verse type="" />
                
        <quantity>1</quantity>
        {{tag 'basePrice' pricing.base}}
        {{!-- {{tag 'productCodeName' info.desc}}
        {{tag 'productTypeDesc' info.typeDesc}}
        {{tag 'selectedProductId' data.values.productId.value}}
        {{tag 'selectedProductIdDesc' data.values.productId.description}}
        {{tag 'wizWigUrl' wizWigUrl}}
        <peelAndSeal>{{peelAndSeal}}</peelAndSeal>
        <folding>{{folding}}</folding>
        <signature>{{signature}}</signature>
        {{#if verse}}
            {{tag 'verse' verse.verseCode type=verse.verseType }}
        {{else}}
            <verse type="" />
        {{/if}}         --}}
        {{!-- {{tag 'quantity' data.values.quantity.value}}
        {{tag 'basePrice' pricing.base}} --}}
        {{#each items}}
            <customizedOptions>
                {{{this}}}
            </customizedOptions>          
        {{/each}}
    </productDetails>
</addToCart>