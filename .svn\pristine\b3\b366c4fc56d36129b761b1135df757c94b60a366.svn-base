/* ---------------------------------------------------------------------
Original Author: <PERSON>
Contributors: N/A

Target Browsers: All
Media Type: Screen, Projection
Width: All Sizes
------------------------------------------------------------------------ */
/*csslint
    adjoining-classes: false,
    compatible-vendor-prefixes: false,
    fallback-colors: false
*/

/* ---------------------------------------------------------------------
 Checkbox Styles
------------------------------------------------------------------------ */
.checkbox {
    position: absolute;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height:1px;
    width:1px;
    margin:-1px;
    padding:0;
    border:0;
}

.checkbox + label {
    /* vertical-align: middle; */
    color: #000F0C;
    font-size: 14px;
    line-height: 19px;
    display: inline-block;
    cursor: pointer;
    font-weight: 400;
}

.checkbox + label:before {
    content: "";
    display: inline-block;
    height: 24px;
    width: 24px;
    float: left;
    margin-right: 5px;
    margin-top: 3px;    
    background: url(../../media/images/checkbox.png) no-repeat;
    background: rgba(255,255,255,0) url(../../media/images/<EMAIL>) no-repeat;
    background-size: 24px auto;
    vertical-align: middle;
    flex: none;
}

/* adjoining classes required for IE8 :checked support. */
.checkbox.isChecked + label:before {
    background-position: 0 -26px;
}

.colorToggle {
    overflow: hidden;
    height: 0;
    margin-top: 0;
    opacity: 0;
    -webkit-transition:
        margin 500ms,
        height 500ms,
        opacity 450ms;
    transition:
        margin 500ms,
        height 500ms,
        opacity 450ms;
}

/* adjoining classes required for IE8 :checked support. */
.colorToggle_checked {
    height: auto;
    margin-top: 12px;
    opacity: 1;
    -webkit-transition:
        margin 500ms,
        height 500ms,
        opacity 450ms 50ms;
    transition:
        margin 500ms,
        height 500ms,
        opacity 450ms 50ms;
}

.checkbox_hold:before {
    display: block;
    float: left;
}

.checkbox_hold-right {
    display: block;
    overflow: hidden;
    padding-left: 8px;
    padding-bottom: 2px; /* helps prevent clipping font descenders */
    line-height: 1;
}
.checkbox + label.labelClass{
    font-weight: bold;
    font-size: 16px;
    color: #333;
}
.foldingHelpLabel{
    cursor: pointer;
    margin-left: 32px;
    margin-top: 5px;
    float: left;

}
#personalizationReview {
    cursor: pointer;
     display: block;
    float: right;
    padding-right: 41px;
}
#foldingHelp, #returnHelp, #envelope_help {
    cursor: pointer;
}
 #returnHelpReview, #foldingHelpReview {
    cursor: pointer;
    display: block;
    float: right;
    padding-left: 5px;
}
#envelope_helpReview {
    cursor: pointer;
    display: block;
    float: right;
    padding-left: 5px;
}
.checkbox + label.envelopeLabel{
    width: calc(100% - 25px);
}
.return_container {
    margin-bottom: 20px;
    float: left;
}
.return_row {
    float: left;
}
.return_col {
    border-right: 1px solid #70898d;
    float: left;
    width: 50px;
    height: 22px;
    padding: 2px;
    text-align: center;
    /* color: #ffffff; */
    background-color: #f1f1f1;

}
.return_top {
    /* background-color: #a1cdd6; */
    background-color: #ffffff;
}
.return_heading {
    font-weight: bold;
}
.return_last {
    border: none;
}

.selectedBorder {
    border: 7px solid #d61120;
    padding: 5px;
    margin-top: -29px;
}

.address_container {
    margin-left: 35px;
}

.address_container_selected {
    margin-left: 35px;
    display: none;
}
.marginer {
    margin-top: -29px;
    padding: 5px;
}
.bluebleed {
    font-weight: normal !important;
    color: #0070c9 !important;
    cursor: pointer;
}

.foldingHelpImg {
    padding: 3px 0px 0px 6px;
}

.envelope_help_img {
    float: right;
    padding-top: 3px;
    width: 66px;
}
.return_help_img {
    float: right;
    padding-top: 4px;
}

[data-id="SL"] > label {
    text-decoration: none !important;
}

@media only screen and (device-width: 768px) {
  /* For general iPad layouts */

    .envelope_help_img {  
        width: 61px;
    }
}