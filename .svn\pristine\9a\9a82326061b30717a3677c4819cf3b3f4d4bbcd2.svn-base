define(function(require) {
    'use strict';

    var AbstractQuestionController = require('./Abstract');
    var inherits = require('mout/lang/inheritPrototype');
    var Content = require('i18n!../../constants/nls/en-us/Content');
    var Classes = require('../../constants/Classes');
    var DomEvents = require('../../constants/DomEvents');
    var Settings = require('../../constants/Settings');
    var Helper = require('../../util/helper');

    /**
     * @class App.Controllers.Question.TextBlock
     * @extends App.Controllers.Question.Abstract
     *
     * @constructor
     * @param {Object} config
     * @param {jQuery} config.view
     * @param {Models.Ui.QuestionBlock} config.model
     */
    function TextBlockQuestionController(config) {
        AbstractQuestionController.call(this, config);
    }

    var proto = inherits(TextBlockQuestionController, AbstractQuestionController);

    /**
     * @property registry
     * @type {Object.<String,App.Controllers.Question.Abstract>}
     */
    proto.registry = {
        TextBlockLineQuestionController: require('./TextBlockLine')
    };

    /**
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = require('hbs!templates/question/text/block');

    /**
     * @method init
     * @chainable
     */
    proto.init = function() {
        var model = this.model;

        this.index = {
            TextBlockLineQuestionController: model.lines
        };

        return this;
    };

    /**
     * @method render
     * @chainable
     */
    proto.render = function() {
        var model = this.model;
        if (model.id === 'SM' && !Settings.SIDEMARGIN_FLAG ) {
            return;
        }
        var lines = model.lines;
        var items = lines && lines._items;

        var renderData = {
                id: model.id,
                isRequired: model.isRequired,
                lines: items
            };

        //For return address in Holiday card
        if (model.info.desc == 'Return Address Area') {
            renderData.desc = Content['Return Address Area'];
        } else if (model.id == 'LS') {
            renderData.desc = 'Signature Lines';
        } else if (model.id == 'ST') {
            renderData.desc = 'Vouchers';
        } else if (model.id == 'SM') {
            renderData.desc = 'Side Margin';
        }

        this.$view
            .html(this.template(renderData));

        return this.start();
    };

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        this.$view
            .on(DomEvents.CLICK, Classes.BUTTON_VOUCHER_ACTION_SELECTOR, this.onVoucherButtonClick)
            .on(DomEvents.CLICK, Classes.BUTTON_SIGNATURE_ACTION_SELECTOR, this.onSignatureButtonClick)
            .on(DomEvents.CLICK, Classes.BUTTON_SIDEMARGIN_ACTION_SELECTOR, this.onSideMarginButtonClick);
        return this;
    };

    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        this.$view
            .off(DomEvents.CLICK, Classes.BUTTON_VOUCHER_ACTION_SELECTOR, this.onVoucherButtonClick)
            .off(DomEvents.CLICK, Classes.BUTTON_SIGNATURE_ACTION_SELECTOR, this.onSignatureButtonClick)
            .off(DomEvents.CLICK, Classes.BUTTON_SIDEMARGIN_ACTION_SELECTOR, this.onSideMarginButtonClick);
        return this;
    };

   /**
     * @method onVoucherButtonClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onVoucherButtonClick = function(e) {
        e.preventDefault();

        if (Helper.isSameTooltipClick(e)) {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
        } else {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
            $('#voucherHelp').popover({
                html: true,
                trigger: "manual",
                placement: 'bottom',
                content: function () {
                    var msg0 = '<div><img src="' + host_url + '/webasset/w2p_mobile/' + baseAppUrl + 'assets/media/images/tooltip-close-new.png" id="voucherCloseIcon" alt="voucherCloseIcon" class="close tooltip-close" style="position: absolute; top: 0; right: 7px;"/>';
                    var msg1 = "<div class='titletxt'> Vouchers</div><br/>";
                    var msg2 = "<div class='othertxt'> The stub headings on these laser checks easily confirms your company identity on the remittance data once the check is</div>";
                    var msg3 = "<p class='othertxt'>removed from the voucher.&nbsp;&nbsp;The area can also be used to identify specific bank accounts a small business uses.<br/><br/>e.g.&nbsp;&nbsp;Company ABC – Payroll Account</p><br/>";
                    return msg0 + msg1 + msg2 + msg3 + "</div>";
                },
            });
            if (!Settings.VOUCHER_FLAG) {
                $('#voucherHelp').popover('show');
            } else {
                $('#voucherHelp').popover('hide');
            }
            Settings.VOUCHER_FLAG = !Settings.VOUCHER_FLAG;

            $('#voucherCloseIcon').click(function (e) {
                Settings.VOUCHER_FLAG = false;
                $('#voucherHelp').popover('hide');
            });
        }
    }

    /**
     * @method onSignatureButtonClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onSignatureButtonClick = function(e) {
        e.preventDefault();

        if (Helper.isSameTooltipClick(e)) {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
        } else {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }

            $('#signatureTextHelp').popover({
                html: true,
                trigger: "manual",
                placement: 'bottom',
                content: function () {
                    var msg0 = '<div><img src="' + host_url + '/webasset/w2p_mobile/' + baseAppUrl + 'assets/media/images/tooltip-close-new.png" id="signatureCloseIcon" alt="signatureCloseIcon" class="close tooltip-close" style="position: absolute; top: 0; right: 7px;"/>';
                    var msg1 = "<div class='titletxt'>Signature Lines</div><br/>";
                    var msg2 = "<div class='othertxt'> The area above the signature line is used to confirm the company information of the signee.&nbsp;&nbsp;It can also be used to identify specific</div>";
                    var msg3 = "<p class='othertxt'> conditions associated with the check:<br/>e.g.&nbsp;&nbsp;Not Valid over $5000<br/><br/>Or to identify an account the check is being used from:<br/>e.g.&nbsp;&nbsp;Company ABC – Trust Account</p><br/>";
                    return msg0 + msg1 + msg2 + msg3 + "</div>";
                },
            });
            if (!Settings.SIGNATURE_FLAG) {
                $('#signatureTextHelp').popover('show');
            } else {
                $('#signatureTextHelp').popover('hide');
            }
            Settings.SIGNATURE_FLAG = !Settings.SIGNATURE_FLAG;

            $('#signatureCloseIcon').click(function (e) {
                Settings.SIGNATURE_FLAG = false;
                $('#signatureTextHelp').popover('hide');
            });
        }
    }

    /**
     * @method onSideMarginButtonClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onSideMarginButtonClick = function(e) {
        e.preventDefault();

        if (Helper.isSameTooltipClick(e)) {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
        } else {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }

            $('#sideMarginHelp').popover({
                html: true,
                trigger: "manual",
                placement: 'bottom',
                content: function () {
                    var msg0 = '<div><img src="' + host_url + '/webasset/w2p_mobile/' + baseAppUrl + 'assets/media/images/tooltip-close-new.png" id="sideMarginCloseIcon" alt="sideMarginCloseIcon" class="close tooltip-close" style="position: absolute; top: 0; right: 7px;"/>';
                    var msg1 = "<div class='titletxt boldText'>Side Margin</div><br/>";
                    var msg2 = "<div class='othertxt boldText'>The side margin is printed along the edge of the form. It contains your company name and phone number so customers can contact you: e.g. Company ABC, (*************</div><br/>";
                    return msg0 + msg1 + msg2 + "</div>";
                },
            });
            if (!Settings.SIDEMARGIN_FLAG) {
                $('#sideMarginHelp').popover('show');
            } else {
                $('#sideMarginHelp').popover('hide');
            }
            Settings.SIDEMARGIN_FLAG = !Settings.SIDEMARGIN_FLAG;

            $('#sideMarginCloseIcon').click(function (e) {
                Settings.SIDEMARGIN_FLAG = false;
                $('#sideMarginHelp').popover('hide');
            });
        }
    }

    /**
     * @method updateErrors
     * @chainable
     */
    proto.updateErrors = function() {
        var started = this.started;
        var length = started.length;
        var i = 0;

        for (; i < length; i++) {
            started[i].updateErrors();
        }

        return this;
    };

    return TextBlockQuestionController;
});
