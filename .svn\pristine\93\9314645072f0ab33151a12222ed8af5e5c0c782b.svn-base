define(function (require) {
    'use strict';

    var AbstractModel = require('../Abstract');
    var UIQuestionsCollection = require('./collections/Questions');
    var Content = require('i18n!../../constants/nls/en-us/Content');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.Ui.Tab
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} tab
     */
    var UITabModel = function (tab) {
        bindAll(this, 'isValid');

        AbstractModel.call(this, tab);
    };

    var proto = inherits(UITabModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} tab
     * @chainable
     */
    proto.init = function(tab) {
        /**
         * @property id
         * @default {null}
         * @type {string}
         */
        this.id = null;

        /**
         * @property control
         * @default {null}
         * @type {string}
         */
        this.description = null;

        /**
         * @property progressTitle
         * @default {null}
         * @type {string}
         */
        this.progressTitle = null;

        /**
         * @property tlId
         * @default {null}
         * @type {string}
         */
        this.tlId = null;

        /**
         * @property previousText
         * @default {null}
         * @type {string}
         */
        this.previousText = null;

        /**
         * @property nextText
         * @default {null}
         * @type {string}
         */
        this.nextText = null;

        /**
         * @property trackEvent
         * @default {null}
         * @type {object}
         */
        this.trackEvent = null;

        /**
         * @property questions
         * @default {null}
         * @type {App.Models.Ui.Collections.Questions}
         */
        this.questions = null;

        // run the parent init method to parse determine the data type
        base.init.call(this, tab);

        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param json the original JSON data.
     */
    proto.fromJSON = function(json) {
        if (!json.id) {
            json = this.stripInvalidFields(json);
            if (!json.id && json.tab !== undefined) {
                json = json.tab;
            }
        }

        this.id = json.id;
        this.description = json.desc;
        this.progressTitle = json.progressTitle;
        this.tlId = json.tlId;
        this.prev = Content.get(json.prev);
        this.next = Content.get(json.next);
        this.trackEvent = this.toJSON(json.trackEvent);

        this.questions = new UIQuestionsCollection(json.question);
        // console.log('llll',   this.progressTitle);

    };

    /**
     * @method getQuestions
     * @return {App.Models.Ui.Collections.Questions}
     */
    proto.getQuestions = function() {
        return this.questions;
    };

    /**
     * Recursively sets product info on all questions.
     *
     * @method setInfo
     * @param {Object} info
     * @param {App.Models.FXG} fxg
     * @param {App.Models.CustomerProfile} customer
     * @chainable
     */
    proto.setInfo = function(info, fxg, customer) {
        this.trackGroup = info.productInfo.group;
        this.questions.setInfo(info, fxg, customer);

        return this;
    };

    /**
     * Returns an object containing all selections made by
     * the user for all contained questions
     *
     * @method getValues
     * @returns {Object}
     */
    proto.getValues = function() {
        var values = this.questions.getValues();

        return {
            values: values.values,
            blocks: values.blocks,
            surcharges: values.surcharges
        };
    };

    /**
     * @method isValid
     * @return {Boolean}
     */
    proto.isValid = function() {
        if (this.questions.isValid() !== true) {
            return false;
        }

        return true;
    };

    return UITabModel;
});
