define(function (require) {
    'use strict';

    var $ = require('jquery');
    var AbstractModel = require('./Abstract');
    var CartProductModel = require('./cart/Product');
    var find = require('mout/array/find');
    var inherits = require('mout/lang/inheritPrototype');
    var Surcharge = require('util/Surcharge');
    var SessionStorage = require('../providers/SessionStorage');

    /**
     * @class App.Models.ProductInfo
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} ProductInfoValues
     */
    var ProductInfoModel = function (ProductInfoValues) {
        AbstractModel.call(this, ProductInfoValues);
    };

    /**
     * ProductInfoModel extends AbstractModel
     * @type {AbstractModel}
     */
    var proto = inherits(ProductInfoModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} ProductInfoValues
     * @chainable
     */
    proto.init = function(ProductInfoValues) {
        /**
         * @property productId
         * @default {null}
         * @type {string}
         */
        this.productId = null;

        /**
         * @property productCode
         * @default {null}
         * @type {string}
         */
        this.productCode = null;

        /**
         * @property customTextExistsInProfile
         * @default {null}
         * @type {string}
         */
        this.customTextExistsInProfile = null;

        /**
         * @property fulfillmentId
         * @default {null}
         * @type {string}
         */
        this.fulfillmentId = null;

        /**
         * @property lineItemId
         * @default {null}
         * @type {string}
         */
        this.lineItemId = null;

        /**
         * @property orderId
         * @default {null}
         * @type {string}
         */
        this.orderId = null;

        /**
         * @property profileId
         * @default {null}
         * @type {string}
         */
        this.profileId = null;

        /**
         * @property endUserProfileId
         * @default {null}
         * @type {string}
         */
        this.endUserProfileId = null;

        /**
         * @property sessionId
         * @default {null}
         * @type {string}
         */
        this.sessionId = null;

        /**
         * @property invalidOptions
         * @default {null}
         * @type {string}
         */
        this.invalidOptions = null;

        /**
         * @property matrixInfo
         * @default {null}
         * @type {Array}
         */
        this.matrixInfo = null;

        /**
         * @property priceInfo
         * @default {null}
         * @type {object}
         */
        this.priceInfo = null;

        /**
         * @property productInfo
         * @default {null}
         * @type {object}
         */
        this.productInfo = null;

        /**
         * @property productConfiguration
         * @default {null}
         * @type {object}
         */
        this.productConfiguration = null;

        // run the parent init method to parse determine the data type
        base.init.call(this, ProductInfoValues);

        this._registerPrices();

        return this;
    };

    /**
     * @method getQuestionInfoById
     * @param {String} id
     * @return {Object}
     */
    proto.getQuestionInfoById = function(id) {
        return find(this.productInfo.question, { id: id });
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param {object} json the original JSON data.
     * @chainable
     */
    proto.fromJSON = function(json) {
        json = this.stripInvalidFields(json);

        var attribute;
        var config = json.configuration;
        var header = config.header;

        for (attribute in header) {
            if (!header.hasOwnProperty(attribute)) {
                continue;
            }

            this[attribute] = header[attribute];
        }

        this.customTextExistsInProfile = (header.customTextExistsInProfile === 'Y');
        this.productInfo = $.extend(true, config.productInfo, {});
        this.matrixInfo = $.extend(true, config.matrixInfo, {}).option;
        this.priceInfo = $.extend(true, config.priceInfo, {}).price;
        this.invalidOptions = config.invalidOptions;

        //Verify reconfiguration data is complete, if not remove.
        config = _scrubReconfigureProduct(config);
        if (config.reConfigureProduct) {
            this.productConfiguration = new CartProductModel(config.reConfigureProduct);
            SessionStorage.storeValue('EDIT_CART', true);
        } else {
            SessionStorage.storeValue('EDIT_CART', false);
        }
        return this;
    };

    //Any imprinted product should always have an imprintTypestyle.
    //If imprintTypestyle node is missing, it has likely been purged.
    var _scrubReconfigureProduct = function(config) {
        var isComplete = true;
        if (config.reConfigureProduct) {
            var customizedOptions = config.reConfigureProduct.productDetails.customizedOptions;
            var imprintLocations = [].concat(customizedOptions.selectedLayout.imprintLocations);
            //if (imprintLocations.length === 0 || imprintLocations[0].imprintBlocks == null || imprintLocations[0].imprintTypestyle == 'undefined') {
            if (imprintLocations.length === 0 || imprintLocations[0].imprintTypestyle == 'undefined') {
                isComplete = false;
            }
            
            var questions = [].concat(config.productInfo.question);
            for (var iii = 0; iii < questions.length; iii++) {
                if(questions[iii].id == 'numbering' && config.reConfigureProduct.productDetails.customizedOptions.selectedLayout.selectedNumbering) {
                    if(questions[iii].minChars > config.reConfigureProduct.productDetails.customizedOptions.selectedLayout.selectedNumbering.length)
                    {
                        while(config.reConfigureProduct.productDetails.customizedOptions.selectedLayout.selectedNumbering.length < questions[iii].minChars)
                        {
                            config.reConfigureProduct.productDetails.customizedOptions.selectedLayout.selectedNumbering = '0' +  config.reConfigureProduct.productDetails.customizedOptions.selectedLayout.selectedNumbering
                        }
                    }
                }
            }
            for (var iii = 0; iii < questions.length; iii++) {
                if (questions[iii].id == 'typestyle') {
                    if (imprintLocations[0].imprintTypestyle === undefined) {
                        config.reConfigureProduct.productDetails.customizedOptions.selectedLayout.imprintLocations.imprintTypestyle = questions[iii].default;
                        break;
                    } else {
                        var currentTypestyleValue = String(imprintLocations[0].imprintTypestyle);
                        if(!Array.isArray( questions[iii].option)) {
                            var currentTypestyleValueArray = [questions[iii].option.id]
                        } else {
                            var currentTypestyleValueArray = questions[iii].option.map(function (e) {
                                return e.id
                            })
                        }                       
                        if(currentTypestyleValueArray.includes( currentTypestyleValue)) {
                            questions[iii].default = currentTypestyleValue;
                            config.reConfigureProduct.productDetails.customizedOptions.selectedLayout.imprintLocations.imprintTypestyle = questions[iii].default;
                            break;
                        } else {
                            config.reConfigureProduct.productDetails.customizedOptions.selectedLayout.imprintLocations.imprintTypestyle = questions[iii].default;
                            break;
                        }
                        // if (typeof questions[iii].option[currentTypestyleValue] === "undefined") {
                        //     config.reConfigureProduct.productDetails.customizedOptions.selectedLayout.imprintLocations.imprintTypestyle = questions[iii].default;
                        //     break;
                        // }
                    }
                }
            }
            
        }
        if (!isComplete) {
            delete config.reConfigureProduct;
        }
		//if (!(window.location.href.indexOf("fromPage") > -1)) {
        //    delete config.reConfigureProduct;
        //}

        return config;
    };

    /**
     * iterates through pricing info arrays to determine the
     * correct base price given the productId and quantity selected.
     *
     * Moved out of toXML() to clarify what it was doing using the
     * method name "getBasePrice".
     *
     * @method _getBasePrice
     * @param {string} productId
     * @param {string|number} quantity
     * @return {number|float}
     * @private
     */
    proto.getBasePrice = function(productId, quantity) {
        var i = 0;
        var price = 0.00;
        var selectedQuantity = parseInt(quantity, 10);
        var priceInfoNode;
        var currentQuantity;

        // missing data;
        if (!productId || !quantity || !this.priceInfo) {
            return price;
        }

        // find the correct pricing group by product ID
        for (; i < this.priceInfo.length; i++) {
            if (this.priceInfo[i].id !== productId) {
                continue;
            }

            priceInfoNode = this.priceInfo[i];
            break;
        }

        // missing data
        if (!priceInfoNode || !priceInfoNode.option.length) {
            return price;
        }

        // find the correct pricing option by matching with the
        // selected quantity (each quantity has its own price)
        for (i = 0; i < priceInfoNode.option.length; i++) {
            currentQuantity = parseInt(priceInfoNode.option[i].qty, 10);

            if (currentQuantity !== selectedQuantity) {
                continue;
            }

            price = parseFloat(priceInfoNode.option[i].price);
            break;
        }

        return price;
    };

    /**
     * Merges surcharge data from priceInfo into surcharge object
     *
     * @method _applySurcharges
     * @param {Array} surcharges
     * @return {number}
     * @private
     */
    proto.applySurcharges = function(surcharges, quantity) {
        var surchargeTotal = 0;
        var i = 0;
        var j;

        if (!surcharges || !surcharges.length || !this.priceInfo) {
            return 0;
        }

        for (; i < surcharges.length; i++) {
            for(j = 0; j < this.priceInfo.length; j++) {
                if (surcharges[i] && surcharges[i].id === this.priceInfo[j].id) {
                    $.extend(surcharges[i], this.priceInfo[j], _getOption(this.priceInfo[j].option, quantity));
                    surchargeTotal += parseFloat(surcharges[i].price);
                }
            }
        }

        return surchargeTotal;
    };

    var _getOption = function(options, quantity) {
        if (!(options instanceof Array)) {
            options = [options];
        }
        var i = 0;
        var length = options.length;
        var option = options[i];

        for (; i < length; i++) {
            if (quantity > options[i].qty) {
                break;
            }
            option = options[i];
        }

        return option;
    };

    /**
     * Register surcharge prices in the registry
     *
     * @method _registerPrices
     * @private
     */
    proto._registerPrices = function() {
        var i = 0;
        var prices = this.priceInfo;
        var length = prices && prices.length;

        for (; i < length; i++) {
            Surcharge.add(prices[i].id, prices[i]);
        }

        return this;
    };

    return ProductInfoModel;
});
