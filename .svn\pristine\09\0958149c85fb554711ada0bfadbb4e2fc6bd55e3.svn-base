define(function(require){

//automatically generated, do not edit!
//run `node build` instead
return {
    'awaitDelay' : require('./function/awaitDelay'),
    'bind' : require('./function/bind'),
    'compose' : require('./function/compose'),
    'constant' : require('./function/constant'),
    'debounce' : require('./function/debounce'),
    'func' : require('./function/func'),
    'identity' : require('./function/identity'),
    'makeIterator_' : require('./function/makeIterator_'),
    'partial' : require('./function/partial'),
    'prop' : require('./function/prop'),
    'series' : require('./function/series'),
    'throttle' : require('./function/throttle'),
    'timeout' : require('./function/timeout'),
    'times' : require('./function/times')
};

});
