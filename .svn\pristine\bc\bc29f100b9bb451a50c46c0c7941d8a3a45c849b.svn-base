define(function (require) {
    'use strict';

    var AbstractModel = require('../Abstract');
    var ClipArtSubcategories = require('./collections/Subcategories');
    var inherits = require('mout/lang/inheritPrototype');

    var Registry = require('util/Registry');

    /**
     * @class App.Models.ClipArt.Category
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} ClipArtCategoryValues
     */
    var ClipArtCategoryModel = function (ClipArtCategoryValues) {
        AbstractModel.call(this, ClipArtCategoryValues);
    };

    var proto = inherits(ClipArtCategoryModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} input
     * @chainable
     */
    proto.init = function(input) {

        /**
         * @property id
         * @default {null}
         * @type {string}
         */
        this.id = null;

        /**
         * @property description
         * @default {null}
         * @type {string}
         */
        this.description = null;

        /**
         * @property subcategories
         * @default {null}
         * @type {ClipArtSubcategoryCollection}
         */
        this.subcategories = null;

        this.createChildren();

        // run the parent init method to parse determine the data type
        base.init.call(this, input);

        return this;
    };

    /**
     * @method createChildren
     * @chainable
     */
    proto.createChildren = function() {
        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param {object} json the original JSON data.
     */
    proto.fromJSON = function(json) {
        if (!json.id) {
            json = this.stripInvalidFields(json);
            if (!json.id && json.cat !== undefined) {
                json = json.cat;
            }
        }
        this.id = json.id;
        this.description = json.d;
        this.subcategories = new ClipArtSubcategories(json.subcategories);

        // Register each logo's category/subcategory by logo ID
        this.subcategories.each(function(subcategory) {
            subcategory.logos.each(function(logo) {
                Registry.set(Registry.LOGO, logo.id, {
                    category: json.id,
                    subcategory: subcategory.id
                })
            })
        });
    };

    return ClipArtCategoryModel;
});
