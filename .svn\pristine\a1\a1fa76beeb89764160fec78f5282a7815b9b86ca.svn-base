/* ---------------------------------------------------------------------
Original Author: <PERSON>
Contributors: N/A

Target Browsers: All
Media Type: Screen, Projection
Width: All Sizes
------------------------------------------------------------------------ */
/*csslint
    box-sizing: false,
    compatible-vendor-prefixes: false
*/

/* ---------------------------------------------------------------------
 Design Box
------------------------------------------------------------------------ */
.designBox {
    display: block; /* if child of a label this style needs to be on a span, so we must set block */
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    background: #ffffff no-repeat center; /* image set inline */
    background-size: contain;
    border: 2px solid #ffffff;
}

.designBox:not(.verse) {
    padding-top: 90%; /* fallback for browsers that don't support calc() */
    padding-top: calc(100% - 16px); /* padding is calculated relative to parent width. This makes it a square */
    margin: 4px 4px 4px 4px;
}

.designBox.verse {
    padding-top: 20%; /* padding is calculated relative to parent width. This makes it a square */
    height: 170px;
    border: 1px solid lightgrey;
    margin-top: 25px;
    background-size: 100%;
}
.verseId {
    z-index: 100;
    position: relative;
    font-size: 13px;
    font-weight: 400;
    left: 185px;
    top: -20px;
    color: #555;
    font-family: 'Arial Regular', 'Arial';
    font-style: normal;
}
.designBox:before {
    content: "";
    display: block;
    border: 4px solid transparent;
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    -webkit-transition: border 250ms;
    transition: border 250ms;
}

@media (hover: hover){
.designBox:focus:before,
.designBox:hover:before {
    border: 2px solid #259cda;
}
}
/*.designRadio.isChecked + label >.verseId  {
    color: #febf04;
}*/
@media (hover: hover){
.designBox_active:before,
.designBox_active:hover:before {
    border: 2px solid #fd9600;
}
}

.designBox_empty {
    padding-top: calc(100% - 10px);
    border: 1px solid #9e9e9e;
    background: #ffffff url(../../media/images/x.png) no-repeat center center;
}

.designBox_empty:before {
    content: none;
}
