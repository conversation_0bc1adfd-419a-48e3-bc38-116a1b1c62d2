
<ul class="progressBlocks">
    {{#each progress_steps}}
        <li>
            {{#is active 1}}
            <input type="radio"
                class="progressRadio "
                id="progress_steps_{{id}}"
                name="proressBar"
                value="{{id}}" />
            {{/is}}
            <label for="progress_steps_{{id}}" class="progressItem {{#is active 1}} itemActive{{/is}} {{#is current 1}} currentItem{{/is}}" alt= "{{description}}" title="{{description}}">
                <div class="descProgress">
                    {{!-- .d-md-none --- removed this class from p class="progress-id to get the circle for the step counter in desktop view --}}
                    <p class="progress-id">{{id}}</p>
                    <p class="progress-title" id="progress_steps_title_{{id}}">{{progressTitle}}</p>
                </div>
            </label>
        </li>
    {{/each}}
</ul>
