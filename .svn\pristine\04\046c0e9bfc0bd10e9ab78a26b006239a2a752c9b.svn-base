define(function() {
    'use strict';

    /**
     * List of key/value pairs of question names
     * and their mapping to avaialable/possible
     * GET parameters if they exist. Used to set
     * default values on fields via GET params
     *
     * @class App.Constants.FieldMappings
     * @static
     */
    var FieldMappings = {

        QUERY: {
            productId: 'skuId',
            quantity: 'qty'
        },

        FXG_ALIASES: {
            'MICR_objects': ['routingNumber', 'accountNumber']
        }
    };

    return FieldMappings;
});
