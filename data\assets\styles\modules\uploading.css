/* ---------------------------------------------------------------------
Original Author: <PERSON>
Contributors: N/A

Target Browsers: All
Media Type: Screen, Projection
Width: All Sizes
------------------------------------------------------------------------ */
/*csslint
    box-sizing: false,
    fallback-colors: false,
    vendor-prefix: false
*/

/* ---------------------------------------------------------------------
 Uploading
------------------------------------------------------------------------ */
/* .uploading {
    margin-top: 60px;
} */


.initial-spinner, .fbt-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100% !important;
}

.fbt-spinner {
    margin-top: 5%;
}

.uploading-title {
    text-align: center;
    margin-bottom: 20px;
}

/* progress bar not currently in use */
.uploading-progress {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    height: 14px;
    padding: 3px;
    background-color: #e9e9e9;
    border-radius: 7px;
}

.uploading-progress-fill {
    width: 0;
    height: 8px;
    background-color: #fd9600;
    border-radius: 4px;
}

.uploading-spinner {
    width: 76px;
    height: 76px;
    border: 10px solid #E7E0D7;
    border-bottom-color: #3174d8;
    border-radius: 50%;
    display: inline-block;
    box-sizing: border-box;
    animation: rotation 1s linear infinite;
    /* Add drop shadow for better visibility */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Add pulsing effect to spinner */
@keyframes rotation {
    0% {
        transform: rotate(0deg);
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: rotate(360deg);
        opacity: 0.8;
    }
} 

