{{#unless isStock}}
	{{!-- <div class="good_proof"> {{good_help}} </div> --}}
{{/unless}}
<div class="fbt-cart-btn">
	<button class="js-fbt-addtocart {{#unless fbtFlow}} kit-continue {{/unless}} button button_fbt_next" id="MC_{{tlId}}">
    	<span class="js-next-label">{{{next}}}</span>
	</button>
</div>


<div class="skip-editor-buttons">
		{{#unless isStock}}
			{{!-- <div class="bad_proof" > {{bad_help}} </div> --}}
			<button class="js-fbt-edit-proof button button_fbt_bad button_neutral" id="MC_GoToEditor">
				<span class="js-next-label">{{{back}}}</span>
			</button>
		{{/unless}}
		{{#unless skipText}}
			<div class="link_Skip ">
				{{#if isLast}}
					{{#if fbtFlow}}
						<p align="center"><a class="js-fbt-skip" id="MC_SkipToSummary">No thanks.&nbsp;Skip to summary.</a></p>
					{{/if}}
				{{else}}
					{{#if fbtFlow}}
						<p align="center"><a class="js-fbt-skip" id="MC_SkipToNextProduct">No thanks.&nbsp;Skip to next product.</a></p>
					{{/if}}
				{{/if}}
			</div>
		{{/unless}}
</div>