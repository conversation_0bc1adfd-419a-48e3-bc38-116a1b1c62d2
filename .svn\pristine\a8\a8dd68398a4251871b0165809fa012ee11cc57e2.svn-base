define(function (require) {
    'use strict';

    var AbstractModel = require('./Abstract');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.Swatch
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} SwatchValues
     */
    var SwatchModel = function (SwatchValues) {
        AbstractModel.call(this, SwatchValues);
    };

    var proto = inherits(SwatchModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} SwatchValues
     * @chainable
     */
    proto.init = function(SwatchValues) {

        /**
         * @property peelAndSeal
         * @default {null}
         * @type {string}
         */
        this.peelAndSeal = null;


        // run the parent init method to parse determine the data type
        base.init.call(this, SwatchValues);

        return this;
    };


    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param json the original JSON data.
     */
    proto.fromJSON = function(json) {
        this.peelAndSeal = json.peelAndSeal;     
    };

    return SwatchModel;
});
