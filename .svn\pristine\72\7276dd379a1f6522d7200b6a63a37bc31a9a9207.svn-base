define(function(require) {
    'use strict';
    require('bootstrap');
    var $ = require('jquery');
    var ActionEvents = require('../../constants/ActionEvents');
    var ProductEvents = require('../../constants/ProductEvents');
    var AbstractQuestionController = require('./Abstract');
    var Classes = require('../../constants/Classes');
    var DomEvents = require('../../constants/DomEvents');
    var Content = require('i18n!../../constants/nls/en-us/Content');
    var EventController = require('../Event');
    var SessionStorage = require('../../providers/SessionStorage');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var setParam = require('mout/queryString/setParam');
    var Settings = require('../../constants/Settings');
    var ProductModel = require('../../models/cart/Product');
    var VerseModel = require('../../models/Verse');
    var Surcharge = require('util/Surcharge');

    /**
     * @class App.Controllers.Question.verse
     * @extends App.Controllers.Question.Abstract
     *
     * @constructor
     * @param {Object} config
     * @param {jQuery} config.view
     * @param {Models.Ui.Question} config.model
     */
    function verseQuestionController(config) {
        bindAll(this,
            'update',
            'onActionClick',
            'onVerseChange',
            'onProductChange'
        );
        this.verseOrientation = null;
        this.isCVBlockExist = 'false';

        AbstractQuestionController.call(this, config);
    }

    var proto = inherits(verseQuestionController, AbstractQuestionController);
    var base = AbstractQuestionController.prototype;
   
    /**
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = require('hbs!templates/question/verse');

    var cv_flag = false;
    var defaultVerse = null;
    var selectedVerse = null;
    var verse = { verseType: '' };
    
    /**
     * @method render
     * @chainable
     */
    proto.render = function() {
        var noc = false;
        this.productModel = new ProductModel();
        var model = this.model;
        var info = model.info;
        var options = model.options;
        var verseOptions,stdVerse = null;
        var defaultVerseOption = 'Standard Verse';
        var value = model.getValue() || (info && info['default']);

        if (model.desc.indexOf(Content.get('Product ID')) > -1){
            noc = true;
        } else {
            noc = false;
        }
        
        

        
            if (model.id === 'verse') {
            stdVerse = options._items[0] ? options._items[0].desc : null;
            VerseModel.verseCode = VerseModel.verseCode ? VerseModel.verseCode : (options._items[0] ? options._items[0].id : null);
            verseOptions = ['No Verse', 'Standard','Custom'];
            verse = {verseType : 'STANDARD', verseCode : VerseModel.verseCode};
            if (this.productModel.getVerseType() === 'STANDARD') {
                if (this.productModel.getVerseCode()) {
                    VerseModel.verseCode = this.productModel.getVerseCode();
                    for (var i=0; i<options._items.length;i++) {
                        if(options._items[i].id === this.productModel.getVerseCode())
                            stdVerse = options._items[i].desc;
                    }
                }
                verse = {verseType : 'STANDARD', verseCode : VerseModel.verseCode};
                VerseModel.selectedVerseImg = Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE +VerseModel.verseCode+this.verseOrientation+'?wid=250';
                if (this.verseOrientation === 'V') {
                    VerseModel.selectedVerseImg = VerseModel.selectedVerseImg+'&hei=110';
                }
                 var selectedVerseImg = VerseModel.selectedVerseImg ;
                $('.js-verse-selected').find('#stdVerse').attr('src',selectedVerseImg);
            } else if (this.productModel.getVerseType() === 'CUSTOM') {
                defaultVerseOption = 'Custom Verse';
                verse = {verseType : 'CUSTOM'};
            } else if (this.productModel.getVerseType() === '') {
                defaultVerseOption ='No Verse';
                verse = {verseType : ''};
            }
            EventController.emit(ProductEvents.VERSE_CHANGE, verse);
            $('.cvBlock').hide();
            $('#PERSONNALIZATION').hide();
        }
        else if (options && options._items.length <= 1) {
            return this;
        }
        var isCVBlockExists = this.isCVBlockExist;
        
        
        if(isCVBlockExists === 'true'){
            $('.cvBlock').show();
            $('#PERSONNALIZATION').show();
        }
        else{
            $('.cvBlock').hide();
            $('#PERSONNALIZATION').hide();
        }
        this.$view
            .html(this.template({
                id: model.id,
                desc: model.desc,
                isRequired: model.isRequired,
                options: options && options._items,
                noc: noc,
                baseAppUrl: baseAppUrl,
                host_url: host_url,
                verseOptions: verseOptions ? verseOptions : null,
                stdVerse: stdVerse,
                cvAvail: $('.vsBlock'),
                personalization: Settings.ULTIMATE_PERSONALIZATION,
                'default': defaultVerseOption,
                selectedVerseImg: VerseModel.selectedVerseImg,
                cvPrice: Surcharge.getPrice('verse'),
                isCVBlockExist: isCVBlockExists
            }));
        return this;
    };

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        this.$view
            .on(DomEvents.CLICK, Classes.verse_ACTION_SELECTOR, this.onActionClick)
            .on(DomEvents.CHANGE, this.onVerseChange);
        EventController.on(ProductEvents.CHANGE, this.onProductChange);

        return this;
    };

    /**
     * @method update
     * @param {Any} [data]
     * @chainable
     */
    proto.update = function(data) {
        var type;
        var model = this.model;
        
        this.model.setValue(data);

        return this.render();
    };

    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        this.$view
            .off(DomEvents.CLICK, Classes.verse_ACTION_SELECTOR, this.onActionClick)
            .off(DomEvents.CHANGE, this.onVerseChange);
        EventController.off(ProductEvents.CHANGE, this.onProductChange);

        return this;
    };

    /**
     * @method isValid
     * @chainable
     */
    proto.isValid = function() {
        var value;
        var data;
        var model = this.model;
        
        return base.isValid.call(this);
    };

    /**
     * @method onActionClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onActionClick = function(event) {
        var action = $(event.target).data('action');

        if (action) {
            event.preventDefault();
            event.stopPropagation();

            EventController
                .emit(ActionEvents.HIDE_ALL)
                .emit(action, this.update);
        }
    };
    
    $(window).resize(function() {
        var a = $('#CVHelp').offset();
        if(a){
        var tp = a.top;
        var lt = a.left;
        $('.popover').offset({top: tp+32 ,left: lt-340});
        $('.arrow').offset({top: tp+25,left:lt-2});
        }
    });

   
    /**
     * @method onVerseChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onVerseChange = function() {
        var href;
        var model = this.model;
        var value = this.$view.find('input:checked').val();

        if (model.id === 'productId') {
            this.model.query.skuId = value;
        }

        model.setValue(value);
        this.isValid();
        var i = 0;
        
        if (model.id === 'verse') {
            if (value === 'Standard Verse') {
                verse = { verseType: 'STANDARD', verseCode : VerseModel.verseCode};
                $('.vsBlock').hide();
                if(typeof window.box != 'undefined') {
                    for (i=0; i<window.box.length; i++) {
                        window.box[i].$el.toggleClass(Classes.ZOOMBOX_ENABLED, false);
                        window.box[i].$overlay.toggleClass(Classes.ZOOMBOX_HIGHLIGHT, false);
                     }
                }
                
            } else if(value === 'Custom Verse') {
                $('.vsBlock').show();
                verse = { verseType: 'CUSTOM' };
                VerseModel.verseType = 'CUSTOM';
            } else {
                verse = {};
                VerseModel.verseType = '';
                $('.vsBlock').hide();
                if(typeof window.box != 'undefined') {
                    for (i=0; i<window.box.length; i++) {
                        window.box[i].$el.toggleClass(Classes.ZOOMBOX_ENABLED, false);
                        window.box[i].$overlay.toggleClass(Classes.ZOOMBOX_HIGHLIGHT, false);
                    }
                }
            }
            
            EventController.emit(ProductEvents.VERSE_CHANGE, verse);
        }
    };

    proto.updateConstruction = function(product) {
        this.construction = product.getConstruction() ;
    };

    proto.updateSelectedStdVerse = function(product) {
        this.verseOrientation = product.getVerseOrientation();
        VerseModel.selectedVerseImg = Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE +VerseModel.verseCode+this.verseOrientation+'?wid=250';
        if(this.verseOrientation === 'V'){
            VerseModel.selectedVerseImg = VerseModel.selectedVerseImg+'&hei=110';
        }
        var selectedVerseImg = VerseModel.selectedVerseImg ;
        $('.js-verse-selected').find('#stdVerse').attr('src',selectedVerseImg);
    };

    proto.isCVBlockExists = function(product) {
        if(product.getBlock('VS') ){
            this.isCVBlockExist = 'true';
            $('.cvBlock').show();
            $('#PERSONNALIZATION').show();

            // DCOM-16284
            // if not edit mode, productConfiguration will be null else it will have _blocks object having VS object as under
            // this.model.productInfo.productConfiguration._blocks.VS.userInput
            if(this.model.productInfo.productConfiguration) {
                this.onVerseChange();
            }
        }
        else {
            this.isCVBlockExist = 'false';
            $('.cvBlock').hide();
            $('#PERSONNALIZATION').hide();
        }
        
    };
    /**
     * @method onProductChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onProductChange = function(event, product) {
        this.updateSelectedStdVerse(product);
        this.updateConstruction(product);
        this.isCVBlockExists(product);
    };

    return verseQuestionController;
});