<fieldset>
{{! used in logoBrowse to show logos }}
<ul class="blocks blocks_5up">
    {{#each items}}
        <li>
            <input type="radio" class="designRadio{{#is id ../default}} isChecked{{/is}}" id="{{../id}}_{{id}}" name="{{../id}}" value="{{id}}"{{#is id ../default}} checked{{/is}} />
            <label for="{{../id}}_{{id}}" title="{{info.desc}}">
                <span id="thumbnail-{{../id}}-{{@index}}" class="designBox"{{#if image}} style="background-image:url('{{../../imageBase}}/{{image}}{{../../imageExt}}');"{{/if}}></span>
                {{description}} <br/>
            </label>
			<span class="id">
                {{id}}
            </span>
        </li>
    {{/each}}
</ul> {{! /blocks }}
<div class="pages_container clipart-page-container">
    <ul class="blocks mix-pages js-mix-pages ">
        {{#each pages}}
        <li>    
            <input type="radio" class="designRadio" id="Page_{{id}}" name="page" value="{{id}}" {{#is id ../current_page}} checked{{/is}} />
            <label for="Page_{{id}}" title="{{id}}">
                <span class="designBox {{#is id ../current_page}} c-page{{/is}}" id="{{id}}">{{id}}</span>
            </label>        
        </li>
        {{/each}}
    </ul>
</div>
</fieldset>
