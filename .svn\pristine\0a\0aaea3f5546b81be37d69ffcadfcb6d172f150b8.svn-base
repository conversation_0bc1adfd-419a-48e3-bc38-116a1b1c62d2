define(function(require) {
    'use strict';

    var ActionEvents = require('../constants/ActionEvents');
    var Classes = require('../constants/Classes');
    var ClipArtProvider = require('../providers/ClipArt');
    var ConfigurationProvider = require('../providers/Configuration');
    var Content = require('i18n!../constants/nls/en-us/Content');
    var Controller = require('./Controller');
    var DomEvents = require('../constants/DomEvents');
    var EventController = require('./Event');
    var ProductEvents = require('../constants/ProductEvents');
    var SessionStorage = require('../providers/SessionStorage');
    var Settings = require('../constants/Settings');
    var StateEvents = require('../constants/StateEvents');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var Query = require('models/Query');
    var q = require('q');
    var Registry = require('util/Registry');

    /**
     * @class App.Controllers.FBT
     * @extends App.Controllers.Controller
     *
     * @constructor
     * @param {Object} config
     */
    function FBTController(config) {
        bindAll(this,
            'initClipArt',
            'onHideAction',
            'onShowAction',
            'onNextClick',
            'onPrevClick',
            'onStateChange',
            'onProductChange'
        );

        /**
         * @property categories
         * @type {App.Models.ClipArt.Collections.Categories}
         * @default null
         */
        this.rec = null;
        this.product = null;
        this.recSkuId = null;
        
        /**
         * @property query
         * @type {App.Models.Query}
         */
        this.query = Query.getInstance();

        Controller.call(this, config);

        
        /**
         * Omniture site prefix
         *
         * @type {String}
         */
        this.site_prefix = Settings.OMNITURE_PREFIX;
    }

    var proto = inherits(FBTController, Controller);

    /**
     * @method init
     * @return {Promise}
     */
    proto.init = function() {
        
    };

    /**
     * @method initClipArt
     * @return {Promise}
     */
    proto.initClipArt = function(config) {
    
        return ClipArtProvider
            .setConfig(config)
            .getClipArt();
    };

    // -- Accessors ------------------------------------------------------------

    
    proto.getRec = function() {
        return this.query.rec ;
    };
    
    proto.getRecSkuId = function() {
        return this.query.recSkuId ;
    };
    

    // -- Methods --------------------------------------------------------------

    
    /**
     * TODO: refactor this
     *
     * @method render
     * @chainable
     */
    proto.render = function() {
        this.getRec();
        this.getRecSkuId();
        // console.log(this.getRec());
        // console.log(this.getRecSkuId());
        return this;
    };

    /**
     * @method cacheElements
     * @chainable
     */
    proto.cacheElements = function() {
        
        return this;
    };

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        this.$view
            .on(DomEvents.CLICK, Classes.NEXT_SELECTOR, this.onNextClick)
            .on(DomEvents.CLICK, Classes.PREV_SELECTOR, this.onPrevClick);
            
        EventController
            .on(ActionEvents.HIDE_ALL, this.onHideAction)
            .on(StateEvents.CHANGE, this.onStateChange)
            .on(ProductEvents.CHANGE, this.onProductChange);

        return this;
    };

    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        this.$view
            .off(DomEvents.CLICK, Classes.NEXT_SELECTOR, this.onNextClick)
            .off(DomEvents.CLICK, Classes.PREV_SELECTOR, this.onPrevClick);

        EventController
            .off(ActionEvents.HIDE_ALL, this.onHideAction)
            .off(StateEvents.CHANGE, this.onStateChange)
            .off(ProductEvents.CHANGE, this.onProductChange);

        return this;
    };

    

    

    

    

    // -- Event Handlers -------------------------------------------------------

    

    

    /**
     * @method onHideAction
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onHideAction = function() {
        
    };

    /**
     * @method onShowAction
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onShowAction = function(event, done) {
        
  
    };

    /**
     * @method onNextClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onNextClick = function(event) {
        
    };

    /**
     * @method onPrevClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onPrevClick = function(event) {
        event.preventDefault();
        
    };

    /**
     * @method onStateChange
     * @callback
     */
    proto.onStateChange = function(event, state) {
        this.state = state;
        
    };

    
    /**
     * @method onProductChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onProductChange = function(event, product) {
        this.product = product;
        
    };
    
    return FBTController;
});
