define(function(require) {
    'use strict';

    // var HashStateModel = require('./state/Hash');
    // var HistoryStateModel = require('./state/History');
    var Stepper = require('./state/Stepper');

    /**
     * History state stepper
     *
     * @class App.Models.State
     *
     * @constructor
     */
    // return 'state' in history ? HistoryStateModel : HashStateModel;
    return Stepper;
});
