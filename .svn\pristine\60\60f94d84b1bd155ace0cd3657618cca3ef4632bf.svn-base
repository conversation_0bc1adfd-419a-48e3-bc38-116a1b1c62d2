define(function (require) {
    'use strict';

    var $ = require('jquery');
    var AbstractProvider = require('./Abstract');
    var UITypesCollection = require('../models/ui/collections/Types');
    var bindAll = require('mout/object/bindAll');
    var controls = require('text!../../data/REF_CONTROLS_mobile.xml');
    var inherits = require('mout/lang/inheritPrototype');
    var q = require('q');

    /**
     * @class App.Providers.UiTypes
     * @extends App.Providers.Abstract
     *
     * @constructor
     */
    var UITypesProvider = function () {
        bindAll(this, '_onResponseReceived');

        AbstractProvider.apply(this, arguments);
    };

    var proto = inherits(UITypesProvider, AbstractProvider);

    /**
     * Makes a call for the required UI Types, or returns the stored
     * types via promise
     *
     * @method getTypes
     * @return {Promise}
     */
    proto.getTypes = function() {
        if (this.promise) {
            return this.promise;
        }

        this.promise = q(controls)
            .then(this._onResponseReceived)
            .fail(this._onError);

        return this.promise;
    };

    /**
     * Handles the response from the ajax call
     *
     * @method _onResponseReceived
     * @param {HTMLDocument} data
     * @private
     */
    proto._onResponseReceived = function(data) {
        // success! we have the data, store and resolve the promise;
        var response = $.xml2json(data);
        var model = new UITypesCollection(data);

        if (response.error) {
            throw new Error(response.error);
        }

        return model;
    };

    return new UITypesProvider();
});
