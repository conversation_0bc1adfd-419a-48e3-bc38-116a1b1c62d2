define(function (require) {
    'use strict';

    var AbstractCollection = require('./Abstract');
    var RegionModel = require('../Region');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.Collections.Fonts
     * @extends App.Models.Collections.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} FontOptions
     */
    var RegionCollection = function (RegionOptions) {
        AbstractCollection.call(this, RegionOptions);
    };

    var proto = inherits(RegionCollection, AbstractCollection);
    var base = AbstractCollection.prototype;

    /**
     * @property itemClass
     * @type {App.Models.Font}
     */
    proto.itemClass = RegionModel;

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param {object} json the original JSON data.
     */
    proto.fromJSON = function(json) {
        var items;

        json = this.stripInvalidFields(json).root;

        items = json.states.option;

        base.fromJSON.call(this, items);
    };

    return RegionCollection;
});
