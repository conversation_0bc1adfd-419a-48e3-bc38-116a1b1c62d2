define(function (require) { // jshint ignore:line
    'use strict';

    var $ = require('jquery');
    var AbstractProvider = require('./Abstract');
    var Settings = require ('../constants/Settings');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Providers.Region
     * @extends App.Providers.Abstract
     *
     * @constructor
     */
    var LogoMixProvider = function () {
        bindAll(this, '_onResponseReceived');

        AbstractProvider.apply(this, arguments);
    };

    var proto = inherits(LogoMixProvider, AbstractProvider);

    /**
     *
     * @method getRegions
     *
     * @param {bool} flushCache
     *
     * @return {Promise}
     */
    proto.getLogos = function(params) {
        var api_path = Settings.SVC_LOGOMIX_URL
        this.promise = this
            .get(api_path, params)
            .then(this._onResponseReceived)
            .fail(this._onError);


        return this.promise;
    };

    proto.uploadLogos = function(params) {
       var endPoint = Settings.SVC_UPLOAD;
       this.promise = this  
       .post(endPoint, params, 'html', {
           processData: false,
           contentType: 'multipart/form-data',
       })
       .then(this._onResponseReceived)
       .fail(this._onError);

    //    this.promise = this
    //        .get(api_path, params)
    //        .then(this._onResponseReceived)
    //        .fail(this._onError);


       return this.promise;
   };

    /**
     * Handles the response from the ajax call
     *
     * @method _onResponseReceived
     *
     * @param {HTMLDocument} data
     * @private
     */
    proto._onResponseReceived = function(data) {
        return data;
    };

    return new LogoMixProvider();
});
