/* @import './base/elements.css'; */
body {
  background-color: #eff0f1;
  font-family: Arial !important;
}

/* @import './modules/radio.css'; */
.radio + label {
  color: #027ab7;
  font-family: Arial;
}
.radio.isChecked + label {
  font-family: Arial;
}

.radio + label {
  font-size: 14px;
}

.radio + label:before {
  background-position: 0px -48px;
}
.radio.isChecked + label:before {
  background-position: 0 -73px;
}

/* @import './modules/button.css'; */
.button,
.button:hover {
  background: #c00;
  color: #ffffff;
  font-family: HelveticaNeueLTStd-Roman, sans-serif;
  height: 40px;
  font-size: 13px;
  border-radius: 5px;
  padding: 8px 8px;
  text-transform: uppercase;
}
.button:focus {
  background: #c00;
  text-decoration: none;
  color: #353535;
}

@media (hover: hover) and (pointer: fine) {
  .button:hover {
    background: #c00;
    text-decoration: none;
    color: #353535;
  }
}

@media (pointer: coarse) {
  .button:active {
    background: #c00;
    text-decoration: none;
    color: #353535;
  }
}

.button_neutral {
  background: #0092db none repeat scroll 0 0;
  border: 1px solid #0092db;
  color: #ffffff;
  font-size: 12px;
  font-weight: normal !important;
}
.button_neutral:focus,
.button_neutral:hover {
  background: #bcd5e3;
  color: #353535;
  font-size: 12px;
}

#button_prev {
  padding-top:10px !important;
  padding-bottom:9px !important;
  font-weight: 700 !important;
  font-size: 12px !important
}
#button_prev:hover {
  padding-top:10px !important;
  padding-bottom:9px !important;
  font-weight: 700 !important;
  font-size: 12px !important;
  border: 1px solid #bcd5e3;
}

.button_file_browse {
  height: 20px;
}
.js-logo-details .hdg_h4,
.js-logo-details .hdg_h4 {
  font-family: Arial !important;
}
.button_grey {
  background: #757575 !important;
}
.proof-button {
  background: #0092db;
  border: 1px solid #0092db;
  width: 212px;
  color: #ffffff;
  font-size: 12px;
  font-weight: 400;
}
.proof-button:hover,
.proof-button:focus {
  background: #0092db;
  color: #353535;
}

.button_grey:focus,
.button_grey:hover {
  background: #757575 !important;
  color: #ffffff !important;
}

/* @import './modules/checkbox.css'; */

.foldingHelpLabel {
  width: 142px !important;
}

/* @import './modules/designSwatch.css'; */
.designRadio.isChecked + label > .designSwatchImage {
  border: 2px solid #c00;
}

.designSwatchDesc {
  font-family: Arial;
  font-size: 10px;
}

.designBox_active::before,
.designBox_active:hover::before {
  border: 2px solid #c00 !important;
}

.designRadio.isChecked + label > .designBox:before {
  border: 2px solid #c00;
}

.choice-list .isChecked+label {
  border-color: #c00 !important;
  box-shadow: inset 2px 2px 0px #c00, inset -2px -2px 0px #c00 !important; 
}

/* @import './modules/error.css'; */
.error {
  margin-bottom: 4px;
  font-size: 16px;
  line-height: 1.075;
  color: #d61120;
}

.error-subtext {
  float: left;
  color: #dd1743;
}

.error-head {
  color: #dd1743;
  font-family: arial;
  font-size: 14px;
  line-height: 1.075;
  margin-bottom: 10px;
}

.vList .error {
  font-family: 'Source Sans Pro', ​sans-serif !important;
}
.vList .error-head {
  font-size: 16px !important;
  line-height: 1.075;
}

/* @import './modules/hdg.css'; */

.hdg {
  color: #231f20;
  margin-bottom: 8px;
  font-family: HelveticaNeueLTStd-Roman, sans-serif;
  font-weight: bold;
  max-width: 490px;
}
.hdg_h1 {
  font-family: HelveticaNeueLTStd-Roman, sans-serif;
  font-weight: normal;
}
.hdg_h2 {
  color: #c00 !important;
  font-size: 17px;
}
.hdg_sub_h2 {
  font-size: 16px;
  margin-bottom: 8px;
}
.price {
  color: #dd1743 !important;
  font-family: HelveticaNeueLTStd-Roman, sans-serif;
  font-weight: normal !important;
  font-size: 20px !important;
}
.mix-hdg_red {
  color: #d61120;
}
.mix-hdg_blue {
  color: #259cda !important;
}
.media-body {
  font-family: HelveticaNeueLTStd-Roman, sans-serif;
}
.js-logo-controls .hdg {
  margin-bottom: 16px !important;
}

/* @import './modules/link.css'; */
.link {
  text-decoration: none;
  color: #0092db;
  cursor: pointer;
  font-family: HelveticaNeueLTStd-Roman, sans-serif;
  font-size: 15px;
  font-weight: bold;
  cursor: pointer;
}

.link_button {
  display: inline-block;
  padding: 7px 16px;
  border: none;
  background: none;
  font-family: HelveticaNeueLTStd-Roman, sans-serif;
  font-size: 13px;
  font-weight: 700;
  vertical-align: middle;
  text-decoration: none !important;
  text-transform: uppercase;
}
.green {
  color: #749c38 !important;
}

/* @import './modules/site.css'; */
.site {
  border: 2px solid #d9e0e5;
  background: #ffffff;
}
.site-hd-right {
  padding: 0px 15px 0px 0px;
}
.site-hd {
  background: #f9f9f9;
  height: 100px;
}
/*DCOM-16528
 .site-bd {
  border-top: 2px solid #eef1f3;
  padding: 25px 20px 25px 60px;
  position: relative;
  background-color: #ffffff;
}
.site-bd:before {
  position: relative;
}DCOM-16528
 */
.cfgSiteLogo {
  float: left;
  margin: 5px 35px 0px 0px;
}
.progressBlocks {
  margin: 0 0 0 0;
}
.descProgress {
  font-size: 12px;
}

[data-id='HIDDEN_LOGO'] {
  margin-top: 5px !important ;
}

[data-id='LS'] {
  margin-top: 20px !important ;
}

[data-id='SL'] {
  margin-top: 20px !important ;
}
.margin-11px {
  margin-bottom: 11px;
}
.margin-14px {
  margin-bottom: 14px;
}
.vList_std .error .grid-col_3of10 {
  padding-top: 0 !important;
}
.site-bd .grid-col_3of10 {
  padding-top: 20px;
}

.media-body .hdg {
  margin-bottom: 0 !important;
}
.js-proofmodalHeading {
  font-family: 'Arial Bold', 'Arial';
  font-weight: 700;
  font-style: normal;
  font-size: 26px;
  color: #c00;
}

.fancybox-skin {
  position: relative;
  border-radius: 10px;
  background-color: #333;
  border: 10px solid #333;
  z-index: -2;
}

.fancybox-skin:after {
  content: '';
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  background-color: #fff;
  z-index: -1;
  top: 0;
  left: 0;
}
.calcelbox {
  float: right;
  font-family: 'Arial Bold', 'Arial';
  font-weight: 700;
  font-style: normal;
  font-size: 14px;
  color: #259cda;
  text-align: center;
  cursor: pointer;
}
.js-proofmodalContent1 p {
  font-family: 'Arial Regular', 'Arial';
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  color: #333333;
  text-align: left;
  line-height: normal;
  padding-top: 10px;
}

.js-proofmodalContent2 p {
  font-family: 'Arial Bold', 'Arial';
  font-weight: 700;
  font-style: normal;
  font-size: 14px;
  color: #231f20;
}

.js-proofmodalContent3 label {
  font-family: 'Arial Regular', 'Arial';
  font-weight: 400;
  font-style: normal;
  font-size: 13px;
  color: #333333;
  text-align: left;
  line-height: normal;
}

.js-proofmodalContent3 p {
  font-family: 'Arial Regular', 'Arial';
  font-weight: 400;
  font-style: normal;
  font-size: 13px;
  color: #333333;
  text-align: left;
  line-height: normal;
  margin-left: 28px;
  margin-top: 5px;
}
.js-proofmodalContent3 p span {
  font-weight: 800;
}

.cancelbtn {
  font-family: 'Arial Bold', 'Arial';
  font-weight: 700;
  font-style: normal;
  font-size: 14px;
  color: #259cda;
  text-align: center;
  padding-left: 40px;
  cursor: pointer;
}
.download-box {
  float: left;
  cursor: pointer;
}
.cancel-box {
  float: left;
  padding-top: 13px;
}
.proof-actions {
  margin: 20px 0;
  float: left;
}
.btn-download {
  width: 215px;
  padding: 13px 0;
  cursor: pointer;
}
.btn-active {
  background: #027ab7 !important;
}

.proof-actions .link-download {
  pointer-events: none;
}

.proof-actions .link-download-active {
  pointer-events: all;
  display: block;
}
/*
.make-visible {	
display: block !important;
}
*/
.js-proofmodalContainer {
  float: left;
  margin-bottom: 15px;
}
.js-proofmodalImager {
  float: left;
}

.js-proofmodalContent {
  float: left;
  margin-left: 15px;
  font-family: 'Arial Regular', 'Arial';
  font-size: 14px;
  line-height: normal;
}
.js-proofmodalContentBold {
  font-weight: bold;
  margin-bottom: 5px;
}
.js-proofmodalContentList ul {
  margin: 5px 0 0 25px;
  padding: 0;
  list-style: inherit;
}

/* @import './modules/step.css'; */
.step {
  color: #c00;
  line-height: 1;
  font-weight: normal;
  text-transform: none;
  font-family: 'NewsGothicBold';
}
.proof-content {
  margin: 15px;
}
.proof-text {
  margin-bottom: 10px;
}
.proof-content p {
  font-family: 'Arial Italic', 'Arial';
  font-weight: 400;
  font-style: italic;
  font-size: 11px;
  line-height: 13px;
}

#basic-modal-content {
  display: none;
}

/* Overlay */
#simplemodal-overlay {
  background-color: #000;
  cursor: wait;
}

/* Container */
#simplemodal-container {
  height: 360px;
  width: 600px;
  color: #bbb;
  background-color: #333;
  border: 4px solid #444;
  padding: 12px;
}
#simplemodal-container .simplemodal-data {
  padding: 8px;
}
#simplemodal-container code {
  background: #141414;
  border-left: 3px solid #65b43d;
  color: #bbb;
  display: block;
  font-size: 12px;
  margin-bottom: 12px;
  padding: 4px 6px 6px;
}
#simplemodal-container a {
  color: #ddd;
}
#simplemodal-container a.modalCloseImg {
  background: url(../img/basic/x.png) no-repeat;
  width: 25px;
  height: 29px;
  display: inline;
  z-index: 3200;
  position: absolute;
  top: -15px;
  right: -16px;
  cursor: pointer;
}
#simplemodal-container h3 {
  color: #84b8d9;
}

/* @import './modules/tooltip.css'; */
.popover {
  width: 445px;
  background-color: #aedfe8;
}

#CopiesHelp,
#NumberingHelp {
  margin-bottom: -4px;
  margin-left: 0px !important;
}

.popover .arrow1,
.popover .arrow1:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.popover .arrow1 {
  border-width: 11px;
}

.popover .arrow1:after {
  border-width: 10px;
  content: '';
}
.popover.bottom .arrow1 {
  margin-left: 342px;
  border-bottom-color: #fff;
  border-top-width: 0;
  top: -11px;
}
.popover.bottom .arrow1:after {
  top: 1px;
  margin-left: -10px;
  border-bottom-color: #aedfe8;
  border-top-width: 0;
  content: ' ';
}

/* @import './modules/uploading.css'; */
.progressbar {
  background: #e9e9e9 none repeat scroll 0 0;
  border-radius: 11px;
  height: 13px;
  padding-left: 3px;
  padding-right: 3px;
  padding-top: 3px;
  width: 100%;
}
.progress {
  background: #c34924;
  border-radius: 11px;
  height: 10px;
}

/* @import './modules/zoomPreview.css'; */
/*DCOM-16528
 #topLens {
  background: #0092db;
}
.topLens {
  top: 42px;
} DCOM-16528
*/
#viewZoomText {
  margin-left: 6px;
  text-align: center;
  width: 24px;
}
/*DCOM-16528
 .lens {
  font-family: Arial;
  font-size: 11px;
  margin-right: 20px;
}

.lens::before {
  padding-right: 3px;
}DCOM-16528
 */

/* @import './modules/txt.css'; */
.txtLarge {
  font-family: HelveticaNeueLTStd-Roman, sans-serif;
  font-size: 15px;
  cursor: auto !important;
}

/* @import './modules/blocks.css'; */
.blocks {
  margin-bottom: 34px;
}
.txt_style {
  font-weight: bold;
  font-size: 18px;
  letter-spacing: 0.02em;
}
.detailBox_active .detailBox-header,
.detailBox_active,
.zoombox_connector {
  background-color: #c00;
  border-color: #c00;
}
.zoombox {
  background-color: rgba(255, 51, 51, 0.5);
}

/* @import './modules/inputBox.css'; */
.inputBox {
  font-family: Arial;
  font-size: 15px;
}
/* @import './modules/comment.css'; */
.commentCopy {
  background-color: rgb(255, 255, 204);
  padding: 10px 12px 10px 12px;
  margin-bottom: 10px;
  font-size: 14px;
}
.commentLabel {
  color: #333;
}
.commentTextArea {
  width: 98%;
  height: 60px;
  resize: none;
  user-select: text;
  -moz-user-select: text;
  -webkit-user-select: text;
  -ms-user-select: text;
}

.logo-option-button-CUSTOM, 
.logo-option-button-CUSTOM:hover,
#UploadLogo-BrowseForFile   {
  padding-top:14px;
  max-height:40px;
}

@media (max-width: 767px) {
  .price {
    font-size: 14px !important;
  }
}
