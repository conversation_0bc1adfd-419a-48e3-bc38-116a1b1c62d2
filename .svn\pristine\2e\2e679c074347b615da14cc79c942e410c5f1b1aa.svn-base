define(function (require) { // jshint ignore:line
    'use strict';

    var AbstractProvider = require('./Abstract');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Providers.SessionId
     * @extends App.Providers.Abstract
     *
     * @constructor
     */
    var SessionIdProvider = function () {
        bindAll(this, '_onResponseReceived');

        AbstractProvider.apply(this, arguments);
    };

    var proto = inherits(SessionIdProvider, AbstractProvider);

    /**
     * Makes a call for the user's current Session ID
     *
     * @method getSessionId
     *
     * @param {bool} flushCache
     *
     * @return {Promise}
     */
    proto.getSessionId = function(flushCache) {
        if (this.promise && flushCache !== true) {
            // data will be cached after the first call
            return this.promise;
        }

        this.promise = this
            .get(Settings.SVC_SESSION, {}, 'text')
            .then(this._onResponseReceived)
            .fail(this._onError);

        return this.promise;
    };

    /**
     * Handles the response from the ajax call
     *
     * @method _onResponseReceived
     *
     * @param {HTMLDocument} data
     * @private
     */
    proto._onResponseReceived = function(data) {
        if (!data) {
            throw new Error(data);
        }

        return data;
    };

    return new SessionIdProvider();
});
