define(function (require) {
    'use strict';

    var $ = require('jquery');
    var AbstractModel = require('../Abstract');
    var UiQuestionsCollection = require('./collections/Questions');
    var Content = require('i18n!../../constants/nls/en-us/Content');
    var UiTabsCollection = require('./collections/Tabs');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.Ui.Step
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} UiStepValues
     */
    var UiStepModel = function (UiStepValues) {
        bindAll(this, 'isValid');

        AbstractModel.call(this, UiStepValues);
    };

    var proto = inherits(UiStepModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} UiStepValues
     * @chainable
     */
    proto.init = function(UiStepValues) {
        /**
         * @property id
         * @default {null}
         * @type {string}
         */
        this.id = null;

        /**
         * @property code
         * @default {null}
         * @type {string}
         */
        this.description = null;

        /**
         * @property progressTitle
         * @default {null}
         * @type {string}
         */
        this.progressTitle = null;

        /**
         * @property tlId
         * @default {null}
         * @type {string}
         */
        this.tlId = null;

        /**
         * @property description
         * @default {null}
         * @type {string}
         */
        this.prev = null;

        /**
         * @property type
         * @default {null}
         * @type {string}
         */
        this.next = null;

        /**
         * @property trackEvent
         * @default {null}
         * @type {object}
         */
        this.trackEvent = null;

        /**
         * @property questions
         * @default {null}
         * @type {array}
         */
        this.questions = null;

        /**
         * @property tabs
         * @default {null}
         * @type {array}
         */
        this.tabs = null;

        // run the parent init method to parse determine the data type
        base.init.call(this, UiStepValues);

        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param json the original JSON data.
     * @chainable
     */
    proto.fromJSON = function(json) {
        if (!json.id) {
            json = this.stripInvalidFields(json);

            if (!json.id && json.step !== undefined) {
                json = json.step;
            }
        }

        this.id = json.id;
        this.description = json.desc;
        this.progressTitle = json.progressTitle;
        this.tlId = json.tlId;
        this.prev = Content.get(json.prev);
        this.next = Content.get(json.next);
        this.trackEvent = this.toJSON(json.trackEvent);
        this.questions = null;
        this.tabs = [];

        this.questions = new UiQuestionsCollection(json.question);
        this.tabs = new UiTabsCollection(json.tab);

        return this;
    };

    /**
     * @method getQuestions
     * @return {Array.<App.Models.Ui.Collections.Questions>}
     */
    proto.getQuestions = function() {
        var questions = this.questions._items
            .concat(this.tabs.getQuestions()._items);

        return new UiQuestionsCollection(questions);
    };

    /**
     * Recursively sets product info on all tabs and questions.
     *
     * @method setInfo
     * @param {Object} info
     * @param {App.Models.FXG} fxg
     * @param {App.Models.CustomerProfile} customer
     * @chainable
     */
    proto.setInfo = function(info, fxg, customer) {
        this.trackGroup = info.productInfo.group;
        var y = -1;
        if(this.tabs._items.length && !info.productInfo.question.some(function(elem) { return(elem.fieldType == "numeric");})) {
            this.tabs._items.some(function(elem) { if(elem.description == "Account Information" || elem.description == "Start Number" ) y = elem.id;});
            if(y != -1) this.tabs._items.splice(y-1,1);
        }
        
        this.tabs.setInfo(info, fxg, customer);
        this.questions.setInfo(info, fxg, customer);

        return this;
    };

    /**
     * Returns an object containing all selections made by
     * the user for all contained questions
     *
     * @method getValues
     * @returns {Object}
     */
    proto.getValues = function() {
        var qValues = this.questions.getValues();
        var tValues = this.tabs.getValues();

        var blocks = $.merge(
            qValues.blocks,
            tValues.blocks
        );

        var surcharges = $.merge(
            qValues.surcharges,
            tValues.surcharges
        );

        var values = $.extend(
            qValues.values,
            tValues.values
        );

        return {
            id: this.id,
            description: this.description,
            tlId: this.tlId,
            values: values,
            blocks: blocks,
            surcharges: surcharges
        };
    };

    /**
     * @method isValid
     * @return {Boolean}
     */
    proto.isValid = function() {
        if (this.questions.isValid() !== true) {
            return false;
        }

        if (this.tabs.isValid() !== true) {
            return false;
        }

        return true;
    };

    return UiStepModel;
});
