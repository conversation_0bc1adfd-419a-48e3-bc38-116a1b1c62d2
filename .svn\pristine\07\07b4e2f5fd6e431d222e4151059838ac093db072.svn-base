<?xml version="1.0" encoding="UTF-8"?>
<addToCart>
    {{!-- Comment Test --}}
    <HeaderInfo>
        {{tag 'profileId' query.profileId}}
        {{tag 'productId' query.skuId}}
        {{tag 'sessionId' query.sessionId}}
        {{tag 'orderId' query.orderId}}
        <lineItemId></lineItemId>
        {{tag 'fulfillmentId' query.fulfillmentId}}
        {{tag 'totalPrice' pricing.total}}
        <customTextExistsInProfile></customTextExistsInProfile>
        {{#if isConfig}}
             {{tag 'configId' query.configId}}
        {{else}}
             {{tag 'configId' query.cartConfigId}}
        {{/if}}     
    </HeaderInfo>
    <productDetails>
        {{tag 'productCode' query.productId}}
        {{tag 'productCodeName' info.desc}}
        {{tag 'productTypeDesc' info.typeDesc}}
        {{tag 'selectedProductId' data.values.productId.value}}
        {{tag 'selectedProductIdDesc' data.values.productId.description}}
        {{tag 'wizWigUrl' wizWigUrl}}
        <peelAndSeal>{{peelAndSeal}}</peelAndSeal>
        <folding>{{folding}}</folding>
        <signature>{{signature}}</signature>
        {{#if verse}}
            {{tag 'verse' verse.verseCode type=verse.verseType }}
        {{else}}
            <verse type="" />
        {{/if}}        
        {{tag 'quantity' data.values.quantity.value}}
        {{tag 'basePrice' pricing.base}}
        <customizedOptions>
            {{tag 'selectedProductId' data.values.productId.value}}
            {{tag 'selectedProductIdDesc' data.values.productId.description}}
            {{#if doLineItemComments}}
                {{tag 'lineItemComments' ../data.values.COMMENT.value}}
            {{/if}}
            {{tag 'productType' info.type}}
            {{tag 'quantity' data.values.quantity.value}}
            <selectedInks>
                {{tag 'firstInkId' data.values.inkColor1.value type=data.values.inkColor1.type}}
                {{tag 'secondInkId' data.values.inkColor2.value type=data.values.inkColor2.type}}
            </selectedInks>
            {{tag 'typeFaceId' data.values.typestyle.value}}
            {{tag 'routingNumber' data.values.routingNumber.value}}
            {{tag 'accountNumber' data.values.accountNumber.value}}
            <selectedMatrix>
                {{tag 'optionInternalDesc' data.values.matrix1.sku}}
                {{tag 'matrix1OptionId' data.values.matrix1.value}}
                {{tag 'matrix1OptionDesc' data.values.matrix1.label}}
                {{tag 'matrix1LongDesc' data.values.matrix1.matrix1OptionLongDesc}}
                {{tag 'quantity' data.values.quantity.value}}
            </selectedMatrix>
            <selectedLayout>
                {{tag 'layoutId' data.values.layout.code}}
                {{tag 'imprintSpecId' data.values.layout.id}}
                {{tag 'selectedNumbering' data.values.numbering.value}}
                <selectedReverseNumbering>{{#is data.values.reverseNumbering.value 'reverseNumbering'}}true{{else}}false{{/is}}</selectedReverseNumbering>
                {{#each selectedLayout.location}}
                <imprintLocations>
                    {{tag 'locationId' id}}
                    {{tag 'locationDesc' desc}}
                    {{tag 'imprintTypestyle' ../data.values.typestyle.value}}
                    <imprintType>companyName</imprintType>
                    {{#each block}}
                    <imprintBlocks>
                        {{#is id 'EI'}}
                            {{tag 'blockCode' id}}
                            {{tag 'blockDesc' desc}}
                            {{#each lines}}
                               <userInput>
                                    {{tag 'lineCode' id}}
                                    {{tag 'lineCodeDesc' description}}
                                    {{tag 'lineColor' ../../../../data.values.inkColor1.value}}
                                    {{#is ../../../../envelopeReturn 'GCENVELOPE'}}
                                        {{#each input}}
                                        {{tag 'userInput' this}}
                                        {{/each}}
                                    {{else}}
                                        {{#each input}}
                                        <userInput/>
                                        {{/each}}
                                    {{/is}}

                               </userInput>
                            {{/each}}
                        {{else}}
                            {{tag 'blockCode' id}}
                            {{tag 'blockDesc' desc}}
                            {{#is id ../../../logoBlock}}
                                {{#if ../../../../logo}}
                                    <selectedLogoDetails>
                                        {{tag 'logoCode' ../../../../../logo.logoCode}}
                                            {{#if ../../../../../data.values.inkColor2.value}}
                                                {{tag 'logoColor' ../../../../../../data.values.inkColor2.value}}
                                            {{else}}
                                                {{tag 'logoColor' ../../../../../../data.values.inkColor1.value}}
                                            {{/if}}
                                        {{tag 'logoImageName' ../../../../../logo.description}}
                                        {{tag 'logoType' ../../../../../logo.logoType}}
                                        <logoPosition></logoPosition>
                                        {{tag 'logoSource' ../../../../../logo.logoType}}
                                        {{tag 'logoHeight' ../../../../logo.height}}
                                        {{tag 'logoWidth' ../../../../logo.width}}
                                        {{#if ../../../../../logo.logoHash}}
                                        <data>
                                            {{tag 'item' key='logoMixRef' value=../../../../../../logo.logoHash}}
                                            {{tag 'item' key='line1' value=../../../../../../logo.line1}}
                                            {{tag 'item' key='line2' value=../../../../../../logo.line2}}
                                            {{tag 'item' key='filter' value=../../../../../../logo.filter}}
                                            {{tag 'item' key='style' value=../../../../../../logo.style}}
                                            {{tag 'item' key='layout' value=../../../../../../logo.layout}}
                                        </data>
                                        {{else}}
                                            {{#if ../../../../../../logo.data}}
                                            <data>
                                                {{tag 'item' key='logoMixRef' value=../../../../../../../logoDataInfo.logoMixRef}}
                                                {{tag 'item' key='line1' value=../../../../../../../logoDataInfo.line1}}
                                                {{tag 'item' key='line2' value=../../../../../../../logoDataInfo.line2}}
                                                {{tag 'item' key='filter' value=../../../../../../../logoDataInfo.filter}}
                                                {{tag 'item' key='style' value=../../../../../../../logoDataInfo.style}}
                                                {{tag 'item' key='layout' value=../../../../../../../logoDataInfo.layout}}
                                            </data>
                                            {{/if}}
                                        {{/if}}
                                    </selectedLogoDetails>
                                {{/if}}
                            {{/is}}
                            {{#each lines}}
                            <userInput>
                                {{tag 'lineCode' id}}
                                {{tag 'lineCodeDesc' description}}
                                {{#if ../requiredInk}}
                                    {{tag 'lineColor' ../../../requiredInk}}
                                {{else}}
                                    {{#is id 'ML'}}
                                        {{#is ../../../../../../data.values.inkColor2.value.length ">" 0}}
                                            {{tag 'lineColor' ../../../../../../../data.values.inkColor2.value}}
                                        {{else}}
                                            {{tag 'lineColor' ../../../../../../../data.values.inkColor1.value}}
                                        {{/is}}
                                    {{else}}
                                        {{tag 'lineColor' ../../../../../../data.values.inkColor1.value}}
                                    {{/is}}
                                {{/if}}
                                {{#is id 'VS'}}
                                    {{#is ../../../../../verse.verseType 'CUSTOM'}}
                                        {{#each input}}
                                        {{tag 'userInput' this}}
                                        {{/each}}
                                    {{else}}
                                        {{#each input}}
                                         <userInput/>
                                        {{/each}}
                                    {{/is}}
                                {{else}}
                                    {{#each input}}
                                    {{tag 'userInput' this}}
                                    {{/each}}
                                {{/is}}
                            </userInput>{{/each}}
                        {{/is}}
                    </imprintBlocks>
                    {{/each}}
                </imprintLocations>
                {{/each}}
            </selectedLayout>
            {{#each data.surcharges}}
            <appliedSurcharges>
                {{tag 'id' id}}
                {{#if price}}{{tag 'price' price}}{{/if}}
                {{tag 'type' type}}
                {{tag 'addToBase' addToBase}}
                {{tag 'description' desc}}
            </appliedSurcharges>
            {{/each}}
        </customizedOptions>
    </productDetails>
</addToCart>