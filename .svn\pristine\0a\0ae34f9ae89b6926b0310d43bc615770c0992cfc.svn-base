<!DOCTYPE html>
<html>
<head>
    <title>Jasmine Spec Runner</title>
    <meta charset="utf-8" />

    <link rel="shortcut icon" type="image/png" href="lib/jasmine-1.2.0/jasmine_favicon.png" />
    <link rel="stylesheet" href="lib/jasmine-1.2.0/jasmine.css" />
    <script src="lib/jasmine-1.2.0/jasmine.js"></script>
    <script src="lib/jasmine-1.2.0/jasmine-html.js"></script>
    <script src="lib/jasmine-promise.js"></script>

    <!-- include source files here... -->
    <script src="../q.js"></script>

    <!-- include spec files here... -->
    <script src="q-spec.js"></script>

    <script>
        (function() {
            var jasmineEnv = jasmine.getEnv();
            jasmineEnv.updateInterval = 1000;

            var htmlReporter = new jasmine.HtmlReporter();

            jasmineEnv.addReporter(htmlReporter);

            jasmineEnv.specFilter = function(spec) {
                return htmlReporter.specFilter(spec);
            };

            var currentWindowOnload = window.onload;

            window.onload = function() {
                if (currentWindowOnload) {
                    currentWindowOnload();
                }
                execJasmine();
            };

            function execJasmine() {
                jasmineEnv.execute();
            }

        })();
    </script>

</head>

<body>
</body>
</html>
