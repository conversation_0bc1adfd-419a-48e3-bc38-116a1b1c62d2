{"name": "jquery-xml2json", "version": "0.0.5", "description": "jQuery plugin to convert XML to JSON.", "license": "MIT", "main": "src/xml2json.js", "repository": {"type": "git", "url": "**************:sergeyt/jQuery-xml2json.git"}, "keywords": ["jQuery-plugin", "xml", "json"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "jose<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"], "dependencies": {}, "devDependencies": {"grunt": "~0.4.1", "grunt-contrib-jshint": "~0.7.1", "grunt-npm": "~0.0.2", "grunt-bump": "~0.0.7", "grunt-auto-release": "~0.0.2", "grunt-cli": "~0.1.11", "karma-chrome-launcher": "~0.1.1", "karma-script-launcher": "~0.1.0", "karma-firefox-launcher": "~0.1.2", "karma-html2js-preprocessor": "~0.1.0", "karma-jasmine": "~0.1.4", "karma-coffee-preprocessor": "~0.1.1", "requirejs": "~2.1.9", "karma-requirejs": "~0.2.0", "karma-phantomjs-launcher": "~0.1.1", "karma": "~0.10.8", "karma-coverage": "~0.1.4", "mocha": "~1.15.1", "karma-mocha": "~0.1.1", "expect.js": "~0.2.0", "grunt-karma": "~0.6.2", "xml2js": "~0.4.0"}, "scripts": {"lint": "grunt lint", "test": "grunt test"}}