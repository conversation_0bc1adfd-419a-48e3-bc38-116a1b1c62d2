define(function (require) {
    'use strict';

    require('jquery-xml2json');

    var $ = require('jquery');
    var bindAll = require('mout/object/bindAll');
    var Settings = require ('../constants/Settings');
    var configControl = require('text!../../data/W2P_Config.xml');
    var q = require('q');

    /**
     * Abstraction for query classes to the Semantria API.
     * Uses jQuery as its transportation method and uses jQuery Promise objects as
     * a method of callbacks.
     *
     * @class App.Providers.Abstract
     *
     * @constructor
     * @param {String} baseUrl
     * @param {App.Models.Configuration} config
     */
    var AbstractProvider = function (baseUrl, config) {
        bindAll(this, '_onError');

        /**
         * The base URL for the api
         * @property baseUrl
         * @type {string}
         */
        this.baseUrl = baseUrl || '';

        /**
         * @property config
         * @type {App.Models.Configuration}
         */
        this.config = config;

        /**
         * Stores the response promise
         * @property promise
         * @type {Promise|Object}
         * @default {null}
         */
        this.promise = null;
    };

    var proto = AbstractProvider.prototype;

    /**
     * Stores the base URL of the api being called
     *
     * @property baseUrl
     * @type {string}
     */
    proto.baseUrl = '';

    /**
     * Method to override original baseURL from the constructor
     *
     * @method setBaseUrl
     * @chainable
     * @param {string} baseUrl
     */
    proto.setBaseUrl = function(baseUrl) {
        this.baseUrl = baseUrl;
        return this;
    };

    /**
     * Sets the config object, if necessary
     *
     * @method setConfig
     * @chainable
     * @param {App.Model.Configuration} config
     */
    proto.setConfig = function(config) {
        this.config = config;
        return this;
    };

    /**
     * Sets up handlers, if any
     *
     * @method setupHandlers
     * @chainable
     * @return {AbstractProvider}
     */
    proto.setupHandlers = function() {
        return this;
    };

    /**
     *
     * @chainable
     * @return {AbstractProvider}
     */
    proto.createChildren = function() {
        return this;
    };

    /**
     * Performs a get request
     *
     * @method get
     *
     * @param {string} endpoint The relative URL endpoint to hit on the API
     * @param {object} params The Associative object of parameters to send with the request
     * @param {string} format The format to expect from the API
     *
     * @return {Promise}
     */
    proto.get = function(endpoint, params, format) {
        if(endpoint == Settings.SVC_CONFIG) {
            return Settings.W2P_XML ? Settings.W2P_XML : Settings.W2P_XML = this.sendRequest(endpoint, params, format, 'GET');
        }
        return this.sendRequest(endpoint, params, format, 'GET');
    };

    /**
     * Performs a post request
     *
     * @method post
     *
     * @param {string} endpoint The relative URL endpoint to hit on the API
     * @param {object} params The Associative object of parameters to send with the request
     * @param {string} format The format to expect from the API
     * @param {object} ajaxParams Parameters object to set up $.ajax();
     *
     * @return {Promise}
     */
    proto.post = function(endpoint, params, format, ajaxParams) {
        return this.sendRequest(endpoint, params, format, 'POST', ajaxParams);
    };

    /**
     * Performs a request to the Deluxe API
     *
     * @method sendRequest
     *
     * @param {string} endpoint The relative URL endpoint to hit on the API
     * @param {object} request The Associative object of parameters to send with the request
     * @param {string} format The format to expect from the API
     * @param {string} method The http method
     * @param {object} ajaxParams sets default $.ajax parameters
     *
     * @return {Promise}
     */
    proto.sendRequest = function(endpoint, request, format, method, ajaxParams) {
        var endpointUrl = this.baseUrl + endpoint;

        var parameters = ajaxParams || {};

        // Compact the XML requests, the endpoints don't like
        // receiving a formatted XML document.
        // if (typeof request === 'string' && (format === 'text' || format === 'xml')) {
        //     request = request.replace(/\>(\s+)\</g, '><');
        // }

        parameters.dataType = format || 'text';
        parameters.data = request;
        parameters.method = method.toUpperCase() || 'GET';
        parameters.type = parameters.method;
        parameters.timeout = 20000; //twenty seconds, temp set to 20 seconds
        //parameters.xhrFields = {
        //    withCredentials: true
        //};

        return q($.ajax(endpointUrl, parameters));
    };

    /**
     * W2P_Config.xml is required by 5 providers;  they can now just use this
     * very basic q(*) call.  This will not pass cookies and is crossdomain friendly.
     *
     * @method getConfig
     * @param 
     * @callback
     */
    proto.getConfig = function() {
        return q(configControl);
    };

    /**
     * TODO: Handle provider errors.
     *
     * @method _onError
     * @param {Any} error
     * @callback
     */
    proto._onError = function(error) {
        var stack = error && error.stack;
        console.error(stack || error);
    };

    /**
     * Converts parameters into url string (recursive)
     *
     * @method serializeParams
     * @param {object} obj The object to serialize
     * @return {string}
     */
    proto.serializeParams = function(obj) {
        var p;
        var str = [];

        for (p in obj) {
            if (obj.hasOwnProperty(p)) {
                if (typeof obj[p] === 'object') {
                    str.push('{' + this.serializeParams(obj[p]) + '}');
                } else {
                    str.push(encodeURIComponent(p) + '=' + encodeURIComponent(obj[p]));
                }
            }
        }

        if (str.length === 0) {
            return '';
        } else {
            return '?' + str.join('&');
        }
    };

    return AbstractProvider;
});
