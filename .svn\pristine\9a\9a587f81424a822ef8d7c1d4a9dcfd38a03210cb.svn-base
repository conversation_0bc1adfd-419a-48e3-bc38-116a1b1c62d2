{{#if simFields.length}}
    {{#each simFields}}
        {{#is id 'BI_2'}}
            {{#is simValue 3 }}
                <li class="button_bank_info mt-15">+ <span id="hide-bank-details" class="underline pointer" >Show Optional Bank Fields</span></li>
                <div id="bank-info" class="mt-15" style="display: none">
            {{/is}}
        {{/is}}
        <label class="mb-0 txtLarge">
        {{> question/required}}
        {{#if personnel}}
            {{#unless @index}}
                <h2 class="hdg hdg_h2 ">
                    {{desc}}
                </h2>
            {{/unless}}
            <span style="display:none;"> Personal </span>
        {{else}}
            {{#unless @index}}
                    {{#is blockId 'BI'}}{{else}}{{desc}}{{/is}}
            {{/unless}}
            <span style="display:none;"> Simulated </span>
        {{/if}}
        {{#is id 'BI_1'}}
            {{desc}}
            {{#if ../bankInfoTooltip}}
                <span id="bankNameHelp" class="js-button-action"
                    data-template='<div id="bankName-popup" class="custpop popover" role="tooltip"><div class="arrow"></div><div class="popover-body"></div></div>'
                    data-container="body" data-content="" data-original-title="" title="" data-placement="bottom"
                    alt="bankNameHelp"></span><span class="bank-name-loader">Loading bank name...</span>                            
            {{/if}}
        {{/is}}

        {{#is blockId 'BI'}}
            {{#ifEq id 'BI_1'}}
                <input class="inputBox {{piiClass}}" 
                    name="{{#if blockId}}{{blockId}}_{{/if}}{{id}}"
                    id="{{#if blockId}}{{blockId}}_{{/if}}{{id}}" 
                    maxlength="{{maxLength}}" 
                    value="{{#isNumber default}}USE BANK LOGO ON FILE{{else}}{{default}}{{/isNumber}}" 
                    data-analytics-action="BankNameEdit" 
                    type="{{inputType}}" >
                    {{#unless @index}}
                        <div class="error js-error"></div>
                    {{/unless}}
                </label>
            {{/ifEq}}
            
            {{#ifEq id 'BI_2'}}
                {{desc}}
                <input class="inputBox {{piiClass}} {{#is simValue 3 }} mb-15 {{/is}}" name="{{#if blockId}}{{blockId}}_{{/if}}{{id}}"
                    id="{{#if blockId}}{{blockId}}_{{/if}}{{id}}" maxlength="{{maxLength}}" value="{{default}}"
                    type="{{inputType}}" />
                </label>
            {{/ifEq}}
            {{#ifEq id 'BI_3'}}
                {{desc}}
                <input class="inputBox {{piiClass}}" name="{{#if blockId}}{{blockId}}_{{/if}}{{id}}"
                    id="{{#if blockId}}{{blockId}}_{{/if}}{{id}}" maxlength="{{maxLength}}" value="{{default}}"
                    type="{{inputType}}" />
                </label>
                    {{#is simValue 3 }}
                        </div>
                    {{/is}}
            {{/ifEq}}
        {{else}}
            <input class="inputBox {{piiClass}}"
                name="{{#if blockId}}{{blockId}}_{{/if}}{{id}}"
                id="{{#if blockId}}{{blockId}}_{{/if}}{{id}}"
                maxlength="{{maxLength}}"
                value="{{default}}"
                {{#is blockId 'BI'}} data-analytics-action="BankNameEdit" {{/is}}
                type="{{inputType}}" />
                {{#is blockId 'BI'}}
                    {{#unless @index}}
                        <div class="error js-error"></div>
                    {{/unless}}
                {{else}}<span class="error js-error"></span>{{/is}}
                </label>
        {{/is}}
    {{/each}}
{{else}}
    <label class="txtLarge">
        {{> question/required}}
        {{#if personnel}}
            <h2 class="hdg hdg_h2 ">
                {{desc}}
            </h2>
        {{else}}
            {{#ifEq desc 'Payment Terms'}}
                {{#if toolTip}}
                    {{#if surchargeTM}}     
                        {{desc}} (+{{surchargeTM}}) 
                    {{else}}     
                        {{desc}}
                    {{/if}}
                    <span id="paymentTermsHelp" data-template='<div id="paymentTerms-popup" class="custpop popover" role="tooltip"><div class="arrow"></div><div class="popover-body"></div></div>' data-container="body" data-placement="bottom" data-content="" data-original-title="" title="" class="js-button-paymentTerms-action pop-space" alt="paymentTermsHelp"></span>
                    {{else}}
                    {{desc}}
                {{/if}}
                 {{else}}
                {{desc}}
                {{#if rtNameHelp}}  
                    <span id="routingNameHelp" class="js-rtname-action" 
        data-template='<div id="account-popup" class="custpop popover" role="tooltip"><div class="arrow"></div><div class="popover-body"></div></div>'
	    data-container="body" data-content="" data-original-title="" title="" data-placement="bottom"  alt="routingNameHelp"></span>                            
                {{/if}}
                {{#if acNameHelp}}  
                    <span id="accountNameHelp" class="js-acname-action" 
        data-template='<div id="account-popup" class="custpop popover" role="tooltip"><div class="arrow"></div><div class="popover-body"></div></div>'
	    data-container="body" data-content="" data-original-title="" title="" data-placement="bottom"  alt="accountNameHelp"></span>                            
                {{/if}}
                 {{#if otACNameHelp}}  
                    <span id="otaccountNameHelp" class="js-other-acname-action" 
        data-template='<div id="account-popup" class="custpop popover" role="tooltip"><div class="arrow"></div><div class="popover-body"></div></div>'
	    data-container="body" data-content="" data-original-title="" title="" data-placement="bottom"  alt="otaccountNameHelp"></span>                          
                {{/if}}
            {{/ifEq}}
           
        {{/if}}
        <form autocomplete="off" onsubmit="return false;">
            <input class="inputBox {{piiClass}}"
                name="{{#if blockId}}{{blockId}}_{{/if}}{{id}}"
                id="{{#if blockId}}{{blockId}}_{{/if}}{{id}}"
                maxlength="{{maxLength}}"
                value="{{default}}"
                type="{{inputType}}"
                autocomplete="off" />
            <span class="error js-error"></span>
        </form>
    </label>
{{/if}}