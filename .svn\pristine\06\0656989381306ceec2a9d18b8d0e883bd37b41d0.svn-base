define(function() {
    'use strict';

    /**
     * @class App.Constants.DomEvents
     * @static
     */
    var DomEvents = {
        /**
         * @event BLUR
         * @param {jQuery.Event} event
         */
        BLUR: 'blur',

        /**
         * @event CHANGE
         * @param {jQuery.Event} event
         */
        CHANGE: 'change',

        /**
         * @event CLICK
         * @param {jQuery.Event} event
         */
        CLICK: 'click',

        /**
         * @event FOCUS
         * @param {jQuery.Event} event
         */
        FOCUS: 'focus',

        /**
         * @event HASH_CHANGE
         * @param {jQuery.Event} event
         */
        HASH_CHANGE: 'hashchange',

        /**
         * @event KEY_DOWN
         * @param {jQuery.Event} event
         */
        KEY_DOWN: 'keydown',

        /**
         * @event KEY_PRESS
         * @param {jQuery.Event} event
         */
        KEY_PRESS: 'keypress',

        /**
         * @event KEY_UP
         * @param {jQuery.Event} event
         */
        KEY_UP: 'keyup',

        /**
         * @event LOAD
         * @param {jQuery.Event} event
         */
        LOAD: 'load',

        /**
         * @event RESIZE
         * @param {jQuery.Event} event
         */
        RESIZE: 'resize',

        /**
         * @event STATE_POP
         * @param {jQuery.Event} event
         */
        STATE_POP: 'popstate',
        
        MOUSE_DOWN : 'mousedown'
    };

    return DomEvents;
});
