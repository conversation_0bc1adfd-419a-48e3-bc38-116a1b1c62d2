
define(function(require) {
    'use strict';

    var $ = require('jquery');
    var AbstractQuestionController = require('./Abstract');
    var ActionEvents = require('../../constants/ActionEvents');
    var CheckboxQuestionController = require('./Checkbox');
    var ConfigurationProvider = require('../../providers/Configuration');
    var RegionProvider = require('../../providers/Region');
    var Classes = require('../../constants/Classes');
    var RegEx = require('../../constants/RegEx');
    var Content = require('i18n!../../constants/nls/en-us/Content');
    var DomEvents = require('../../constants/DomEvents');
    var EventController = require('../Event');
    var ProductEvents = require('../../constants/ProductEvents');
    var Settings = require('../../constants/Settings');
    var ZoomEvents = require('../../constants/ZoomEvents');
    var bindAll = require('mout/object/bindAll');
    var debounce = require('mout/function/debounce');
    var inherits = require('mout/lang/inheritPrototype');
    var SessionStorage = require('../../providers/SessionStorage');
    var Helper = require('../../util/helper');
    var Surcharge = require('util/Surcharge');
    /**
     * @class App.Controllers.Question.TextBlockLine
     * @extends App.Controllers.Question.Abstract
     *
     * @constructor
     * @param {Object} config
     * @param {jQuery} config.view
     * @param {App.Model.Ui.QuestionBlockLine} config.model
     */
    function TextBlockLineQuestionController(config) {
        bindAll(this,
            'onChange',
            'onFocus',
            'onFocusKeyDown',
            'onButtonClick',
            'onPaymentTermsButtonClick',
            'cleanseText',
            'onProductChange',
            'onChangeRM',
            'onSaveRM'
        );

        /**
         * @method onChangeLazy
         * @param {jQuery.Event} event
         * @callback
         */
        this.onChangeLazy = debounce(this.onChange, Settings.DEBOUNCE_DELAY);
        AbstractQuestionController.call(this, config);
    }

    var proto = inherits(TextBlockLineQuestionController, AbstractQuestionController);


    proto.initialRoutingValue = '';
    proto.initialAccountValue = '';

    /**
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = require('hbs!templates/question/text/blockLine');

     /**
     * @method stateTemplate
     * @param {Object} model
     * @return {String}
     */
    proto.stateTemplate = require('hbs!templates/question/text/dropdown/state');

      /**
     * @method dieCutTemplate
     * @param {Object} model
     * @return {String}
     */
    proto.dieCutTemplate = require('hbs!templates/question/text/dieCut');

       /**
     * @method discountTemplate
     * @param {Object} model
     * @return {String}
     */
    proto.discountTemplate = require('hbs!templates/question/text/discount');


    /**
     * @method bankInformationTemplate
     * @param {Object} model
     * @return {String}
     */
    proto.bankInformationTemplate = require('hbs!templates/question/text/bankInformation');

    /**
     * @method customerInformationTemplate
     * @param {Object} model
     * @return {String}
     */
    proto.customerInformationTemplate = require('hbs!templates/question/text/customerInformation');

    /**
     * @method rmBlockTemplate
     * @param {Object} model
     * @return {String}
     */
    proto.rmBlockTemplate = require('hbs!templates/question/text/rmBlock');


     /**
     * @method init
     * @chainable
     */
    proto.init = function() {
        (this.model.blockId == 'BI' && this.model.info && this.model.info.desc == 'Routing Number' && RegEx.ASTERISK_NUMERIC_MUST.test(this.model.getValue())) ? this.initialRoutingValue = this.model.getValue() : null;
        (this.model.blockId == 'CI' && this.model.info && this.model.info.desc == 'Account Number' && RegEx.ASTERISK_NUMERIC_MUST.test(this.model.getValue())) ? this.initialAccountValue = this.model.getValue() : null;
        return this;
    };


    /**
     * @method render
     * @chainable
     */
    proto.render = function() {
        var model = this.model;
        switch (model.id) {
            /*case 'BI':
                this.renderBankInformation();
                break;
            case 'CI':
                this.renderCustomerInformation();
                break;*/
            case 'SL':
                this.renderSignatureLine();
                break;
            case 'ML':
                this.renderDiecut();
                break;
            case 'DS':
                this.renderDiscount();
                break;
            case 'GC':
                this.renderPersonnel();
                break;
            case 'ST':
                this.renderState();
                break;
            case 'BX':
                this.renderRMBlock();
                break;
            default:
                this.renderDefault();
                break;
        }
        EventController.emit(ZoomEvents.UPDATE);
        return this;
    };



    /**
     * @method renderRMBlock
     * @chainable
     */
    proto.renderRMBlock = function() {
        var value = this.model.getValue()
        if(value === "") {
            this.model.setValue(this.model.default, this.model.blockId + "_" + this.model.id)
        } else {           
            let valueCheck = this.model.options.some(function(option) {
                return option.id == value
            })
            // Reset to default if value not present in options
            if(!valueCheck){
                this.model.setValue(this.model.default, this.model.blockId + "_" + this.model.id)
            }
        }        
       
        let  rmImageUrl = Settings.SCENE7_HOST + Settings.SCENE7_RM_IMAGE +  this.model.getValue().replace("+", "_").toLowerCase() + "?wid=300&fmt=jpg&qlt=85";
        this.$view.html(this.rmBlockTemplate({selectedValue: value,
            rmImageUrl : rmImageUrl
        }));

        return this.start();
    };


       /**
     * @method renderPersonnel
     * @chainable
     */
    proto.renderPersonnel = function() {

        var model = Object.create(this.model);
        var value = model.getValue();

        model.isRequired = model.isRequired;
        model.simFields = [];
        
        if (model.sim > 1) {
            for (var i=1; i <= model.sim; i++) {
                model.simFields.push({
                    desc: model.desc,
                    'default': model.getSimValue(i),
                    blockId: model.blockId,
                    id: model.id + '_' + i,
                    personnel: true,
                    maxLength: this.model.maxLength,
                    inputType: 'text'
                });
            }
        } else {
            model['default'] = value;
            model['personnel'] = true;
            model['inputType'] = 'text';
        }
        this.$view.html(this.template(model));

        return this.start();
    };

     /**
     * @method renderDiscount
     * @chainable
     */
    proto.renderDiscount = function() {

        var model = Object.create(this.model);
        var value = model.getValue();

        model.isRequired = model.isRequired;
        model.simFields = [];
        
        if (model.sim > 1) {
            for (var i=1; i <= model.sim; i++) {
                model.simFields.push({
                    desc: model.desc,
                    'default': model.getSimValue(i),
                    blockId: model.blockId,
                    id: model.id + '_' + i,
                    inputType: 'text'
                });
            }
        } else {
            model['default'] = value;
            model['inputType'] = 'text';
        }
        // Set max length for some routing numbers
        if (model.blockId == 'BI' && this.model.info && this.model.info.desc == 'Account Number') {
            model.maxLength = 9;
            model.inputType = 'tel';
        }
        this.$view.html(this.discountTemplate(model));

        return this.start();
    };

    /**
     * @method renderDiecut
     * @chainable
     */
    proto.renderDiecut = function() {

        var model = Object.create(this.model);
        var value = model.getValue();
        model.isRequired = model.isRequired;
        model.piiClass = 'pii-encrypt';
        model.simFields = [];
        if (model.blockId === 'EI' && (!(value ===null || value === ''))) {
            if (!($('#ENVELOPE_RETURN').is(':checked'))) {
                $('#ENVELOPE_RETURN').click();
            }
        }

        if (model.sim > 1) {
            for (var i=1; i <= model.sim; i++) {
                 if ($.inArray(model.blockId, Settings.CUSTOM_VALIDATION_BLOCKS) > -1 &&
                     $.inArray(model.id, Settings.CUSTOM_VALIDATION_LINES) > -1) {
                    model.desc = Content.get(model.blockId+'_'+model.id+'_'+i);
                    if (i === 1 && model.isRequired) {
                        model.isRequired = true;
                    } else {
                        model.isRequired = false;
                    } 
                } 
                model.simFields.push({
                    desc: model.desc,
                    addDesc: model.additionaldesc,
                    'default': model.getSimValue(i),
                    blockId: model.blockId,
                    id: model.id + '_' + i,
                    maxLength: this.model.maxLength,
                    isRequired: model.isRequired,
                    inputType: 'text',
                    piiClass: model.piiClass
                });
            }
        } else {
            model['default'] = value;
            model['iputType'] = 'text';
        }
        this.$view.html(this.dieCutTemplate(model));

        return this.start();
    };


     /**
     * @method renderState
     * @chainable
     */
    proto.renderState = function() {

        var model = Object.create(this.model);
        var value = model.getValue();  
        model.isRequired = model.isRequired;
        model.piiClass = 'pii-encrypt';
        model.simFields = [];
        if (model.blockId === 'EI' && (!(value ===null || value === ''))) {
            if (!($('#ENVELOPE_RETURN').is(':checked'))) {
                $('#ENVELOPE_RETURN').click();
            }
        }
        if (model.sim > 1) {
            for (var i=1; i <= model.sim; i++) {
                if (model.blockId === 'BI') {
                    model.desc = Content.get('BI_'+i);
                    if (i === 1) {
                        model.isRequired = true;
                    } else {
                        model.isRequired = false;
                    } 
                } 
                model.simFields.push({
                    desc: model.desc,
                    'default': model.getSimValue(i),
                    isRequired: model.isRequired,
                    blockId: model.blockId,
                    id: model.id + '_' + i,
                    maxLength: 50,
                    inputType: 'text',
                    piiClass: model.piiClass
                });
            }
        } else {
            model['default'] = value;
            model['inputType'] = 'text';
        }
        // Set max length for some routing numbers ;)
        if (model.blockId == 'BI' && this.model.info && this.model.info.desc == 'Account Number') {
            model.maxLength = 9;
            model.inputType = 'tel';
            model.piiClass = 'pii-block';
        }       
        
        // For ECHKSD PRODUCT WE HAVE TO SHOW DROPDOWN FOR STATE
        if (model.ECHKSD == true) {
            var items = model.regionOption && model.regionOption._items;
            this.items = items;
            model.options = items;
            var flag = false;
            if (model.getValue().trim().length > 2) {
                
                for (var i=0; i<model.options.length;i++) {
                    if ((model.options[i].desc).trim().toLowerCase() == model.getValue().trim().toLowerCase()) {
                        // console.log(model.options[i].id);
                        model.default=model.options[i].id;
                        flag = true;
                        break;
                    }
                }
                
                if (!flag) {
                    model.default='';
                    model.setValue = '';
                    SessionStorage.storeValue('CI_ST','');
                }
            } else if (model.getValue().trim().length > 0){
                model.default=model.getValue().trim().toUpperCase();
            } else {
                model.default='';
                model.setValue = '';
            }
            this.$view.html(this.stateTemplate(model));
            
        } else {
            this.$view.html(this.template(model));
        }       

        return this.start();
    };

    /**
     * @method renderDefault
     * @chainable
     */
    proto.renderDefault = function() {

        var model = Object.create(this.model);
        var value = model.getValue();
        var bankInfoTooltip = false;
        var defaultValue = "";

        model.isRequired = model.isRequired;
        model.piiClass = 'pii-encrypt';
        model.simFields = [];
        model.toolTip = false;

        if (model.blockId === 'EI' && (!(value ===null || value === ''))) {
            if (!($('#ENVELOPE_RETURN').is(':checked'))) {
                $('#ENVELOPE_RETURN').click();
            }
        }
        if (model.sim > 1) {
            for (var i=1; i <= model.sim; i++) {
                if (model.blockId === 'BI') {
                    model.piiClass = 'pii-block';
                    model.desc = Content.get('BI_'+i);
                    if (i === 1 && model.isRequired) {
                        model.isRequired = true;
                    } else {
                        model.isRequired = false;
                    } 
                } 

                if (i === 1 && Settings.BANK_INFO_SERVICE_USE == true) {
                    bankInfoTooltip = true; 
                }

                var sessionStorageValue = SessionStorage.getValue(model.blockId + "_" + model.id + "_" + i);
                if (!sessionStorageValue) {
                    defaultValue = i === 1 ? model.getSimValue(i) : "";
                } else {
                    defaultValue = sessionStorageValue;
                }
                this.model.setValue(defaultValue, model.blockId + "_" + model.id + "_" + i);

                model.simFields.push({
                    desc: model.desc,
                    'default': defaultValue,
                    isRequired: model.isRequired,
                    blockId: model.blockId,
                    id: model.id + '_' + i,
                    maxLength: 50,
                    inputType: 'text',
                    piiClass: model.piiClass,
                    bankInfoTooltip:bankInfoTooltip,
                    simValue:model.sim
                });
            }
        } else if (model.sim == 1 && model.blockId === 'BI' && model.id === 'BI' ) {
            // to set field id like BI_BI_! even if sim = 1
            for (var i=1; i <= model.sim; i++) {
                if (model.blockId === 'BI') {
                    model.piiClass = 'pii-block';
                    model.desc = Content.get('BI_'+i);
                    if (i === 1 && model.isRequired) {
                        model.isRequired = true;
                    } else {
                        model.isRequired = false;
                    } 
                    if (i === 1 && Settings.BANK_INFO_SERVICE_USE == true) {
                        bankInfoTooltip = true; 
                    }
                }
                
                var sessionStorageValue = SessionStorage.getValue(model.blockId + "_" + model.id + "_" + i);
                if (!sessionStorageValue) {
                    defaultValue = i === 1 ? model.getSimValue(i) : "";
                } else {
                    defaultValue = sessionStorageValue;
                }
                this.model.setValue(defaultValue, model.blockId + "_" + model.id + "_" + i);
                model.simFields.push({
                    desc: model.desc,
                    'default': defaultValue,
                    isRequired: model.isRequired,
                    blockId: model.blockId,
                    id: model.id + '_' + i,
                    maxLength: 50,
                    inputType: 'text',
                    piiClass: model.piiClass,
                    bankInfoTooltip: bankInfoTooltip,
                    simValue:model.sim
                });
            }

        } else {
            if (SessionStorage.getValue(model.blockId + "_" + model.id) == undefined && SessionStorage.getValue(model.blockId + "_" + model.id + "_1") !== "") {
                defaultValue = SessionStorage.getValue(model.blockId + "_" + model.id + "_1");
            }
            model['default'] = defaultValue !== "" ? defaultValue : value;
            model['inputType'] = 'text';
            if (SessionStorage.getValue(model.blockId + "_" + model.id) == undefined ) {
                this.model.setValue(model['default'], model.blockId + "_" + model.id);
                EventController.emit(ZoomEvents.UPDATE);
            }
        }
        // Set max length for some routing numbers ;)
        if (model.blockId == 'BI' && this.model.info && this.model.info.desc == 'Account Number') {
            model.maxLength = 9;
            model.inputType = 'tel';
            model.piiClass = 'pii-block';
        }
        if (model.blockId == 'CI' && this.model.info && this.model.info.desc == 'Account Number') {
            model.inputType = 'tel';
            model.piiClass = 'pii-block';
            model.acNameHelp = true;
        }
        else if (model.blockId == 'CI' && this.model.info && this.model.info.desc.includes('Account Number')) {
            model.otACNameHelp = true;
        }

        if (model.blockId == 'BI' && this.model.info && this.model.info.desc == 'Routing Number') {
            model.inputType = 'tel';
            model.piiClass = 'pii-block';
            model.rtNameHelp = true;
        }

        if (model.blockId == 'TM' && this.model && this.model.desc == 'Payment Terms') {
            model.toolTip = true;
            var blocks = model.question.option.location.block
            var surchargeID;
            if(blocks && blocks.length > 0)
            {
                var block = blocks.find(function(block) {
                    return block.id == "TM";
                })
                surchargeID = block.surcharge.id;
            }
            if(surchargeID) {
                var surchargeTM = Surcharge.getPrice(surchargeID, false, 100);
            }
            model.surchargeTM = surchargeTM;            
        }
        //console.log('TextBlockLine: ' + model.id + ' - ' + model.piiClass);
        this.$view.html(this.template(model));

        return this.start();
    };

    /**
     * @method renderBankInformation
     * @chainable
     */
    // TODO: this renderBankInformation function is no longer in use
    // TODO: Template file bankInformation is no longer in use
    proto.renderBankInformation = function() {
        var $view = this.$view;
        var model = this.model;
        var bankValue = model.getValue('1');
        var address1Value = model.getValue('2');
        var address2Value = model.getValue('3');
        var cityValue = model.getValue('4');
        /*var stateValue = model.getValue('4_state');
        var zipValue = model.getValue('4_zipCode');*/
        var piiClass = 'pii-block';

        $view.html(this.bankInformationTemplate({
            id: model.id,
            blockId: model.blockId,
            isRequired: model.isRequired,
            maxLength: model.maxLength,
            bankDefault: bankValue,
            address1Default: address1Value,
            address2Default: address2Value,
            cityDefault: cityValue,
            /*stateDefault: stateValue,
            zipDefault: zipValue,*/
            bankLabel: Content.get('BANK_NAME'),
            address1Label: Content.get('ADDRESS_LINE_1'),
            address2Label: Content.get('ADDRESS_LINE_2'),
            cityLabel: Content.get('CITY'),
            /*stateLabel: Content.get('STATE'),
            zipLabel: Content.get('ZIP'),
            stateOptions: Content.get('STATE_OPTIONS').sort(this._sortStates)*/
            inputType: 'text',
            piiClass: piiClass
        }));

        return this.start();
    };

    /**
     * @method renderCustomerInformation
     * @chainable
     */
    proto.renderCustomerInformation = function() {
        var $view = this.$view;
        var model = this.model;
        var piiClass = 'pii-encrypt';

        /*var cityValue = model.getValue('city');
        var stateValue = model.getValue('state');
        var zipValue = model.getValue('zipCode');

        if(model.blockId === 'EI'&&(model.getValue('CI_CI_city') || model.getValue('CI_CI_state') || model.getValue('CI_CI_zipCode')) ){
            cityValue = model.getValue('CI_CI_city');
            stateValue = model.getValue('CI_CI_state');
            zipValue = model.getValue('CI_CI_zipCode');
        
        }*/
    
        $view.html(this.customerInformationTemplate({
            id: model.id,
            blockId: model.blockId,
            isRequired: model.isRequired,
            maxLength: model.maxLength,
            /*cityDefault: cityValue,
            stateDefault: stateValue,
            zipDefault: zipValue,
            cityLabel: Content.get('CITY') || model.desc,
            stateLabel: Content.get('STATE'),
            zipLabel: Content.get('ZIP'),
            stateOptions: Content.get('STATE_OPTIONS').sort(this._sortStates)*/
            inputType: 'text',
            piiClass: piiClass
        }));

        return this.start();
    };

    /**
     * @method renderSignatureLine
     * @chainable
     */
    proto.renderSignatureLine = function() {
        var model = this.model;
        var checkbox = new CheckboxQuestionController();

        const value = model.getValue();
        model.fieldValue = (value.toLowerCase() === "+sg" || value === "true") ? value : false;
        model.value = model.id !== "SL" ? false : value === "SL" ? "" : value;
        checkbox.model = model;

        checkbox.$view = this
            .$view
            .html(checkbox.template(checkbox.model));

        return checkbox.start();
    };

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        this.$view
            .on(DomEvents.CLICK, this.onFocus)
            .on(DomEvents.FOCUS, this.onFocus)
            .on(DomEvents.BLUR, this.onChange)
            .on(DomEvents.CHANGE, this.onChange)
            .on(DomEvents.KEY_DOWN, this.onChangeLazy)
            .on(DomEvents.KEY_DOWN, this.onFocusKeyDown)
            .on(DomEvents.CHANGE, this.onFocusKeyDown)
            .on(DomEvents.CLICK, Classes.BUTTON_PAYMENT_TERMS_ACTION, this.onPaymentTermsButtonClick)
            .on(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick)
            .on(DomEvents.CLICK, Classes.BUTTON_RTNAME_SELECTOR, this.onRTNameClick)
            .on(DomEvents.CLICK, Classes.BUTTON_ACNAME_SELECTOR, this.onACNameClick)
            .on(DomEvents.CLICK, Classes.BUTTON_OTHER_ACNAME_SELECTOR, this.onOtherACNameClick)
            .on(DomEvents.CLICK, Classes.RM_CHANGE_SELECTOR, this.onChangeRM);
        EventController
            .on(ProductEvents.CHANGE, this.onProductChange)
            .on(ProductEvents.SAVE_RM, this.onSaveRM);
        return this;
    };

    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        this.$view
            .off(DomEvents.CLICK, this.onFocus)
            .off(DomEvents.FOCUS, this.onFocus)
            .off(DomEvents.BLUR, this.onChange)
            .off(DomEvents.CHANGE, this.onChange)
            .off(DomEvents.KEY_DOWN, this.onChangeLazy)
            .off(DomEvents.KEY_DOWN, this.onFocusKeyDown)
            .off(DomEvents.CHANGE, this.onFocusKeyDown)
            .off(DomEvents.CLICK, Classes.BUTTON_PAYMENT_TERMS_ACTION, this.onPaymentTermsButtonClick)
            .off(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick)
            .off(DomEvents.CLICK, Classes.BUTTON_RTNAME_SELECTOR, this.onRTNameClick)
            .off(DomEvents.CLICK, Classes.BUTTON_ACNAME_SELECTOR, this.onACNameClick)
            .off(DomEvents.CLICK, Classes.BUTTON_OTHER_ACNAME_SELECTOR, this.onOtherACNameClick)
            .off(DomEvents.CLICK, Classes.RM_CHANGE_SELECTOR, this.onChangeRM);
        EventController.off(ProductEvents.SAVE_RM, this.onSaveRM)
        .off(ProductEvents.CHANGE, this.onProductChange);
        return this;
    };

     /**
     * @method onChangeRM
     * @param {jQuery.Event} event
     * @callback
     */
     proto.onChangeRM = function(event) {
        var action = $(event.target).data('action');
        if (action) {
            event.preventDefault();
            event.stopPropagation();
            EventController
                .emit(ActionEvents.HIDE_ALL)
                .emit(action);
        }
     }

      /**
     * @method onSaveRM
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onSaveRM = function(event, selectedValue) {        
        if(this.model.blockId == 'RM' && this.model.id == 'BX') {
            this.model.setValue(selectedValue.value, this.model.blockId + "_" + this.model.id)
            return this.render();
        }
    }

    /**
     * @method cacheElements
     * @chainable
     */
    proto.cacheElements = function() {
        this.$input = this.$view.find(Classes.INPUT_SELECTOR);

        return this;
    };

    /**
     * @method onFocus
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onFocus = function(event) {

        var name = event.target.name;
        var $input = $(event.target);
       
        if ($input[0].id == 'BI_NO') {
            if (RegEx.ASTERISK_NUMERIC_MUST.test($input.val())) {
                $input.focus();
                $input.select();
            }
            // if(!this.user_click) {
            //     $input.val('');
            // }       
            // this.user_click = 1;
        } else if ($input[0].id == 'CI_NO') {
            if (RegEx.ASTERISK_NUMERIC_MUST.test($input.val())) {
                $input.focus();
                $input.select();
            }        
        } else {
            return;
        }
        var value = $input.val().trim();
        this.model.setValue(value, name);
        this.updateErrors($input);

        EventController.emit(ZoomEvents.UPDATE);
    };

    /**
     * @method onFocusKeyDown
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onFocusKeyDown = function(event) {

        var name = event.target.name;
        var $input = $(event.target);
       
        if ($input[0].id == 'BI_NO') {
            if ((RegEx.ASTERISK_NUMERIC_MUST.test($input.val()))  && (this.initialRoutingValue != $input.val())) {
                $input.val('');
            }
            if ((RegEx.ASTERISK_ONLY.test($input.val()))  && (this.initialRoutingValue != $input.val())) {
                $input.val('');
            }
        } else if ($input[0].id == 'CI_NO') {
            if (RegEx.ASTERISK_NUMERIC_MUST.test($input.val()) && (this.initialAccountValue != $input.val()) ) {
                $input.val('');
            }
            if (RegEx.ASTERISK_ONLY.test($input.val()) && (this.initialAccountValue != $input.val()) ) {
                $input.val('');
            }
        } else {
            return;
        }
        var value = $input.val().trim();
        this.model.setValue(value, name);
        this.updateErrors($input);

        EventController.emit(ZoomEvents.UPDATE);
    };

    /**
     * @method onChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onChange = function(event) {
        var name = event.target.name;
        var $input = $(event.target);
        if (event.target.id === 'SL' && event.target.value === ""){
            event.target.value = "+sg"
        }
        var replacedText = this.cleanseText($input);
        $input.val(replacedText);
        var value = $input.val().trim();
        if ($input.attr('type') === 'checkbox' && !$input.prop('checked')) {
            value = '';
        }

        this.model.setValue(value, name);
        this.updateErrors($input);

        EventController.emit(ZoomEvents.UPDATE);
        if(name == 'TM_TM' && event.type == DomEvents.KEY_DOWN)
        {
            EventController.emit(ProductEvents.CHANGE, this.product);
        }
    };

    /**
     * Sorting criteria for textblock
     * @method _sortStates
     * @param {Object} state1 Object containing properties of first state
     * @param {Object} state2 Object containing properties of second state
     * @returns {Number}      Sort modifier
     */
    proto._sortStates = function(state1, state2) {
        state1 = state1.name;
        state2 = state2.name;
        if (state1 === state2) {
            return 0;
        }
        return state1 < state2 ? -1 : 1;
    };

    /**
     * @method onButtonClick
     * @@param {jQuery.Event} event
     * @callback
     */    
    proto.onButtonClick = function(event){
        var model = this.model;

        if (Helper.isSameTooltipClick(event)) {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
        } else {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
            $('#bankNameHelp').popover({
                html: true,
                trigger: "manual",
                placement: 'bottom',
                content: function () {
                    var msg0 = '<div><img src="' + host_url + '/webasset/w2p_mobile/' + baseAppUrl + 'assets/media/images/tooltip-close-new.png" id="bankNameCloseIcon" alt="bankNameCloseIcon" class="close tooltip-close" style="position: absolute; top: 0; right: 7px;"/>';
                    var msg1 = "<div class='titletxt'>Bank Name</div><br/>";
                    var msg2 = "<div class='othertxt'>When possible, the bank name will be automatically added based on the given routing number. If the bank name has changed, please update the Bank Name field. Additional validation is completed after your order is placed to help ensure the bank account information is properly encoded for bank processing.</div></br>";
                    return msg0 + msg1 + msg2 + "</div>";
                },
            });
            if (!Settings.BANK_INFO_FLAG) {
                $('#bankNameHelp').popover('show');
            } else {
                $('#bankNameHelp').popover('hide');
            }
            Settings.BANK_INFO_FLAG = !Settings.BANK_INFO_FLAG;

            $("#bankNameCloseIcon.close.tooltip-close").on("click", function () {
                Settings.BANK_INFO_FLAG = false;
                $('#bankNameHelp').popover('hide');
            });
        }
    }

     /**
     * @method onRTNameClick
     * @param {jQuery.Event} event
     * @callback
     */
     proto.onRTNameClick = function(event) {
        event.preventDefault();

        if (Helper.isSameTooltipClick(event)) {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
        } else {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }

            $('#routingNameHelp').popover({
                html: true,
                trigger: "manual",
                placement: 'bottom',
                content: function () {
                    var msg0 = '<div><img src="' + host_url + '/webasset/w2p_mobile/' + baseAppUrl + 'assets/media/images/tooltip-close-new.png" id="rtNameCloseIcon" alt="rtNameCloseIcon" class="close tooltip-close" style="position: absolute; top: 0; right: 7px;"/>';
                    var msg1 = "<div class='titletxt'>Routing Number </div><br/>";
                    var msg2 = "<div class='othertxt'> Your Routing Number is usually found on the bottom of your checks between the following two symbols, I: and :I</div>";
                    var msg3 = "<p class='othertxt'>These 9-digit numbers always start with 0, 1, 2, or 3.</p>";
                    let msg4 = '<p class="othertxt">The purpose of the routing number is to identify the financial institution where your account is held, acting as a "bank address" for electronic transactions.</p><br/>'
                    return msg0 + msg1 + msg2 + msg3 + msg4 + "</div>";
                },
            });
            $('#routingNameHelp').popover('show');
            // if (!Settings.ROUTING_FLAG) {
            //     $('#routingNumberHelp').popover('show');
            // } else {
            //     $('#routingNumberHelp').popover('hide');
            // }
            Settings.ROUTING_FLAG = !Settings.ROUTING_FLAG;

            $('#rtNameCloseIcon').click(function (e) {
                e.preventDefault();
                Settings.ROUTING_FLAG = false;
                $('#routingNameHelp').popover('hide');
            });
        }

    }

     /**
     * @method onACNameClick
     * @param {jQuery.Event} event
     * @callback
     */
     proto.onACNameClick = function(event) {
        event.preventDefault();

        if (Helper.isSameTooltipClick(event)) {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
        } else {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }

            $('#accountNameHelp').popover({
                html: true,
                trigger: "manual",
                placement: 'bottom',
                content: function () {
                    var msg0 = '<div><img src="' + host_url + '/webasset/w2p_mobile/' + baseAppUrl + 'assets/media/images/tooltip-close-new.png" id="acNameCloseIcon" alt="acNameCloseIcon" class="close tooltip-close" style="position: absolute; top: 0; right: 7px;"/>';
                    var msg1 = "<div class='titletxt'>Account Number </div><br/>";
                    var msg2 = "<div class='othertxt'> Your Account Number is the longer set of numbers, usually 8 to 12 digits long, located in the middle of the bottom row, between the nine-digit routing number and the check number.</div>";
                    var msg3 = "<p class='othertxt'>The purpose of the account number is to uniquely identify your specific bank account within your specific bank institution.</p><br/>";
                    return msg0 + msg1 + msg2 + msg3 + "</div>";
                },
            });
            $('#accountNameHelp').popover('show');
            // if (!Settings.ROUTING_FLAG) {
            //     $('#routingNumberHelp').popover('show');
            // } else {
            //     $('#routingNumberHelp').popover('hide');
            // }
            Settings.ROUTING_FLAG = !Settings.ROUTING_FLAG;

            $('#acNameCloseIcon').click(function (e) {
                e.preventDefault();
                Settings.ROUTING_FLAG = false;
                $('#accountNameHelp').popover('hide');
            });
        }

    }

     /**
     * @method onACNameClick
     * @param {jQuery.Event} event
     * @callback
     */
     proto.onOtherACNameClick = function(event) {
        event.preventDefault();

        if (Helper.isSameTooltipClick(event)) {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
        } else {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }

            $('#otaccountNameHelp').popover({
                html: true,
                trigger: "manual",
                placement: 'bottom',
                content: function () {
                    var msg0 = '<div><img src="' + host_url + '/webasset/w2p_mobile/' + baseAppUrl + 'assets/media/images/tooltip-close-new.png" id="otacNameCloseIcon" alt="otheracNameCloseIcon" class="close tooltip-close" style="position: absolute; top: 0; right: 7px;"/>';
                    var msg1 = "<div class='titletxt'>Account Number </div><br/>";
                    var msg2 = "<div class='othertxt'> Your Account Number is the longer set of numbers, usually 8 to 12 digits long, located in the middle of the bottom row, between the nine-digit routing number and the check number.</div>";
                    var msg3 = "<p class='othertxt'>The purpose of the account number is to uniquely identify your specific bank account within your specific bank institution.</p><br/>";
                    return msg0 + msg1 + msg2 + msg3 + "</div>";
                },
            });
            $('#otaccountNameHelp').popover('show');
            // if (!Settings.ROUTING_FLAG) {
            //     $('#routingNumberHelp').popover('show');
            // } else {
            //     $('#routingNumberHelp').popover('hide');
            // }
            Settings.ROUTING_FLAG = !Settings.ROUTING_FLAG;

            $('#otacNameCloseIcon').click(function (e) {
                e.preventDefault();
                Settings.ROUTING_FLAG = false;
                $('#otaccountNameHelp').popover('hide');
            });
        }

    }

    proto.cleanseText = function($input) {
        var value = $input.val();
        return value.replace(/[\u2018\u2019\u201B\u2032\u2035]/g, "'")  // Replace smart single quotes
                    .replace(/[\u201C\u201D\u201F\u2033\u2036]/g, '"');  // Replace smart double quotes
    }

    /**
     * @method onProductChange
     * @param {jQuery.Event} event
     * @param {App.Models.Product} product
     * @callback
     */
    proto.onProductChange = function(event, product) {
        this.product = product;
    }

    /**
     * @method onPaymentTermsButtonClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onPaymentTermsButtonClick = function(e) {
        e.preventDefault();
        var blocks = this.model.question.option.location.block;
        var priceInfo = this.product.info.priceInfo;
        var surchargeTM = '';
        if(blocks && blocks.length > 0 && priceInfo && priceInfo.length > 0)
        {
            var block = blocks.find(function(block) {
                return block.id == "TM"
            })
            if(block && block.surcharge.id)
            {
                if(block.surcharge.id)
                {
                    var priceOption = priceInfo.find(function(price) {
                        return price.id == block.surcharge.id
                    })
                    if(priceOption.option.price)
                    {
                        surchargeTM = priceOption.option.price;
                    }
                    else {
                        surchargeTM = '';
                    }
                }
            }
        }
    
        if (Helper.isSameTooltipClick(e)) {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
            } else {
                if ($("div[class*='custpop']").is(':visible')) {
                    $('.close.tooltip-close').trigger("click");
                }

                $('#paymentTermsHelp').popover({
                    html: true,
                    trigger: "manual",
                    placement: 'bottom',
                    content: function () {
                        var msg0 = '<div><img src="' + host_url + '/webasset/w2p_mobile/' + baseAppUrl + 'assets/media/images/tooltip-close-new.png" id="paymentTermsCloseIcon" alt="paymentTermsCloseIcon" class="close tooltip-close" style="position: absolute; top: 0; right: 0px;"/>';
                        var msg1 = "<div class='titletxt'>Payment Terms - " + (surchargeTM ? "$" + surchargeTM : "") + "</div><br/>"; 
                        var msg2 = '<div class="othertxt">Additional '+ (surchargeTM ? "$" + surchargeTM : "") + ' manufacturing charge applied.<br/></div>';
                        return msg0 + msg1 + msg2 + "</div>";
                    },
                });

                if (!Settings.PAYMENT_TERMS_FLAG) {
                    $('#paymentTermsHelp').popover('show');
                } else {
                    $('#paymentTermsHelp').popover('hide');
                }

                Settings.PAYMENT_TERMS_FLAG = !Settings.PAYMENT_TERMS_FLAG;
    
                $('#paymentTermsCloseIcon').click(function (e) {
                    Settings.PAYMENT_TERMS_FLAG = false;
                    $('#paymentTermsHelp').popover('hide');
                });
            }
        }
    return TextBlockLineQuestionController;
});
