define(function(require) {
    'use strict';

    var $ = require('jquery');
    var q = require('q');

    /**
     * @class App.Controllers.Event
     * @static
     */
    var EventController = {
        /**
         * jQuery.fn.on
         *
         * @method on
         * @chainable
         */
        on: $.fn.on,

        /**
         * jQuery.fn.one
         *
         * @method one
         * @chainable
         */
        one: $.fn.one,

        /**
         * jQuery.fn.off
         *
         * @method off
         * @chainable
         */
        off: $.fn.off,

        /**
         * jQuery.fn.trigger
         *
         * @method emit
         * @chainable
         */
        emit: $.fn.trigger,

        /**
         * Each of the above methods uses jQuery.each to call these methods
         * on all jQuery elements. This does not apply to this object, and
         * leaving the each method as is will iterate through all methods.
         * Instead, just call the callback with the context of EventController.
         *
         * @method each
         * @param {Function} callback
         * @param {Array} args
         * @chainable
         */
        each: function(callback, args) {
            // IE8 requires args to be defined
            callback.apply(this, args || []);

            return this;
        },

        /**
         * When an event is emitted on one emitter, trigger the same event
         * on this controller.
         *
         * @method tie
         * @param {String} event
         * @param {EventEmitter} emitter
         * @chainable
         */
        tie: function(event, emitter) {
            emitter.on(event, this.emit.bind(this, event));

            return this;
        },

        /**
         * Returns a promise that will be resolved when an event is fired once.
         * Useful for handling cases where more than one event needs to have
         * been fired before a callback is executed.
         *
         * @method when
         * @param {String} event
         * @return {Promise}
         */
        when: function(event) {
            var defer = q.defer();

            this.one(event, function(event, data) {
                defer.resolve(data);
            });

            return defer.promise;
        }
    };

    return EventController;

});
