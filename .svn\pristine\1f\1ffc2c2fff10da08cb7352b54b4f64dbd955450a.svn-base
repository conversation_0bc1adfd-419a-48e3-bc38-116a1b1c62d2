define(function(require) {
    'use strict';

    var AbstractStateModel = require('./Abstract');
    var DomEvents = require('../../constants/DomEvents');
    var EventController = require('../../controllers/Event');
    var StateEvents = require('../../constants/StateEvents');
    var indexOf = require('mout/array/indexOf');
    var inherits = require('mout/lang/inheritPrototype');
    var pluck = require('mout/array/pluck');

    /**
     * HTML4 history state stepper
     *
     * @class App.Models.State.Hash
     * @extends App.Models.State.Abstract
     *
     * @constructor
     */
    function HashStateModel() {
        AbstractStateModel.apply(this, arguments);

        EventController.on(DomEvents.HASH_CHANGE, this.onChange);
    }

    var proto = inherits(HashStateModel, AbstractStateModel);

    // -- Methods --------------------------------------------------------------

    /**
     * @method start
     * @chainable
     */
    proto.start = function() {
        this.current = -1;
        this.forward();

        return this;
    };

    /**
     * @method back
     * @chainable
     */
    proto.back = function() {
        // Restrict to lower bound
        if (this.current - 1 < 0) {
            history.back();
            return this;
        }

        // Go back
        this.current--;
        setTimeout(this.pushState, 0);

        return this;
    };

    /**
     * @method forward
     * @chainable
     */
    proto.forward = function() {
        // Restrict to upper bound
        if (this.current + 1 >= this.states.length) {
            return this;
        }

        // Go forward
        this.current++;
        setTimeout(this.pushState, 0);

        return this;
    };

    /**
     * @method pushState
     * @chainable
     */
    proto.pushState = function() {
        if (!location.hash || location.hash === '#') {
            // Set state (doesn't break the back button)
            location.replace(this.getState().hash);
        } else {
            // Change state
            location.assign(this.getState().hash);
        }

        return this;
    };

    // -- Event Handlers -------------------------------------------------------

    /**
     * @fires {Constants.StateEvents.CHANGE}
     *
     * @method onChange
     * @param {jQuery.Event} event
     * @chainable
     */
    proto.onChange = function() {
        var state = indexOf(pluck(this.states, 'hash'), location.hash);

        // Reset state
        if (state === -1) {
            this.start();
            return this;
        }

        // Update current on change
        this.current = state;

        // Notify application
        EventController.emit(StateEvents.CHANGE, this.getState().hash);

        return this;
    };

    return HashStateModel;
});
