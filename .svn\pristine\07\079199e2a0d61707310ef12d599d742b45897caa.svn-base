define(function(require) {
    'use strict';

    require('selectonic');

    var $ = require('jquery');
    var AbstractQuestionController = require('./Abstract');
    var Classes = require('../../constants/Classes');
    var ConfigurationProvider = require('../../providers/Configuration');
    var Content = require('i18n!../../constants/nls/en-us/Content');
    var DomEvents = require('../../constants/DomEvents');
    var EventController = require('../Event');
    var FontProvider = require('../../providers/Font');
    var KeyCodes = require('../../constants/KeyCodes');
    var Settings = require('../../constants/Settings');    
    var FbtModel = require('../../models/Fbt');
    var bindAll = require('mout/object/bindAll');
    var find = require('mout/array/find');
    var inherits = require('mout/lang/inheritPrototype');
    var ActionEvents = require('../../constants/ActionEvents');
    var StateEvents = require('../../constants/StateEvents');
    var ProductEvents = require('../../constants/ProductEvents');

    /**
     * @class App.Controllers.Question.Dropdown
     * @extends App.Controllers.Question.Abstract
     *
     * @constructor
     * @param {Object} config
     * @param {jQuery} config.view
     * @param {Models.Ui.Question} config.model
     */
    function DropdownQuestionController(config) {
        bindAll(this,
            'onChange',
            'onDocumentClick',
            'onTriggerClick',
            'onTriggerKeyUp',
            'onListBlur',
            'onListSelect',
            'onChangeCopies',
            'onFBTchangquantity',
            'onChangeQuantity',
            'hideVisibleList'
        );

        AbstractQuestionController.call(this, config);
    }

    var proto = inherits(DropdownQuestionController, AbstractQuestionController);

    /**
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = require('hbs!templates/question/dropdown');

    /**
     * @method richTemplate
     * @param {Object} model
     * @return {String}
     */
    proto.richTemplate = require('hbs!templates/question/dropdown/rich');

    /**
     * @method quantityTemplate
     * @param {Object} model
     * @type {String}
     */
    proto.quantityTemplate = require('hbs!templates/question/dropdown/quantity');

    /**
     * @method render
     * @chainable
     */
    proto.render = function() {
        var model = this.model;

        if (model.id === 'quantity') {
            this.template = this.quantityTemplate;
        }
        if (typeof FbtModel.fbtProducts != 'undefined' && model.id == 'productId' ) {
            if(Object.keys(FbtModel.fbtProducts).length > 0){
            if(!FbtModel.currentFbtProductIndex)
                FbtModel.currentFbtProductIndex =0;
             this.model.query.skuId = FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].pid;             
             this.model.setValue(FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].pid);
            }
        }
        if (model.id === 'typestyle') {
            return this.renderTypestyle();
        }

        return this.renderDefault();
    };

    /**
     * @method renderDefault
     * @chainable
     */
    proto.renderDefault = function() {
        var model = this.model;
        //console.log("Dropdown.renderDefault", model);
        var info = model.info;
        var options = model.options;
        var productId = false;

        var value = model.getValue() || (info && info['default']);
        if (typeof FbtModel.fbtProducts != 'undefined' && model.id == 'productId' ) {
            if (Object.keys(FbtModel.fbtProducts).length > 0) {
                if (!FbtModel.currentFbtProductIndex) {
                    FbtModel.currentFbtProductIndex = 0;
                }
                this.model.query.skuId = FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].pid;
                value = FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].pid;
            }
        }

        if (model.id === "quantity") {
            var unique = Math.random();
            model.unique = unique;
            
            for (let i = 0; i < options._items.length; i++) {
                options._items[i].unique = unique;
            }
            for (var i = 0; i < options._items.length; i++) {
              var currentQuantity = options._items[i].desc;
              var isQnatityValid = false;
              if(Array.isArray(info.option)) {
                info.option.some(function (qtyObj) {
                    if (qtyObj.id == model.getValue()) {
                      isQnatityValid = true;
                    }
                  });
              }              
              if (isQnatityValid) {
                if (parseInt(currentQuantity, 10) >= parseInt(model.getValue(), 10)) {
                  value = options._items[i].desc;
                  this.model.setValue(value);
                  break;
                }
          
                value = options._items[i].desc;
              } else {
                value = options._items[0].desc;
                this.model.setValue(value);          
                break;
              }
            }
          }
          
        if(model.id == 'productId' ){
            var unique = Math.random();
            model.unique = unique;
            
            for (let i = 0; i < options._items.length; i++) {
                options._items[i].unique = unique;
            }
            productId = true;
            model.isRequired = false;
            if ((options && options._items.length <= 1) || model.info.imprintType ) {
                return this;
            }
        }

        this.$view
            .html(this.template({
                id: model.id,
                desc: model.desc,
                isRequired: model.isRequired,
                'default': value,
                options: options && options._items,
                productId: productId
            }));

        return this;
    };

    /**
     * @method renderTypestyle
     * @chainable
     */
    proto.renderTypestyle = function() {
        var model = this.model;
        var info = model.info;
        var value = model.getValue() || (info && info['default']);

        return ConfigurationProvider
            .getConfiguration()
            .then(function(config) {
                return FontProvider
                    .setConfig(config)
                    .getFonts();
            }.bind(this))
            .then(function(fonts) {
                var items = fonts && fonts._items;

                // Prune the list of Font options to only
                // those available to the product.
                items = items.filter(function(font) {
                    return !!find(info.option, { id: font.id });
                });
                this.items = items;

                this.$view
                    .html(this.richTemplate({
                        id: model.id,
                        desc: model.desc,
                        isRequired: model.isRequired,
                        heading: Content.get('FONT_HEADING'),
                        innertext: Content.get('INNER_TEXT'),
                        'default': value,
                        options: items,
                        imageBase: Settings.SCENE7_HOST + Settings.SCENE7_API_AGM,
                        showSubHeading: false
                    }));
            }.bind(this));
    };

    /**
     * @method cacheElements
     * @chainable
     */
    proto.cacheElements = function() {
        var $view = this.$view;
        var $list = $view.find(Classes.DROPDOWN_LIST_SELECTOR);

        if (!$list.length) {
            return this;
        }

        this.$list = $list;
        this.$trigger = $view.find(Classes.DROPDOWN_TRIGGER_SELECTOR);
        this.$input = $view.find(Classes.DROPDOWN_VALUE_SELECTOR);

        return this;
    };

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        var model = this.model;
        var info = model.info;
        var value = model.getValue() || (info && info['default']);

        this.$view
            .on(DomEvents.CHANGE, this.onChange);

         EventController
        .on(ProductEvents.CHANGECOPIES, this.onChangeCopies);

        this.$view
                .on(DomEvents.CHANGE, Classes.INPUT_BOX_QUANTITY_SELECTOR, this.onChangeQuantity);

        EventController
        .on(ProductEvents.FBTQUANTITYCHANGE, this.onFBTchangquantity);


        if (!this.$list) {
            return this;
        }

        this.$view
            .on(DomEvents.CHANGE, Classes.INPUT_BOX_QUANTITY_SELECTOR, this.onChangeQuantity)
            .on(DomEvents.CLICK, Classes.DROPDOWN_TRIGGER_SELECTOR, this.onTriggerClick)
            .on(DomEvents.KEY_UP, Classes.DROPDOWN_TRIGGER_SELECTOR, this.onTriggerKeyUp);

        this.$list
            .selectonic({
                focusClass: Classes.DROPDOWN_ITEM_FOCUSED,
                selectedClass: Classes.DROPDOWN_ITEM_SELECTED,
                keyboard: true,
                keyboardMode: 'toggle',
                loop: true,
                multi: false,
                focusLost: this.onListBlur,
                select: this.onListSelect
            })
            .selectonic('select', '[data-value=' + value + ']')
            .selectonic('disable');

        return this;
    };

    proto.onChangeQuantity = function() {
        var value = $(event.target).val();
        /*if (this.model.id === 'productId') {
            this.model.query.skuId = value;
        }*/
        // this.model.setValue(value);
        EventController.emit(ActionEvents.CHANGE_QUANTITY_VALUE, value);
    }

    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        this.$view
            .off(DomEvents.CHANGE, this.onChange);

        if (!this.$list) {
            return this;
        }

        this.$view
            .off(DomEvents.CLICK, Classes.DROPDOWN_TRIGGER_SELECTOR, this.onTriggerClick)
            .off(DomEvents.KEY_UP, Classes.DROPDOWN_TRIGGER_SELECTOR, this.onTriggerKeyUp);

         EventController
        .off(ProductEvents.CHANGECOPIES, this.onChangeCopies);

        EventController
        .on(ProductEvents.FBTQUANTITYCHANGE, this.onFBTchangquantity);

        this.$list
            .selectonic('destroy');

        return this;
    };

    /**
     * @method showList
     * @chainable
     */
    proto.showList = function () {
        var $list = this.$list;
        this.hideVisibleList();
        if (!$list) {
            return this;
        }

        // Show list and enable selection
        $list
            .show()
            .selectonic('enable')
            .selectonic('focus', $list.selectonic('getSelected'))
            .selectonic('scroll');

        // Handle clicks on other stuff
        EventController
            .one(DomEvents.CLICK, this.onDocumentClick);

        if(window.box){
         for (var i = 0; i<window.box.length; i++) {
            window.box[i].$el.toggleClass(Classes.ZOOMBOX_ENABLED, false);
            window.box[i].$overlay.toggleClass(Classes.ZOOMBOX_HIGHLIGHT, false);
            }
        }
        return this;
    };

    /**
     * @method hideList
     * @chainable
     */
    proto.hideList = function () {
        var $list = this.$list;

        if (!$list) {
            return this;
        }

        // Flag as closing
        this.isClosing = true;

        // Hide list and disable selection
        $list
            .hide()
            .selectonic('disable');

        // Stop worrying about clicks on other stuff
        EventController
            .off(DomEvents.CLICK, this.onDocumentClick);

        return this;
    };

    /**
     * @method hideVisibleList
     * @chainable
     */
    proto.hideVisibleList = function () {
        var $list = $('.js-dropdown-list').filter(function(index,item){ 
            var style = window.getComputedStyle(item); 
            return style.display !== 'none';
        }).first();
        
        if ($list.length === 0) {
            return this;
        }

        // Flag as closing
        this.isClosing = true;

        // Hide list and disable selection
        $list
            .hide()
            .selectonic('disable');

        // Stop worrying about clicks on other stuff
        EventController
            .off(DomEvents.CLICK, this.onDocumentClick);

        return this;
    }

    /**
     * @method onChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onChange = function(event) {
        
        var value = $(event.target).val();       
        if(this.model.id == 'quantity') {
            let qty = this.model.options._items.map(mapQuantity);
            if(!qty.includes(value)) {
                this.model.setValue(this.model.options._items[0].desc);
                this.renderDefault();
                $("#QuantityCombobox").val(this.model.options._items[0].desc)	
            } else {
                this.model.setValue(value);
            }
            EventController.emit(ProductEvents.QNTY_CHANGE, this.model);
        } else {
            this.model.setValue(value);
        }        
        this.isValid();

        function mapQuantity(item) {
			return item.id;
		}
        
    };

    /**
     * When something was clicked outside of the current dropdown.
     * @method onDocumentClick
     * @param {jQuery.Event} event
     * @param {jQuery.Event} domEvent
     * @callback
     */
    proto.onDocumentClick = function(event, domEvent) {
        var $el = $(domEvent.target);

        if (!$el.closest(this.$view).length) {
            this.hideList();
        }
    };

    /**
     * @method onListBlur
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onListBlur = function() {
        this.hideList();
    };

    /**
     * @method onListSelect
     * @param {jQuery.Event} event
     * @param {Object} item
     * @param {HTMLElement} item.target
     * @callback
     */
    proto.onListSelect = function (event, item) {
        var $item = $(item.target);
        var value = $item.data('value');
        if (this.model.id == "typestyle") {
            var selectedFontImg = "";
            for (var i = 0; i < this.items.length; i++) {
                var fontItemModel = this.items[i];
                if (value == fontItemModel.id) {
                    selectedFontImg = '<img src="' + fontItemModel.url + '" alt="' + fontItemModel.desc + '">';
                    break;
                }
            }
            this.$trigger.html(selectedFontImg);
        } else {
            var selectedValue = this.model.id == "productId" ? this.model.options._items.filter(function (item) { return item.id == value; })[0].desc : value;
            var selectedString = '<span class="dList-term pricing-widget-list-item-label cursor-pointer">' + this.model.desc + ':</span><span class="dList-desc mb0 cursor-pointer">' + selectedValue + '<img src="' + host_url + '/webasset/w2p_mobile/' + baseAppUrl + 'assets/media/images/dropdown-arrow.png" class="dropdown-arrow"/></span>';
            this.$trigger.html(selectedString);
        }

        this.$input
            .val(value)
            .trigger(DomEvents.CHANGE);

        this.hideList();
    };

    /**
     * @method onTriggerClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onTriggerClick = function(event) {
        if (!this.$list) {
            return this;
        }

        if (this.$list.is(':visible')) {
            this.hideList();
        } else {
            event.stopPropagation();
            this.showList();
        }
    };

    /**
     * @method onTriggerKeyUp
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onTriggerKeyUp = function(event) {
        if (this.isClosing === true) {
            this.isClosing = false;
            return;
        }

        switch (event.which) {
            case KeyCodes.ENTER:
                event.preventDefault();
                this.showList();
                break;

            case KeyCodes.ESCAPE:
                event.preventDefault();
                this.hideList();
                break;
        }
    };

      /**
     * @method 
     * @callback onChangeCopies
     */
    proto.onChangeCopies = function(event, value) {
        var model = this.model;
        var prevValue = model.value;

        // Quantity option change based on copies
        if(model.id === 'quantity'){
            var info = model.info;
            var quantity = info.infooptions;
            var onflag = false;
            
            for (var i = 0; i < quantity.length; i ++ ) {
                if( quantity[i].id == value ) {
                    var options = quantity[i].option;
                 }       
             }

            if(options){
                for ( i = 0; i < options.length; i ++ ) {
                    options[i].desc = options[i].qty;
                    if(prevValue ==  options[i].desc) {
                        onflag = true;
                    }
                }
                this.model.options._items = options;
                this.model.options._tempItems = options
            }
                  
            this.renderDefault();
            if(onflag) {
                this.model.setValue(prevValue);
                $("#QuantityCombobox").val(prevValue);
            } else {
                this.model.setValue(this.model.options._items[0].desc);
                $("#QuantityCombobox").val(this.model.options._items[0].desc);
            }
            
            // this.isValid();
        }
    };

       proto.onFBTchangquantity = function(event, value) {
                if(this.model.id == 'quantity'){
                    this.model.value = value;
                    this.renderDefault();
                }
                
                // this.isValid();
          }


    return DropdownQuestionController;
});
