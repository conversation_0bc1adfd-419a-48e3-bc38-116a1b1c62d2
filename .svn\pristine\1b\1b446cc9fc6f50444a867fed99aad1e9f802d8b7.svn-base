define(function (require) {
    'use strict';

    var AbstractModel = require('../Abstract');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.ClipArt.Logo
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} logo
     */
    var ClipArtLogoModel = function (logo) {
        AbstractModel.call(this, logo);
    };

    var proto = inherits(ClipArtLogoModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} input
     * @chainable
     */
    proto.init = function(input) {

        /**
         * @property id
         * @default {null}
         * @type {string}
         */
        this.id = null;

        /**
         * @property description
         * @default {null}
         * @type {string}
         */
        this.description = null;

        /**
         * @property image
         * @default {null}
         * @type {string}
         */
        this.image = null;

        this.createChildren();

        // run the parent init method to parse determine the data type
        base.init.call(this, input);

        return this;
    };

    /**
     * @method createChildren
     * @return {App.Models.ClipArt.Logo}
     * @chainable
     */
    proto.createChildren = function() {
        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param {object} json the original JSON data.
     */
    proto.fromJSON = function(json) {
        if (!json.id) {
            json = this.stripInvalidFields(json);
            if (!json.id && json.logo !== undefined) {
                json = json.logo;
            }
        }
        this.id = json.id;
        this.description = json.d;
        this.image = json.img;
    };

    return ClipArtLogoModel;
});
