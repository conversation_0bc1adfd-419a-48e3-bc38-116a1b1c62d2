define(function(require) {
    'use strict';

    var handlebars = require('hbs/handlebars');

    /**
     * Native array slice method for use with arguments.
     *
     * @type {Function}
     */
    var slice = Array.prototype.slice;

    /**
     * Template cache.
     *
     * @type {Object.<Function(Object):String>}
     */
    var templates = {};

    /**
     * Matches all `$[[VAR]]` in a string.
     *
     * @type {RegExp}
     */
    var variablePlaceholder = /\$\[\[VAR\]\]/g;

    /**
     * Localization abstract.
     * http://requirejs.org/docs/api.html#i18n
     *
     * @class App.Constants.Localization.Abstract
     * @static
     */
    var Abstract = {
        root: {
            /**
             * Gets a content constant by key, if it exists, otherwise returns
             * the key verbatim. Multiple keys may be passed. If the last
             * argument is an object, it's assumed to be template data.
             *
             * @method get
             * @param {...String} key
             * @param {Object} [data]
             * @return {String}
             */
            get: function() {
                var value;
                var data;
                var args = slice.call(arguments, 0);
                var length = args.length;
                var i = 0;

                if (typeof args.slice(-1)[0] === 'object') {
                    data = args.pop();
                    length = args.length;
                }

                // Lookup values by key
                for (i = 0; i < length; i++) {
                    value = this[args[i]];

                    if (value != null) {
                        return this.parse(value, data);
                    }
                }

                // Fallback to literal key values
                for (i = length; i--;) {
                    value = args[i];

                    if (value != null) {
                        return this.parse(value, data);
                    }
                }

                return '';
            },

            /**
             * @method parse
             * @param {String} value
             * @param {Object} data
             */
            parse: function (value, data) {
                data = data || {};

                if (!value || typeof value !== 'string') {
                    return value;
                }

                if (variablePlaceholder.test(value)) {
                    value = value.replace(variablePlaceholder, '{{value}}');
                }

                var template = templates[value] || (templates[value] = handlebars.compile(value));

                return template(data);
            }
        }
    };

    return Abstract;
});
