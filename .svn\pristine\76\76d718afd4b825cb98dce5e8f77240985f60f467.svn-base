<?xml version="1.0" encoding="UTF-8"?>
<controls>
   <!-- Stock - 92663 -->
   <type id="UI_X">
      <step id="Step 1" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="" next="Add To Cart" trackEvent="{'omniture': 'event9,event64'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="quantity" required="Y" ctl="dropdown" price="Y" />
         <question id="productId" required="Y" ctl="dropdown" price="Y" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- DLT104 -->
   <type id="UI_1">
      <step id="Step 1" desc="Designs" progressTitle="Designs" tlId="Designs" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event15'}">
         <question id="matrix1" required="Y" ctl="swatch" price="Y" />
         <question id="layout" required="Y" ctl="text">
               <block id="RM" required="N" />
         </question>
      </step>
      <step id="Step 2" desc="Company &amp; Bank Information"   tlId="CompanyBankInfo" prev="Previous Step" next="Next Step">
         <tab id="1" desc="Bank Information" progressTitle="Bank" tlId="BankInfo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event18'}">
            <question id="BANK_INFORMATION" required="N" ctl="x-note" />
            <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
            <question id="routingNumber" required="Y" ctl="text" />
            <question id="accountNumber" required="Y" ctl="text" />
            <question id="numbering" required="Y" ctl="text" />
            <question id="reverseNumbering" required="N" ctl="checkbox" />
            <question id="layout" required="Y" ctl="text">
               <block id="BI" required="Y" />
            </question>
         </tab>
         <tab id="2" desc="Company Information" progressTitle="Company" tlId="YourInfo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event16'}">
            <question id="YOUR_INFORMATION" required="N" ctl="x-note" />
            <question id="layout" required="N" ctl="text">
               <block id="AD" required="N" />
               <block id="AL" required="N" />
               <block id="BA" required="N" />
               <block id="BP" required="N" />
               <block id="CA" required="N" />
               <block id="CC" required="N" />
               <!--<block id="CD" required="N"/> -->
               <block id="CE" required="N" />
               <block id="CF" required="N" />
               <block id="CG" required="N" />
               <block id="CH" required="N" />
               <block id="CI" required="N" />
               <block id="CJ" required="N" />
               <block id="CR" required="N" />
               <block id="CS" required="N" />
               <block id="DE" required="N" />
               <block id="DP" required="N" />
               <block id="DW" required="N" />
               <block id="ED" required="N" />
               <block id="EI" required="N" />
               <block id="EM" required="N" />
               <block id="I1" required="N" />
               <block id="I2" required="N" />
               <block id="LC" required="N" />
               <block id="LL" required="N" />
               <block id="LO" required="N" />
               <block id="LR" required="N" />
               <block id="MA" required="N" />
               <block id="ML" required="N" />
               <block id="NN" required="N" />
               <block id="PE" required="N" />
               <block id="PH" required="N" />
               <block id="PN" required="N" />
               <block id="PO" required="N" />
               <block id="PV" required="N" />
               <block id="PP" required="N" />
               <block id="RA" required="N" />
               <block id="RC" required="N" />
               <!-- <block id="RM" required="N" /> -->
               <block id="SA" required="N" />
               <block id="SC" required="N" />
               <block id="SE" required="N" />
               <block id="SM" required="N" />
               <block id="SN" required="N" />
               <block id="SS" required="N" />
               <block id="SW" required="N" />
               <block id="TA" required="N" />
               <block id="TM" required="N" />
               <block id="UC" required="N" />
               <block id="UL" required="N" />
               <block id="UR" required="N" />
               <block id="US" required="N" />
               <!-- ADDITIONAL_INFORMATION -->
               <block id="H1" required="N" />
               <block id="H2" required="N" />
               <block id="C1" required="N" />
               <block id="C2" required="N" />
               <block id="C3" required="N" />
               <block id="C4" required="N" />
               <block id="C5" required="N" />
               <block id="C6" required="N" />
               <block id="C7" required="N" />
               <block id="C8" required="N" />
               <block id="C9" required="N" />
               <block id="LS" required="N" />
               <block id="SL" required="N" />
               <block id="ST" required="N" />
               <block id="SH" required="N" />
            </question>
         </tab>
      </step>
      <step id="Step 3" desc="Logo"  tlId="Logo" prev="Previous Step" next="Next Step">
          <tab id="1" desc="Logo" tlId="Logo" progressTitle="Logo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event76'}">
            <question id="logo" required="Y" ctl="radio" price="Y" />
         </tab>
         <tab id="2" desc="Colors &amp; Fonts" progressTitle="Color" tlId="FontTextColor" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event75'}">
            <!--<question id="COLORS_AND_FONTS" required="N" ctl="x-note" />-->
            <question id="inkColor1" required="Y" ctl="rgb" price="Y" />
            <question id="inkColor2" required="N" ctl="rgb" price="Y" />
            <question id="typestyle" required="Y" ctl="dropdown" />
         </tab>
      </step>
      <step id="Step 4" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event64'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="quantity" required="Y" ctl="dropdown" price="Y" />
         <question id="productId" required="Y" ctl="dropdown" price="Y" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- 100018 -->
   <type id="UI_2">
      <step id="Step 1" desc="Company &amp; Bank Information"  tlId="CompanyBankInfo" prev="" next="Next Step">
          <tab id="1" desc="Bank Information" progressTitle="Bank" tlId="BankInfo" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event18'}">
            <question id="BANK_INFORMATION" required="N" ctl="x-note" />
            <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
            <question id="routingNumber" required="Y" ctl="text" />
            <question id="accountNumber" required="Y" ctl="text" />
            <question id="numbering" required="Y" ctl="text" />
            <question id="reverseNumbering" required="N" ctl="checkbox" />
            <question id="layout" required="Y" ctl="text">
               <block id="BI" required="Y" />
            </question>
         </tab>
         <tab id="2" desc="Your Information" progressTitle="Company" tlId="YourInfo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event16'}">
            <question id="YOUR_INFORMATION" required="N" ctl="x-note" />
            <question id="layout" required="N" ctl="text">
               <block id="AD" required="N" />
               <block id="AL" required="N" />
               <block id="BA" required="N" />
               <block id="BP" required="N" />
               <block id="CA" required="N" />
               <block id="CC" required="N" />
               <!--<block id="CD" required="N"/> -->
               <block id="CE" required="N" />
               <block id="CF" required="N" />
               <block id="CG" required="N" />
               <block id="CH" required="N" />
               <block id="CI" required="N" />
               <block id="CJ" required="N" />
               <block id="CR" required="N" />
               <block id="CS" required="N" />
               <block id="DE" required="N" />
               <block id="DP" required="N" />
               <block id="DW" required="N" />
               <block id="ED" required="N" />
               <block id="EI" required="N" />
               <block id="EM" required="N" />
               <block id="I1" required="N" />
               <block id="I2" required="N" />
               <block id="LC" required="N" />
               <block id="LL" required="N" />
               <block id="LO" required="N" />
               <block id="LR" required="N" />
               <block id="MA" required="N" />
               <block id="ML" required="N" />
               <block id="NN" required="N" />
               <block id="PE" required="N" />
               <block id="PH" required="N" />
               <block id="PN" required="N" />
               <block id="PO" required="N" />
               <block id="PV" required="N" />
               <block id="PP" required="N" />
               <block id="RA" required="N" />
               <block id="RC" required="N" />
               <!-- <block id="RM" required="N" /> -->
               <block id="SA" required="N" />
               <block id="SC" required="N" />
               <block id="SE" required="N" />
               <block id="SM" required="N" />
               <block id="SN" required="N" />
               <block id="SS" required="N" />
               <block id="SW" required="N" />
               <block id="TA" required="N" />
               <block id="TM" required="N" />
               <block id="UC" required="N" />
               <block id="UL" required="N" />
               <block id="UR" required="N" />
               <block id="US" required="N" />
               <!-- ADDITIONAL_INFORMATION -->
               <block id="H1" required="N" />
               <block id="H2" required="N" />
               <block id="C1" required="N" />
               <block id="C2" required="N" />
               <block id="C3" required="N" />
               <block id="C4" required="N" />
               <block id="C5" required="N" />
               <block id="C6" required="N" />
               <block id="C7" required="N" />
               <block id="C8" required="N" />
               <block id="C9" required="N" />
               <block id="LS" required="N" />
               <block id="SL" required="N" />
               <block id="ST" required="N" />
               <block id="SH" required="N" />
            </question>
         </tab>
      </step>
      <step id="Step 2" desc="Logo" tlId="Logo" prev="Previous Step" next="Next Step">
         <tab id="1" desc="Logo" progressTitle="Logo" tlId="Logo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event76'}">
            <question id="logo" required="Y" ctl="radio" price="Y" />
         </tab>
         <tab id="2" desc="Colors &amp; Fonts" progressTitle="Color" tlId="FontTextColor" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event75'}">
            <question id="inkColor1" required="Y" ctl="rgb" price="Y" />
            <question id="inkColor2" required="N" ctl="rgb" price="Y" />
            <question id="typestyle" required="Y" ctl="dropdown" />
         </tab>
      </step>
      <step id="Step 3" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event64'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="quantity" required="Y" ctl="dropdown" price="Y" />
         <question id="productId" required="Y" ctl="dropdown" price="Y" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- 55T -->
   <type id="UI_3">
     <step id="Step 1" desc="Design" progressTitle="Design" tlId="Design" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event15'}">
         <question id="matrix1" required="Y" ctl="swatch" price="Y" />
      </step>
      <step id="Step 2" desc="Company Information" tlId="CompanyBankInfo" prev="Previous Step" next="Next Step">
         <tab id="1" desc="Your Information" progressTitle="Company" tlId="YourInfo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event16'}">
            <question id="NUMBERING_INFORMATION" required="N" ctl="x-note" />
            <question id="numbering" required="Y" ctl="text" />
            <question id="reverseNumbering" required="N" ctl="checkbox" />
            <question id="YOUR_INFORMATION" required="N" ctl="x-note" />
            <question id="layout" required="N" ctl="text">
               <block id="AD" required="N" />
               <block id="AL" required="N" />
               <block id="BA" required="N" />
               <block id="BP" required="N" />
               <block id="CA" required="N" />
               <block id="CC" required="N" />
               <!--<block id="CD" required="N"/> -->
               <block id="CE" required="N" />
               <block id="CF" required="N" />
               <block id="CG" required="N" />
               <block id="CH" required="N" />
               <block id="CI" required="N" />
               <block id="CJ" required="N" />
               <block id="CR" required="N" />
               <block id="CS" required="N" />
               <block id="DP" required="N" />
               <block id="DE" required="N" />
               <block id="DW" required="N" />
               <block id="ED" required="N" />
               <block id="EI" required="N" />
               <block id="EM" required="N" />
               <block id="I1" required="N" />
               <block id="I2" required="N" />
               <block id="LC" required="N" />
               <block id="LL" required="N" />
               <block id="LO" required="N" />
               <block id="LR" required="N" />
               <block id="MA" required="N" />
               <block id="ML" required="N" />
               <block id="NN" required="N" />
               <block id="PE" required="N" />
               <block id="PH" required="N" />
               <block id="PN" required="N" />
               <block id="PO" required="N" />
               <block id="PV" required="N" />
               <block id="PP" required="N" />
               <block id="RA" required="N" />
               <block id="RC" required="N" />
               <!-- <block id="RM" required="N" /> -->
               <block id="SA" required="N" />
               <block id="SC" required="N" />
               <block id="SE" required="N" />
               <block id="SM" required="N" />
               <block id="SN" required="N" />
               <block id="SS" required="N" />
               <block id="SW" required="N" />
               <block id="TA" required="N" />
               <block id="TM" required="N" />
               <block id="UC" required="N" />
               <block id="UL" required="N" />
               <block id="UR" required="N" />
               <block id="US" required="N" />
               <!-- ADDITIONAL_INFORMATION -->
               <block id="H1" required="N" />
               <block id="H2" required="N" />
               <block id="C1" required="N" />
               <block id="C2" required="N" />
               <block id="C3" required="N" />
               <block id="C4" required="N" />
               <block id="C5" required="N" />
               <block id="C6" required="N" />
               <block id="C7" required="N" />
               <block id="C8" required="N" />
               <block id="C9" required="N" />
               <block id="LS" required="N" />
               <block id="SL" required="N" />
               <block id="ST" required="N" />
               <block id="SH" required="N" />
            </question>
         </tab>
         <tab id="2" desc="Logo" progressTitle="Logo" tlId="Logo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event76'}">
            <question id="logo" required="Y" ctl="radio" price="Y" />
         </tab>
      </step>
      <step id="Step 3" desc="Colors &amp; Fonts" tlId="FontTextColor" prev="Previous Step" next="Next Step">
         <tab id="1" desc="Colors &amp; Fonts" progressTitle="Color" tlId="FontTextColor" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event75'}">
            <question id="inkColor1" required="Y" ctl="rgb" price="Y" />
            <question id="inkColor2" required="N" ctl="rgb" price="Y" />
            <question id="typestyle" required="Y" ctl="dropdown" />
         </tab>
      </step>
      <step id="Step 4" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event64'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="quantity" required="Y" ctl="dropdown" price="Y" />
         <question id="productId" required="Y" ctl="dropdown" price="Y" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- 610 -->
   <type id="UI_4">
      <step id="Step 1" desc="Company Information" tlId="CompanyBankInfo" prev="" next="Next Step">
         <tab id="1" desc="Your Information" progressTitle="Company" tlId="YourInfo" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event16'}">
            <question id="NUMBERING_INFORMATION" required="N" ctl="x-note" />
            <question id="numbering" required="Y" ctl="text" />
            <question id="reverseNumbering" required="N" ctl="checkbox" />
            <question id="YOUR_INFORMATION" required="N" ctl="x-note" />
            <question id="layout" required="N" ctl="text">
               <block id="AD" required="N" />
               <block id="AL" required="N" />
               <block id="BA" required="N" />
               <block id="BP" required="N" />
               <block id="CA" required="N" />
               <block id="CC" required="N" />
               <!--<block id="CD" required="N"/> -->
               <block id="CE" required="N" />
               <block id="CF" required="N" />
               <block id="CG" required="N" />
               <block id="CH" required="N" />
               <block id="CI" required="N" />
               <block id="CJ" required="N" />
               <block id="CR" required="N" />
               <block id="CS" required="N" />
               <block id="DE" required="N" />
               <block id="DP" required="N" />
               <block id="DW" required="N" />
               <block id="ED" required="N" />
               <block id="EI" required="N" />
               <block id="EM" required="N" />
               <block id="I1" required="N" />
               <block id="I2" required="N" />
               <!--<block id="IM" required="N"/> -->
               <block id="LC" required="N" />
               <block id="LL" required="N" />
               <block id="LO" required="N" />
               <block id="LR" required="N" />
               <block id="MA" required="N" />
               <block id="ML" required="N" />
               <block id="NN" required="N" />
               <block id="PE" required="N" />
               <block id="PH" required="N" />
               <block id="PN" required="N" />
               <block id="PO" required="N" />
               <block id="PV" required="N" />
               <block id="PP" required="N" />
               <block id="RA" required="N" />
               <block id="RC" required="N" />
               <!-- <block id="RM" required="N" /> -->
               <block id="SA" required="N" />
               <block id="SC" required="N" />
               <block id="SE" required="N" />
               <block id="SM" required="N" />
               <block id="SN" required="N" />
               <block id="SS" required="N" />
               <block id="SW" required="N" />
               <block id="TA" required="N" />
               <block id="TM" required="N" />
               <block id="UC" required="N" />
               <block id="UL" required="N" />
               <block id="UR" required="N" />
               <block id="US" required="N" />
               <!-- ADDITIONAL_INFORMATION -->
               <block id="H1" required="N" />
               <block id="H2" required="N" />
               <block id="C1" required="N" />
               <block id="C2" required="N" />
               <block id="C3" required="N" />
               <block id="C4" required="N" />
               <block id="C5" required="N" />
               <block id="C6" required="N" />
               <block id="C7" required="N" />
               <block id="C8" required="N" />
               <block id="C9" required="N" />
               <block id="LS" required="N" />
               <block id="SL" required="N" />
               <block id="ST" required="N" />
               <block id="SH" required="N" />
            </question>
         </tab>
         <tab id="3" desc="Logo" progressTitle="Logo" tlId="Logo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event76'}">
            <question id="logo" required="Y" ctl="radio" price="Y" />
         </tab>
      </step>
      <step id="Step 3" desc="Colors &amp; Fonts" tlId="FontTextColor" prev="Previous Step" next="Next Step">
         <tab id="1" desc="Colors &amp; Fonts" progressTitle="Color" tlId="FontTextColor" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event75'}">
            <question id="inkColor1" required="Y" ctl="rgb" price="Y" />
            <question id="inkColor2" required="N" ctl="rgb" price="Y" />
            <question id="typestyle" required="Y" ctl="dropdown" />
         </tab>
      </step>
      <step id="Step 4" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event64'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="quantity" required="Y" ctl="dropdown" price="Y" />
         <question id="productId" required="Y" ctl="dropdown" price="Y" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- D142 -->
   <type id="UI_5">
      <step id="Step 1" desc="Company &amp; Bank Information" tlId="CompanyBankInfo" prev="" next="Next Step">
         <tab id="1" desc="Your Information" progressTitle="Company" tlId="YourInfo" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event16'}">
            <question id="YOUR_INFORMATION" required="N" ctl="x-note" />
            <question id="layout" required="N" ctl="text">
               <block id="AD" required="N" />
               <block id="AL" required="N" />
               <block id="BA" required="N" />
               <block id="BP" required="N" />
               <block id="CA" required="N" />
               <block id="CC" required="N" />
               <!--<block id="CD" required="N"/> -->
               <block id="CE" required="N" />
               <block id="CF" required="N" />
               <block id="CG" required="N" />
               <block id="CH" required="N" />
               <block id="CI" required="N" />
               <block id="CJ" required="N" />
               <block id="CR" required="N" />
               <block id="CS" required="N" />
               <block id="DE" required="N" />
               <block id="DP" required="N" />
               <block id="DW" required="N" />
               <block id="ED" required="N" />
               <block id="EI" required="N" />
               <block id="EM" required="N" />
               <block id="I1" required="N" />
               <block id="I2" required="N" />
               <block id="LC" required="N" />
               <block id="LL" required="N" />
               <block id="LO" required="N" />
               <block id="LR" required="N" />
               <block id="MA" required="N" />
               <block id="ML" required="N" />
               <block id="NN" required="N" />
               <block id="PE" required="N" />
               <block id="PH" required="N" />
               <block id="PN" required="N" />
               <block id="PO" required="N" />
               <block id="PV" required="N" />
               <block id="PP" required="N" />
               <block id="RA" required="N" />
               <block id="RC" required="N" />
               <!-- <block id="RM" required="N" /> -->
               <block id="SA" required="N" />
               <block id="SC" required="N" />
               <block id="SE" required="N" />
               <block id="SM" required="N" />
               <block id="SN" required="N" />
               <block id="SS" required="N" />
               <block id="SW" required="N" />
               <block id="TA" required="N" />
               <block id="TM" required="N" />
               <block id="UC" required="N" />
               <block id="UL" required="N" />
               <block id="UR" required="N" />
               <block id="US" required="N" />
               <!-- ADDITIONAL_INFORMATION -->
               <block id="H1" required="N" />
               <block id="H2" required="N" />
               <block id="C1" required="N" />
               <block id="C2" required="N" />
               <block id="C3" required="N" />
               <block id="C4" required="N" />
               <block id="C5" required="N" />
               <block id="C6" required="N" />
               <block id="C7" required="N" />
               <block id="C8" required="N" />
               <block id="C9" required="N" />
               <block id="LS" required="N" />
               <block id="SL" required="N" />
               <block id="ST" required="N" />
               <block id="SH" required="N" />
            </question>
         </tab>
         <tab id="2" desc="Account Information" progressTitle="Account" tlId="TransactionalInfo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event18'}">
            <question id="ACCOUNT_INFORMATION" required="N" ctl="x-note" />
            <question id="numbering" required="Y" ctl="text" />
            <question id="reverseNumbering" required="N" ctl="checkbox" />
         </tab>
         <tab id="3" desc="Colors &amp; Fonts" progressTitle="Color" tlId="FontTextColor" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event75'}">
            <question id="inkColor1" required="Y" ctl="rgb" price="Y" />
            <question id="inkColor2" required="N" ctl="rgb" price="Y" />
            <question id="typestyle" required="Y" ctl="dropdown" />
         </tab>
      </step>
      <step id="Step 2" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event64'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="quantity" required="Y" ctl="dropdown" price="Y" />
         <question id="productId" required="Y" ctl="dropdown" price="Y" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- 56201N -->
   <type id="UI_6">
      <step id="Step 1" desc="Design" progressTitle="Design" tlId="Design" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event15'}">
         <question id="matrix1" required="Y" ctl="swatch" price="Y" />
      </step>
      <step id="Step 2" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event64'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="quantity" required="Y" ctl="dropdown" price="Y" />
         <question id="productId" required="Y" ctl="dropdown" price="Y" />
         <question id="DETAILS" required="N" ctl="x-details" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
      </step>
   </type>
   <!-- D2023 - C701 -->
   <type id="UI_7">
      <step id="Step 1" desc="Company &amp; Bank Information" tlId="CompanyBankInfo" prev="" next="Next Step">
         <tab id="1" desc="Bank Information" progressTitle="Bank" tlId="BankInfo" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event18'}">
            <question id="BANK_INFORMATION" required="N" ctl="x-note" />
            <question id="routingNumber" required="Y" ctl="text" />
            <question id="accountNumber" required="Y" ctl="text" />
            <question id="numbering" required="Y" ctl="text" />
            <question id="reverseNumbering" required="N" ctl="checkbox" />
            <question id="layout" required="Y" ctl="text">
               <block id="BI" required="Y" />
            </question>
         </tab>
         <tab id="2" desc="Your Information" progressTitle="Company" tlId="YourInfo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event16'}">
            <question id="YOUR_INFORMATION" required="N" ctl="x-note" />
            <question id="layout" required="N" ctl="text">
               <block id="AD" required="N" />
               <block id="AL" required="N" />
               <block id="BA" required="N" />
               <block id="BP" required="N" />
               <block id="CA" required="N" />
               <block id="CC" required="N" />
               <!--<block id="CD" required="N"/> -->
               <block id="CE" required="N" />
               <block id="CF" required="N" />
               <block id="CG" required="N" />
               <block id="CH" required="N" />
               <block id="CI" required="N" />
               <block id="CJ" required="N" />
               <block id="CR" required="N" />
               <block id="CS" required="N" />
               <block id="DE" required="N" />
               <block id="DP" required="N" />
               <block id="DW" required="N" />
               <block id="ED" required="N" />
               <block id="EI" required="N" />
               <block id="EM" required="N" />
               <block id="I1" required="N" />
               <block id="I2" required="N" />
               <block id="LC" required="N" />
               <block id="LL" required="N" />
               <block id="LO" required="N" />
               <block id="LR" required="N" />
               <block id="MA" required="N" />
               <block id="ML" required="N" />
               <block id="NN" required="N" />
               <block id="PE" required="N" />
               <block id="PH" required="N" />
               <block id="PN" required="N" />
               <block id="PO" required="N" />
               <block id="PV" required="N" />
               <block id="PP" required="N" />
               <block id="RA" required="N" />
               <block id="RC" required="N" />
               <!-- <block id="RM" required="N" /> -->
               <block id="SA" required="N" />
               <block id="SC" required="N" />
               <block id="SE" required="N" />
               <block id="SM" required="N" />
               <block id="SN" required="N" />
               <block id="SS" required="N" />
               <block id="SW" required="N" />
               <block id="TA" required="N" />
               <block id="TM" required="N" />
               <block id="UC" required="N" />
               <block id="UL" required="N" />
               <block id="UR" required="N" />
               <block id="US" required="N" />
               <!-- ADDITIONAL_INFORMATION -->
               <block id="H1" required="N" />
               <block id="H2" required="N" />
               <block id="C1" required="N" />
               <block id="C2" required="N" />
               <block id="C3" required="N" />
               <block id="C4" required="N" />
               <block id="C5" required="N" />
               <block id="C6" required="N" />
               <block id="C7" required="N" />
               <block id="C8" required="N" />
               <block id="C9" required="N" />
               <block id="LS" required="N" />
               <block id="SL" required="N" />
               <block id="ST" required="N" />
               <block id="SH" required="N" />
            </question>
         </tab>
         <!--
         <tab id="3" desc="Account Information" tlId="TransactionalInfo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event18'}">
            <question id="ACCOUNT_INFORMATION" required="N" ctl="x-note" />
            <question id="routingNumber" required="Y" ctl="text" />
            <question id="accountNumber" required="Y" ctl="text" />
            <question id="numbering" required="Y" ctl="text" />
            <question id="reverseNumbering" required="N" ctl="checkbox" />
         </tab>
         -->
         <tab id="3" desc="Colors &amp; Fonts" progressTitle="Color" tlId="FontTextColor" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event76'}">
            <question id="inkColor1" required="Y" ctl="rgb" price="Y" />
            <question id="inkColor2" required="N" ctl="rgb" price="Y" />
            <question id="typestyle" required="Y" ctl="dropdown" />
         </tab>
      </step>
      <step id="Step 2" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event64'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="quantity" required="Y" ctl="dropdown" price="Y" />
         <question id="productId" required="Y" ctl="dropdown" price="Y" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- -->
   <type id="UI_8">
      <step id="Step 1" desc="Design" progressTitle="Design" tlId="Design" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event15'}">
         <question id="matrix1" required="Y" ctl="swatch" price="Y" />
      </step>
      <step id="Step 2" desc="Company &amp; Bank Information" tlId="CompanyBankInfo" prev="Previous Step" next="Next Step">
         <tab id="1" desc="Bank Information" progressTitle="Bank" tlId="BankInfo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event18'}">
            <question id="BANK_INFORMATION" required="N" ctl="x-note" />
            <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
            <question id="routingNumber" required="Y" ctl="text" />
            <question id="accountNumber" required="Y" ctl="text" />
            <question id="numbering" required="Y" ctl="text" />
            <question id="reverseNumbering" required="N" ctl="checkbox" />
            <question id="layout" required="Y" ctl="text">
               <block id="BI" required="Y" />
            </question>
         </tab>
         <tab id="2" desc="Your Information" progressTitle="Company" tlId="YourInfo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event16'}">
            <question id="YOUR_INFORMATION" required="N" ctl="x-note" />
            <question id="layout" required="N" ctl="text">
               <block id="AD" required="N" />
               <block id="AL" required="N" />
               <block id="BA" required="N" />
               <block id="BP" required="N" />
               <block id="CA" required="N" />
               <block id="CC" required="N" />
               <!--<block id="CD" required="N"/> -->
               <block id="CE" required="N" />
               <block id="CF" required="N" />
               <block id="CG" required="N" />
               <block id="CH" required="N" />
               <block id="CI" required="N" />
               <block id="CJ" required="N" />
               <block id="CR" required="N" />
               <block id="CS" required="N" />
               <block id="DE" required="N" />
               <block id="DP" required="N" />
               <block id="DW" required="N" />
               <block id="ED" required="N" />
               <block id="EI" required="N" />
               <block id="EM" required="N" />
               <block id="I1" required="N" />
               <block id="I2" required="N" />
               <block id="LC" required="N" />
               <block id="LL" required="N" />
               <block id="LO" required="N" />
               <block id="LR" required="N" />
               <block id="MA" required="N" />
               <block id="ML" required="N" />
               <block id="NN" required="N" />
               <block id="PE" required="N" />
               <block id="PH" required="N" />
               <block id="PN" required="N" />
               <block id="PO" required="N" />
               <block id="PV" required="N" />
               <block id="PP" required="N" />
               <block id="RA" required="N" />
               <block id="RC" required="N" />
               <!-- <block id="RM" required="N" /> -->
               <block id="SA" required="N" />
               <block id="SC" required="N" />
               <block id="SE" required="N" />
               <block id="SM" required="N" />
               <block id="SN" required="N" />
               <block id="SS" required="N" />
               <block id="SW" required="N" />
               <block id="TA" required="N" />
               <block id="TM" required="N" />
               <block id="UC" required="N" />
               <block id="UL" required="N" />
               <block id="UR" required="N" />
               <block id="US" required="N" />
               <!-- ADDITIONAL_INFORMATION -->
               <block id="H1" required="N" />
               <block id="H2" required="N" />
               <block id="C1" required="N" />
               <block id="C2" required="N" />
               <block id="C3" required="N" />
               <block id="C4" required="N" />
               <block id="C5" required="N" />
               <block id="C6" required="N" />
               <block id="C7" required="N" />
               <block id="C8" required="N" />
               <block id="C9" required="N" />
               <block id="LS" required="N" />
               <block id="SL" required="N" />
               <block id="ST" required="N" />
               <block id="SH" required="N" />
            </question>
         </tab>
         <tab id="3" desc="Colors &amp; Fonts" progressTitle="Color" tlId="FontTextColor" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event75'}">
            <question id="inkColor1" required="Y" ctl="rgb" price="Y" />
            <question id="inkColor2" required="N" ctl="rgb" price="Y" />
            <question id="typestyle" required="Y" ctl="dropdown" />
         </tab>
      </step>
      <step id="Step 3" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event64'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="quantity" required="Y" ctl="dropdown" price="Y" />
         <question id="productId" required="Y" ctl="dropdown" price="Y" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- -->
   <type id="UI_9">
      <step id="Step 1" desc="Design" progressTitle="Design" tlId="Design" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event15'}">
         <question id="matrix1" required="Y" ctl="swatch" price="Y" />
      </step>
      <step id="Step 2" desc="Company &amp; Bank Information" tlId="CompanyBankInfo" prev="Previous Step" next="Next Step">
         <tab id="1" desc="Your Information" progressTitle="Company" tlId="YourInfo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event16'}">
            <question id="YOUR_INFORMATION" required="N" ctl="x-note" />
            <question id="layout" required="N" ctl="text">
               <block id="AD" required="N" />
               <block id="AL" required="N" />
               <block id="BA" required="N" />
               <block id="BP" required="N" />
               <block id="CA" required="N" />
               <block id="CC" required="N" />
               <!--<block id="CD" required="N"/> -->
               <block id="CE" required="N" />
               <block id="CF" required="N" />
               <block id="CG" required="N" />
               <block id="CH" required="N" />
               <block id="CI" required="N" />
               <block id="CJ" required="N" />
               <block id="CR" required="N" />
               <block id="CS" required="N" />
               <block id="DE" required="N" />
               <block id="DP" required="N" />
               <block id="DW" required="N" />
               <block id="ED" required="N" />
               <block id="EI" required="N" />
               <block id="EM" required="N" />
               <block id="I1" required="N" />
               <block id="I2" required="N" />
               <block id="LC" required="N" />
               <block id="LL" required="N" />
               <block id="LO" required="N" />
               <block id="LR" required="N" />
               <block id="MA" required="N" />
               <block id="ML" required="N" />
               <block id="NN" required="N" />
               <block id="PE" required="N" />
               <block id="PH" required="N" />
               <block id="PN" required="N" />
               <block id="PO" required="N" />
               <block id="PV" required="N" />
               <block id="PP" required="N" />
               <block id="RA" required="N" />
               <block id="RC" required="N" />
               <!-- <block id="RM" required="N" /> -->
               <block id="SA" required="N" />
               <block id="SC" required="N" />
               <block id="SE" required="N" />
               <block id="SM" required="N" />
               <block id="SN" required="N" />
               <block id="SS" required="N" />
               <block id="SW" required="N" />
               <block id="TA" required="N" />
               <block id="TM" required="N" />
               <block id="UC" required="N" />
               <block id="UL" required="N" />
               <block id="UR" required="N" />
               <block id="US" required="N" />
               <!-- ADDITIONAL_INFORMATION -->
               <block id="H1" required="N" />
               <block id="H2" required="N" />
               <block id="C1" required="N" />
               <block id="C2" required="N" />
               <block id="C3" required="N" />
               <block id="C4" required="N" />
               <block id="C5" required="N" />
               <block id="C6" required="N" />
               <block id="C7" required="N" />
               <block id="C8" required="N" />
               <block id="C9" required="N" />
               <block id="LS" required="N" />
               <block id="SL" required="N" />
               <block id="ST" required="N" />
               <block id="SH" required="N" />
            </question>
         </tab>
         <tab id="2" desc="Account Information" progressTitle="Account" tlId="TransactionalInfo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event18'}">
            <question id="ACCOUNT_INFORMATION" required="N" ctl="x-note" />
            <question id="numbering" required="Y" ctl="text" />
            <question id="reverseNumbering" required="N" ctl="checkbox" />
         </tab>
         <tab id="3" desc="Colors &amp; Fonts" progressTitle="Color" tlId="FontTextColor" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event75'}">
            <question id="inkColor1" required="Y" ctl="rgb" price="Y" />
            <question id="inkColor2" required="N" ctl="rgb" price="Y" />
            <question id="typestyle" required="Y" ctl="dropdown" />
         </tab>
      </step>
      <step id="Step 3" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event64'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="quantity" required="Y" ctl="dropdown" price="Y" />
         <question id="productId" required="Y" ctl="dropdown" price="Y" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- ECHKSD -->
   <type id="UI_10">
      <step id="Step 1" desc="Company Information" progressTitle="Company" tlId="CopiesQty" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event199'}">
            <question id="YOUR_INFORMATION" required="N" ctl="x-note" />
            <question id="layout" required="N" ctl="text">
               <block id="AD" required="N" />
               <block id="AL" required="N" />
               <block id="BA" required="N" />
               <block id="BP" required="N" />
               <block id="CA" required="N" />
               <block id="CC" required="N" />
               <!--<block id="CD" required="N"/> -->
               <block id="CE" required="N" />
               <block id="CF" required="N" />
               <block id="CG" required="N" />
               <block id="CH" required="N" />
               <block id="CI" required="N" />
               <block id="CJ" required="N" />
               <block id="CR" required="N" />
               <block id="CS" required="N" />
               <block id="DE" required="N" />
               <block id="DP" required="N" />
               <block id="DW" required="N" />
               <block id="ED" required="N" />
               <block id="EI" required="N" />
               <block id="EM" required="N" />
               <block id="I1" required="N" />
               <block id="I2" required="N" />
               <block id="LC" required="N" />
               <block id="LL" required="N" />
               <block id="LO" required="N" />
               <block id="LR" required="N" />
               <block id="MA" required="N" />
               <block id="ML" required="N" />
               <block id="NN" required="N" />
               <block id="PE" required="N" />
               <block id="PH" required="N" />
               <block id="PN" required="N" />
               <block id="PO" required="N" />
               <block id="PV" required="N" />
               <block id="PP" required="N" />
               <block id="RA" required="N" />
               <block id="RC" required="N" />
               <block id="SA" required="N" />
               <block id="SC" required="N" />
               <block id="SE" required="N" />
               <block id="SM" required="N" />
               <block id="SN" required="N" />
               <block id="SS" required="N" />
               <block id="SW" required="N" />
               <block id="TA" required="N" />
               <block id="TM" required="N" />
               <block id="UC" required="N" />
               <block id="UL" required="N" />
               <block id="UR" required="N" />
               <block id="US" required="N" />
            </question>
      </step>
      <step id="Step 2" desc="Account Information" progressTitle="Account" tlId="CompanyBankInfo" prev="Previous Step" next="Next Step">
         <tab id="1" desc="Account Information" progressTitle="Account" tlId="TransactionalInfo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event18'}">
            <question id="ACCOUNT_INFORMATION" required="N" ctl="x-note" />
            <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
            <question id="routingNumber" required="Y" ctl="text" />
            <question id="accountNumber" required="Y" ctl="text" />
            <question id="reverseNumbering" required="N" ctl="checkbox" />
         </tab>
      </step>
      <step id="Step 3" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event64'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="quantity" required="Y" ctl="dropdown" price="Y" />
         <question id="productId" required="Y" ctl="dropdown" price="Y" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- 81501 -->
   <type id="UI_11">
     <step id="Step 1" desc="Designs" progressTitle="Design" tlId="Designs" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event15'}">
         <question id="matrix1" required="Y" ctl="swatch" price="Y" />
      </step>
      <step id="Step 2" desc="Company &amp; Bank Information" tlId="CompanyBankInfo" prev="Previous Step" next="Next Step">
         <tab id="1" desc="Bank Information" progressTitle="Bank" tlId="BankInfo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event18'}">
            <question id="BANK_INFORMATION" required="N" ctl="x-note" />
            <question id="layout" required="N" ctl="text">
               <block id="BI" required="N" />
            </question>
            <!--<question id="NUMBERING_INFORMATION" required="N" ctl="x-note" />-->
            <question id="numbering" required="Y" ctl="text" />
            <question id="reverseNumbering" required="N" ctl="checkbox" />
         </tab>
         <tab id="2" desc="Company Information" progressTitle="Company" tlId="YourInfo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event16'}">
            <question id="YOUR_INFORMATION" required="N" ctl="x-note" />
            <question id="layout" required="N" ctl="text">
               <block id="AD" required="N" />
               <block id="AL" required="N" />
               <block id="BA" required="N" />
               <block id="BP" required="N" />
               <block id="CA" required="N" />
               <block id="CC" required="N" />
               <!--<block id="CD" required="N"/> -->
               <block id="CE" required="N" />
               <block id="CF" required="N" />
               <block id="CG" required="N" />
               <block id="CH" required="N" />
               <block id="CI" required="N" />
               <block id="CJ" required="N" />
               <block id="CR" required="N" />
               <block id="CS" required="N" />
               <block id="DE" required="N" />
               <block id="DP" required="N" />
               <block id="DW" required="N" />
               <block id="ED" required="N" />
               <block id="EI" required="N" />
               <block id="EM" required="N" />
               <block id="I1" required="N" />
               <block id="I2" required="N" />
               <block id="LC" required="N" />
               <block id="LL" required="N" />
               <block id="LO" required="N" />
               <block id="LR" required="N" />
               <block id="MA" required="N" />
               <block id="ML" required="N" />
               <block id="NN" required="N" />
               <block id="PE" required="N" />
               <block id="PH" required="N" />
               <block id="PN" required="N" />
               <block id="PO" required="N" />
               <block id="PV" required="N" />
               <block id="PP" required="N" />
               <block id="RA" required="N" />
               <block id="RC" required="N" />
               <!-- <block id="RM" required="N" /> -->
               <block id="SA" required="N" />
               <block id="SC" required="N" />
               <block id="SE" required="N" />
               <block id="SM" required="N" />
               <block id="SN" required="N" />
               <block id="SS" required="N" />
               <block id="SW" required="N" />
               <block id="TA" required="N" />
               <block id="TM" required="N" />
               <block id="UC" required="N" />
               <block id="UL" required="N" />
               <block id="UR" required="N" />
               <block id="US" required="N" />
               <!-- ADDITIONAL_INFORMATION -->
               <block id="H1" required="N" />
               <block id="H2" required="N" />
               <block id="C1" required="N" />
               <block id="C2" required="N" />
               <block id="C3" required="N" />
               <block id="C4" required="N" />
               <block id="C5" required="N" />
               <block id="C6" required="N" />
               <block id="C7" required="N" />
               <block id="C8" required="N" />
               <block id="C9" required="N" />
               <block id="LS" required="N" />
               <block id="SL" required="N" />
               <block id="ST" required="N" />
               <block id="SH" required="N" />
            </question>
         </tab>
      </step>
      <step id="Step 3" desc="Logo"  tlId="Logo" prev="Previous Step" next="Next Step">
          <tab id="1" desc="Logo" progressTitle="Logo" tlId="Logo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event76'}">
            <question id="logo" required="Y" ctl="radio" price="Y" />
         </tab>
         <tab id="2" desc="Colors &amp; Fonts" progressTitle="Color" tlId="FontTextColor" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event75'}">
            <question id="inkColor1" required="Y" ctl="rgb" price="Y" />
            <question id="inkColor2" required="N" ctl="rgb" price="Y" />
            <question id="typestyle" required="Y" ctl="dropdown" />
         </tab>
      </step>
      <step id="Step 4" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event64'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="quantity" required="Y" ctl="dropdown" price="Y" />
         <question id="productId" required="Y" ctl="dropdown" price="Y" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- Verbose Holiday Card - H15609/H16603 -->
   <type id="UI_HCARD1">
      <step id="Step 1" desc="Front of Card - Text Imprint" progressTitle="Front" tlId="Frontcard" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event118'}">
         <question id="requiredInkColor" required="N" ctl="reqInk" price="N" />
         <question id="layout" required="N" ctl="text">
            <block id="CI" required="N" />
            <block id="CV" required="N" />
         </question>
      </step>
      <step id="Step 2" desc="Inside of Card - Text Color" progressTitle="Inside-Color" tlId="TextColor" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event93'}">
         <question id="inkColor1" required="Y" ctl="rgb" price="Y" tt="holidayInk" />
         <question id="inkColor2" required="N" ctl="rgb" price="Y" />
      </step>
      <step id="Step 3" desc="Inside of Card - Verse" progressTitle="Verse" tlId="Verse" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event113'}">
         <question id="verse" required="Y" ctl="verse" price="Y" />
         <question id="PERSONNALIZATION" required="N" ctl="model" />
         <question id="layout" required="N" ctl="text">
            <block id="VS" required="N" />
         </question>
      </step>
      <step id="Step 4" desc="Inside of Card - Text Imprint" progressTitle="Imprint" tlId="TextImprint" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event92'}">
         <question id="layout" required="N" ctl="text">
            <block id="DW" required="N" />
            <block id="GC" required="N" />
            <block id="LO" required="N" />
            <block id="SF" required="N" />
         </question>
         <question id="typestyle" required="Y" ctl="dropdown" />
      </step>
      <step id="Step 5" desc="Inside of Card - Logo" progressTitle="Logo" tlId="Logo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event76'}">
         <question id="logo" required="Y" ctl="radio" price="Y" />
      </step>
      <step id="Step 6" desc="Additional Options" progressTitle="Options" tlId="AdditionalOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event116'}">
         <question id="ADDITIONAL_OPTION" required="N" ctl="x-note" />
         <question id="signatures" required="Y" ctl="checkbox" price="Y" />
         <question id="PERSONNALIZATION_OPTION" required="N" ctl="model" />
         <question id="folding" required="Y" ctl="checkbox" price="Y" />
      </step>
      <step id="Step 7" desc="Envelope Options" progressTitle="Envelope" tlId="EnvelopeOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event117'}">
         <question id="ENVELOPE_OPTION" required="N" ctl="x-note" />
         <question id="ENVELOPE_RETURN" required="N" ctl="checkbox" price="Y" />
         <question id="layout" required="N" ctl="text">
            <block id="EI" required="N" />
         </question>
         <question id="envelope" required="Y" ctl="swatch" price="Y" />
      </step>
      <step id="Step 8" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event95'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- Verbose Holiday Card - MT14013 -->
   <type id="UI_HCARD1P">
      <step id="Step 1" desc="Paper Choice" progressTitle="Paper" tlId="PaperChoice" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event15'}">
         <question id="matrix1" required="Y" ctl="radioExt" price="Y" />
      </step>
      <step id="Step 2" desc="Front of Card - Text Imprint" progressTitle="Front-Imprint" tlId="Frontcard" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event118'}">
         <question id="requiredInkColor" required="N" ctl="reqInk" price="N" />
         <question id="layout" required="N" ctl="text">
            <block id="CI" required="N" />
            <block id="CV" required="N" />
         </question>
      </step>
      <step id="Step 3" desc="Inside of Card - Text Color" progressTitle="Inside-Color" tlId="TextColor" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event93'}">
         <question id="inkColor1" required="Y" ctl="rgb" price="Y"  tt="holidayInk" />
         <question id="inkColor2" required="N" ctl="rgb" price="Y" />
      </step>
      <step id="Step 4" desc="Inside of Card - Verse" progressTitle="Verse" tlId="Verse" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event113'}">
         <question id="verse" required="Y" ctl="verse" price="Y" />
         <question id="PERSONNALIZATION" required="N" ctl="model" />
         <question id="layout" required="N" ctl="text">
            <block id="VS" required="N" />
         </question>
      </step>
      <step id="Step 5" desc="Inside of Card - Text Imprint" progressTitle="Imprint" tlId="TextImprint" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event92'}">
         <question id="layout" required="N" ctl="text">
            <block id="DW" required="N" />
            <block id="GC" required="N" />
            <block id="LO" required="N" />
            <block id="SF" required="N" />
         </question>
       <question id="typestyle" required="Y" ctl="dropdown" />
      </step>
      <step id="Step 6" desc="Inside of Card - Logo" progressTitle="Logo" tlId="Logo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event76'}">
         <question id="logo" required="Y" ctl="radio" price="Y" />
      </step>
      <step id="Step 7" desc="Additional Options" progressTitle="Options" tlId="AdditionalOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event116'}">
         <question id="ADDITIONAL_OPTION" required="N" ctl="x-note" />
         <question id="signatures" required="Y" ctl="checkbox" price="Y" />
         <question id="PERSONNALIZATION_OPTION" required="N" ctl="model" />
         <question id="folding" required="Y" ctl="checkbox" price="Y" />
      </step>
      <step id="Step 8" desc="Envelope Options" progressTitle="Envelope" tlId="EnvelopeOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event117'}">
         <question id="ENVELOPE_OPTION" required="N" ctl="x-note" />
         <question id="ENVELOPE_RETURN" required="N" ctl="checkbox" price="Y" />
         <question id="layout" required="N" ctl="text">
            <block id="EI" required="N" />
         </question>
         <question id="envelope" required="Y" ctl="swatch" price="Y" />
      </step>
      <step id="Step 9" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event95'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- Holiday Card - No Front of Card - H16616 -->
   <type id="UI_HCARD2">
      <step id="Step 1" desc="Inside of Card - Text Color" progressTitle="Inside-Color" tlId="TextColor" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event93'}">
         <question id="inkColor1" required="Y" ctl="rgb" price="Y" tt="holidayInk" />
         <question id="inkColor2" required="N" ctl="rgb" price="Y" />
      </step>
      <step id="Step 2" desc="Inside of Card - Verse" progressTitle="Verse" tlId="Verse" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event113'}">
         <question id="verse" required="Y" ctl="verse" price="Y" />
         <question id="PERSONNALIZATION" required="N" ctl="model" />
         <question id="layout" required="N" ctl="text">
            <block id="VS" required="N" />
         </question>
      </step>
      <step id="Step 3" desc="Inside of Card - Text Imprint" progressTitle="Imprint" tlId="TextImprint" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event92'}">
         <question id="layout" required="N" ctl="text">
            <block id="GC" required="N" />
            <block id="CI" required="N" />
            <block id="DW" required="N" />
            <block id="LO" required="N" />
            <block id="SF" required="N" />
         </question>
         <question id="typestyle" required="Y" ctl="dropdown" />
      </step>
      <step id="Step 4" desc="Inside of Card - Logo" progressTitle="Logo" tlId="Logo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event76'}">
         <question id="logo" required="Y" ctl="radio" price="Y" />
      </step>
      <step id="Step 5" desc="Additional Options" progressTitle="Options" tlId="AdditionalOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event116'}">
         <question id="ADDITIONAL_OPTION" required="N" ctl="x-note" />
         <question id="signatures" required="Y" ctl="checkbox" price="Y" />
         <question id="PERSONNALIZATION_OPTION" required="N" ctl="model" />
         <question id="folding" required="Y" ctl="checkbox" price="Y" />
      </step>
      <step id="Step 6" desc="Envelope Options" progressTitle="Envelope" tlId="EnvelopeOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event117'}">
         <question id="ENVELOPE_OPTION" required="N" ctl="x-note" />
         <question id="ENVELOPE_RETURN" required="N" ctl="checkbox" price="Y" />
         <question id="layout" required="N" ctl="text">
            <block id="EI" required="N" />
         </question>
         <question id="envelope" required="N" ctl="swatch" price="Y" />
      </step>
      <step id="Step 7" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event95'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- Holiday Card - No Front of Card - HP09314 -->
   <type id="UI_HCARD2P">
      <step id="Step 1" desc="Paper Choice" progressTitle="Paper" tlId="PaperChoice" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event15'}">
         <question id="matrix1" required="Y" ctl="radioExt" price="Y" />
      </step>
      <step id="Step 2" desc="Inside of Card - Text Color" progressTitle="Inside-Color" tlId="TextColor" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event93'}">
         <question id="inkColor1" required="Y" ctl="rgb" price="Y" tt="holidayInk" />
         <question id="inkColor2" required="N" ctl="rgb" price="Y" />
      </step>
      <step id="Step 3" desc="Inside of Card - Verse" progressTitle="Verse" tlId="Verse" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event113'}">
         <question id="verse" required="Y" ctl="verse" price="Y" />
         <question id="PERSONNALIZATION" required="N" ctl="model" />
         <question id="layout" required="N" ctl="text">
            <block id="VS" required="N" />
         </question>
      </step>
      <step id="Step 4" desc="Inside of Card - Text Imprint" progressTitle="Imprint" tlId="TextImprint" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event92'}">
         <question id="layout" required="N" ctl="text">
            <block id="GC" required="N" />
            <block id="CI" required="N" />
            <block id="DW" required="N" />
            <block id="LO" required="N" />
            <block id="SF" required="N" />
         </question>
       <question id="typestyle" required="Y" ctl="dropdown" />
      </step>
      <step id="Step 5" desc="Inside of Card - Logo" progressTitle="Logo" tlId="Logo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event76'}">
         <question id="logo" required="Y" ctl="radio" price="Y" />
      </step>
      <step id="Step 6" desc="Additional Options" progressTitle="Options" tlId="AdditionalOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event116'}">
         <question id="ADDITIONAL_OPTION" required="N" ctl="x-note" />
         <question id="signatures" required="Y" ctl="checkbox" price="Y" />
         <question id="PERSONNALIZATION_OPTION" required="N" ctl="model" />
         <question id="folding" required="Y" ctl="checkbox" price="Y" />
      </step>
      <step id="Step 7" desc="Envelope Options" progressTitle="Envelope" tlId="EnvelopeOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event117'}">
         <question id="ENVELOPE_OPTION" required="N" ctl="x-note" />
         <question id="ENVELOPE_RETURN" required="N" ctl="checkbox" price="Y" />
         <question id="layout" required="N" ctl="text">
            <block id="EI" required="N" />
         </question>
         <question id="envelope" required="N" ctl="swatch" price="Y" />
      </step>
      <step id="Step 8" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event95'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- Holiday Card - No Verse - N7407 -->
   <type id="UI_HCARD2B">
      <step id="Step 1" desc="Inside of Card - Text Color" progressTitle="Inside-Color" tlId="TextColor" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event93'}">
         <question id="inkColor1" required="Y" ctl="rgb" price="Y" tt="holidayInk" />
         <question id="inkColor2" required="N" ctl="rgb" price="Y" />
      </step>
      <step id="Step 2" desc="Inside of Card - Text Imprint" progressTitle="Imprint" tlId="TextImprint" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event92'}">
         <question id="layout" required="N" ctl="text">
            <block id="GC" required="N" />
            <block id="CI" required="N" />
            <block id="DW" required="N" />
            <block id="LO" required="N" />
            <block id="SF" required="N" />
         </question>
         <question id="typestyle" required="Y" ctl="dropdown" />
      </step>
      <step id="Step 3" desc="Inside of Card - Logo" progressTitle="Logo" tlId="Logo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event76'}">
         <question id="logo" required="Y" ctl="radio" price="Y" />
      </step>
      <step id="Step 4" desc="Additional Options" progressTitle="Options" tlId="AdditionalOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event116'}">
         <question id="ADDITIONAL_OPTION" required="N" ctl="x-note" />
         <question id="signatures" required="Y" ctl="checkbox" price="Y" />
         <question id="PERSONNALIZATION_OPTION" required="N" ctl="model" />
         <question id="folding" required="Y" ctl="checkbox" price="Y" />
      </step>
      <step id="Step 5" desc="Envelope Options" progressTitle="Envelope" tlId="EnvelopeOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event117'}">
         <question id="ENVELOPE_OPTION" required="N" ctl="x-note" />
         <question id="ENVELOPE_RETURN" required="N" ctl="checkbox" price="Y" />
         <question id="layout" required="N" ctl="text">
            <block id="EI" required="N" />
         </question>
         <question id="envelope" required="N" ctl="swatch" price="Y" />
      </step>
      <step id="Step 6" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event95'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- Holiday Card - No Front of Card - No Logo - No Envelope Imprint - HP2302T -->
   <type id="UI_HCARD3">
      <step id="Step 1" desc="Inside of Card - Text Color" progressTitle="Inside_color" tlId="TextColor" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event93'}">
         <question id="inkColor1" required="Y" ctl="rgb" price="Y" tt="holidayInk" />
         <question id="inkColor2" required="N" ctl="rgb" price="Y" />
      </step>
      <step id="Step 2" desc="Inside of Card - Verse" progressTitle="Verse" tlId="Verse" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event113'}">
         <question id="verse" required="Y" ctl="verse" price="Y" />
         <question id="PERSONNALIZATION" required="N" ctl="model" />
         <question id="layout" required="N" ctl="text">
            <block id="VS" required="N" />
         </question>
      </step>
      <step id="Step 3" desc="Inside of Card - Text Imprint" progressTitle="Imprint" tlId="TextImprint" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event92'}">
         <question id="layout" required="N" ctl="text">
            <block id="GC" required="N" />
            <block id="CI" required="N" />
            <block id="DW" required="N" />
            <block id="LO" required="N" />
            <block id="SF" required="N" />
         </question>
         <question id="typestyle" required="Y" ctl="dropdown" />
      </step>
      <step id="Step 4" desc="Additional Options" progressTitle="Options" tlId="AdditionalOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event116'}">
         <question id="ADDITIONAL_OPTION" required="N" ctl="x-note" />
         <question id="signatures" required="Y" ctl="checkbox" price="Y" />
         <question id="PERSONNALIZATION_OPTION" required="N" ctl="model" />
         <question id="folding" required="Y" ctl="checkbox" price="Y" />
      </step>
      <step id="Step 5" desc="Envelope Options" progressTitle="Envelope" tlId="EnvelopeOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event117'}">
         <question id="envelope" required="Y" ctl="swatch" price="Y" />
      </step>
      <step id="Step 6" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event95'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- Holiday Card - No Front of Card - No Logo - No Envelope Imprint - HP2302T -->
   <type id="UI_HCARD3P">
      <step id="Step 1" desc="Paper Choice" progressTitle="Paper" tlId="PaperChoice" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event15'}">
         <question id="matrix1" required="Y" ctl="radioExt" price="Y" />
      </step>
      <step id="Step 2" desc="Inside of Card - Text Color" progressTitle="Inside-Color" tlId="TextColor" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event93'}">
         <question id="inkColor1" required="Y" ctl="rgb" price="Y" tt="holidayInk" />
         <question id="inkColor2" required="N" ctl="rgb" price="Y" />
      </step>
      <step id="Step 3" desc="Inside of Card - Verse" progressTitle="Verse" tlId="Verse" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event113'}">
         <question id="verse" required="Y" ctl="verse" price="Y" />
         <question id="PERSONNALIZATION" required="N" ctl="model" />
         <question id="layout" required="N" ctl="text">
            <block id="VS" required="N" />
         </question>
      </step>
      <step id="Step 4" desc="Inside of Card - Text Imprint" progressTitle="Imprint" tlId="TextImprint" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event92'}">
         <question id="layout" required="N" ctl="text">
            <block id="GC" required="N" />
            <block id="CI" required="N" />
            <block id="DW" required="N" />
            <block id="LO" required="N" />
            <block id="SF" required="N" />
         </question>
         <question id="typestyle" required="Y" ctl="dropdown" />
      </step>
      <step id="Step 5" desc="Additional Options" progressTitle="Options" tlId="AdditionalOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event116'}">
         <question id="ADDITIONAL_OPTION" required="N" ctl="x-note" />
         <question id="signatures" required="Y" ctl="checkbox" price="Y" />
         <question id="PERSONNALIZATION_OPTION" required="N" ctl="model" />
         <question id="folding" required="Y" ctl="checkbox" price="Y" />
      </step>
      <step id="Step 6" desc="Envelope Options" progressTitle="Envelope" tlId="EnvelopeOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event117'}">
         <question id="envelope" required="Y" ctl="swatch" price="Y" />
      </step>
      <step id="Step 7" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event95'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- Z-Fold Calendar - HHZ6401 -->
   <type id="UI_ZCARD1">
      <step id="Step 1" desc="Text Color" progressTitle="Color" tlId="TextColor" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event93'}">
         <question id="inkColor1" required="Y" ctl="rgb" price="Y" tt="holidayInk" />
         <question id="inkColor2" required="N" ctl="rgb" price="Y" />
      </step>
      <step id="Step 2" desc="Text Imprint" progressTitle="Imprint" tlId="TextImprint" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event92'}">
         <question id="layout" required="N" ctl="text">
            <block id="GC" required="N" />
            <block id="CI" required="N" />
            <block id="LO" required="N" />
            <block id="SF" required="N" />
         </question>
         <question id="typestyle" required="Y" ctl="dropdown" />
      </step>
      <step id="Step 3" desc="Logo" progressTitle="Logo" tlId="Logo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event76'}">
         <question id="logo" required="Y" ctl="radio" price="Y" />
      </step>
      <step id="Step 4" desc="Additional Options" progressTitle="Options" tlId="AdditionalOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event116'}">
         <question id="ADDITIONAL_OPTION" required="N" ctl="x-note" />
         <question id="folding" required="Y" ctl="checkbox" price="Y" />
      </step>
      <step id="Step 5" desc="Envelope Options" progressTitle="Envelope" tlId="EnvelopeOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event117'}">
         <question id="ENVELOPE_OPTION" required="N" ctl="x-note" />
         <question id="ENVELOPE_RETURN" required="N" ctl="checkbox" price="Y" />
         <question id="layout" required="N" ctl="text">
            <block id="EI" required="N" />
         </question>
         <question id="envelope" required="Y" ctl="swatch" price="Y" />
      </step>
      <step id="Step 6" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event95'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- Z-Fold Calendar - ?????? -->
   <type id="UI_ZCARD1P">
      <step id="Step 1" desc="Paper Choice" progressTitle="Paper" tlId="PaperChoice" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event15'}">
         <question id="matrix1" required="Y" ctl="radioExt" price="Y" />
      </step>
      <step id="Step 2" desc="Text Color" progressTitle="Color" tlId="TextColor" prev="Previous Steps" next="Next Step" trackEvent="{'omniture': 'event93'}">
         <question id="inkColor1" required="Y" ctl="rgb" price="Y" tt="holidayInk" />
         <question id="inkColor2" required="N" ctl="rgb" price="Y" />
      </step>
      <step id="Step 3" desc="Text Imprint" progressTitle="Imprint" tlId="TextImprint" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event92'}">
         <question id="layout" required="N" ctl="text">
            <block id="GC" required="N" />
            <block id="CI" required="N" />
            <block id="DW" required="N" />
            <block id="LO" required="N" />
            <block id="SF" required="N" />
         </question>
         <question id="typestyle" required="Y" ctl="dropdown" />
      </step>
      <step id="Step 4" desc="Logo" progressTitle="Logo" tlId="Logo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event76'}">
         <question id="logo" required="Y" ctl="radio" price="Y" />
      </step>
      <step id="Step 5" desc="Additional Options" progressTitle="Options" tlId="AdditionalOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event116'}">
         <question id="ADDITIONAL_OPTION" required="N" ctl="x-note" />
         <question id="folding" required="Y" ctl="checkbox" price="Y" />
      </step>
      <step id="Step 6" desc="Envelope Options" progressTitle="Envelope" tlId="EnvelopeOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event117'}">
         <question id="ENVELOPE_OPTION" required="N" ctl="x-note" />
         <question id="ENVELOPE_RETURN" required="N" ctl="checkbox" price="Y" />
         <question id="layout" required="N" ctl="text">
            <block id="EI" required="N" />
         </question>
         <question id="envelope" required="Y" ctl="swatch" price="Y" />
      </step>
      <step id="Step 6" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event95'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- Postcard - HPC5201-->
   <type id="UI_PCARD1">
      <step id="Step 1" desc="Back of Postcard - Text Color" progressTitle="Color" tlId="TextColor" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event93'}">
         <question id="inkColor1" required="Y" ctl="rgb" price="Y" tt="holidayInk" />
         <question id="inkColor2" required="N" ctl="rgb" price="Y" />
      </step>
      <step id="Step 2" desc="Back of Postard - Verse" progressTitle="Verse" tlId="Verse" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event113'}">
         <question id="verse" required="Y" ctl="verse" price="Y" />
         <question id="PERSONNALIZATION" required="N" ctl="model" />
         <question id="layout" required="N" ctl="text">
            <block id="VS" required="N" />
         </question>
      </step>
      <step id="Step 3" desc="Back of Postcard - Text Imprint" progressTitle="Imprint" tlId="TextImprint" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event92'}">
         <question id="layout" required="N" ctl="text">
            <block id="GC" required="N" />
            <block id="CI" required="N" />
            <block id="DW" required="N" />
            <block id="LO" required="N" />
            <block id="SF" required="N" />
         </question>
      </step>
      <step id="Step 4" desc="Back of Postard - Logo" progressTitle="Logo" tlId="Logo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event76'}">
         <question id="logo" required="Y" ctl="radio" price="Y" />
      </step>
      <step id="Step 5" desc="Additional Options" progressTitle="Options" tlId="AdditionalOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event116'}">
         <question id="ADDITIONAL_OPTION" required="N" ctl="x-note" />
         <question id="signatures" required="Y" ctl="checkbox" price="Y" />
         <question id="PERSONNALIZATION_OPTION" required="N" ctl="model" />
      </step>
      <step id="Step 6" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event95'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- HPC2201 -->
   <type id="UI_PCARD1C">
      <step id="Step 1" desc="Front of Postcard - Text Imprint" progressTitle="Front-Imprint" tlId="CoverImprint" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event118'}">
         <question id="layout" required="N" ctl="text">
            <block id="CV" required="N" />
         </question>
      </step>
      <step id="Step 2" desc="Back of Postcard - Text Color" progressTitle="Color" tlId="TextColor" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event93'}">
         <question id="inkColor1" required="Y" ctl="rgb" price="Y" tt="holidayInk" />
         <question id="inkColor2" required="N" ctl="rgb" price="Y" />
      </step>
      <step id="Step 3" desc="Back of Postard - Verse" progressTitle="Back-Verse" tlId="Verse" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event113'}">
         <question id="verse" required="Y" ctl="verse" price="Y" />
         <question id="PERSONNALIZATION" required="N" ctl="model" />
         <question id="layout" required="N" ctl="text">
            <block id="VS" required="N" />
         </question>
      </step>
      <step id="Step 4" desc="Back of Postcard - Text Imprint" progressTitle="Imprint" tlId="TextImprint" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event92'}">
         <question id="layout" required="N" ctl="text">
            <block id="GC" required="N" />
            <block id="CI" required="N" />
            <block id="DW" required="N" />
            <block id="LO" required="N" />
            <block id="SF" required="N" />
         </question>
      </step>
      <step id="Step 5" desc="Back of Postard - Logo" progressTitle="Logo" tlId="Logo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event76'}">
         <question id="logo" required="Y" ctl="radio" price="Y" />
      </step>
      <step id="Step 6" desc="Additional Options" progressTitle="Options" tlId="AdditionalOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event116'}">
         <question id="ADDITIONAL_OPTION" required="N" ctl="x-note" />
         <question id="signatures" required="Y" ctl="checkbox" price="Y" />
         <question id="PERSONNALIZATION_OPTION" required="N" ctl="model" />
      </step>
      <step id="Step 7" desc="Review" tlId="ReviewProof" progressTitle="Review" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event95'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- Postcard - ????????-->
   <type id="UI_PCARD1P">
      <step id="Step 1" desc="Paper Choice" progressTitle="Paper" tlId="PaperChoice" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event15'}">
         <question id="matrix1" required="Y" ctl="radioExt" price="Y" />
      </step>
      <step id="Step 2" desc="Back of Postcard - Text Color" progressTitle="Back-Color" tlId="TextColor" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event93'}">
         <question id="inkColor1" required="Y" ctl="rgb" price="Y" tt="holidayInk" />
         <question id="inkColor2" required="N" ctl="rgb" price="Y" />
      </step>
      <step id="Step 3" desc="Back of Postard - Verse" progressTitle="Verse" tlId="Verse" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event113'}">
         <question id="verse" required="Y" ctl="verse" price="Y" />
         <question id="PERSONNALIZATION" required="N" ctl="model" />
         <question id="layout" required="N" ctl="text">
            <block id="VS" required="N" />
         </question>
      </step>
      <step id="Step 4" desc="Back of Postcard - Text Imprint" progressTitle="Imprint" tlId="TextImprint" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event92'}">
         <question id="layout" required="N" ctl="text">
            <block id="GC" required="N" />
            <block id="CI" required="N" />
            <block id="DW" required="N" />
            <block id="LO" required="N" />
            <block id="SF" required="N" />
         </question>
      </step>
      <step id="Step 5" desc="Back of Postard - Logo" progressTitle="Logo" tlId="Logo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event76'}">
         <question id="logo" required="Y" ctl="radio" price="Y" />
      </step>
      <step id="Step 6" desc="Additional Options" progressTitle="Options" tlId="AdditionalOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event116'}">
         <question id="ADDITIONAL_OPTION" required="N" ctl="x-note" />
         <question id="signatures" required="Y" ctl="checkbox" price="Y" />
         <question id="PERSONNALIZATION_OPTION" required="N" ctl="model" />
      </step>
      <step id="Step 7" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event95'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- Postcard - 6157, 6181 -->
   <type id="UI_PCARD2">
      <step id="Step 1" desc="Back of Postcard - Text Color" progressTitle="Back-Color" tlId="TextColor" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event93'}">
         <question id="inkColor1" required="Y" ctl="rgb" price="Y" tt="holidayInk" />
         <question id="inkColor2" required="N" ctl="rgb" price="Y" />
      </step>
      <step id="Step 2" desc="Back of Postard - Verse" progressTitle="Verse" tlId="Verse" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event113'}">
         <question id="verse" required="Y" ctl="verse" price="Y" />
         <question id="PERSONNALIZATION" required="N" ctl="model" />
         <question id="layout" required="N" ctl="text">
            <block id="VS" required="N" />
         </question>
      </step>
      <step id="Step 3" desc="Back of Postcard - Text Imprint" progressTitle="Imprint" tlId="TextImprint" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event92'}">
         <question id="layout" required="N" ctl="text">
            <block id="GC" required="N" />
            <block id="CI" required="N" />
            <block id="DW" required="N" />
            <block id="LO" required="N" />
            <block id="SF" required="N" />
         </question>
      </step>
      <step id="Step 4" desc="Back of Postard - Logo" progressTitle="Logo" tlId="Logo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event76'}">
         <question id="logo" required="Y" ctl="radio" price="Y" />
      </step>
      <step id="Step 5" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event95'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- Postcard - ????????-->
   <type id="UI_PCARD2P">
      <step id="Step 1" desc="Paper Choice" progressTitle="Paper" tlId="PaperChoice" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event15'}">
         <question id="matrix1" required="Y" ctl="radioExt" price="Y" />
      </step>
      <step id="Step 2" desc="Back of Postcard - Text Color" progressTitle="Back-Color" tlId="TextColor" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event93'}">
         <question id="inkColor1" required="Y" ctl="rgb" price="Y" tt="holidayInk" />
         <question id="inkColor2" required="N" ctl="rgb" price="Y" />
      </step>
      <step id="Step 3" desc="Back of Postard - Verse" progressTitle="Verse" tlId="Verse" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event113'}">
         <question id="verse" required="Y" ctl="verse" price="Y" />
         <question id="PERSONNALIZATION" required="N" ctl="model" />
         <question id="layout" required="N" ctl="text">
            <block id="VS" required="N" />
         </question>
      </step>
      <step id="Step 4" desc="Back of Postcard - Text Imprint" progressTitle="Imprint" tlId="TextImprint" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event92'}">
         <question id="layout" required="N" ctl="text">
            <block id="GC" required="N" />
            <block id="CI" required="N" />
            <block id="DW" required="N" />
            <block id="LO" required="N" />
            <block id="SF" required="N" />
         </question>
      </step>
      <step id="Step 5" desc="Back of Postard - Logo" progressTitle="Logo" tlId="Logo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event76'}">
         <question id="logo" required="Y" ctl="radio" price="Y" />
      </step>
      <step id="Step 6" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event95'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- Postcard - 6177 -->
   <type id="UI_PCARD3">
      <step id="Step 1" desc="Back of Postcard - Text Color" progressTitle="Back-Color" tlId="TextColor" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event93'}">
         <question id="inkColor1" required="Y" ctl="rgb" price="Y" tt="holidayInk" />
         <question id="inkColor2" required="N" ctl="rgb" price="Y" />
      </step>
      <step id="Step 2" desc="Back of Postcard - Text Imprint" progressTitle="Imprint" tlId="TextImprint" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event92'}">
         <question id="layout" required="N" ctl="text">
            <block id="GC" required="N" />
            <block id="CI" required="N" />
            <block id="DW" required="N" />
            <block id="LO" required="N" />
            <block id="SF" required="N" />
         </question>
      </step>
      <step id="Step 3" desc="Back of Postard - Logo" progressTitle="Logo" tlId="Logo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event76'}">
         <question id="logo" required="Y" ctl="radio" price="Y" />
      </step>
      <step id="Step 4" desc="Additional Options" progressTitle="Options" tlId="AdditionalOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event116'}">
         <question id="ADDITIONAL_OPTION" required="N" ctl="x-note" />
         <question id="signatures" required="Y" ctl="checkbox" price="Y" />
         <question id="PERSONNALIZATION_OPTION" required="N" ctl="model" />
      </step>
      <step id="Step 5" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event95'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
   <!-- Postcard - ????????-->
   <type id="UI_PCARD3P">
      <step id="Step 1" desc="Paper Choice" progressTitle="Paper" tlId="PaperChoice" prev="" next="Next Step" trackEvent="{'omniture': 'event9,event15'}">
         <question id="matrix1" required="Y" ctl="radioExt" price="Y" />
      </step>
      <step id="Step 2" desc="Back of Postcard - Text Color" progressTitle="Back-Color" tlId="TextColor" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event93'}">
         <question id="inkColor1" required="Y" ctl="rgb" price="Y" tt="holidayInk" />
         <question id="inkColor2" required="N" ctl="rgb" price="Y" />
      </step>
      <step id="Step 3" desc="Back of Postcard - Text Imprint" progressTitle="Imprint" tlId="TextImprint" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event92'}">
         <question id="layout" required="N" ctl="text">
            <block id="GC" required="N" />
            <block id="CI" required="N" />
            <block id="DW" required="N" />
            <block id="LO" required="N" />
         </question>
      </step>
      <step id="Step 4" desc="Back of Postard - Logo" progressTitle="Logo" tlId="Logo" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event76'}">
         <question id="logo" required="Y" ctl="radio" price="Y" />
      </step>
      <step id="Step 5" desc="Additional Options" progressTitle="Options" tlId="AdditionalOptions" prev="Previous Step" next="Next Step" trackEvent="{'omniture': 'event116'}">
         <question id="ADDITIONAL_OPTION" required="N" ctl="x-note" />
         <question id="signatures" required="Y" ctl="checkbox" price="Y" />
         <question id="PERSONNALIZATION_OPTION" required="N" ctl="model" />
      </step>
      <step id="Step 6" desc="Review" progressTitle="Review" tlId="ReviewProof" prev="Previous Step" next="Add To Cart" trackEvent="{'omniture': 'event95'}">
         <question id="REVIEW_PROOF" required="N" ctl="x-note" />
         <question id="HIDDEN_LINK" required="N" ctl="x-note" />
         <question id="HIDDEN_ACCOUNT" required="N" ctl="x-note" />
         <question id="SHOW_LOGO" required="N" ctl="x-note" />
         <question id="HIDDEN_LOGO" required="N" ctl="x-note" />
         <question id="COMMENT" required="N" ctl="comment" />
         <question id="addToCart" required="Y" ctl="checkbox" />
         <question id="DETAILS" required="N" ctl="x-details" />
      </step>
   </type>
</controls>