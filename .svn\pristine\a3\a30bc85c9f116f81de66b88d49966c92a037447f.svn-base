define(function (require) {
    'use strict';

    require('jquery-xml2json');
    var $ = require('jquery');
    var xmlToObject = require('util/xmlToObject');

    /**
     * @class App.Models.Abstract
     *
     * @constructor
     * @param {Object|undefined} input
     */
    var AbstractModel = function (input) {
        this.init(input);
    };

    var proto = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|String|undefined} data
     * @chainable
     */
    proto.init = function(data) {

        if (!data) {
            return this;
        }

        if (typeof data.documentElement !== 'undefined' || typeof data.appendChild !== 'undefined') {
            this.fromXML(data);
        } else if (typeof data === 'string') {
            this.fromString(data);
        } else if (typeof data === 'object') {
            this.fromJSON(data);
        }

        return this;
    };

    /**
     * Parses data from string into XML, then passes data to .fromXML
     *
     * @method fromString
     * @param {String} string
     */
    proto.fromString = function(string) {
        string = $.trim(string.replace(/\n+/g, '\n'));
        var xml = $.parseXML(string);
        if (typeof this.fromJSON === 'function') {
            var json = xmlToObject(xml);
            this.fromJSON(json);
        } else {
            this.fromXML(xml);
        }
    };

    /**
     * Parses data from XML
     *
     * @method fromXML
     * @param {Document} xml
     */
    proto.fromXML = function(xml) {
        var xmlString;
        if (xml.documentElement) {
            xmlString = xml.documentElement.outerHTML || xml.documentElement.xml;
        } else if (xml.appendChild) {
            xmlString = xml.outerHTML;
        }

        var json = $.xml2json(xmlString);

        this.fromJSON(json);
    };

    /**
     * Parses a string to a JSON object.
     * Replace single quotes with double so parseJSON works.
     * We don't want to use single quotes in our HTML markup.
     *
     * @param {String} string
     * @return {Object}
     */
    proto.toJSON = function(string) {
        if (typeof string === 'string' && string !== '') {

            string = string.replace(/'/g, '"');
            string = $.parseJSON(string);
        }

        return string;
    };

    /**
     * strips out '$' objects and merges their values into the parent object
     *
     * @method stripInvalidFields
     * @param {object} json
     * @return {object}
     */
    proto.stripInvalidFields = function(json) {
        var prop;
        for (prop in json) {
            if (!json.hasOwnProperty(prop) || prop === 'fxg') {
                continue;
            }
            if (prop === '$' && typeof json[prop] === 'object') {
                json = $.extend(json, json[prop]);
                json[prop] = null;
            } else if (typeof json[prop] === 'object') {
                json[prop] = this.stripInvalidFields(json[prop]);
            }
        }
        return json;
    };

    /**
     * Sets up handlers, if any
     *
     * @method setupHandlers
     * @chainable
     */
    proto.setupHandlers = function() {
        return this;
    };

    return AbstractModel;
});
