define(function (require) {
    'use strict';

    var Classes = require('../constants/Classes');
    var Settings = require ('../constants/Settings');
    var Content = require('i18n!../constants/nls/en-us/Content');

    /**
     * @class App.Models.Zoombox
     *
     * @constructor
     * @param {TextBlockQuestionController} ctrl
     */
    var ZoomboxModel = function(ctrl) {
        this.active = false;
        this.$el = null;
        this.setController(ctrl);
    };

    /**
     * ZoomboxModel extends AbstractModel
     * @type {AbstractModel}
     */
    var proto = ZoomboxModel.prototype;

    /**
     * Set controller to gather data from
     * @method
     * @chainable
     * @param {TextBlockQuestionController} ctrl The controller
     */
    proto.setController = function(ctrl) {
        //console.log('JoeTest.ZoomBox.setController', ctrl);
        this.ctrl = ctrl;
        this.model = ctrl.model;
        this.$view = ctrl.$view;
        this.title = Content.get('zbox_' + ctrl.model.id, ctrl.model.info.desc);

        // bind activation methods to instance
        this.activate = this._setState.bind(this, true);
        this.deactivate = this._setState.bind(this, false);

        // new controller === new position info
        return this._setPosition();
    };

    /**
     * Change active state of model
     * @method
     * @private
     * @chainable
     * @param {Boolean} active Should be active or not
     */
    proto._setState = function(active) {
        //console.log('JoeTest.ZoomBox._setState', active);
		if (!active) {
			var isNotEmpty =
                this.$el.children().children('ul').text().trim().length > 0 ||
                this.$el.siblings().children().children('ul').text().trim().length > 0 ? true : false;
			this.$el.toggleClass(Classes.ZOOMBOX_ENABLED, isNotEmpty);
			this.$overlay.toggleClass(Classes.ZOOMBOX_HIGHLIGHT, isNotEmpty);
			/*
            if(!isNotEmpty) {
				for(var i = 0; i<window.box.length; i++) {
					if(window.box[i].model.value) {
						window.box[i].$el.toggleClass(Classes.ZOOMBOX_ENABLED, true);
						window.box[i].$overlay.toggleClass(Classes.ZOOMBOX_HIGHLIGHT, true);
						break;
					}
				}
			}
            */
		} else {
			for(var i=0; i<window.box.length; i++) {
				window.box[i].$el.toggleClass(Classes.ZOOMBOX_ENABLED, false);
				window.box[i].$overlay.toggleClass(Classes.ZOOMBOX_HIGHLIGHT, false);
			}
			this.$el.toggleClass(Classes.ZOOMBOX_ENABLED, active);
			this.$overlay.toggleClass(Classes.ZOOMBOX_HIGHLIGHT, active);
            
            // For FBT products previous fxg clearing           
            $('.zoombox_active').hide();
            $('.zoombox_active:first').show();
		}
		/*
        if (this.active !== active) {
            this.active = active;
            this.$el.toggleClass(Classes.ZOOMBOX_ENABLED, active);
            this.$overlay.toggleClass(Classes.ZOOMBOX_HIGHLIGHT, active);

            if (this.master) {
                this.master.$overlay
                    .find('.zoombox')
                    .first()
                    .toggleClass(Classes.ZOOMBOX_HIGHLIGHT, active);
            }
		}
        */

        return this;
    };

    /**
     * Convert position data into usable format
     * @method
     * @private
     * @chainable
     */
    proto._setPosition = function() {
        //console.log('ZoomBox._setPosition');
        var model = this.model;
        var pos = {};

        // check for position information
        if (!(model && model.info && model.info.pos)) {
            return this;
        }

        // convert values into numbers, if possible
        Object.keys(model.info.pos).forEach(function(val) {
            var posVal = model.info.pos[val];
            pos[val] = (typeof posVal === 'string' && !isNaN(+posVal)) ? +posVal : posVal;
            //console.log('ZoomBox.key/val.2: ' + val + ':' + pos[val]);
        });

        // link to model
        this.pos = model.info.pos;
        //console.log('ZoomBox._setPosition: ' + JSON.stringify(this.pos));

        return this;
    };

    /**
     * Get positioning information for box
     * @method
     * @return {Object} Object with position information
     */
    proto.getPosition = function() {
        return this.pos;
    };

    /**
     * Comparison function for ordering
     * @param  {ZoomboxModel} zoombox Model to compare to
     * @return {Number} Relative position number
     */
    proto.compareTo = function(zoombox) {
        return this.getPosition().x - zoombox.getPosition().x;
    };

    /**
     * Compile information for all lines in model
     * @param  {UIQuestionBlockModel}   model Question block to render
     * @param  {UIQuestionBlockModel[]} lines Question lines to render
     * @return {Object[]}                     Array of property objects
     */
    proto.getLines = function() {
        var hotjarMaskInput = false;

        if (!this.model.lines) {
            if (Settings.HOTJAR_MASK.indexOf(this.model.id) > -1) {
                hotjarMaskInput = true;
            }
            return [{
                      value: this.model.getValue(),
                      hotjarMaskInput: hotjarMaskInput
                   }];
        }

        return this.model.lines._items.map(function(line) {
            switch (line.id) { // only way to distinguish type at this time :(
            case 'BI':
                // bank information
                return {
                    complex: true,
                    fields: [
                        line.getValue('1'), // bank
                        line.getValue('2'), // addr1
                        line.getValue('3'), // addr2
                        line.getValue('4'), // city state zip
                        line.getValue('5')  // phone
                    ],
                   /* city: line.getValue('4_city'),
                    state: line.getValue('4_state'),
                    zip: line.getValue('4_zipCode')*/
                };
            /*case 'CI':
                // customer information
                return {
                    complex: true,
                    city: line.getValue('city'),
                    state: line.getValue('state'),
                    zip: line.getValue('zipCode')
                };*/
            default:
                if (Settings.HOTJAR_MASK.indexOf(line.blockId + '_' + line.id) > -1) {
                    hotjarMaskInput = true;
                }
                // Other blocks with simulated values
                if (line.sim && line.sim > 1) {
                    var lines = [];
                    for (var i=1; i <= line.sim; i++) {
                        lines.push(line.getSimValue(i));
                    }

                    return {
                        complex: true,
                        fields: lines,
                        hotjarMaskInput: hotjarMaskInput
                    };
                // Regular values
                } else {
                    // console.log(line);
                    return {
                        value: line.getValue(),
                        hotjarMaskInput: hotjarMaskInput
                    };
                }
            }
        });
    };

    return ZoomboxModel;
});