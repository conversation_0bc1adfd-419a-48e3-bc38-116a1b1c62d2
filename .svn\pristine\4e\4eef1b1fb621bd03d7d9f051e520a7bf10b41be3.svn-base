{{#if options}}
    <h2 class="hdg hdg_h2" id = "text-heading-{{first_word}}">{{desc}}
    {{#if envelope}} 
        <div class ="envelope_help_img">
        <img id="envelope_help" alt="envelope_help" class="js-button-action" src="{{host_url}}/webasset/w2p_mobile/{{baseAppUrl}}assets/media/images/helpText.png"
            data-container="body" data-template='<div id="envelope_link" class="custpop popover" role="tooltip"><div class="arrow"></div><div class="popover-body"></div></div>'
            data-placement="bottom"  data-content="" data-original-title="" title=""></img> 
        </div>

    {{/if}}
    </h2> 
    <ul class="blocks {{layoutClass}} js-inkColors">
        {{#each options}}
            <li>
                <input type="radio"
                    class="designRadio{{#is id ../default}} isChecked{{/is}}"
                    id="{{slug}}"
                    name="{{../id}}"
                    value="{{matrix.uid}}"
                    {{#is id ../default}} checked{{/is}} />

                <label for="{{slug}}" class="designSwatchLabel">
                    {{> question/required}}
                    <img class="designSwatchImage" src="{{imgSrc}}" id="Matrix-Img-{{tlId}}" alt="{{tlId}}"/>
                    <div class="designSwatchDesc" id="Matrix-Label-{{tlId}}">{{uiDesc}}</div>
                </label>

                <!--
                <label><div class="designSwatchDesc">-{{matrix.code}}-{{matrix.code2}}-</div></label>
                <label><div class="designSwatchDesc">-{{matrix.id}}-</div></label>
                <label><div class="designSwatchDesc">-{{layoutClass}}-</div></label>
                -->
                
            </li>
        {{/each}}
    </ul>
{{/if}}

{{#if envelope}} 
<div id="envelope_popup_model" style="display:none">

    <div id="envelope_content"><img src="{{host_url}}/webasset/w2p_mobile/{{baseAppUrl}}/assets/media/images/tooltip-close.png" id="CVCloseIcon" alt="CVCloseIcon" class="close tooltip-close" style="position: absolute; top: 0; right: 7px;"/>
        <div class='popovertitle'>Envelope Upgrade Options</div></br>
        {{#if hasSelfSealEnvelope}}
            <div><span class='popover-heading'>KwikSeal Envelopes - ${{selfSealEnvelopePrice}} per card</span></div>
            <div>Save time with KwikSeal Envelopes. Simply remove the protective strip and press the self-adhesive edge.</div><br/>
            
        {{/if}}            

        {{#if hasFoilLinedEnvelope}}
            <div><span class='popover-heading'>Foil Lined Envelopes - ${{foilLinedEnvelopePrice}} per card</span></div>
            <div>Your cards will look elegant in these foil-lined, regular sealed envelopes.</div><br/>
            
        {{/if}}

        {{#if hasFoilLinedSelfSealEnvelope}}
            <div><span class='popover-heading'>Foil Lined KwikSeal Envelopes - ${{foilLinedSelfSealEnvelopePrice}} per card</span></div>
            <div>Your cards will look elegant in these foil-lined KwikSeal envelopes. Just remove the protective strip and press the self-adhesive edge.</div></br>
        {{/if}}
        {{#if hasShimmerKwikSealEnvelope}}
            <div><span class='popover-heading'>Shimmer KwikSeal Envelopes - ${{shimmerKwikSealEnvelopePrice}} per card</span></div>
            <div>These distinctive unlined envelopes feature a metallic shimmer and are sure to stand out in the mail! The KwikSeal adhesive strip makes sealing super easy.</div>
        {{/if}}

    </div>

</div>
{{/if}}