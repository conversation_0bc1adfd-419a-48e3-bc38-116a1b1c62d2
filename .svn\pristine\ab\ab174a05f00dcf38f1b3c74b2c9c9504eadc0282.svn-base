define(function (require) {
    'use strict';

    var $ = require('jquery');
    var AbstractModel = require('../../Abstract');
    var CartProductImprintLocationModel = require('./ImprintLocation');
    var inherits = require('mout/lang/inheritPrototype');
    var merge = require('mout/object/merge');

    /**
     * @class App.Models.Cart.Product.Layout
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} LayoutValues
     */
    var CartProductLayoutModel = function (LayoutValues) {
        AbstractModel.call(this, LayoutValues);
    };

    var proto = inherits(CartProductLayoutModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} values
     * @chainable
     */
    proto.init = function(values) {

        /**
         * @property id
         * @default {null}
         * @type {string}
         */
        this.id = null;

        /**
         * @property imprintSpecId
         * @default {null}
         * @type {string}
         */
        this.imprintSpecId = null;

        /**
         * @property selectedNumbering
         * @default {null}
         * @type {number}
         */
        this.selectedNumbering = null;

        /**
         * @property selectedReverseNumbering
         * @default {null}
         * @type {boolean}
         */
        this.selectedReverseNumbering = null;

        /**
         * @property imprintLocations
         * @default {[]}
         * @type {Array}
         */
        this.imprintLocations = [];

        base.init.call(this, values);

        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param json the original JSON data.
     */
    proto.fromJSON = function(json) {
        //console.log('=================================================');
        //console.log('Layout.fromJSON()', json);
        this.id = json.layoutId;
        this.imprintSpecId = json.imprintSpecId;
        this.selectedNumbering = json.selectedNumbering;
        this.selectedReverseNumbering = json.selectedReverseNumbering === 'true' ? 'reverseNumbering' : '';
        var imprintLocations = [].concat(json.imprintLocations);
        for (var i=0; i<imprintLocations.length; i++) {
            //console.log('JoeTest[model.cart.product.Layout.imprintLocation]', imprintLocations[i]);
            var imprintLocation = new CartProductImprintLocationModel(imprintLocations[i]);
            this.imprintLocations.push(imprintLocation);
        }

        this.availableValues = {
            specId: this.imprintSpecId,
            numbering: this.selectedNumbering,
            reverseNumbering: this.selectedReverseNumbering
        };
    };

    /**
     * Converts all data into an XML string for sending
     * to the API
     * @method toXML
     * @return {string}
     */
    proto.toXML = function() {
        //console.log('Layout.toXML()');
        var xml;
        var $layout = $('<selectedLayout/>')
            .append($('<layoutId/>').text(this.id))
            .append($('<imprintSpecId/>').text(this.imprintSpecId))
            .append($('<selectedNumbering/>').text(this.selectedNumbering))
            .append($('<selectedReverseNumbering/>').text(this.selectedReverseNumbering));
            for (var i=0; i<this.imprintLocations.length; i++) {
                var imprintLocation = this.imprintLocations[i];
                $layout.append(imprintLocation.toXML());
            }
        xml = $layout[0].outerHTML;
        return xml;
    };

    /**
     * Returns all associated blocks so that we can pass them up
     * to the parent Cart Product model, which can then be used
     * to apply the imprint block values to associated UI Question Blocks
     * in an unconfigured product.
     *
     * @method getBlocks
     * @return {App.Models.Cart.Product.Collections.ImprintBlocks}
     */
    proto.getBlocks = function() {
        var blocks = {};
        for (var i=0; i<this.imprintLocations.length; i++) {
            var blockData =  this.imprintLocations[i].getBlocks();        
            blocks = merge(blocks, this.removeDuplicateBlocks(blocks,blockData));
        }
        return blocks;
    };

    /**
     * @method removeDuplicateBlocks
     * @param {Object} currentBlocks 
     * @param {Object} newBlocks 
     * @returns {Object}
     */
    proto.removeDuplicateBlocks = function (currentBlocks, newBlocks) {
        var sanitizedBlocks = {};
        if (currentBlocks && Object.keys(currentBlocks).length === 0
            && Object.getPrototypeOf(currentBlocks) === Object.prototype) {
            return newBlocks;
        } else {
            Object.keys(newBlocks).forEach(function (key) {
                if (currentBlocks[key] == null) {
                    sanitizedBlocks[key] = newBlocks[key];
                }
            });
            return sanitizedBlocks;
        }

    };

    /**
     * @method getValue
     * @return {Any}
     */
    proto.getValue = function(name) {
        var val = this.availableValues[name];
        if (!val) {
            for (var i=0; i<this.imprintLocations.length; i++) {
                if (this.imprintLocations[i].getValue(name)) {
                    val = this.imprintLocations[i].getValue(name);
                }
            }
        }
        return val;
    };

    return CartProductLayoutModel;
});
