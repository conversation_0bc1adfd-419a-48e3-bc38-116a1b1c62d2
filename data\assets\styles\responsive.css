/* For desktop */
:root {
  --widthvalue: 0px;
  --leftvalue: 50px;
}

.site-bd > .row {
  display: flex !important;
}

.footer-preview-btn,
.footer-preview-btn:hover .footer-preview-btn:focus {
  height: 100px;
}

.footer-preview-btn:before {
  font-family: fontAwesome;
  content: '\f002';
  color: #fff;
  font-size: 22px;
}
/* Fix for footer preview button hover state */
.footer-preview-btn:hover,
.footer-preview-btn:focus {
  height: 100px;
}

/* Add transition for smoother icon appearance */
.footer-preview-btn:before,
.closePreview:before {
  transition: color 0.3s ease;
}

/* Fix subtotal popup duplicate declaration */
.subtotal-popup {
  color: #000f0c;
  left: 0px !important;
  position: fixed;
  
}

/* Add hover effect for better interaction */
.footer-preview-btn:hover:before,
.closePreview:hover:before {
  color: #e6e6e6;
}

/* Add focus styles for accessibility */
.footer-preview-btn:focus,
.closePreview:focus {
  outline: 2px solid #b8dade;
  outline-offset: 2px;
}

.closePreview:before {
  font-family: fontAwesome;
  content: '\f00d';
  color: #fff;
  font-size: 22px;
}

.subtotal-popup {
  color: #000f0c;
}
.subtotal-popup {
  left: 0px !important;
  position: fixed;
}

.view-details-mob {
  font-size: 14px;
  text-align: right;
}

.view-details-mob span {
  transition: background-size 0.3s ease;
  font-weight: normal;
  color: #000f0c !important;
  text-decoration: underline;
  background: linear-gradient(to bottom, transparent 62%, #b8dade 0) left
    center/0 100% no-repeat;
}
.view-details-mob span:hover {
  font-weight: normal;
  color: #000f0c !important;
  text-decoration: none;
  background-size: 100% 100%;
}

p.progress-id {
  background-color: #f1f1f1;
  border-radius: 50%;
  border: 2px solid lightgrey;
  font-size: 14px;
  font-weight: 900;
  height: 32px;
  line-height: 28px;
  margin: 0 auto;
  position: relative;
  width: 32px;
  text-align: center;
  z-index: 1;
  color: #000f0c;
}

label.progressItem.itemActive.currentItem .progress-id {
  cursor: pointer;
  background-color: #3174d8;
  color: white;
  border: 2px solid black;
}

label.progressItem.itemActive .progress-id {
  cursor: pointer;
  background-color: #b8dade;
  color: #000f0c;
  border: 2px solid green;
}

p.progress-title {
  margin-top: 5px;
  margin-bottom: 0;
  margin-right: 4px;
  font-size: 10px;
  color: #000f0c;
  font-weight: 400;
  text-align: center;
  margin-left: 4px;
  line-height: 1;
}

.btn.btn-primary.previewMobile.footer-preview-btn {
  box-shadow: none !important;
}

@media (min-width: 991px) {
  .toggle_rgb {
    width: 26px;
    height: 26px;
  }
  .design-bd img {
    width: revert;
  }
}


@media (max-width: 767px) {
  .cfgTopDivider-proof {
    margin-top: 0;
    padding-top: 0;
  }
  #config-container {
    height: 100vh;
  }

  .site-bd > .row {
    margin: 0px;
  }

  .split {
    text-align: center;
  }

  #Zoom-ViewLarger-Bottom,
  #viewZoomText {
    /* display: none;
    visibility: hidden; */
    text-align: center;
    margin-bottom: 15px;
  }

  .bottom-row {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
  }

  .dropdown-50-left,
  .dropdown-50-right {
    width: 100% !important;
    float: none !important;
  }
  
  .pricing-widget-mobile {
    margin-left: -8.5% !important;
    margin-right: -8.5% !important;
  }

  .options-mobile,
  .sub-pop-mobile,
  .productTotal-mobile,
  .js-product-next-mobile,
  .js-product-next-fbt-mobile {
    width: 95%;
    margin: auto;
  }

  .footer-preview {
    background-color: #f1f1f1;
  }

  .footer-widget{
    padding-top: 10px;
  }

  /* 13179 */
  .container {
    padding: unset;
  }

  .site-hd-right {
    padding: 0px !important;
  }

  ul.progressBlocks {
    display: flex;
    justify-content: space-between;
    list-style-type: none;
    position: relative;
    padding-left: 10px;
    padding-right: 10px;
    margin-top: 10px;
  }

  .progress-id {
    border: 1px solid #ddd;
    border-radius: 50%;
    text-align: center;
  }

  ul.progressBlocks li::after {
    content: '';
    position: absolute;
    border: 1px solid;
    width: 91%;
    top: 12px;
    z-index: -1;
    left: 5%;
  }
  .progressBlocks li {
    padding: 0 !important;
    border-bottom: 0 !important;
  }
  .progressBlocks .currentItem {
    text-decoration: none !important;
  }

  ul.progressBlocks li:first-child::after {
    border: 0;
  }

  p.progress-title {
    width: min-content;
  }

  /* End 13179 */

  .brandLogo {
    padding-left: 10px;
  }

  .mobile-mt-3 {
    margin-top: 10px;
    box-shadow: inset 0px 10px 8px -11px #908c8c;
    padding-top: 5px;
  }

  .site-hd {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 10px 0px 0 0px;
    background: #fff;
    position: relative;
    z-index: 50;
    width: 100% !important;
    height: auto !important;
  }

  .site-bd {
    position: relative;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
    background: #fff;
    z-index: 50;
    /* padding: 25px 10px 154px 10px !important; */
    padding: 10px 10px 25px 10px !important;
  }

  .site-hd-desktop {
    display: none;
  }

  .site-hd-mobile {
    display: block;
  }

  .site {
    width: 100% !important;
  }

  /* .progressBlocks li {
          padding-right: 12px !important;
          border: none !important;
      } */

  .footer-preview-btn {
    width: 100%;
  }
  .footer-preview-btn,
  .footer-preview-btn:hover .footer-preview-btn:focus {
    height: 100%;
  }

  /* 13502 */
  .verseSelector ul {
    display: grid;
  }

  .verseSelector ul li {
    width: 100% !important;
    margin-bottom: 0px;
  }

  .designBox.verse {
    margin-top: 0px;
  }

  .cfgSiteLogo-mobile {
    height: auto !important;
  }

  .js-product-nav-mobile {
    padding-top: 10px !important;
  }

  .custpop {
    width: 95vw !important;
    margin-left: 10px;
  }

  [data-controller='PreviewController'] {
    display: none !important;
  }

  .left-preview-panel {
    order: 1;
    -ms-flex-order: 1;
  }

  .js-logos .blocks_5up > * {
    width: 50%;
  }

  .chooseALogo,
  .cfgLogoTopDivider {
    margin: 15px 0px;
  }

  /* div#ax_paragraph {
    display: none;
  } */

  .fbt-product-desc {
    min-width: auto;
  }

  .fbtProductHeader {
    flex-direction: column;
    align-items: baseline;
  }

  .cfgSiteLogo {
    padding: 0px 10px 10px;
  }

  .fbtTitle {
    box-shadow: inset 0px 10px 8px -11px #908c8c;
    padding: 10px;
    width: 100%;
  }

  .verseId {
    left: 150px;
  }

  .footerWrapper {
    display: none !important;
    width: 0 !important;
    max-width: 0 !important;
  }

  .split-right.mt4.price-bd-mob {
    margin-top: 1.5px !important;
  }

  .price {
    font-size: 14px !important;
  }

  p.progress-id {
    width: 28px !important;
    font-size: 10px !important;
    height: 28px !important;
    line-height: 24px !important;
  }

  p.progress-title {
    font-size: 10px !important;
  }

  ul.progressBlocks li::after {
    top: 13.5px !important;
  }

  

  .button_fbt_next {
    margin-bottom: 0px !important;
  }

  .skip-editor-buttons {
    padding: 20px;
  }

  .js-fbt-reviewcart {
    width: max-content;
  }

  .fbt-product-row-summary,
  .fbt-product-row {
    text-align: center;
  }

  .fbt-product-image,
  .fbt-product-action {
    width: 100%;
  }

  .fbt-product-desc {
    min-width: 100%;
  }

  .mix-hdg_caps_temp {
    font-size: 30px;
    margin-bottom: 5px;
  }

  .subTitle {
    font-size: 19px;
  }

  .continue-shopping {
    padding: 0px;
  }

  .mix-hdg_caps_tempzzz,
  .mix-hdg_caps_tempzz {
    font-size: 30px;
  }
  #fbt-help-img {
    width: 100% !important;
  }

  /* DCOM-16492 start */
  /* .js-product-next {
    padding: 0px 20px 2px 20px;
  } */
  .productTotal {
    padding-top: 3px;
  }
  /* DCOM-16492 end */

  #Matrix-Img-paper_sig_white_sm {
    width: 100% !important;
  }

  .personalization_link_review {
    margin-left: 10px !important;
    margin-top: 0px !important;
    margin-bottom: 0px !important;
  }

  .flex-body {
    flex-direction: column;
  }

  #mainImg {
    max-height: 30vh;
    max-width: 100%;
    width: auto;
  }

  #popup-mobile {
    display: block !important;
  }
  #popup-desktop {
    display: none !important;
  }

  .js-preview-mobile {
    display: flex !important;
    justify-content: center;
  }
  .js-mobile-single-row {
    flex-direction: column !important;
  }
  div#ax_paragraph {
    float: right !important;
    position: relative !important;
    padding: 0 15px !important;
    font-size: 12px !important;
  }
  #Header-BrandLogo {
    display: inline !important;
  }

  .currentPdt {
    font-size: 18px !important;
  }
  .currentPdt1 {
    font-size: 8px;
    font-weight: bolder;
  }
  .inkColorLabel {
    margin-top: 3px;
  }
  .design-bd {
    min-height: 0px !important;
  }
  .mob-none{
    display: none !important;
  }

  .choice-list label {
    height: 140px;
  }

  .choice-list-img {
    background-size: contain;
  }
}

@media (max-width: 1200px) {
  .container {
    max-width: 100%;
    padding: 0px;
  }

  .site-bd {
    width: 100vw;
  }
}




@media (max-width: 300px) {
  ul.progressBlocks li::after {
    width: 89%;
    left: 6%;
  }
}

@media (max-width: 480px) {
  .custpop {
    width: 88vw !important;
    margin-left: 0;
  }
}
@media (max-width: 450px) {
  .custpop {
    width: 80vw !important;
  }
}
@media (max-width: 400px) {
  .subtotal_val {
    font-size: 14px !important;
  }
  .subtotal_desc {
    font-size: 14px !important;
    margin-top: 0px !important;
  }
  .subTotalPopUp {
    font-size: 12px !important;
  }
  .split-right.mt4.price-bd-mob {
    margin-top: 0.5px !important;
  }

  /* ul.progressBlocks {
          width: auto !important;
      }
  
      .js-product-title {
          overflow-y: auto
      } */

  p.progress-id {
    width: 25px !important;
    font-size: 8px !important;
    height: 25px !important;
    line-height: 21px !important;
  }

  p.progress-title {
    font-size: 8px !important;
  }

  ul.progressBlocks li::after {
    top: 12.5px !important;
  }
  .custpop {
    width: 78vw !important;
  }
}

@media (max-width: 339px) {
  .subtotal_val {
    font-size: 14px !important;
  }
  .subtotal_desc {
    font-size: 14px !important;
    margin-top: 0px !important;
  }
  .subTotalPopUp {
    font-size: 11px !important;
  }
  .split-right.mt4.price-bd-mob {
    margin-top: -0.5px !important;
  }

  p.progress-id {
    width: 22px !important;
    font-size: 7px !important;
    height: 22px !important;
    line-height: 18px !important;
  }

  p.progress-title {
    font-size: 6px !important;
  }

  ul.progressBlocks li::after {
    top: 11.5px !important;
  }
}

@media (min-width: 768px) and (max-width: 1000px) {
  .zoombox,
  .detailBox {
    display: none !important;
  }
}



@media (min-width: 768px) {
    /*DCOM-16528
 .left-preview-panel {
    padding-left: 50px;
  }DCOM-16528
 */

  /*DCOM-16528
 .topLens {
    width: 50px;
    left: -13px;
    text-align: center;
  }DCOM-16528
 */
 .site-hd-desktop {
  display: block;
}
.site-hd-mobile {
  display: none;
}

ul.progressBlocks {
  display: flex;
  justify-content: space-between;
  list-style-type: none;
  position: relative;
  padding-left: 10px;
  padding-right: 10px;
  margin-top: 10px;
}

ul.progressBlocks li::after {
  content: '';
  position: absolute;
  border: 1px solid;
  width: var(--widthvalue);
  top: 16px;
  z-index: -1;
  left: var(--leftvalue);
}

.progressBlocks li {
  padding: 0 !important;
  border-bottom: 0 !important;
  margin-right: 30px;
}

.progressBlocks .currentItem {
  text-decoration: none !important;
}

ul.progressBlocks li:first-child::after {
  border: 0;
}

.progress-id {
  border: 1px solid #ddd;
  border-radius: 50%;
  text-align: center;
}
/* 
p.progress-id {
  width: 28px !important;
  font-size: 10px !important;
  height: 28px !important;
  line-height: 24px !important;
}

p.progress-id {
  width: 25px !important;
  font-size: 8px !important;
  height: 25px !important;
  line-height: 21px !important;
}

p.progress-id {
  width: 22px !important;
  font-size: 7px !important;
  height: 22px !important;
  line-height: 18px !important;
}*/

/* p.progress-title {
  width: min-content;
}  */
  .newCounterPos {
    float: right !important;
    position: relative !important;
    padding: 20px !important;
    /* font-size: 16px !important; */
  }
  .newCounterPos1 {
    float: left !important;
    position: relative !important;
  }

  .currentPdt {
    font-size: 25px !important;
  }
  .currentPdt1 {
    font-size: 10.5px;
    font-weight: bolder;
    margin-left: 3px;
  }
  label.progressItem.itemActive .progress-title {
    cursor: pointer;
    color: #3174d8 !important;
  }

  label.progressItem.itemActive.currentItem .progress-title {
    cursor: pointer;
    color: #000f0c !important;
  }
}

@media (min-width: 576px) {
  .choice-list {
    grid-template-columns: repeat(2, 1fr);
  }

  .choice-hd .nav-action-btns {
    justify-content: end !important;
  }
}