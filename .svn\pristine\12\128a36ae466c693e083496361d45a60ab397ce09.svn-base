{"version": 3, "file": "selectonic.min.js", "sources": ["selectonic.js"], "names": ["$", "window", "undefined", "Options", "schema", "initial", "TypeError", "this", "_schema", "_options", "_callbacks", "set", "Plugin", "element", "options", "_name", "pluginName", "el", "$el", "ui", "_selected", "_isEnable", "_keyModes", "_this", "on", "value", "_itemsSelector", "get", "_setScrolledElem", "_init", "_throttle", "func", "wait", "context", "args", "result", "timeout", "previous", "later", "leading", "Date", "apply", "now", "remaining", "arguments", "clearTimeout", "trailing", "setTimeout", "$document", "document", "itContains", "array", "elem", "Array", "length", "i", "checkType", "val", "type", "isNullable", "nullable", "prototype", "obj", "isNew", "option", "callback", "newOptions", "defaults", "defOption", "unchangeable", "Error", "msg", "join", "values", "RangeError", "extend", "call", "opt", "cb", "off", "filter", "default", "multi", "mouseMode", "focusBlur", "<PERSON><PERSON><PERSON><PERSON>", "handle", "textSelection", "focusOnHover", "keyboard", "keyboardMode", "autoScroll", "loop", "preventInputs", "listClass", "focusClass", "selectedClass", "disabledClass", "create", "before", "focusLost", "select", "unselect", "unselectAll", "stop", "destroy", "keyCode", "DOWN", "UP", "SHIFT", "END", "HOME", "PAGE_DOWN", "PAGE_UP", "A", "SPACE", "ENTER", "getDataObject", "data", "addClass", "_bindEvents", "_callEvent", "selector", "_scrolledElem", "_cancel", "e", "params", "wasCancelled", "isCancellation", "_isPrevented", "each", "changedItems", "index", "item", "prevItemsStates", "_select", "_unselect", "prevFocus", "_setFocus", "name", "_mouseEvent", "_<PERSON><PERSON><PERSON><PERSON>", "_keyboardEvent", "_key<PERSON><PERSON><PERSON>", "_selectstartEvent", "_mousemoveEvent", "_mousemoveHandler", "_unbindEvents", "_getTarget", "$elem", "target", "handleElem", "is", "parentNode", "_getItems", "items", "j<PERSON>y", "find", "fn", "_getNextPageElem", "allItems", "first", "last", "$candidate", "candHeight", "currentIndex", "cand", "_isOptimized", "isShiftPageRange", "box", "boxViewHeight", "clientHeight", "winViewHeight", "outerHeight", "$current", "isBoxBigger", "pageHeight", "itemHeight", "currentHeight", "itemsHeight", "direction", "rangeStart", "eq", "rangeEnd", "event", "focus", "selected", "unselected", "_controller", "method", "_stop", "wasSelected", "isTargetWasSelected", "_getIsSelected", "isRangeSelect", "_perfomRangeSelect", "isMultiSelect", "_unselectAll", "_blur", "beforeStart", "afterStart", "beforeEnd", "afterEnd", "endAfterStart", "top", "bot", "isNewSolidSelection", "slice", "add", "solidInitialElem", "_changeItemsStates", "delta", "aboveZero", "isSelected", "selectedCondition", "isSelectedTarget", "push", "toggleClass", "concat", "silent", "isOnlyTargetSelected", "_multiSelect", "_rangeSelect", "arr", "x", "y", "subArr", "hasClass", "map", "removeClass", "_checkIfElem", "res", "nodeType", "_checkIfSelector", "tagName", "isAllSelect", "page", "key", "which", "_shiftModeAction", "shift", "_isMulti", "_findNextTarget", "isDown", "shift<PERSON>ey", "preventDefault", "_rangeVariator", "_multiVariator", "scroll", "isFocusSelected", "isTargetSelected", "prevItem", "afterTarget", "isSelectedAfterTarget", "edge", "_refreshBoxScroll", "$box", "isWindow", "boxScrollTop", "scrollTop", "boxWindowY", "offset", "$item", "itemBoxTop", "_isRange", "ctrl<PERSON>ey", "metaKey", "is<PERSON><PERSON><PERSON>", "isRange", "_mousedownOnItem", "_isFocusOnHoverPrevented", "_isHovered", "_preventMouseMove", "_focusHoverTimeout", "_callPublicMethod", "publicMethod", "isFunction", "char<PERSON>t", "isEnabled", "isPlainObject", "getSelected", "removeData", "blur", "getIds", "id", "getSelectedId", "enable", "disable", "cancel", "refresh", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;CAGC,SAASA,EAAGC,EAAQC,GACnB,YAwCA,SAASC,GAAUC,EAAQC,GACzB,GAAuB,gBAAXD,GAAwB,KAAM,IAAIE,WAAU,mEAKxD,OAJAC,MAAKC,QAAaJ,EAClBG,KAAKE,YACLF,KAAKG,cACLH,KAAKI,IAAKN,GAAS,GACZE,KAiHT,QAASK,GAAQC,EAASC,GACxBP,KAAKQ,MAAaH,EAAOI,WACzBT,KAAKU,GAAaJ,EAClBN,KAAKW,IAAalB,EAAGa,GACrBN,KAAKY,MACLZ,KAAKa,UAAa,EAClBb,KAAKc,WAAa,EAClBd,KAAKe,aACLf,KAAKO,QAAa,GAAIX,GAASC,EAAQU,EAEvC,IAAIS,GAAQhB,IACZA,MAAKO,QAAQU,GAAG,SAAU,SAAUC,GAGlC,MADAF,GAAMG,eAAiB,IAAMH,EAAMT,QAAQa,IAAI,aAAe,IAAMF,EAC7DA,IAETlB,KAAKO,QAAQU,GAAG,aAAc,SAAUC,GAEtC,MADAF,GAAMK,iBAAkBH,GACjBA,IAETlB,KAAKmB,eAAiB,IAAMnB,KAAKO,QAAQa,IAAI,aAAe,IAAMpB,KAAKO,QAAQa,IAAI,UACnFpB,KAAKqB,iBAAkBrB,KAAKO,QAAQa,IAAI,eACxCpB,KAAKsB,QAlLP,GAAIC,GAAY,SAASC,EAAMC,EAAMlB,GACnC,GAAImB,GAASC,EAAMC,EACfC,EAAU,KACVC,EAAW,CACfvB,GAAUA,KACV,IAAIwB,GAAQ,WACVD,EAAWvB,EAAQyB,WAAY,EAAQ,EAAI,GAAIC,MAC/CJ,EAAU,KACVD,EAASJ,EAAKU,MAAMR,EAASC,GAE/B,OAAO,YACL,GAAIQ,GAAM,GAAIF,KACTH,IAAYvB,EAAQyB,WAAY,IAASF,EAAWK,EACzD,IAAIC,GAAYX,GAAQU,EAAML,EAW9B,OAVAJ,GAAU1B,KACV2B,EAAOU,UACU,GAAbD,GACFE,aAAaT,GACbA,EAAU,KACVC,EAAWK,EACXP,EAASJ,EAAKU,MAAMR,EAASC,IACnBE,GAAWtB,EAAQgC,YAAa,IAC1CV,EAAUW,WAAWT,EAAOK,IAEvBR,IAIXa,EAAYhD,EAAGC,EAAOgD,UAkBlBC,EAAa,SAAUC,EAAOC,GAChC,GAAKD,YAAiBE,QAASF,EAAMG,OAAS,GAAKF,IAASlD,EAC1D,IAAK,GAAIqD,GAAI,EAAGA,EAAIJ,EAAMG,OAAQC,IAAO,GAAIH,IAASD,EAAMI,GAAK,OAAO,CAE1E,QAAO,EAGTpD,GAAQqD,UAAY,SAASC,EAAKrD,GAChC,GAAIsD,SAAcD,GAAKE,EAAqB,OAARF,GAAgBrD,EAAOwD,QAC3D,OAASxD,GAAOsD,eAAgBL,OAAUH,EAAW9C,EAAOsD,KAAMA,IAASC,EAAaD,IAAStD,EAAOsD,MAAQC,GAGlHxD,EAAQ0D,UAAUlD,IAAM,SAAUmD,EAAKC,GACrC,GAGAC,GAAQC,EAHJ7D,EAASG,KAAKC,QAClB0D,EAAaH,KAAaxD,KAAKoB,MAC/BwC,IAEAL,GAAMA,KAGN,KAAME,IAAUF,GAAM,CACpB,GAAIL,GAAMK,EAAKE,GACfI,EAAYhE,EAAQ4D,EAEpB,IAAKI,IAAclE,EAAY,CAE7B,GAAKkE,EAAUC,eAAiBN,EAC9B,KAAM,IAAIO,OAAO,WAAcN,EAAS,0CAG1C,KAAM7D,EAAQqD,UAAUC,EAAKW,GAAa,CACxC,GAAIG,GAAM,WAAcP,EAAS,cAC7BI,EAAUV,eAAgBL,OAAQe,EAAUV,KAAKc,KAAK,MAAQJ,EAAUV,OACxEU,EAAUR,SAAW,YAAc,IACvC,MAAM,IAAItD,WAAWiE,GAGvB,GAAKH,EAAUK,SAAWvB,EAAWkB,EAAUK,OAAQhB,GACrD,KAAM,IAAIiB,YAAY,WAAcV,EAAS,qCAAyCI,EAAUK,OAAOD,KAAK,QAAY,OAK9H,GAAKT,EACH,IAAMC,IAAU5D,GACTA,EAAQ4D,GAAS,aAAe9D,IAAciE,EAAUH,GAAW5D,EAAQ4D,GAAS,WAG7FE,GAAaH,EAAQ/D,EAAE2E,OAAQR,EAAUL,GAAQA,CAEjD,KAAME,IAAUF,IACRG,EAAW1D,KAAKG,WAAWsD,MAC/BF,EAAIE,GAAUC,EAASW,KAAMrE,KAAMuD,EAAIE,IAG3CzD,MAAKE,SAAWT,EAAE2E,OAAQpE,KAAKE,SAAUyD,IAG3C/D,EAAQ0D,UAAUlC,IAAM,SAAUkD,GAChC,MAAOA,GAAMtE,KAAKE,SAAUoE,GAAQ7E,EAAE2E,UAAYpE,KAAKE,WAGzDN,EAAQ0D,UAAUrC,GAAK,SAAUwC,EAAQc,GACvCvE,KAAKG,WAAYsD,GAAWc,GAG9B3E,EAAQ0D,UAAUkB,IAAM,SAAUf,GAC3BzD,KAAKG,WAAYsD,UAAoBzD,MAAKG,WAAWsD,GAI5D,IAAI5D,IAEF4E,QAAkBC,UAAU,MAAgBvB,KAAK,UACjDwB,OAAkBD,WAAU,EAAgBvB,KAAK,WAEjDyB,WAAkBF,UAAU,WAAgBvB,KAAK,SAAUe,QAAQ,WAAW,UAAU,WACxFW,WAAkBH,WAAU,EAAgBvB,KAAK,WACjD2B,eAAkBJ,WAAU,EAAgBvB,KAAK,WACjD4B,QAAkBL,UAAU,KAAgBvB,KAAK,SAAUE,UAAS,GACpE2B,eAAkBN,WAAU,EAAgBvB,KAAK,WACjD8B,cAAkBP,WAAU,EAAgBvB,KAAK,WAEjD+B,UAAkBR,WAAU,EAAgBvB,KAAK,WACjDgC,cAAkBT,UAAU,SAAgBvB,KAAK,SAAUe,QAAQ,SAAS,WAC5EkB,YAAkBV,WAAU,EAAgBvB,MAAM,UAAU,WAC5DkC,MAAkBX,WAAU,EAAgBvB,KAAK,WACjDmC,eAAkBZ,WAAU,EAAgBvB,KAAK,WAEjDoC,WAAkBb,UAAU,eAAgBvB,KAAK,SAAUW,cAAa,GACxE0B,YAAkBd,UAAU,YAAgBvB,KAAK,SAAUW,cAAa,GACxE2B,eAAkBf,UAAU,aAAgBvB,KAAK,SAAUW,cAAa,GACxE4B,eAAkBhB,UAAU,aAAgBvB,KAAK,SAAUW,cAAa,GAExE6B,QAAkBjB,UAAU,KAAgBvB,KAAK,WAAYE,UAAS,GACtEuC,QAAkBlB,UAAU,KAAgBvB,KAAK,WAAYE,UAAS,GACtEwC,WAAkBnB,UAAU,KAAgBvB,KAAK,WAAYE,UAAS,GACtEyC,QAAkBpB,UAAU,KAAgBvB,KAAK,WAAYE,UAAS,GACtE0C,UAAkBrB,UAAU,KAAgBvB,KAAK,WAAYE,UAAS,GACtE2C,aAAkBtB,UAAU,KAAgBvB,KAAK,WAAYE,UAAS,GACtE4C,MAAkBvB,UAAU,KAAgBvB,KAAK,WAAYE,UAAS,GACtE6C,SAAkBxB,UAAU,KAAgBvB,KAAK,WAAYE,UAAS,GAkCxEhD,GAAOI,WAAa,aACpBJ,EAAO8F,SAAeC,KAAK,GAAIC,GAAG,GAAIC,MAAM,GAAIC,IAAI,GAAIC,KAAK,GAAIC,UAAU,GAAIC,QAAQ,GAAIC,EAAE,GAAIC,MAAM,GAAIC,MAAM,IAWjHxG,EAAOyG,cAAgB,SAAUpG,GAC/B,MAAOjB,GAAGiB,GAAKqG,KAAM,UAAY1G,EAAOI,aAe1CJ,EAAOiD,UAAUhC,MAAQ,WACvBtB,KAAKW,IAAIqG,SAAUhH,KAAKO,QAAQa,IAAI,cACpCpB,KAAKiH,cACLjH,KAAKW,IAAIoG,KAAM,UAAY1G,EAAOI,WAAYT,MAC9CA,KAAKkH,WAAW,WAUlB7G,EAAOiD,UAAUjC,iBAAmB,SAAU8F,GAC5C,GAAItE,EAEJ,IAAK,OAASsE,IAAY,IAAUA,EAElC,kBADOnH,MAAKoH,aAGd,IAAyB,gBAAbD,GAAwB,CAElC,GADAtE,EAAOpD,EAAG0H,KACNtE,EAAKE,OAAS,GAGhB,KAAM,IAAIgB,OAAM,qDAAwDoD,EAAW,IAErF,aAJEnH,KAAKoH,cAAgBvE,EAAK,IAM9B7C,KAAKoH,cAAgBpH,KAAKU,IAY5BL,EAAOiD,UAAU+D,QAAU,SAAUC,EAAGC,GACtC,IAAKA,EAAOC,aAAZ,CACAD,EAAOE,eAAiBzH,KAAK0H,cAAe,CAC5C,IAAI1G,GAAQhB,IAGZP,GAAEkI,KACAlI,EAAE8H,EAAOK,cACT,SAAUC,EAAOC,GAGVP,EAAOQ,gBAAiBF,GAC3B7G,EAAMgH,QAASV,EAAGC,EAAQ9H,EAAEqI,IAAO,GAEnC9G,EAAMiH,UAAWX,EAAGC,EAAQ9H,EAAEqI,IAAO,KAKtCP,EAAOW,WAAclI,KAAKmI,UAAWZ,EAAOW,iBAC1CX,GAAOE,eACdF,EAAOC,cAAe,IASxBnH,EAAOiD,UAAU2D,YAAc,WAC7B,GAAIjG,GAAQhB,KAAMoI,EAAOpI,KAAKQ,KAE9BR,MAAKqI,YAAc,SAASf,GACrBtG,EAAMF,WAAcE,EAAMsH,cAAcjE,KAAKrD,EAAOsG,IAE3DtH,KAAKuI,eAAiB,SAASjB,GACzBtG,EAAMT,QAAQa,IAAI,aAAeJ,EAAMF,WAAcE,EAAMwH,YAAYnE,KAAKrD,EAAOsG,IAEzFtH,KAAKyI,kBAAoB,WACvB,MAAMzH,GAAMT,QAAQa,IAAI,iBAAxB,QAAoD,GAEtDpB,KAAK0I,gBAAkBnH,EAAW,SAAS+F,GACrCtG,EAAMF,WAAaE,EAAMT,QAAQa,IAAI,iBAAoBJ,EAAM2H,kBAAkBtE,KAAKrD,EAAOsG,IAChG,IAEH7E,EAAUxB,GAAI,WAAWmH,EAAYpI,KAAKuI,gBAC1C9F,EAAUxB,GAAI,SAASmH,EAAcpI,KAAKuI,gBAC1C9F,EAAUxB,GAAI,aAAamH,EAAUpI,KAAK0I,iBAC1CjG,EAAUxB,GAAI,SAASmH,EAAcpI,KAAKqI,aAC1C5F,EAAUxB,GAAI,aAAamH,EAAUpI,KAAKqI,aAC1CrI,KAAKW,IAAIM,GAAK,WAAWmH,EAAYpI,KAAKqI,aAC1CrI,KAAKW,IAAIM,GAAK,eAAemH,EAAQpI,KAAKyI,oBAS5CpI,EAAOiD,UAAUsF,cAAgB,WAC/B,GAAIR,GAAOpI,KAAKQ,KAChBiC,GAAU+B,IAAK,WAAW4D,EAAYpI,KAAKuI,gBAC3C9F,EAAU+B,IAAK,SAAS4D,EAAcpI,KAAKuI,gBAC3C9F,EAAU+B,IAAK,aAAa4D,EAAUpI,KAAK0I,iBAC3CjG,EAAU+B,IAAK,SAAS4D,EAAcpI,KAAKqI,aAC3C5F,EAAU+B,IAAK,aAAa4D,EAAUpI,KAAKqI,aAC3CrI,KAAKW,IAAI6D,IAAM,WAAW4D,EAAYpI,KAAKqI,aAC3CrI,KAAKW,IAAI6D,IAAM,eAAe4D,EAAQpI,KAAKyI,oBAY7CpI,EAAOiD,UAAUuF,WAAa,SAAUvB,GAMtC,IALA,GAEEwB,GAAOC,EAAQC,EAFbnG,EAAOyE,EAAEyB,OACXhE,EAAS/E,KAAKO,QAAQa,IAAI,UAIX,OAATyB,GAAiBA,IAAS7C,KAAKU,IACrCoI,EAAQrJ,EAAEoD,GAEViG,EAAMpH,QAAUhC,EAAOgD,SACnBoG,EAAMG,GAAGjJ,KAAKmB,kBAAoB4H,EAASlG,GAC3CkC,GAAU+D,EAAMG,GAAGlE,KAAYiE,EAAanG,GAChDA,EAAOA,EAAKqG,UAEd,OAAInE,IAAUlC,GAAQmG,EACbD,GAGGhE,GAAUlC,EACbkG,EAGF,MAcT1I,EAAOiD,UAAU6F,UAAY,SAAU5B,EAAQwB,EAAQlG,GACrD,GAAIuG,EAEJ,QAAQL,GACR,IAAK,OACL,IAAK,OAKH,IAJA,GACAjB,GAAOjF,EAAKwG,OAASxG,EAAOpD,EAAGoD,GAC/ByG,EAAO7J,EAAE8J,GAAGR,KAEC,CAEX,GADAjB,EAAOwB,EAAKjF,KAAMyD,GACG,IAAhBA,EAAK/E,OAAiB,KAG3B,IADA+E,EAAKpG,QAAUhC,EAAOgD,SACjBoF,EAAKmB,GAAGjJ,KAAKmB,gBAAoB,MAAO2G,GAE/C,MAAO,KAET,KAAK,SACL,IAAK,WACH,MAAO9H,MAAKwJ,iBAAkBjC,EAAQwB,EAAQlG,EAEhD,KAAK,QAGH,MAFAuG,GAAQ7B,EAAOkC,SAAWlC,EAAOkC,SAAWzJ,KAAKW,IAAI2I,KAAMtJ,KAAKO,QAAQa,IAAI,WAC5EmG,EAAOkC,SAAWL,EACXA,EAAMM,OAEf,KAAK,OAGH,MAFAN,GAAQ7B,EAAOkC,SAAWlC,EAAOkC,SAAWzJ,KAAKW,IAAI2I,KAAMtJ,KAAKO,QAAQa,IAAI,WAC5EmG,EAAOkC,SAAWL,EACXA,EAAMO,MAEf,SAGE,MAFAP,GAAQ7B,EAAOkC,SAAWlC,EAAOkC,SAAWzJ,KAAKW,IAAI2I,KAAMtJ,KAAKO,QAAQa,IAAI,WAC5EmG,EAAOkC,SAAWL,EACXA,IAsBX/I,EAAOiD,UAAUkG,iBAAmB,SAAUjC,EAAQwB,EAAQlG,GAC5D,GAYE+G,GAAYC,EAAYC,EAAcL,EAAUM,EAXhDC,EAAgBzC,EAAO0C,iBACvBC,EAAgBlK,KAAKoH,eAAiBpH,KAAKU,GAC3CyJ,EAAgBD,EAAIE,aACpBC,EAAgB5K,EAAGC,GAAS4K,cAC5BC,EAAgB9K,EAAGoD,GACnB2H,EAAgBL,EAAgBE,EAChCI,EAAgBD,EAAcH,EAAgBF,EAC9CO,EAAgBH,EAASD,cACzBK,EAAgBD,EAChBE,EAAgBF,EAChBG,EAA4B,WAAX9B,EAAuB,OAAS,MASnD,KANOiB,IACHa,EAAwB,WAAX9B,EAAuB,GAAK,EACzCU,EAAWzJ,KAAKmJ,UAAW5B,GAC3BA,EAAOuD,WAAahB,EAAeL,EAAS5B,MAAOhF,MAGzC,CASZ,GARKmH,GACHF,GAA8Be,EAC9Bd,EAAOD,GAAgB,EAAIL,EAASsB,GAAIjB,GAAiB,KACzDF,EAAaG,GAAQA,EAAKhH,OAAS,EAAIgH,EAAO,MAE9CH,EAAa5J,KAAKmJ,UAAW5B,EAAQsD,EAAWN,IAG5CX,GAAcW,EAAStB,GAAIpG,GAC/B,KACK,KAAM+G,EAEX,MADKI,KAAiBzC,EAAOyD,SAAWlB,EAAee,GAChDN,CAMT,IAHAV,EAAaD,EAAWU,cACxBM,GAA4Bf,EAEvBe,EAAcH,EAEjB,MAAKE,GAAgBd,EAAaY,GAC3BT,IAAiBzC,EAAOyD,SAAWlB,GACjCF,IAEJI,IAAiBzC,EAAOyD,SAAWlB,EAAee,GAChDN,EAETI,GAAgBd,EAChBU,EAAWX,EAEb,MAAO,OAcTvJ,EAAOiD,UAAU4D,WAAa,SAAUkB,EAAM6C,EAAO1D,GACnD,GAAI3G,GAAI2D,EAAKvE,KAAKO,QAAQa,IAAIgH,EAC9B,IAAM7D,EAAN,CACA,GAAc,WAAT6D,GAA8B,YAATA,EACxB,MAAO7D,GAAGF,KAAMrE,KAAKW,IAMvB,QAJAC,KACK2G,EAAOwB,SAAWnI,EAAGmI,OAASxB,EAAOwB,QACrC/I,KAAKY,GAAGsK,QAAUtK,EAAGsK,MAASlL,KAAKY,GAAGsK,OAElC9C,GACP,IAAK,SAAexH,EAAGwI,MAAQ7B,EAAO4D,QAAU,MAChD,KAAK,cACL,IAAK,WAAevK,EAAGwI,MAAQ7B,EAAO6D,UAAY,MAClD,KAAK,OAAqB7D,EAAOC,eAAiB5G,EAAGwI,MAAQ7B,EAAOK,cAGtErD,EAAGF,KAAMrE,KAAKW,IAAKsK,GAAS,KAAMrK,KAapCP,EAAOiD,UAAU+H,YAAc,SAAU/D,EAAGC,GAC1C,GAAI+D,EAMJ,OALA/D,GAAOK,gBACPL,EAAOQ,yBACA/H,MAAK0H,aACZ1H,KAAKkH,WAAW,SAAUI,EAAGC,GAEzBvH,KAAK0H,cACP1H,KAAKqH,QAASC,EAAGC,OACjBvH,MAAKuL,MAAOjE,EAAGC,KAGjBA,EAAOiE,YAAgBxL,KAAKa,UAAY,EACnC0G,EAAOwB,QAAUxB,EAAOkE,sBAAwB9L,IACnD4H,EAAOkE,oBAAsBzL,KAAK0L,eAAgBnE,EAAOwB,SAIzDxB,EAAOoE,eACPpE,EAAOkE,qBACPlE,EAAOwB,SAAW/I,KAAKY,GAAGsK,QAKhB3D,EAAOoE,cACjB3L,KAAK4L,mBAAoBtE,EAAGC,GAGlBA,EAAOsE,eACjBP,EAAS/D,EAAOkE,oBAAsBzL,KAAKiI,UAAYjI,KAAKgI,QAC5DsD,EAAOjH,KAAMrE,KAAMsH,EAAGC,EAAQA,EAAO6B,QAG3B7B,EAAOwB,SAAWxB,EAAO6B,OAAoB,cAAX9B,EAAEnE,OAIpCoE,EAAOwB,QAAUxB,EAAO6B,OAG7BpJ,KAAKa,WAAgC,IAAnBb,KAAKa,WAAmBb,KAAK0L,eAAe1L,KAAKY,GAAGsK,OAGzElL,KAAKiI,UAAWX,EAAGC,EAAQvH,KAAKY,GAAGsK,MAAO3D,EAAOkE,qBAExCzL,KAAKa,WACdb,KAAK8L,aAAcxE,EAAGC,GAGxBvH,KAAKgI,QAASV,EAAGC,EAAQA,EAAO6B,MAAO7B,EAAOkE,uBAEnClE,EAAOwB,QAAU/I,KAAKa,UAAY,GAAKb,KAAKO,QAAQa,IAAI,kBACnEpB,KAAK8L,aAAcxE,EAAGC,MAGnBvH,KAAKa,WAAa0G,EAAOiE,aAC5BxL,KAAKkH,WAAW,cAAeI,EAAGC,GAGpCA,EAAOW,UAAclI,KAAKY,GAAS,MAAIZ,KAAKY,GAAGsK,MAAQ,MAEjD3D,EAAOwB,QAAU/I,KAAKO,QAAQa,IAAI,aACtCpB,KAAK+L,MAAMzE,EAAGC,GACJA,EAAOwB,SAAWxB,EAAOC,cACnCxH,KAAKmI,UAAWZ,EAAOwB,YAIzB/I,MAAKuL,MAAOjE,EAAGC,KAWjBlH,EAAOiD,UAAUsI,mBAAqB,SAAUtE,EAAGC,GACjD,GAAI+D,GAAQlC,EAAOtJ,EAASkM,EAAaC,EAAYC,EAAWC,EAEhEC,EAAgB7E,EAAOuD,WAAavD,EAAOyD,SAC3CvB,EAAgBzJ,KAAKmJ,UAAW5B,GAChC8E,EAAgB,EAAoB9E,EAAOuD,WAAavD,EAAOyD,SAC/DsB,EAAgB,EAAoB/E,EAAOyD,SAAWzD,EAAOuD,UAGxDvD,GAAOgF,qBAEVnD,EAAQK,EAAS+C,MAAO,EAAGH,GAE3BjD,EAAQA,EAAMqD,IAAKhD,EAAS+C,MAAOF,EAAM,IACzCtM,KAAKiI,UAAWX,EAAGC,EAAQ6B,GAC3BpJ,KAAKgI,QAASV,EAAGC,EAAQA,EAAO6B,QAKhCpJ,KAAKY,GAAG8L,mBACPnF,EAAOkE,sBACP3L,EAAUyH,EAAO6B,MAAMvB,MAAO7H,KAAKY,GAAG8L,oBAAuB,GAG9D5M,EAAc,EAAoByH,EAAOuD,WAAahL,EAAUyH,EAAOyD,SAAWlL,EAClFkM,EAAclM,EAAUyH,EAAOuD,WAC/BmB,EAAc1E,EAAOuD,WAAahL,EAClCoM,EAAcpM,EAAUyH,EAAOyD,SAC/BmB,EAAc5E,EAAOyD,SAAWlL,IAEzBoM,GAAaF,IAAoBG,GAAYF,KAElD7C,EAAQ6C,EAAaxC,EAAS+C,MAAOH,EAAKvM,GAAY2J,EAAS+C,MAAO1M,EAAQ,EAAGwM,EAAI,GACjFlD,EAAMrG,OAAS,GACjB/C,KAAKiI,UAAWX,EAAGC,EAAQ6B,KAGzB+C,IAAaF,GAAkBC,IAAcF,KAEjD5C,EAAQ+C,EAAW1C,EAAS+C,MAAOH,EAAKvM,GAAY2J,EAAS+C,MAAO1M,EAAQ,EAAGwM,EAAI,GAC/ElD,EAAMrG,OAAS,GACjB/C,KAAKgI,QAASV,EAAGC,EAAQ6B,MAM7BkC,EAAS/D,EAAOkE,oBAAsBzL,KAAKiI,UAAYjI,KAAKgI,QAC5DsD,EAAOjH,KAAMrE,KAAMsH,EAAGC,EAAQA,EAAO6B,SAezC/I,EAAOiD,UAAUqJ,mBAAqB,SAAUvD,EAAOwD,EAAOrF,GAC5D,GACEsF,GAAYD,EAAQ,EACpBhF,KACA5G,EAAQhB,IAEVP,GAAG2J,GAAQzB,KAAM,SAAUE,EAAOC,GAChC,GACEgF,GAAa9L,EAAM0K,eAAgB5D,GAEnCiF,EAAoB,GAAiBD,EAAaA,EAClDE,EAAqBlF,IAASP,EAAOwB,QAAUxB,EAAOkE,sBAMpDuB,GAAqBH,GAActF,EAAOsE,eAAkBtE,EAAOoE,iBAEnEoB,IACGxF,EAAOE,iBACVG,EAAaqF,KAAMnF,GACnBP,EAAOQ,gBAAgBkF,KAAMH,IAE/B9L,EAAMH,WAAa+L,GAErBnN,EAAGqI,GAAOoF,YAAalM,EAAMT,QAAQa,IAAI,iBAAkByL,MAIxDtF,EAAOE,iBACVF,EAASsF,EAAU,WAAW,cAAkBpN,EAAGmI,GACnDL,EAAOK,aAAeL,EAAOK,aAAauF,OAAQvF,KActDvH,EAAOiD,UAAU0E,QAAU,SAAUV,EAAGC,EAAQ6B,EAAOgE,GACrDpN,KAAK2M,mBAAoBvD,EAAO,EAAG7B,GAC7B6F,GAAWpN,KAAKkH,WAAW,SAAUI,EAAGC,GAC1CvH,KAAK0H,eAAiBH,EAAOE,gBAAmBzH,KAAKqH,QAASC,EAAGC,IAavElH,EAAOiD,UAAU2E,UAAY,SAAUX,EAAGC,EAAQ6B,EAAOgE,GACvDpN,KAAK2M,mBAAoBvD,EAAO,GAAI7B,GAC9B6F,GAAWpN,KAAKkH,WAAW,WAAYI,EAAGC,GAC5CvH,KAAK0H,eAAiBH,EAAOE,gBAAmBzH,KAAKqH,QAASC,EAAGC,IAWvElH,EAAOiD,UAAUwI,aAAe,SAAUxE,EAAGC,GAC3C,GAAI8F,GAAsBjE,CACrBpJ,MAAKa,WAAgC,IAAnBb,KAAKa,YAE5BuI,EAAQpJ,KAAKmJ,UAAW5B,GAExB8F,EAAuB9F,EAAOwB,QAAUxB,EAAOkE,qBAA0C,IAAnBzL,KAAKa,UAC3Eb,KAAKiI,UAAWX,EAAGC,EAAQ6B,EAAOiE,KAUpChN,EAAOiD,UAAUgK,aAAe,SAAU/F,GAExC,MADAA,GAAOsE,eAAgB,EAChBpM,EAAG8H,EAAOwB,SAWnB1I,EAAOiD,UAAUiK,aAAe,SAAUhG,GAExC,GADAA,EAAOoE,eAAgB,EACnBpE,EAAOwB,SAAW/I,KAAKY,GAAGsK,MAAU,MAAOzL,GAAG8H,EAAOwB,OAGzD,IAAIyE,GAAMjG,EAAOkC,SAAWlC,EAAOkC,SAAWzJ,KAAKmJ,UAAW5B,GAC5DkG,EAAID,EAAI3F,MAAON,EAAOwB,QACtB2E,EAAIF,EAAI3F,MAAO7H,KAAKY,GAAGsK,OAGzByC,EAAmBD,EAAJD,EAAUD,EAAIhB,MAAOiB,EAAGC,GAAMF,EAAIhB,MAAOkB,EAAGD,EAM3D,OALAE,GAAOV,KAAYS,EAAJD,EAAUD,EAAKE,GAAeF,EAAKC,IAElDlG,EAAOkC,SAAW+D,EAClBjG,EAAOuD,WAAa4C,EACpBnG,EAAOyD,SAAWyC,EACXE,GAWTtN,EAAOiD,UAAUoI,eAAiB,SAAU3C,GAC1C,GAAIxI,GAAUP,KAAKO,QAAQa,KAE3B,OAAI3B,GAAEsJ,GAAQhG,QAAU,EACftD,EAAGsJ,GAAS6E,SAAUrN,EAAQkF,eAEhChG,EAAEoO,IAAKpO,EAAEsJ,GAAS,SAAUjB,GACjC,MAAOrI,GAAGqI,GAAO8F,SAAUrN,EAAQkF,kBAavCpF,EAAOiD,UAAUyI,MAAQ,SAAUzE,EAAGC,EAAQ6F,IACvCA,GAAUpN,KAAKY,GAAGsK,OACrBlL,KAAKkH,WAAW,YAAaI,EAAGC,GAE9BvH,KAAKY,GAAGsK,QACVzL,EAAGO,KAAKY,GAAGsK,OAAQ4C,YAAa9N,KAAKO,QAAQa,IAAI,qBAC1CpB,MAAKY,GAAGsK,QAWnB7K,EAAOiD,UAAU6E,UAAY,SAAUY,GACrC,MAAKA,IACD/I,KAAKY,GAAGsK,OACVzL,EAAEO,KAAKY,GAAGsK,OAAO4C,YAAa9N,KAAKO,QAAQa,IAAI,eAEjDpB,KAAKY,GAAGsK,MAAQnC,EAChBtJ,EAAGO,KAAKY,GAAGsK,OAAQlE,SAAUhH,KAAKO,QAAQa,IAAI,eACvCpB,KAAKY,GAAGsK,OANf,QAiBF7K,EAAOiD,UAAUiI,MAAQ,SAAUjE,EAAGC,GACpCvH,KAAKkH,WAAW,OAAQI,EAAGC,GACvBvH,KAAK0H,cAAiB1H,KAAKqH,QAASC,EAAGC,IAW7ClH,EAAOiD,UAAUyK,aAAe,SAAU5G,GACxC,GAAI6G,EACJ,OAAK7G,KAAaA,EAASkC,QAAUlC,EAAS8G,WAC5C9G,EAAWA,EAASkC,OAASlC,EAAW1H,EAAG0H,GAC3C6G,EAAM7G,EAAS1C,OAAQzE,KAAKmB,gBACrB6M,EAAIjL,OAAS,EAAIiL,EAAM,OAEhB,GAYlB3N,EAAOiD,UAAU4K,iBAAmB,SAAU/G,GAC5C,GAAI6G,EACJ,OAAK7G,IAAgC,gBAAbA,IACtB6G,EAAMhO,KAAKW,IACR2I,KAAMnC,GACN1C,OAAQzE,KAAKmB,gBACP6M,EAAI3E,QAAU2E,EAAIjL,OAAS,EAAMiL,EAAM,OAElC,GAgBlB3N,EAAOiD,UAAUkF,YAAc,SAAUlB,GAEvC,GAAMtH,KAAKO,QAAQa,IAAI,eAClBpB,KAAKO,QAAQa,IAAI,kBAAyC,UAArBkG,EAAEyB,OAAOoF,SAA4C,aAArB7G,EAAEyB,OAAOoF,SAAnF,CACA,GAAgCpF,GAAQqF,EAAavD,EAAWwD,EAA5DC,EAAMhH,EAAEiH,MAAOhH,IAEnB,IAAe,UAAXD,EAAEnE,KAKJ,YAJKmL,IAAQjO,EAAO8F,QAAQG,cACnBtG,MAAKwO,uBACLxO,MAAKe,UAAU0N,OAI1B,IAAKH,IAAQjO,EAAO8F,QAAQQ,GAAK3G,KAAK0O,SAASpH,IAAMtH,KAAKO,QAAQa,IAAI,SACpE2H,EAAS/I,KAAKmJ,UAAW5B,GACzB6G,GAAc,MAId,QAASE,GACT,IAAKjO,GAAO8F,QAAQC,KAClByE,EAAY,OACZ9B,EAAY/I,KAAK2O,gBAAiB,OAAQpH,EAC1C,MACF,KAAKlH,GAAO8F,QAAQE,GAClBwE,EAAY,OACZ9B,EAAY/I,KAAK2O,gBAAiB,OAAQpH,EAC1C,MACF,KAAKlH,GAAO8F,QAAQK,KAClBqE,EAAY,OACZ9B,EAAY/I,KAAKmJ,UAAW5B,EAAQ,QACpC,MACF,KAAKlH,GAAO8F,QAAQI,IAClBsE,EAAY,OACZ9B,EAAY/I,KAAKmJ,UAAW5B,EAAQ,OACpC,MACF,KAAKlH,GAAO8F,QAAQM,UACpB,IAAKpG,GAAO8F,QAAQO,QAClB,GAAIkI,GAASN,IAAQjO,EAAO8F,QAAQM,SACpCoE,GAAa+D,EAAS,OAAS,OAC/BP,EAAaO,EAAS,WAAa,SACnCrH,EAAO0C,iBAAmBjK,KAAKO,QAAQa,IAAI,UAAYkG,EAAEuH,WAAaT,EACtErF,EAAS/I,KAAK2O,gBAAiBN,EAAM9G,EACrC,MACF,KAAKlH,GAAO8F,QAAQS,MAClBmC,EAAStJ,EAAGO,KAAKY,GAAGsK,MACpB,MACF,KAAK7K,GAAO8F,QAAQU,MACZ7G,KAAKO,QAAQa,IAAI,WAAa2H,EAAStJ,EAAGO,KAAKY,GAAGsK,QAKvDnC,GAAUA,EAAOhG,OAAS,GAC7BuE,EAAEwH,iBAEFvH,EAAOwB,OAASA,EAAO,GACvBxB,EAAO6B,MAAQL,EAG2B,WAArC/I,KAAKO,QAAQa,IAAI,iBAElBkN,IAAQjO,EAAO8F,QAAQS,OACrB0H,IAAQjO,EAAO8F,QAAQU,QAAU7G,KAAKO,QAAQa,IAAI,gBAE7CmG,GAAO6B,MAEXpJ,KAAKO,QAAQa,IAAI,WAAamG,EAAOsE,eAAgB,SACnD7L,MAAKY,GAAG8L,kBAGL1M,KAAKY,GAAGsK,OAASlL,KAAKO,QAAQa,IAAI,UAAYkG,EAAEuH,WAAaT,GAIrEE,IAAQjO,EAAO8F,QAAQI,KAAW+H,IAAQjO,EAAO8F,QAAQK,MACzD8H,IAAQjO,EAAO8F,QAAQO,SAAW4H,IAAQjO,EAAO8F,QAAQM,UAEzDzG,KAAK+O,eAAgBxH,GAErBvH,KAAKgP,eAAgBzH,EAAQ+G,EAAKzD,EAAW9B,GAIzC/I,KAAKY,GAAG8L,kBAAoBnF,EAAOwB,SAAW/I,KAAKY,GAAGsK,QAC1DlL,KAAKY,GAAG8L,iBAAmB1M,KAAKY,GAAGsK,MACnC3D,EAAOgF,qBAAsB,GAIzBvM,KAAKwO,mBAAqBxO,KAAKwO,iBAAmB,UAClDxO,KAAKe,UAAU0N,QAAWzO,KAAKe,UAAU0N,MAASH,UAGjDtO,MAAKY,GAAG8L,iBAEjB1M,KAAKqL,YAAa/D,EAAGC,GACrBvH,KAAKiP,WAEL1H,EAAOQ,mBACP/H,KAAKkH,WAAW,SAAUI,EAAGC,GAC7BvH,KAAKkH,WAAW,OAAQI,EAAGC,MAW/BlH,EAAOiD,UAAUyL,eAAiB,SAAUxH,GAC1C,GACE2H,GAAkB,SAAW3H,EAAO2H,gBAAkBlP,KAAK0L,eAAgB1L,KAAKY,GAAGsK,OAAU3D,EAAO2H,gBACpGC,EAAmB5H,EAAOkE,oBAAsBzL,KAAK0L,eAAgBnE,EAAOwB,OAExEmG,IAAoBC,GAKxB5H,EAAO6B,MAAQpJ,KAAKuN,aAAchG,GAE7B4H,IACH5H,EAAO6B,MAAQ7B,EAAOuD,WAAavD,EAAOyD,SAC1CzD,EAAO6B,MAAMoD,MAAM,EAAGjF,EAAO6B,MAAMrG,OAAO,GACrCwE,EAAO6B,MAAMoD,MAAM,MAR1BjF,EAAOwB,OAASxB,EAAO6B,MAAQpJ,KAAKY,GAAGsK,MACvC3D,EAAOsE,eAAgB,IAwB3BxL,EAAOiD,UAAU0L,eAAiB,SAAUzH,EAAQ+G,EAAKzD,EAAW9B,GAClE,GAKEqG,GAJAF,EAAwB,SAAW3H,EAAO2H,gBAAkBlP,KAAK0L,eAAgB1L,KAAKY,GAAGsK,OAAU3D,EAAO2H,gBAC1GC,EAAwBnP,KAAK0L,eAAgBnE,EAAOwB,QACpDsG,EAAwBrP,KAAKmJ,UAAW5B,EAAQsD,EAAW9B,GAC3DuG,EAAwBtP,KAAK0L,eAAgB2D,EAQ/C,IAJKrP,KAAKe,UAAU0N,OAASzO,KAAKe,UAAU0N,QAAUH,IACpDtO,KAAKe,UAAU0N,MAAQzO,KAAKwO,iBAAmB,MAG5CxO,KAAKe,UAAU0N,OAAmC,WAA1BzO,KAAKwO,kBAAiCW,EAAmB,CAKpF,KAAOnP,KAAK0L,eAAenE,EAAO6B,QAAU7B,EAAO6B,MAAMrG,OAAS,GAChEqM,EAAW7H,EAAO6B,MAClB7B,EAAO6B,MAAQpJ,KAAKmJ,UAAW5B,EAAQsD,EAAWtD,EAAO6B,MAI3D7B,GAAOwB,OAASxB,EAAO6B,MAAQ7B,EAAO6B,MAAQgG,MAEpCD,IAAoBD,IAAoBI,GAIlDtP,KAAKe,UAAU0N,MAAQzO,KAAKwO,iBAAmB,KAC/CjH,EAAO6B,MAAQpJ,KAAKY,GAAGsK,OAGbgE,GAAmBC,GAC7B5H,EAAO6B,MAAQpJ,KAAKY,GAAGsK,MAGjBlL,KAAKwO,mBAAqBxO,KAAKwO,iBAAmB,aAG7CU,IAEX3H,EAAOwB,OAASxB,EAAO6B,MAAQpJ,KAAKY,GAAGsK,MAEzC3D,GAAOsE,eAAgB,GAczBxL,EAAOiD,UAAUqL,gBAAkB,SAAU9D,EAAWtD,GACtD,GAAIgI,GAAuB,SAAd1E,GAAsC,aAAdA,EAA6B,QAAU,OAG1EmD,EAAQhO,KAAKY,GAAS,MAAIZ,KAAKmJ,UAAW5B,EAAQsD,EAAW7K,KAAKY,GAAGsK,OAAUlL,KAAKmJ,UAAW5B,EAAQgI,EAMzG,OAHc,QAARvB,GAA+B,IAAfA,EAAIjL,SAAiB/C,KAAKO,QAAQa,IAAI,UAC1D4M,EAAMhO,KAAKmJ,UAAW5B,EAAQgI,IAEzBvB,GAWT3N,EAAOiD,UAAUkM,kBAAoB,SAAUtF,GAC7C,GACEuF,GAAgBhQ,EAAGyK,GACnBwF,EAAgBxF,IAAQxK,EACxByK,EAAgBuF,EAAWD,EAAKnF,cAAgBJ,EAAIE,aACpDuF,EAAgBF,EAAKG,YACrBC,EAAgBH,EAAW,EAAID,EAAKK,SAASzD,IAE7C0D,EAAgBtQ,EAAGO,KAAKY,GAAGsK,OAC3BR,EAAgBqF,EAAMzF,cACtB0F,EAAgBN,EAAWK,EAAMD,SAASzD,IAAQ0D,EAAMD,SAASzD,IAAMwD,EAAaF,CAEpEA,GAAbK,EAEHP,EAAKG,UAAWI,GAELA,EAAatF,EAAeiF,EAAexF,GAGtDsF,EAAKG,UAAWI,EAAatF,EAAaP,IAW9C9J,EAAOiD,UAAU2M,SAAW,SAAU3I,GACpC,MAAOA,GAAEuH,UAAavH,EAAEuH,UAAYvH,EAAE4I,SAAa5I,EAAEuH,UAAYvH,EAAE6I,SAUrE9P,EAAOiD,UAAUoL,SAAW,SAAUpH,GACpC,MAAOA,GAAE4I,SAAW5I,EAAE6I,SAgBxB9P,EAAOiD,UAAUgF,cAAgB,SAAUhB,GACzC,GAMAyB,GALAxI,EAAUP,KAAKO,QAAQa,MACvB+B,EAAUmE,EAAEnE,KACZiN,EAAUpQ,KAAK0O,SAASpH,GACxB+I,EAAUrQ,KAAKiQ,SAAS3I,GACxBC,IAIA,IAA0B,YAAtBhH,EAAQqE,UAAyB,CACnC,GAAa,YAATzB,EAEG,MAAc,cAATA,IAAyB4F,EAAS/I,KAAK6I,WAAWvB,IAC5D,OACO,MAHPyB,GAAS/I,KAAK6I,WAAWvB,OAMtB,CAAA,GAAa,UAATnE,IAAqBnD,KAAKsQ,iBACnC,MAEK,IAAa,cAATnN,GAAiC,UAATA,EAW1B,MALP,IALA4F,EAAS/I,KAAK6I,WAAWvB,GAKZ,cAATnE,GAAwB4F,KAAaxI,EAAQoE,QAAUyL,IAASC,GAAkC,aAAtB9P,EAAQqE,WAEtF,YADA5E,KAAKsQ,iBAAmBvH,SAGnB/I,MAAKsQ,iBAGd/I,EAAOwB,OAASA,EACZxI,EAAQoE,OAAS4C,EAAOwB,SAGrBsH,GAAWrQ,KAAKY,GAAGsK,MACtB3D,EAAO6B,MAAQpJ,KAAKuN,aAAchG,IAGxB6I,GAAiC,WAAtB7P,EAAQqE,aAC7B2C,EAAO6B,MAAQpJ,KAAKsN,aAAc/F,KAIjCA,EAAOwB,SAAWxB,EAAO6B,QAAU7B,EAAO6B,MAAQ3J,EAAG8H,EAAOwB,eAC1D/I,MAAKY,GAAG8L,iBACf1M,KAAKqL,YAAa/D,EAAGC,IAUvBlH,EAAOiD,UAAUqF,kBAAoB,SAAUrB,GAC7C,IAAKtH,KAAKuQ,yBAAV,CACA,GAAiBxH,GAAbxB,IAEJwB,GAAS/I,KAAK6I,WAAYvB,GACrByB,SACI/I,MAAKY,GAAG8L,iBACf1M,KAAKwQ,YAAa,EACbzH,IAAW/I,KAAKY,GAAGsK,QACtB3D,EAAOwB,OAASA,EAChB/I,KAAKqL,YAAa/D,EAAGC,KAEbvH,KAAKwQ,aACfxQ,KAAKwQ,YAAa,EAClBxQ,KAAKqL,YAAa/D,EAAGC,MAYzBlH,EAAOiD,UAAUmN,kBAAoB,WACnC,GAAIzP,GAAQhB,IACZA,MAAKuQ,0BAA2B,EAE3BvQ,KAAK0Q,qBACRpO,aAActC,KAAK0Q,0BACZ1Q,MAAK0Q,oBAGd1Q,KAAK0Q,mBAAqBlO,WAAY,iBAC7BxB,GAAMuP,+BACNvP,GAAM0P,oBACZ,MAiBLrQ,EAAOsQ,kBAAoB,SAAUrF,GACnC,GAEEsF,GAAcjP,EADdX,EAAQX,EAAOyG,cAAe9G,KAGhC,IAAI,OAASgB,GAAS,SAAWA,EAC/B,KAAM,IAAI+C,OAAO,WAAa/D,KAAK,GAAK,kBAAoBK,EAAOI,WAOrE,IAJKO,EAAMsK,IAAW7L,EAAEoR,WAAW7P,EAAMsK,MACvCsF,EAAe5P,EAAMsK,IAGlBsF,GAAgBnR,EAAEoR,WAAYD,IAAuC,MAArBtF,EAAOwF,OAAO,GAGjE,MAFAnP,GAAOmB,MAAMQ,UAAUkJ,MAAMnI,KAAMhC,WACnCV,EAAK8M,QACEmC,EAAa1O,MAAOlB,EAAOW,EAEpC,MAAM,IAAIoC,OAAO,WAAc1D,EAAOI,WAAa,oBAAwB6K,EAAS,MAQtFjL,EAAOiD,UAAUyN,UAAY,WAC3B,MAAO/Q,MAAKc,WAWdT,EAAOiD,UAAUG,OAAS,SAAUA,EAAQvC,GAC1C,GAAIS,GAAOU,UAAUU,MAGrB,IAAIpB,EAAO,GAAuB,gBAAX8B,GAAsB,CAE3C,GAAI9B,EAAO,EAAI,CACb,GAAI2C,KAGJ,OAFAA,GAAIb,GAAUvC,EACdlB,KAAKO,QAAQH,IAAKkE,GACXtE,KAAKW,IAGd,MAAOX,MAAKO,QAAQa,IAAKqC,GAG3B,GAAI9B,EAAO,GAAKlC,EAAEuR,cAAevN,GAE/B,MADAzD,MAAKO,QAAQH,IAAKqD,GACXzD,KAAKW,GAGd,IAAc,IAATgB,EACH,MAAO3B,MAAKO,QAAQa,KAEpB,MAAM,IAAI2C,OAAM,mGASpB1D,EAAOiD,UAAU4C,QAAU,WACzBlG,KAAKkH,WAAW,WAChBlH,KAAK4I,gBACA5I,KAAK0Q,oBAAuBpO,aAAatC,KAAK0Q,oBAC/C1Q,KAAKY,GAAGsK,QACVzL,EAAEO,KAAKY,GAAGsK,OAAO4C,YAAa9N,KAAKO,QAAQa,IAAI,qBACxCpB,MAAKY,GAAGsK,OAEblL,KAAKa,UAAY,GACnBb,KAAKiR,cAAcnD,YAAa9N,KAAKO,QAAQa,IAAI,kBAEnDpB,KAAKW,IAAImN,YAAa9N,KAAKO,QAAQa,IAAI,kBACvCpB,KAAKW,IAAImN,YAAa9N,KAAKO,QAAQa,IAAI,cACvCpB,KAAKO,QAAQiE,YACNxE,MAAKO,cACLP,MAAKoH,oBACLpH,MAAKY,GAAG8L,iBACf1M,KAAKW,IAAIuQ,WAAY,UAAY7Q,EAAOI,YACxCT,KAAKW,IAAM,MAUbN,EAAOiD,UAAUwC,OAAS,SAAUqB,GAClC,GAAI2B,EAIJ,IAFAA,EAAQ9I,KAAK+N,aAAc5G,GACtB2B,KAAU,IAASA,EAAQ9I,KAAKkO,iBAAkB/G,IAClD2B,KAAU,EACb,KAAM,IAAI/E,OAAO,6DASnB,OAPK+E,WACI9I,MAAKY,GAAG8L,iBACf1M,KAAKqL,YAAa,MAChBjC,MAAUN,EAAe,SAAIA,EAAQrJ,EAAGqJ,GACxCC,OAAQD,EAAM,IAAMA,KAGjB9I,KAAKW,KAQdN,EAAOiD,UAAU6N,KAAO,WAEtB,MADAnR,MAAKqL,YAAa,MAAQtC,OAAQ,OAC3B/I,KAAKW,KAUdN,EAAOiD,UAAU2N,YAAc,SAAUG,GACvC,GAAI5D,GACJpE,EAAQpJ,KAAKmJ,cAAc1E,OAAQ,IAAMzE,KAAKO,QAAQa,IAAI,iBAE1D,IAAIgQ,EAAS,CACX5D,IACA,KAAK,GAAIxK,GAAI,EAAGA,EAAIoG,EAAMrG,OAAQC,IAAOwK,EAAIP,KAAK7D,EAAMpG,GAAGqO,IAAM,KACjE,OAAQ7D,IAAOA,EAAIzK,OAAS,EAAKyK,EAAM,KAEzC,MAAOpE,IAST/I,EAAOiD,UAAUgO,cAAgB,WAC/B,MAAOtR,MAAKiR,aAAa,IAS3B5Q,EAAOiD,UAAU4H,MAAQ,SAAU/D,GACjC,GAAI2B,EAEJ,IAAKzG,UAAUU,OAAS,EAAI,CAE1B,GADA+F,GAASA,EAAQ9I,KAAK+N,aAAc5G,OAAgB,EAAQnH,KAAKkO,iBAAkB/G,GAAa2B,EAC3FA,GAASA,EAAMO,OAClBrJ,KAAKmI,UAAWW,EAAM,QAEjB,IAAKA,KAAU,EACpB,KAAM,IAAI/E,OAAO,gFAEnB,OAAO/D,MAAKW,IAGd,MAAIX,MAAKY,GAAGsK,MAAgBlL,KAAKY,GAAGsK,MAAuB,MAQ7D7K,EAAOiD,UAAU2L,OAAS,WACxBjP,KAAKyQ,oBACDzQ,KAAKY,GAAGsK,QACLlL,KAAKoH,eAAkBpH,KAAKwP,kBAAmBxP,KAAKoH,eACzDpH,KAAKwP,kBAAmB9P,KAS5BW,EAAOiD,UAAUiO,OAAS,WAGxB,MAFAvR,MAAKc,WAAY,EACjBd,KAAKW,IAAImN,YAAa9N,KAAKO,QAAQa,IAAI,kBAChCpB,KAAKW,KAQdN,EAAOiD,UAAUkO,QAAU,WAIzB,MAHAxR,MAAKc,WAAY,EACjBd,KAAKwQ,YAAa,EAClBxQ,KAAKW,IAAIqG,SAAUhH,KAAKO,QAAQa,IAAI,kBAC7BpB,KAAKW,KAUdN,EAAOiD,UAAUmO,OAAS,WAExB,MADAzR,MAAK0H,cAAe,EACb1H,KAAKW,KAQdN,EAAOiD,UAAUoO,QAAU,WACzB,GAAIxG,GAAQlL,KAAKY,GAAGsK,KAGpB,OAFKA,KAAUzL,EAAEyL,GAAOjC,GAAG,mBAAuBjJ,MAAKY,GAAGsK,MAC1DlL,KAAKa,UAAcb,KAAKiR,cAAgBlO,OACjC/C,KAAKW,KAUdlB,EAAE8J,GAAGlJ,EAAOI,YAAc,SAAUF,GAClC,MAAIA,IAAWA,EAAQuQ,OACdzQ,EAAOsQ,kBAAkBzO,MAAOlC,KAAMqC,WAExCrC,KAAK2H,KAAM,SAAS2G,EAAKzL,GACxBxC,EAAOyG,cAAcjE,IAAU,GAAIxC,GAAQwC,EAAMtC,OAI3DoR,OAAQjS"}