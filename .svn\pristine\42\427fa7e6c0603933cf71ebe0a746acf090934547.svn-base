define(function (require) { // jshint ignore:line
    'use strict';

    var $ = require('jquery');
    var AbstractCollection = require('../../collections/Abstract');
    var UiQuestionsCollection = require('./Questions');
    var UITabModel = require('../Tab');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.Ui.Collections.Tabs
     * @extends App.Models.Collections.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} tabs
     */
    var UITabsCollection = function (tabs) {
        bindAll(this,
            '_getQuestions'
        );

        AbstractCollection.call(this, tabs);
    };

    var proto = inherits(UITabsCollection, AbstractCollection);

    /**
     * @property itemClass
     * @type {App.Models.Ui.Tab}
     */
    proto.itemClass = UITabModel;

    /**
     * @method getQuestions
     * @return {App.Models.Ui.Collections.Questions}
     */
    proto.getQuestions = function() {
        var questions = this._items
            .reduce(this._getQuestions, []);

        return new UiQuestionsCollection(questions);
    };

    /**
     * @method _getQuestions
     * @return {Array.<UiQuestionModel>}
     */
    proto._getQuestions = function(list, tab) {
        return list.concat(tab.getQuestions()._items);
    };

    /**
     * Recursively sets product info on all steps.
     *
     * @method setInfo
     * @param {Object} info
     * @param {App.Models.FXG} fxg
     * @param {App.Models.CustomerProfile} customer
     * @chainable
     */
    proto.setInfo = function(info, fxg, customer) {
        this.each(this._setInfo.bind(this, info, fxg, customer));

        return this;
    };

    /**
     * @method _setInfo
     * @param {Object} info
     * @param {App.Models.FXG} fxg
     * @param {App.Models.CustomerProfile} customer
     * @param {App.Models.Ui.Tab} tab
     * @callback
     */
    proto._setInfo = function(info, fxg, customer, tab) {
        tab.setInfo(info, fxg, customer);
    };

    /**
     * Returns an object containing all selections made by
     * the user for all contained questions
     *
     * @method getValues
     * @returns {Object}
     */
    proto.getValues = function() {
        var results = {blocks: [], values: {}, surcharges: []};
        this.each(this._getValues.bind(this, results));
        return results;
    };

    /**
     * Appends a single question's value to an existing object
     *
     * @method _getValue
     * @param {object} results result set to append to
     * @param {App.Models.Ui.Tab} tab
     * @private
     */
    proto._getValues = function(results, tab) {
        var values = tab.getValues();
        $.extend(results.values, values.values);
        results.blocks = $.merge(results.blocks, values.blocks);
        results.surcharges = $.merge(results.surcharges, values.surcharges);
    };

    /**
     * @method isValid
     * @return {Boolean}
     */
    proto.isValid = function () {
        var tabs = this._items;
        var length = tabs.length;
        var i = 0;

        for (; i < length; i++) {
            if (tabs[i].isValid() !== true) {
                return false;
            }
        }

        return true;
    };

    return UITabsCollection;
});
