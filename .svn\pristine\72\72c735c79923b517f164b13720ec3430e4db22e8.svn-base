define(function(require) {
    'use strict';

    var AbstractQuestionController = require('./Abstract');
    var DomEvents = require('../../constants/DomEvents');
    var Content = require('i18n!../../constants/nls/en-us/Content');
    var EventController = require('../Event');
    var ProductEvents = require('../../constants/ProductEvents');
    var Classes = require('../../constants/Classes');
    var Settings = require('../../constants/Settings');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var currency = require('util/currencyFormat');
    var Query = require('models/Query');
    var Surcharge = require('util/Surcharge');
    var ConfigurationProvider = require('../../providers/Configuration');
    var PriceProvider = require('../../providers/Price');
    var Helper = require('../../util/helper');
    var SessionStorage = require('../../providers/SessionStorage');
    var ENVELOPE_HELP_FLAG = false;
    var DEFAULT_HELP_FLAG = false;
    var RETURN_HELP_FLAG = false;
    var FOLDING_HELP_FLAG = false;

    /**
     * @class App.Controllers.Question.Details
     * @extends App.Controllers.Question.Abstract
     *
     * @constructor
     * @param {Object} config
     * @param {jQuery} config.view
     * @param {Models.Ui.Question} config.model
     */
    function DetailsQuestionController(config) {
        bindAll(this,
            'onProductChange'
        );
        AbstractQuestionController.call(this, config);
        this.logoSurchargeArr = [];
    }

    var query = Query.getInstance();

    var proto = inherits(DetailsQuestionController, AbstractQuestionController);

    /**
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = require('hbs!templates/question/details');

    proto.product = null;

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        EventController.on(ProductEvents.CHANGE, this.onProductChange);
        this.$view.on(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick);
        return this;
    };

    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        EventController.off(ProductEvents.CHANGE, this.onProductChange);
        this.$view.off(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick);
        return this;
    };

    proto.onButtonClick = function(e) {
        if ($(e.currentTarget).attr('id') == 'envelope_helpReview') {
            popupProcess(e.currentTarget, '#CVCloseIcon.close.tooltip-close', '#envelope_popup_model',e,ENVELOPE_HELP_FLAG);
        } else if ($(e.currentTarget).attr('id') == 'foldingHelpReview') {
            popupProcess(e.currentTarget, '#foldingCloseIcon.close.tooltip-close', '#foldingPopup',e,FOLDING_HELP_FLAG);
        } else if ($(e.currentTarget).attr('id') == 'returnHelpReview') {
            popupProcess(e.currentTarget, '#foldingCloseIcon.close.tooltip-close', '#returnPopup',e,RETURN_HELP_FLAG);
        } else {
            popupProcess(e.currentTarget, '#CVCloseIcon.close.tooltip-close', '#popup_model',e,DEFAULT_HELP_FLAG);
        }
    };

    function popupProcess(id, closeId, popupModel,event,cv_flag){
        if (Helper.isSameTooltipClick(event)) {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
        } else {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
            $(id).popover({
                html: true,
                trigger: "manual",
                content: function () {
                    return $(popupModel).html();
                }
            });
            if (!cv_flag) {
                $(id).popover('show');
                if ($(id).attr('id') === "personalizationReview") {
                    var popup_style = $('#personalization_link').attr('style');
                    $('#personalization_link').attr('style', popup_style + "margin-top: 7px !important; margin-left: -170px !important");
                }
            } else {
                $(id).popover('hide');
            }
            cv_flag = !cv_flag;

            $(closeId).on('click', function (e) {
                cv_flag = false;    
                $(id).popover('hide');
            });
        }
    }

    /**
     * @method update
     * @param {App.Models.Product} product
     * @chainable
     */
    proto.update = function(product) {
        this.product = product;
        //console.log('Details.update.product', product);
        var i = 0;
        var details = [];
        var toolTips = [];
        var surcharge;

        var part = product.getProductDescription();
        var parts = product.questions.getById('productId');
        var quantity = product.getQuantityValue() || query.qty || query.quantity;

        //var surcharges = product.steps.getValues().surcharges;
        var surcharges = this.addSurcharges();
        
        if (surcharges.length) {
            for (i=0; i<surcharges.length; i++) {
                if (surcharges[i]) {
                    surcharge = surcharges[i];
                    surcharge.isEnvUpgradeExist = false;
                    surcharge.isEnvImprintExist = false;
                    surcharge.isPersonalizationExist = false;
                    surcharge.isFoldingExist = false;
                    surcharge.isInkcolorExist = false;
                    /** Add the id of envelope tile to add envelope upgrade option tooltip in surcharge summary */
                    let surchargeArray = ['ENVPS', 'ENVLND', 'ENVSS', 'SHIMKS', 'ENVKS', 'ENVLNR'];
                    if (surchargeArray.includes(surcharge.id) && product.steps.getValues().values.envelope) {
                        var swatchSel = product.steps.getValues().values.envelope.description;
                        surcharge.isEnvUpgradeExist = true;
                        surcharge.desc = Content.get('ENVPS');
                        surcharge.swatchSel = swatchSel;
                    } else if ((surcharge.id === 'ENVIM' || surcharge.id === 'EDENVIM') && product.steps.getValues().values.ENVELOPE_RETURN) {
                        surcharge.isEnvImprintExist = true;
                        surcharge.desc = Content.get('ENVIM');
                    } else if (surcharge.id === '2701' && product.steps.getValues().values.inkColor1) {
                        var inkSel = product.steps.getValues().values.inkColor1.description;
                        surcharge.isInkcolorExist = true;
                        surcharge.desc = Content.get(surcharge.desc);
                        surcharge.inkColor = inkSel;
                    } else if (surcharge.id === 'LAYCHG') {
                        surcharge.isPersonalizationExist = true;
                        if (product.steps.getValues().values.verse && product.steps.getValues().values.verse.value == 'Custom Verse') { surcharge.customVerse = 'Custom Verse'; }
                        if (product.steps.getValues().values.logo && product.steps.getValues().values.logo.value == 'CUSTOM') { surcharge.customLogo = 'Custom Logo'; }
                        if (product.steps.getValues().values.signatures && product.steps.getValues().values.signatures.value == 'SIGN') { surcharge.signature = 'Signature(s)'; }
                        surcharge.desc = Content.get(surcharge.desc);
                    } else if (surcharge.id === 'FOLD' || surcharge.id === 'EDFOLD') {
                        surcharge.desc = Content.get('FOLD');
                        surcharge.isFoldingExist = true;
                    } else {
                        surcharge.desc = Content.get(surcharge.desc);
                    }
                    surcharge.baseAppUrl = baseAppUrl;
                    surcharge.host_url = host_url;
                    surcharge.priceFormatted = parseFloat(surcharge.priceFormatted.replace(/,/g, "")).toFixed(2).toString();
                    surcharge.amount = currency(surcharge.price, 0);
                }
            }
        }

        if(this.getQuestionModel('quantity') == null){
            if (quantity) {
                details.push({
                    desc: Content.get('Quantity'),
                    value: quantity
                });
            }
        }

        /*if (part && parts && parts.options.length() > 1) {
            details.push({
                desc: Content.get('Part'),
                value: part
            });
        }*/

        if (surcharges.length) {
            for (i=0; i<surcharges.length; i++) {
                if (surcharges[i].price === '0.00') {
                    surcharges[i].price = '0';
                }
            }
        }

        //console.log('Details.update.surcharges', surcharges);
        product.surcharges = surcharges;
        this.product.surcharges = surcharges;
        EventController.emit(ProductEvents.SURCHARGE_CHANGE, product);

        this.$view
            .html(this.template({
                baseAppUrl: baseAppUrl,
                details: details,
                surcharges: surcharges,
                host_url: host_url
            }));

        return this;
    };

    /**
     * Iterates through values to determine if the selection
     * results in a surcharge and, if so, adds it.
     *
     * @method addSurcharges
     * @param {object} result
     * @private
     */
    proto.addSurcharges = function() {
        var surcharges = [];
        var surcharge = null;

        //apply matrix charge  (option match)
        surcharge = this.getChargeByMatch('matrix1');
        if (surcharge !== null) { surcharges.push(surcharge); }

        //apply ink 1 charge  (option match)
        surcharge = this.getChargeByMatch('inkColor1');
        if (surcharge !== null) { surcharges.push(surcharge); }

        //apply ink 2 charge  (option match)
        surcharge = this.getChargeByMatch('inkColor2');
        if (surcharge !== null) { surcharges.push(surcharge); }

        if (Settings.ULTIMATE_PERSONALIZATION === 'true' && Surcharge.get(Settings.PERSONALIZATION_SURCHARGEID)) {
            if (this.getQuestionValue('signatures') === 'SIGN' ||
                this.getQuestionValue('logo') === 'CUSTOM' ||
                this.getQuestionValue('verse') === 'Custom Verse') {
                surcharge = this.lookupSurcharge('LAYCHG');
                if (surcharge !== null) { surcharges.push(surcharge); }
            }
        } else {
            //apply signature charge  (option match)
            surcharge = this.getChargeByMatch('signatures');
            if (surcharge !== null) { surcharges.push(surcharge); }

            //apply custom logo charge  (option match)
            surcharge = this.getChargeByMatch('logo');
            if (surcharge !== null) { 
                surcharges.push(surcharge); 
                // LogoMxSurcharge - if logomix selected, add surcharge obj in temporary array "this.logoSurchargeArr"
                if( this.isLogoMxSelected() && !this.logoSurchargeArr.length ) {
                    this.logoSurchargeArr.push(surcharge); 
                }
            }

            //apply custom verse charge  (custom)
            surcharge = this.getCustomVerseCharge('verse');
            if (surcharge !== null) { surcharges.push(surcharge); }
        }

        //apply folding charge  (option match)
        surcharge = this.getChargeByMatch('folding');
        if (surcharge !== null) { surcharges.push(surcharge); }

        //apply envelope imprint charge  (custom)
        surcharge = this.getLocationUsageCharge('layout');
        if (surcharge !== null) { surcharges.push(surcharge); }

        //apply paymentterms charge (custom)
        surcharge = this.getLocationUsagePaymentSurcharge();
        if (surcharge !== null) { surcharges.push(surcharge); }

        if(this.product.locationSurcharge.applicable) {
            surcharge = this.getLocationUsageSurcharge();
            if (surcharge !== null) { surcharges.push(surcharge); }
        }

        //apply envelope upgrade charge  (option match)
        surcharge = this.getChargeByMatch('envelope');
        if (surcharge !== null) { surcharges.push(surcharge); }

        //ECHKSD adapter:  inkColor1 question is part of the product xml but not part of the UI/UX steps.  If charge is
        // present on inkColor1, we should auto apply it.  Counter-test: Product 59000HZ has inkColor1 and has the step.
        surcharge = this.lookupSurcharge(Settings.EZ_SHIELD_INCLUDED_SURCHARGEID);
        if (surcharge !== null && !Surcharge.isChargeApplied(surcharges, Settings.EZ_SHIELD_INCLUDED_SURCHARGEID)) { surcharges.push(surcharge); }

        //console.log('Details.addSurcharges.surcharges', surcharges);

        // LogoMxSurcharge - Add logo surcharge obj from temp array "this.logoSurchargeArr" if not present in array "surcharges"
        if( this.logoSurchargeArr.length && this.isLogoMxSelected() ) {
            var addObj = true;
            if(surcharges.length) {
                surcharges.forEach( function(element) {
                    if (element.id === 'LOGOMX') {
                        addObj = false;
                    }
                });
            }
            if(!surcharges.length || addObj) {
                surcharges.push(this.logoSurchargeArr[0])
            }
        }

        return surcharges;
    };

    /**
     * @method getQuestionsValue
     * @return {string}
     * product.questions._items[].options_items[].surcharge.id
     */
    proto.getQuestionValue = function(questionId) {
        var val = null;
        var questionModel = this.getQuestionModel(questionId);
        if (questionModel !== null) {
            val = questionModel.value;
        }
        return val;
    };

    /**
     * @method getChargeByMatch
     * @return {surcharge}
     * product.questions._items[].options_items[].surcharge.id
     */
    proto.getChargeByMatch = function(questionId) {
        var surcharge = null;
        var questionModel = this.getQuestionModel(questionId);
        if (questionModel !== null) {
            var optionsObj =  questionId === "logo" ? 'logoButtonOptions' : 'options';
            var options = [].concat(questionModel[optionsObj]._items);
            var selectedValue = questionModel.value;
            if (selectedValue == 'SIGN') { selectedValue = 'yes'; }
            for (var i=0; i<options.length; i++) {
                if (options[i].id == selectedValue) {
                    if (options[i].surcharge) {
                        surcharge = this.lookupSurcharge(options[i].surcharge.id);
                        break;
                    }
                }
            }
        }
        return surcharge;
    };

    /**
     * @method getCustomVerseCharge
     * @return {surcharge}
     * product.questions._items[].options_items[].surcharge.id
     */
    proto.getCustomVerseCharge = function(questionId) {
        var surcharge = null;
        var questionModel = this.getQuestionModel(questionId);
        if (questionModel !== null) {
            if (questionModel.value == 'Custom Verse') {
                if (questionModel.info.surcharge) {
                    surcharge = this.lookupSurcharge(questionModel.info.surcharge.id);
                }
            }
        }
        return surcharge;
    };

    /**
     * @method getLocationUsageCharge
     * @return {surcharge}
     * product.questions._items[].options_items[].surcharge.id
     */
    proto.getLocationUsageCharge = function() {
        var surcharge = null;
        var questionModel = null;
        for (var i=0; i<this.product.questions._items.length; i++) {
            if (this.product.questions._items[i].id == 'layout') {
                questionModel = this.product.questions._items[i];
                //console.log('Details.getLocationUsageCharge', questionModel);
                if (questionModel !== null && questionModel.blocks && questionModel.blocks._items && questionModel.blocks._items.length > 0) {
                    var block = questionModel.blocks._items[0];
                    if (block.id == 'EI' && block.surcharges && block.surcharges.length > 0) {
                        var returnEnvelopeQuestion = this.getQuestionModel('ENVELOPE_RETURN');
                        if (returnEnvelopeQuestion && returnEnvelopeQuestion.value == 'GCENVELOPE') {
                            //console.log('Details.getLocationUsageCharge.returnEnvelopeQuestion', returnEnvelopeQuestion);
                            surcharge = this.lookupSurcharge(block.surcharges[0].id);
                            break;
                        }
                    }
                }
            }
        }
        return surcharge;
    };

     /**
     * @method getLocationUsagePaymentSurcharge
     * @return {surcharge}
     * product.questions._items[].options_items[].surcharge.id
     */
     proto.getLocationUsagePaymentSurcharge = function() {
        var surcharge = null;
        var questionModel = null;
        for (var i=0; i<this.product.questions._items.length; i++) {
            if (this.product.questions._items[i].id == 'layout') {
                questionModel = this.product.questions._items[i];
                if (questionModel !== null && questionModel.blocks && questionModel.blocks._items && questionModel.blocks._items.length > 0) {
                    var blockTM = questionModel.blocks._items.filter(function(item){
                        return item.id == 'TM'
                    });
                    var paymentTermValue = this.getPaymentTerms();
                    if(blockTM && blockTM.length > 0 && paymentTermValue)
                    {
                        surcharge = this.lookupSurcharge(blockTM[0].info.surcharge.id);
                        break;
                    }
                }
            }
        }
        return surcharge;
    };

     /**
     * @method getPaymentTerms
     * @return {paymenttermsvalue}
     */
     proto.getPaymentTerms = function() {
        var paymentterms;
        var layout = this.getQuestionModel('layout');
        if(layout && layout.info && layout.info.option && layout.info.option.location)
        {
            var blocks = layout.info.option.location.block;

            if(blocks && Array.isArray(blocks))
            {
                var block = blocks.find(function(item){
                    return item.id == 'TM'
                });
            }
                
            if(block && block.lines && Array.isArray(block.lines))
            {
                var line = block.lines.find(function(item){
                        return item.id == 'TM'
                });
            }

            if(line && line.input  && Array.isArray(line.input) && line.input.length > 0)
            {
                paymentterms =  line.input[0];
            }
        }
        return paymentterms;
    };

    /**
     * @method getLocationUsageSurcharge
     * @return {surcharge}
     * product.questions._items[].info.option.location.surcharge.id
     */
    proto.getLocationUsageSurcharge = function() {
        var surcharge = null;
        var questionModel = null;
        for (var i=0; i<this.product.questions._items.length; i++) {
            if (this.product.questions._items[i].id === 'layout') {
                questionModel = this.product.questions._items[i];
                // if(questionModel !== null && questionModel.info.option.id === questionModel.value && questionModel.info.option.location !== undefined && questionModel.info.option.location.surcharge !== undefined ) {
                // DCOM-15341
                if(questionModel !== null && questionModel.info.option.location !== undefined && questionModel.info.option.location.surcharge !== undefined ) {
                    surcharge = this.lookupSurcharge(questionModel.info.option.location.surcharge.id);
                    break;
                }
            }
        }
        return surcharge;
    };
    /**
     * @method getQuestionModel
     * @return {QuestionModel}
     */
    proto.getQuestionModel = function(questionId) {
        var questionModel = null;
        for (var i=0; i<this.product.questions._items.length; i++) {
            if (this.product.questions._items[i].id == questionId) {
                questionModel = this.product.questions._items[i];
                break;
            }
        }
        return questionModel;
    };

    /**
     * @method lookupSurcharge
     * @return {surcharge}
     */
    proto.lookupSurcharge = function(surchargeId) {
        var surcharge = null;
        var surchargeData = Surcharge.get(surchargeId);
        if (surchargeData) {
            surcharge = {};
            surcharge.id = surchargeData.id;
            surcharge.description = surchargeData.desc;
            surcharge.desc = surchargeData.desc;
            surcharge.type =  surchargeData.type;
            surcharge.addToBase = false;    //Not available in W2P, only used with apparel
            if (surcharge.type === 'ONE') {
                surcharge.qty = 1;
            } else {
                surcharge.qty = this.product.getQuantityValue() || query.qty || query.quantity;
            }
            surcharge.price = parseFloat(Surcharge.getPrice(surchargeId, true, parseInt(surcharge.qty))).toFixed(2);
            surcharge.priceFormatted = currency(surcharge.price, 2).replace('.00', '');
        }               
        //console.log('Details.lookupSurcharge', surcharge);
        return surcharge;
    };

    /**
     * @method onProductChange
     * @param {jQuery.Event} event
     * @param {App.Models.Product} product
     * @callback
     */
    proto.onProductChange = function(event, product) {
        this.update(product);
    };

    /**
     * @method isLogoMxSelected
     * @return {Boolean}
     * Checks in session storage - if logomx is set and selected
     */

    proto.isLogoMxSelected = function() {
        var logoType = SessionStorage.getValue('logo');
        var logo = null;
        if (logoType) { 
            logo = SessionStorage.getValue('logo_' + logoType); 
        }
        if (logo) { 
            this.model.logo = logo; 
        }
        return (logo && logoType === 'LOGOMX'); // Boolean;
    }

    return DetailsQuestionController;
});
