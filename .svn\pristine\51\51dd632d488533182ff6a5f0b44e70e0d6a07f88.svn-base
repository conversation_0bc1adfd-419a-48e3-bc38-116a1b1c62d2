define(function (require) {
    'use strict';

    var AbstractModel = require('./Abstract');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.ErrorCode
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} ErrorCodeValues
     */
    var ErrorCodeModel = function (ErrorCodeValues) {
        AbstractModel.call(this, ErrorCodeValues);
    };

    var proto = inherits(ErrorCodeModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} ErrorCodeValues
     * @chainable
     */
    proto.init = function(ErrorCodeValues) {

        /**
         * @property typecase
         * @default {null}
         * @type {object}
         */
        this.codes = null;

        // run the parent init method to parse determine the data type
        base.init.call(this, ErrorCodeValues);

        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param json the original JSON data.
     */
    proto.fromJSON = function(json) {
        this.codes = json;
    };

    /**
     * Get a specific error message from the object by code value.
     *
     * @param {string} code
     *
     * @return {string}
     */
    proto.getMessage = function(code) {
        if (code in this.codes) {
            return this.codes[code];
        }

        return '';
    };

    /**
     * Sets up handlers, if any
     *
     * @method setupHandlers
     * @chainable
     */
    proto.setupHandlers = function() {
        return this;
    };

    return ErrorCodeModel;
});
