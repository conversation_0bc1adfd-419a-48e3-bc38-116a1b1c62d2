define(function() {
    'use strict';

    /**
     * Error response codes and corresponding messages.
     *
     * @class App.Constants.Localization.EN_US.Errors
     * @static
     */
    var Errors = {
        '-1': 'Unknown error',
        '0': 'No internet connection',
        '100': 'This file does not meet our recommendations. For best results, please browse for a new file, or choose a logo from our <span class="inline-link logo-error-inline-clipart">ClipArt Library</span>.',
        '101': 'This file does not meet our recommendations. For best results, please browse for a new file, or choose a logo from our <span class="inline-link logo-error-inline-clipart">ClipArt Library</span>.',
        '102': 'File not attached',
        '104': "This file does not meet our recommendations. For best results, please browse for a new file, or choose a logo from our <span class='inline-link logo-error-inline-clipart'>ClipArt Library</span>.",
        '105': "This file does not meet our recommendations. For best results, please browse for a new file, or choose a logo from our <span class='inline-link logo-error-inline-clipart'>ClipArt Library</span>.",
        '108': 'Unknown error while processing file',
        '109': 'File is larger than the allowable limit',
        '110': 'Couldn\'t process the image, please check the file and try again (image alchemy).',
        '201': 'Possible presence of Virus or Trojan in the file (or generic failure during virus check).',
        '301': 'Unable to process the file at this time. Please try again later (couldn\'t FTP to file server)',
        '400': 'Bad request',
        '401': 'Unable to process file (error during image alchemy conversion)',

        MAX_LENGTH: '{{field}} exceeds {{max}} {{type}}s.',
        MIN_LENGTH: '{{field}} must be at least {{min}} {{type}}s.',
        NOT_NUMERIC: '{{field}} must be numeric.',
        REQUIRED: '{{field}} is required.',
        NOT_VALID: 'This is not a valid {{field}}',
        PREPAID: 'This pre-paid routing number cannot be used to write check.',
        TRIM_LOGO_TEXT: 'Line 1 and Line 2 are limited to 30 characters. Your previous entry has been trimmed.'
    };

    return Errors;
});
