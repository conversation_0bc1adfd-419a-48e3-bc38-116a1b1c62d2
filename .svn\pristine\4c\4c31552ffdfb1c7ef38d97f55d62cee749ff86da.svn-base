<!DOCTYPE html>
<!--[if lt IE 7]>      <html class="no-js lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7]>         <html class="no-js ie7 lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8]>         <html class="no-js ie8 lt-ie9"> <![endif]-->
<!--[if IE 9]>         <html class="no-js ie9"> <![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js"> <!--<![endif]-->
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
        <title>Selectonic — jQuery plugin</title>
        <meta name="description" content="Selectonic — jQuery plugin for making selectable lists by mouse and keyboard, configurable with options, methods and callbacks | Author: <PERSON><PERSON>">
        <meta name="viewport" content="width=device-width">

        <link rel="stylesheet" href="select.css">
        <style>
            body {
                margin: 0;
                padding: 0;
                font-family: Helvetica, Arial, sans-serif;
                line-height: 1.4;
            }
            * {
                box-sizing: border-box;
            }
            .example {
                position: absolute;
                top: 150px;
                left: 200px;
            }
        </style>
</head>
<body>

<div class="example">
    <p><strong>Simple select.</strong> <br>It has full keyboard support: <br>Arrows, PageUp, PageDown and HOME, END. <br>You could focus it by TAB, press arrow and choose item.</p>
    <div class="b-select">
        <button class="select-trigger"><div class="select-title">Choose something…</div></button>
        <ul class="select-group">
            <li class="select-option">Item 1</li>
            <li class="select-option">Item 2</li>
            <li class="select-option">Item 3</li>
            <li class="select-option">Item 4</li>
            <li class="select-option">Item 5</li>
            <li class="select-option">Item 6</li>
            <li class="select-option">Item 7</li>
            <li class="select-option">Item 8</li>
            <li class="select-option">Item 9</li>
            <li class="select-option">Item 10</li>
            <li class="select-option">Item 11</li>
            <li class="select-option">Item 12</li>
            <li class="select-option">Item 13</li>
            <li class="select-option">Item 14</li>
            <li class="select-option">Item 15</li>
            <li class="select-option">Item 16</li>
            <li class="select-option">Item 17</li>
            <li class="select-option">Item 18</li>
            <li class="select-option">Item 19</li>
            <li class="select-option">Item 20</li>
        </ul>
    </div>
</div>

<script src="http://code.jquery.com/jquery-1.10.2.min.js"></script>
<script src="../../dist/selectonic.js"></script>
<script src="select.js"></script>
<script>
    $('.b-select').mySelect();
</script>
</body>
</html>