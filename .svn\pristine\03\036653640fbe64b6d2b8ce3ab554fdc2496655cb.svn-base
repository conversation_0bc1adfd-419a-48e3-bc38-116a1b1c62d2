define(function (require) {
    'use strict';

    var $ = require('jquery');
    var AbstractModel = require('../../Abstract');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.Cart.Product.Surcharge
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} CartProductValues
     */
    var CartProductSurchargeModel = function (CartProductValues) {
        AbstractModel.call(this, CartProductValues);
    };

    var proto = inherits(CartProductSurchargeModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} values
     * @chainable
     * @return {App.Models.Cart.Product.Surcharge}
     */
    proto.init = function(values) {

        /**
         * @property id
         * @default {null}
         * @type {string}
         */
        this.id = null;

        /**
         * @property price
         * @default {null}
         * @type {float}
         */
        this.price = null;

        /**
         * @property addToBase
         * @default {null}
         * @type {boolean}
         */
        this.addToBase = null;

        /**
         * @property description
         * @default {null}
         * @type {string}
         */
        this.description = null;

        // run the parent init method to parse determine the data type
        base.init.call(this, values);

        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param json the original JSON data.
     */
    proto.fromJSON = function(json) {

        this.id = json.id;
        this.price = parseFloat(json.price);
        this.addToBase = json.addToBase;
        // QA API uses desc, PROD uses descr
        this.description = json.description;

    };

    /**
     * Converts all data into an XML string for sending
     * to the API
     * @method toXML
     * @return {string}
     */
    proto.toXML = function() {
        var xml;
        var $surcharge = $('<appliedSurcharges/>')
            .append($('<id/>').text(this.id))
            .append($('<price/>').text(this.price))
            .append($('<addToBase/>').text(this.addToBase))
            .append($('<description/>').text(this.description));

        xml = $surcharge[0].outerHTML;
        return xml;
    };

    return CartProductSurchargeModel;
});
