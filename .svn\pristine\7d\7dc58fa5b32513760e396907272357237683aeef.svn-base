define(function(require) {
    'use strict';

    var q = require('q');

    /**
     * Native array slice method for use with arguments.
     *
     * @type {Function}
     */
    var slice = Array.prototype.slice;

    /**
     * A simple event emitter.
     * https://github.com/component/emitter
     *
     * @class App.Util.EventEmitter
     * @constructor
     */
    function EventEmitter() {
        /**
         * @property callbacks
         * @type {Object.<Array.<Function>>}
         */
        this.callbacks = {};
    }

    var proto = EventEmitter.prototype;

    // -- Accessors ------------------------------------------------------------

    /**
     * Return array of `event` callbacks.
     *
     * @method getListeners
     * @param {String} event
     * @return {Array}
     */
    proto.getListeners = function(event) {
        return this._callbacks[event] || (this._callbacks[event] = []);
    };

    /**
     * Check if this emitter has any `event` callbacks.
     *
     * @method hasListeners
     * @param {String} event
     * @return {Boolean}
     */
    proto.hasListeners = function(event) {
        return !!this.getListeners(event).length;
    };

    // -- Methods --------------------------------------------------------------

    /**
     * Listen on the given `event` with `fn`.
     *
     * @method on
     * @param {String} event
     * @param {Function} fn
     * @chainable
     */
    proto.on = function(event, fn) {
        this.getListeners(event).push(fn);

        return this;
    };

    /**
     * Adds an `event` listener that will be invoked a single
     * time then automatically removed.
     *
     * @method one
     * @param {String} event
     * @param {Function} fn
     * @chainable
     */
    proto.one = function(event, fn) {
        var self = this;

        function on() {
            self.off(event, on);
            fn.apply(this, arguments); //jshint ignore: line
        }

        on.fn = fn;
        this.on(event, on);

        return this;
    };

    /**
     * Remove the given callback for `event` or all
     * registered callbacks.
     *
     * @method off
     * @param {String} event
     * @param {Function} fn
     * @chainable
     */
    proto.off = function(event, fn) {
        var callbacks;
        var length;
        var cb;
        var i;

        switch (arguments.length) {
            case 0:
                // all
                this._callbacks = {};
                return this;

            case 1:
                // specific type
                delete this._callbacks[event];
                return this;

            default:
                // specific method
                callbacks = this.getListeners(event);
                length = callbacks.length;

                for (i = 0; i < length; i++) {
                    cb = callbacks[i];

                    if (cb === fn || cb.fn === fn) {
                        callbacks.splice(i, 1);
                        break;
                    }
                }

                return this;
        }
    };

    /**
     * Emit `event` with the given args.
     *
     * @method emit
     * @param {String} event
     * @param {Mixed} ...
     * @chainable
     */
    proto.emit = function(event) {
        var args = slice.call(arguments, 1);
        var callbacks = this.getListeners(event).slice(0);
        var length = callbacks.length;
        var i = 0;

        for (; i < length; i++) {
            callbacks[i].apply(this, args);
        }

        return this;
    };

    /**
     * When an event is emitted on one emitter, trigger the same event
     * on this controller.
     *
     * @method tie
     * @param {String} event
     * @param {EventEmitter} emitter
     * @chainable
     */
    proto.tie = function(event, emitter) {
        emitter.on(event, this.emit.bind(this, event));

        return this;
    };

    /**
     * Returns a promise that will be resolved when an event is fired once.
     * Useful for handling cases where more than one event needs to have
     * been fired before a callback is executed.
     *
     * @method when
     * @param {String} event
     * @return {Promise}
     */
    proto.when = function(event) {
        var defer = q.defer();

        this.one(event, function(event, data) {
            defer.resolve(data);
        });

        return defer.promise;
    };

    return EventEmitter;
});
