
.button {
    background: #ff9900 url(../../media/images/button-bg.png) repeat-x 0 -64px;
    background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#ff9900), to(#e77401));
    background: -webkit-linear-gradient(top, #ff9900, #e77401);
    background: -moz-linear-gradient(top, #ff9900, #e77401);
    background: -ms-linear-gradient(top, #ff9900, #e77401);
    background: -o-linear-gradient(top, #ff9900, #e77401);
    background: linear-gradient(to bottom,#ffd65e,#febf04);
    font-family: 'Source Sans Pro', sans-serif;
    text-transform: uppercase;
    height: 31px;
    font-size: 12px;
    border-radius: 3px;
    padding: 7px 7px;
}
.button:focus,
.button:hover {
    background: #e77401 url(../../media/images/button-bg.png) repeat-x 0 -96px;
    background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#ff9900), to(#e77401));
    background: -webkit-linear-gradient(top, #e77401, #ff9900);
    background: -moz-linear-gradient(top, #e77401, #ff9900);
    background: -ms-linear-gradient(top, #e77401, #ff9900);
    background: -o-linear-gradient(top, #e77401, #ff9900);
    background: linear-gradient(to bottom, #febf04, #ffd65e);
    text-decoration: none;
}
.button_neutral {
    background: #f0ede7 url(../../media/images/button-bg.png) repeat-x 0 -128px;
    background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#d6d1c1), to(#f0ede7));
    background: -webkit-linear-gradient(top, #f0ede7, #d6d1c1);
    background: -moz-linear-gradient(top, #f0ede7, #d6d1c1);
    background: -ms-linear-gradient(top, #f0ede7, #d6d1c1);
    background: -o-linear-gradient(top, #f0ede7, #d6d1c1);
    background: linear-gradient(to bottom, #f0ede7, #d6d1c1);
    color: #777777;
}
.button_neutral:focus,
.button_neutral:hover {
    background: #d6d1c1 url(../../media/images/button-bg.png) repeat-x 0 -160px;
    background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#f0ede7), to(#d6d1c1));
    background: -webkit-linear-gradient(top, #d6d1c1, #f0ede7);
    background: -moz-linear-gradient(top, #d6d1c1, #f0ede7);
    background: -ms-linear-gradient(top, #d6d1c1, #f0ede7);
    background: -o-linear-gradient(top, #d6d1c1, #f0ede7);
    background: linear-gradient(to bottom, #d6d1c1, #f0ede7);
}
.button_grey
{
    background: #757575 !important;
}
.button_file_browse
{
    height: 15px;
}
.progressbar
{
    background: #e9e9e9 none repeat scroll 0 0;
    border-radius: 11px;
    height: 13px;
    padding-left: 3px;
    padding-right: 3px;
    padding-top: 3px;
    width: 100%;
}
.progress
{
    background: #c34924 ;
    border-radius: 11px;
    height: 10px;
}
.padding_4px
{
    padding-top: 4px;
}
/*
.vList_loose ul
{
    margin-top: 19px;
}
*/
h2
{
    line-height: 1.3846153846153846em;
}

.button_grey:focus,
.button_grey:hover {
    background: #757575 !important;
    color: #ffffff !important;
}