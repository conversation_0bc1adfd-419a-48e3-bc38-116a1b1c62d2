/* ---------------------------------------------------------------------
Target Browsers: All
Media Type: Screen, Projection
Width: All Sizes
------------------------------------------------------------------------ */
/*csslint
    box-model: false,
    duplicate-background-images: false
*/

/* ---------------------------------------------------------------------
 Button
------------------------------------------------------------------------ */
.button,
.button:hover {
  display: inline-block;
  min-width: 156px;
  border-width: 0;
  position: relative;
  color: #ffffff;
  font-weight: 700;
  text-align: center;
  cursor: pointer;
  letter-spacing: 0.55px;
  border-radius: 2px;
}

.button:focus {
  background: #e77401 url(../../media/images/button-bg.png) repeat-x 0 -96px;
  background: -webkit-gradient( linear, 0% 0%, 0% 100%, from(#ff9900), to(#e77401) );
  background: -webkit-linear-gradient(top, #e77401, #ff9900);
  background: -moz-linear-gradient(top, #e77401, #ff9900);
  background: -ms-linear-gradient(top, #e77401, #ff9900);
  background: -o-linear-gradient(top, #e77401, #ff9900);
  background: linear-gradient(to bottom, #e77401, #ff9900);
  text-decoration: none;
}

@media (hover: hover) {
  .button:hover {
    background: #e77401 url(../../media/images/button-bg.png) repeat-x 0 -96px;
    background: -webkit-gradient( linear, 0% 0%, 0% 100%, from(#ff9900), to(#e77401) );
    background: -webkit-linear-gradient(top, #e77401, #ff9900);
    background: -moz-linear-gradient(top, #e77401, #ff9900);
    background: -ms-linear-gradient(top, #e77401, #ff9900);
    background: -o-linear-gradient(top, #e77401, #ff9900);
    background: linear-gradient(to bottom, #e77401, #ff9900);
    text-decoration: none;
  }
}
@media (hover: none) {
  .button:active {
    background: #e77401 url(../../media/images/button-bg.png) repeat-x 0 -96px;
    background: -webkit-gradient( linear, 0% 0%, 0% 100%, from(#ff9900), to(#e77401) );
    background: -webkit-linear-gradient(top, #e77401, #ff9900);
    background: -moz-linear-gradient(top, #e77401, #ff9900);
    background: -ms-linear-gradient(top, #e77401, #ff9900);
    background: -o-linear-gradient(top, #e77401, #ff9900);
    background: linear-gradient(to bottom, #e77401, #ff9900);
    text-decoration: none;
  }
}

.button_next, 
.button_prev {
  height: auto !important;
}

.button_fbt_next,
.button_fbt_bad {
  width: 100%;
  height: auto !important;
  margin-bottom: 20px;
}

.button_fbt_next {
  margin-top: 10px;
}

.button_fluid {
  min-width: 0;
}

.button_grey {
  background: #757575 !important;
}

.button_grey:focus,
.button_grey:hover {
  background: #757575 !important;
  color: #ffffff !important;
}

.button_blue {
  display: inline-block;
  min-width: 156px;
  border-width: 0;
  position: relative;
  color: #ffffff;
  font-weight: 700;
  text-align: center;
  cursor: pointer;
  letter-spacing: 0.55px;
  background-color: #259cda;
  padding: 10px;
  border-radius: 3px;
}

.button_blue:focus,
.button_blue:hover {
  background: #e77401 url(../../media/images/button-bg.png) repeat-x 0 -96px;
  background: -webkit-gradient( linear, 0% 0%, 0% 100%, from(#259cda), to(#4e6989) );
  background: -webkit-linear-gradient(top, #4e6989, #259cda);
  background: -moz-linear-gradient(top, #4e6989, #259cda);
  background: -ms-linear-gradient(top, #4e6989, #259cda);
  background: -o-linear-gradient(top, #4e6989, #259cda);
  background: linear-gradient(to bottom, #4e6989, #259cda);
  text-decoration: none;
}

.button_neutral {
  background: #f0ede7 url(../../media/images/button-bg.png) repeat-x 0 -128px;
  background: -webkit-gradient( linear, 0% 0%, 0% 100%, from(#d6d1c1), to(#f0ede7) );
  background: -webkit-linear-gradient(top, #f0ede7, #d6d1c1);
  background: -moz-linear-gradient(top, #f0ede7, #d6d1c1);
  background: -ms-linear-gradient(top, #f0ede7, #d6d1c1);
  background: -o-linear-gradient(top, #f0ede7, #d6d1c1);
  background: linear-gradient(to bottom, #f0ede7, #d6d1c1);
  color: #777777;
}

.button_neutral:focus,
.button_neutral:hover {
  background: #d6d1c1 url(../../media/images/button-bg.png) repeat-x 0 -160px;
  background: -webkit-gradient( linear, 0% 0%, 0% 100%, from(#f0ede7), to(#d6d1c1) );
  background: -webkit-linear-gradient(top, #d6d1c1, #f0ede7);
  background: -moz-linear-gradient(top, #d6d1c1, #f0ede7);
  background: -ms-linear-gradient(top, #d6d1c1, #f0ede7);
  background: -o-linear-gradient(top, #d6d1c1, #f0ede7);
  background: linear-gradient(to bottom, #d6d1c1, #f0ede7);
}

a.button {
  height: auto;
  min-width: 0;
}

.button_next_header {
  display: none;
}
