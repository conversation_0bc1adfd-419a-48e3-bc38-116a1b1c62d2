/*jshint forin: false, laxbreak: true */
define(function (require) {
    'use strict';

    var AbstractModel = require('../Abstract');
    var Content = require('i18n!../../constants/nls/en-us/Content');
    var Errors = require('i18n!../../constants/nls/en-us/Errors');
    var FieldMappings = require ('../../constants/FieldMappings');
    var inherits = require('mout/lang/inheritPrototype');
    var SessionStorage = require('../../providers/SessionStorage');
    var RegEx = require('../../constants/RegEx');
    var Query = require('models/Query');
    var Settings = require('../../constants/Settings');
    var Helper = require('../../util/helper');

    var editflow;
    if (window.location.href.indexOf("fromPage") > -1) {
        editflow = true;
    } else {
        editflow = false;
    }
    
    /**
     * @class App.Models.Ui.QuestionBlockLine
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} questionBlockLine
     */
    var UIQuestionBlockLineModel = function (questionBlockLine) {
        AbstractModel.call(this, questionBlockLine);
    };

    var proto = inherits(UIQuestionBlockLineModel, AbstractModel);
    var base = AbstractModel.prototype;
    var printEnvelopInput = false;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} questionBlock
     * @chainable
     */
    proto.init = function(questionBlock) {
        /**
         * @property query
         * @type {App.Models.Query}
         */
        this.query = Query.getInstance();

        /**
         * @property id
         * @type {String}
         * @default null
         */
        this.id = null;

        /**
         * @property desc
         * @type {String}
         * @default null
         */
        this.desc = null;

        /**
         * @property isRequired
         * @type {Boolean}
         * @default {null}
         */
        this.isRequired = null;

        /**
         * @property maxLength
         * @type {Number}
         * @default null
         */
        this.maxLength = null;

        /**
         * @property minLength
         * @type {Number}
         * @default null
         */
        this.minLength = null;

        /**
         * @property sim
         * @type {Number}
         * @default null
         */
        this.sim = null;

        /**
         * @property value
         * @type {String}
         * @default null
         */
        this.value = null;

        /**
         * To be assigned to the value="" attribute
         * on an html form field.
         *
         * @property fieldValue
         * @type {String}
         * @default null
         */
        this.fieldValue = null;

        /**
         * Mappings for special handling of fields
         *
         * @property mappings
         * @type {Object}
         */
        this.mappings = {
            /*'CI': [
                {name: 'city', value: ''},
                {name: 'state', value: ''},
                {name: 'zipCode', value: ''}
            ],
            'BI': [
                {name: '4_city', value: ''},
                {name: '4_state', value: ''},
                {name: '4_zipCode', value: ''}
            ]*/
        };

        this.prefix = {};

        // run the parent init method to parse determine the data type
        base.init.call(this, questionBlock);

        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param {object} json the original JSON data.
     */
    proto.fromJSON = function(json) {
        var maxLength = json.max;
        this.id = json.id;
        this.desc = json.desc;
        this.maxLength = maxLength == null ? 50 : maxLength;
        this.minLength = json.min;
        this.sim = json.sim;
        this.hasLogo = json.logo === 'Y' || false;
    };

    /**
     * Sets internal information.
     *
     * @method setInfo
     * @param {Object} line
     * @param {Object} block
     * @param {Object} question
     * @param {Object} product
     * @param {Object} fxg
     * @param {Object} customer
     * @chainable
     */
    proto.setInfo = function(line, block, question, product, fxg, customer) {
        //console.log('QuestionBlockLine.setInfo.this.1', this);
// console.log(this)
        var desc;
        var contentId = this._getContentId(line, question);
        
        if (line && !Array.isArray(block.line)) {
             desc = line.desc;
        }       
       
        this.blockId = block.id;
        this.info = this._setFXG(line, fxg);

        if(block.id == 'RM' && line.id == 'BX') {
            this.default = line.default
            this.options = line.option
        }


        if(block.id == 'BI' && this.info && (this.info.desc == 'Routing Number')) {
            this.desc = Content.get(contentId, desc || this.desc, {range: Content.get('ROUTING_NUMBER_MAX_LENGTH')});
        } else if (block.id == 'CI' && this.info && this.info.desc == 'Account Number') {
            this.desc = Content.get(contentId, desc || this.desc, {range: Content.get('ACCOUNT_NUMBER_MAX_LENGTH')});
        } else {

            if(block.id == 'SH'){
                var SH_Flag=false, ST_Flag=false;
                let blocks= question.option.location.block;
                if(blocks){
                    blocks.forEach( function(block) {
                        if(block.id =='SH'){
                            SH_Flag=true;
                        }
                        if(block.id =='ST'){
                            ST_Flag=true;
                        }
                    });
                }
                if(ST_Flag == false && SH_Flag == true){
                    this.desc = 'Voucher';
                }
                else{
                    this.desc = Content.get(contentId, desc || this.desc);
                }
            }else{
                this.desc = Content.get(contentId, desc || this.desc);
            }

        }

        this.isRequired = block.isRequired;
        this.question = question;
        this.customer = customer;
        this.configuration = product.productConfiguration;
        this.productId = product.productId;
        this.setDefaultValue();

        //Override product xml since routing number is setup as a 'normal imprint line'
        //instead of a routing number option as it is for checks & deposits.
        if (block.id == 'BI' && this.info && this.info.desc == 'Routing Number') {
            block.isRequired = true;
            this.isRequired = true;
            this.maxLength = 9;
            this.minLength = 9;
        }
        if (block.id == 'CI' && this.info && this.info.desc == 'Account Number') {
            block.isRequired = true;
            this.isRequired = true;
            this.maxLength = 15;
            this.minLength = 1;
        }

        if ($.inArray(this.productId , Settings.CUSTOM_VALIDATION_PRODUCTS) > -1) {
            if ($.inArray(this.blockId, Settings.CUSTOM_VALIDATION_BLOCKS) > -1 && $.inArray(this.id, Settings.CUSTOM_VALIDATION_LINES) > -1) {
                if (this.sim > 1) {
                    for (var i=1; i <= this.sim; i++) {
                        //this.desc = Content.get(this.blockId+'_'+this.id+'_'+i);
                        if (i == 1) {
                            this.isRequired = true;
                        }
                    }
                } else {
                    this.isRequired =  true;
                }
            }
        }
        //console.log('QuestionBlockLine.setInfo.this.2', this);

        return this;
    };

    /**
     * Generate a unique id to fetch content language strings
     *
     * @param {Object} info
     * @param {App.Models.Ui.Question} question
     * @return {String}
     */
    proto._getContentId = function(info, question) {
        var id = this.id;
        var line = question && question.line;

        if (question && question.id) {
            id = question.id +'_'+ id;
        }

        if (info && info.sim) {
            id = id +'_'+ info.sim;
        } else if (line && line.sim) {
            id = id +'_'+ question.line.sim;
        } else if (this.sim) {
            id = id +'_'+ this.sim;
        }

        return id;
    };

    /**
     * Sets the default value of the field based on the
     * customer's profile information from Deluxe account.
     *
     * @method setDefaultValue
     * @chainable
     */
    proto.setDefaultValue = function() {
        var i = 0;
        var query = this.query;
        var savedValue = '';
        var id = this.blockId ? this.blockId +'_'+ this.id : this.id;
        if (this.configuration) {
            savedValue = this.configuration.getValue(this.id, this.blockId);
        } else {
            if(this.blockId === 'CI' && this.id==='CT' && !SessionStorage.getValue(this.blockId+'_'+this.id))
                {
                    if( SessionStorage.getValue("CI_CI")){
                        var CT_ST_AP= SessionStorage.getValue("CI_CI");
                        var flag = false;
                        
                        if(SessionStorage.getValue("CI_CI").indexOf(',') > -1){
                            SessionStorage.storeValue(this.blockId+'_'+'CT',SessionStorage.getValue("CI_CI").split(',')[0]);
                            CT_ST_AP=SessionStorage.getValue("CI_CI").substring(SessionStorage.getValue("CI_CI").indexOf(',') + 1);
                        }
                        
                        if( CT_ST_AP.indexOf(',') > -1 ){
                            CT_ST_AP = CT_ST_AP.split(',');
                            flag = true;
                        }
                        else if(CT_ST_AP.indexOf(' ') > -1 ){
                            CT_ST_AP = CT_ST_AP.split(' ');
                            flag = true;
                        }

                        if (CT_ST_AP.length>2 && flag){
                        if(CT_ST_AP[0]){
                            SessionStorage.storeValue(this.blockId+'_'+'CT',CT_ST_AP[0]);
                        }
                        if(CT_ST_AP[1]){
                                SessionStorage.storeValue(this.blockId+'_'+'ST',CT_ST_AP[1].replace(',',''));
                        }
                        if(CT_ST_AP[2]){
                            SessionStorage.storeValue(this.blockId+'_'+'AP',CT_ST_AP[2]);
                        }
                        } else if (CT_ST_AP.length>1 && flag){
                            if(CT_ST_AP[0]){
                                SessionStorage.storeValue(this.blockId+'_'+'ST',CT_ST_AP[0].replace(',',''));
                            }
                            if(CT_ST_AP[1]){
                                SessionStorage.storeValue(this.blockId+'_'+'AP',CT_ST_AP[1]);
                            }
                        } else {
                            SessionStorage.storeValue(this.blockId+'_'+'CT',SessionStorage.getValue("CI_CI"));
                        }
                    }
                } else if(this.blockId === 'CI' && this.id ==='CI' && !SessionStorage.getValue(this.blockId+'_'+this.id))
                {      
                    if( SessionStorage.getValue('CI_CT')){
                        SessionStorage.storeValue('CI_CI',
                        (SessionStorage.getValue('CI_CT')+', '+SessionStorage.getValue('CI_ST')+' '+SessionStorage.getValue('CI_AP')).replace(/,\s*$/, ""));
                    }
                }
            savedValue = SessionStorage.getValue(id);
        }

        if (this.info && this.info['default']) {
            this.value = this.info['default'];
        }

        if (this.id in FieldMappings.QUERY && query[FieldMappings.QUERY[this.id]]) {
            var value = query[FieldMappings.QUERY[this.id]];
            this.value = value.toString();
        }

        if (this.customer.lines && this.customer.lines[this.id]) {
            
            if( (this.query.recSkuId && !savedValue) || !(this.query.recSkuId)){
                        var blockId = this.id;
                    
                if (this.mappings[blockId]) {
                    var mappings = this.mappings[blockId];
                    var length = mappings.length;
                    var name;

                    for (; i < length; i++) {
                        name = mappings[i].name;
                        mappings[i].value = (savedValue && savedValue[name])
                            || this.customer.lines[blockId +'_'+ name]
                            || '';
                    }
                // Many products have ML fields, but we only set the default
                // values from the user data for the CI and BI blocks.
                } else if (this.blockId && (this.blockId === 'CI' || this.blockId === 'BI')) {
                    if(!editflow){
                    this.value = this.customer.lines[this.id];
                    // console.log(this.desc+':'+this.value);
                    SessionStorage.storeValue(this.blockId+'_'+this.id,this.value);
                    }
                }
            }
        }

        this._processStoredConfigurationData();

        return this;
    };

    /**
     * Processes any data stored as XML from the Deluxe server
     *
     * @method _processStoredConfigurationData
     * @private
     */
    proto._processStoredConfigurationData = function() {
        var i = 0;
        var key;
        var savedValue = '';
        // blockID will be present if its a field such as ED_ML or PV_ML
        var id = this.blockId ? this.blockId +'_'+ this.id : this.id;

        if (this.configuration) {
            savedValue = this.configuration.getValue(this.id, this.blockId);
        } else {
            savedValue = SessionStorage.getValue(id);
            if (savedValue) {
                this.value = savedValue;
                return;
            }
        }
        if (savedValue !== "" || savedValue !== undefined) {
            if (Array.isArray(savedValue)) {
                savedValue.forEach(function (value, index) {
                    const parseData = Helper.parseValue(value);
                    savedValue[index] = parseData[1];
                    const keyId = this._prependBlockId(index + 1)
                    this.prefix[keyId] = parseData[0];
                }.bind(this));
            } else {
                const parseData = Helper.parseValue(savedValue);
                savedValue = parseData[1];
                this.prefix[id] = parseData[0];
            }
        }
        if (savedValue ) {
            if (Array.isArray(savedValue)) {
                for (i; i < savedValue.length; i++) {
                    key = this._prependBlockId(i + 1);
                    /*if (SessionStorage.getValue(key)){
                        continue;
                    }*/
                    /*if (this.id === 'BI' && RegEx.CITY_STATE.test(savedValue[i])) {
                        //this._processBICityStateZip(savedValue[i]);
                        continue;
                    }*/
                    this.setValue(savedValue[i], key);
                }
            } else if (typeof savedValue === 'object' && savedValue.city) {
                //this._processCICityStateZip(savedValue);
            } else {
                this.setValue(savedValue, id);
            }
        }
    };

    /**
     * Custom processing CI string
     *
     * @method _processBICityStateZip
     * @param {string} cityStateString
     * @private
     */
    proto._processBICityStateZip = function(cityStateString) {
        var j = 1;
        var key;
        var cityStateArray = cityStateString.match(RegEx.CITY_STATE);

        if (cityStateArray) {
            for (; j < cityStateArray.length; j++) {
                key = 'BI_' + this.id + '_' + this.mappings[this.id][j - 1].name;

                if (SessionStorage.getValue(key)) {
                    //don't override session storage
                    continue;
                }

                //this.setValue(cityStateArray[j], key);
            }
        }
    };

    /**
     * Custom processing for BI values
     *
     * @method _processCICityStateZip
     * @param {object} savedValue
     * @private
     */
    proto._processCICityStateZip = function(savedValue) {
        var j;
        var key;

        for (j in savedValue) {
            if (!savedValue.hasOwnProperty(j)) {
                continue;
            }

            key = 'CI_' + this.id + '_' + j;

            if (SessionStorage.getValue(key)) {
                //don't override session storage
                continue;
            }

            //this.setValue(savedValue[j], key);
        }
    };

    /**
     * Fetch the default field value from the customer.lines
     *
     * @param {String} name
     * @return {String|null}
     */
    proto.getDefaultValue = function(name) {
        if (!editflow) {
            var blockId = this.id;
            if (!this.customer.lines) {
                return '';
            }

            if (this.mappings[blockId]) {
                var i = 0;
                var mappings = this.mappings[blockId];
                var length = mappings.length;

                for (; i < length; i++) {
                    if (name === mappings[i].name && this.customer.lines[blockId + '_' + name]) {
                        return this.customer.lines[blockId + '_' + name];
                    }
                }
            } else if (this.customer.lines[blockId]) {
                if (this.sim > 1 && (typeof name === "number" && name !== 1)) {
                    return '';
                } else {
                    return this.customer.lines[blockId];
                }
            }
        }
    };

    /**
     * Sets the current value and saves it to our Session
     *
     * @method setValue
     * @param {string} value
     * @param {string} name
     * @chainable
     */
    proto.setValue = function(value, name) {

            if(!name){
                return;
            }
        /**
            The following if statement is a stopgap measure to handle ALM 1656, should be replaced
            with a more permanent fix
        */
        if ((parseInt(this.sim) > 1) && !(/\d+/.test(name.substring(name.length-1, name.length))) && (this.blockId != "BI")) {
            name = name + "_1";
        } else if ((parseInt(this.sim) === 1) && !(/\d+/.test(name.substring(name.length-1, name.length))) && (this.blockId === "BI") && (this.id === "BI")) {
            // to save in session storage as BI_BI_1 even if sim = 1
            name = name + "_1";
        }
        name = name || this.id;

        SessionStorage.storeValue(name, value);

        if (!this.values) {
            this.values = {};
        }

        name = this._stripBlockId(name);

        this.values[name] = value;

        return this;
    };

    /**
     * Gets a value from the Session, if it does not exist
     * it will return the current value or null.
     *
     * @method getValue
     * @param  name
     * @param  blockId
     * @return {String}
     */
    var defaultValue = '';
    proto.getValue = function(name) {
        if (!editflow) {
            defaultValue = this.getDefaultValue(name);
        }
        else if(editflow)
        {
            defaultValue = '';
        }
        name = name ? name : this._prependBlockId(name);
        if (!editflow) {
            if (name && this.id === 'BI') {
                name = this._prependBlockId(name);
            }
        }
        var value = this.values ? this.values[name] : this.value;
        var stored = SessionStorage.getValue(name);
        if (!editflow) {
            if ( stored && typeof stored === 'string') {
                return stored;
            }
            
            if (value != null && value != undefined) {
                SessionStorage.storeValue(name,value);
                return value;
            } else {
                SessionStorage.storeValue(name,defaultValue);
                return defaultValue || '';
            }
        }
        else if(editflow)
        {
            if (value != null && value != undefined) {
                SessionStorage.storeValue(name,value);
                return value;
            } else {
                return '';
            }
        }
    };

    /**
     * Remove block ID from name
     *
     * @method _stringBlockId
     * @param {String} name
     * @returns {String}
     * @private
     */
    proto._stripBlockId = function(name) {
        if (name && this.blockId) {
            name = name.replace(this.blockId + '_' + this.id + '_', '');
        } else if (name) {
            name = name.replace(this.id + '_', '');
        }

        return name;
    };

    /**
     * Prepend block ID to name and prevent double prefixing.
     *
     * @method _prependBlockId
     * @param {String} name
     * @returns {String}
     * @private
     */
    proto._prependBlockId = function(name) {
        var fullPrefix = this.blockId + '_' + this.id + '_';
        var partPrefix = this.blockId + '_';

        // name will be either a sim number or city, state, zip
        // If we have a name then we have a a custom field name, e.g.
        //      CI_CI_city
        //      BI_BI_4_city
        //      SW_SW_1 (simulated/repeated fields)
        if (name && this.blockId && String(name).indexOf(fullPrefix) !== 0) {
            name = fullPrefix + name;
        // No custom field name, then just normal CI_CI field
        } else if (!name && this.blockId) {
            name = partPrefix + this.id;
        }

        return name;
    };

    /**
     * Sets the x/y coordinates on the line items within a block
     * based on the FXG data.
     *
     * @param {Object} lineInfo
     * @param {Object} fxg
     * @return {Object} lineInfo
     */
    proto._setFXG = function(lineInfo, fxg) {
        if (!lineInfo || !fxg) {
            return;
        }

        var child;
        var rotation;
        var i = 0;

        for (; i < fxg.children.length; i++) {
            child = fxg.children[i];

            if (child.id && child.id === lineInfo.id) {
                rotation = child.rotation || 0;
                lineInfo.pos = {
                    x: child.x,
                    y: child.y
                };
                this._applyRotation(lineInfo.pos, +rotation);
            }
        }

        return lineInfo;
    };

    /**
     * Calculate bounding box of rotated field
     * @param  {Object} pos      Position values
     * @param  {Number} rotation Degrees to rotate
     */
    proto._applyRotation = function(pos, rotation) {
        var rad = 2 * Math.PI * parseInt(rotation, 10) / 360;
        var w = Math.abs( pos.w * Math.cos(rad) + pos.h * Math.sin(rad));
        var h = Math.abs( pos.w * Math.sin(rad) + pos.h * Math.cos(rad));
        pos.w = w;
        pos.h = h;
    };

    /**
     * Determines if values are valid, or gives a reason why not.
     *
     * TODO: Refactor this
     *
     * @method isValid
     * @param {String} name
     * @return {Boolean|String} Either `true`, or an error message.
     */
    proto.isValid = function(name) {
        var isValid;
        var fields = [];
        var length;
        var i = 0;

        // if (name) {
        //     return this._isValid(name);
        // }
        
        // These values should be configured somewhere
        /*switch (this.id) {
            case 'BI':
                fields = ['1', '2', '3', '4_city', '4_state', '4_zipCode'];
                break;

            case 'CI':
                fields = ['city', 'state', 'zipCode'];
                break;
           
        }*/
        //console.log('QuestionBlockLine.isValid', this);

        if (this.id === 'BI' && this.isRequired) {
            isValid = this._isValid('1');
            if (isValid !== true) {
                return isValid;
            }
            return true;
        } else if (this.info && this.info.desc == 'Routing Number' && this.isRequired) {
            isValid = this._isValid('');
            if (isValid !== true) {
                return isValid;
            }
            return true;
        } else {
             if ($.inArray(this.productId , Settings.CUSTOM_VALIDATION_PRODUCTS) > -1 && name == 'CI_ML_2') {
                return true;
            }
             
            for (length = fields.length; i < length; i++) {
                isValid = this._isValid(fields[i]);
                if (isValid !== true) {
                    // console.log('this.valid'+this.valid);
                    return isValid;
                }
            }
            if (fields.length == 0) {
                isValid = this._isValid(fields[0]);
                if (isValid !== true) {
                    return isValid;
                }
            }
            return true;
        }
    };

    /**
     * TODO: Refactor this
     *
     * @method _isValid
     * @param {String} name
     * @return {Boolean|String}
     */
    proto._isValid = function(name) {

        var value = this.getValue(name);
        var desc = this.desc.replace(/\([^\)]*\)/g, '');
        //console.log('QuestionBlockLine._isValid', this);
        if ($.inArray(this.productId , Settings.CUSTOM_VALIDATION_PRODUCTS) > -1 && this.id == 'ST') {
            //console.log("name"+this.id+"sthis.getValue(name)"+this.getValue());
            if (!(SessionStorage.getValue('CI_ST') && SessionStorage.getValue('CI_ST').length >0)) {
                value = '';
            }
        }
        if (value === null || value === '') {
            //Only the BI Bank Name field is required.
            if (this.id === 'BI' && this.isRequired) {
                var test = false;
                if (this.getValue(1).length >0) {
                    test = true;
                }
                if (!test) {
                    return  Errors.get('REQUIRED', {
                        field: Content.get(name, desc)
                    });
                }
            }
            if ($.inArray(this.productId , Settings.CUSTOM_VALIDATION_PRODUCTS) > -1 && this.id=='ML') {
                for (var j=1; j<= this.sim; j++) {
                    if (name='CI_ML_1' && this.getValue(j).length <=0) {
                        return  Errors.get('REQUIRED', {
                            field: Content.get(name, desc)
                        });
                    }
                    return true;
                }
            }

            let personalization_array = ['AD', 'CI', 'ML', 'DE'];
            if ( $('#ENVELOPE_RETURN').is(':checked') && personalization_array.includes(this.id)) {
                for (let index = 0; index < personalization_array.length; index++) {
                    const element = personalization_array[index];
                    const anyValue = $("input[name=EI_" + element +"]").val();
                    if(anyValue.length > 0) {
                        return true;
                    }
                }
                return false;
            }
            
            if ((this.isRequired && this.info && this.info.desc == 'Routing Number') ||
                (this.isRequired && this.info && this.info.desc == 'Account Number') ||
                ($.inArray(this.productId , Settings.CUSTOM_VALIDATION_PRODUCTS) > -1 && this.id=='AP')) {

                var test = false;
                if (this.getValue().length >0 && !RegEx.NUMERIC.test(value)) {
                    test = true;
                }
                if (!test) {
                    return  Errors.get('REQUIRED', {
                        field: Content.get(name, desc)
                    });
                }
            }

            if (this.isRequired && (this.id != 'BI' && this.id != 'NO' && ($.inArray(this.productId , Settings.CUSTOM_VALIDATION_PRODUCTS) > -1 && (this.id!='ML' && this.id!='AP')))) {
                return Errors.get('REQUIRED', {
                    field: Content.get(name, desc)
                });
            }

            return true;
        }
        
        if ((this.isRequired && this.info && this.info.desc == 'Account Number') ||
            (this.isRequired && this.info && this.info.desc == 'Routing Number') ||
            ($.inArray(this.productId , Settings.CUSTOM_VALIDATION_PRODUCTS) > -1 && this.id=='AP')) {

            if (this.id=='AP') {
                this.maxLength = 9;
                this.minLength = 5;
            }
            if (!RegEx.NUMERIC.test(value.trim())) {
                if (this.info && this.info.desc == 'Account Number' ||
                    this.info && this.info.desc == 'Routing Number') {
                    if (!RegEx.ASTERISK_NUMERIC.test(value)) {
                        return Errors.get('NOT_NUMERIC', {
                            field: desc
                        })
                    }
                } else {
                    return Errors.get('NOT_NUMERIC', {
                        field: desc
                    });
                }
            }
        }

        if (this.maxLength != null && value.length > this.maxLength) {
            return Errors.get('MAX_LENGTH', {
                field: desc,
                max: this.maxLength,
                type: Content.get('digit')
            });
        }

        if (this.minLength != null && value.length < this.minLength) {
            return Errors.get('MIN_LENGTH', {
                field: this.desc,
                min: this.minLength,
                type: Content.get('digit')
            });
        }

        return true;
    };

    /**
     * @method getSimvalue
     * @param {Number} name 
     * @returns {String}
     */

    proto.getSimValue = function (name) {
        var tempName = "";
        if (typeof name == "number") {
            tempName = name;
            if (!editflow) {
                defaultValue = this.getDefaultValue(name);
                name = this._prependBlockId(name);
            } else {
                defaultValue = "";
            }
            var value = this.values ? (this.values[name] || this.values[tempName]) : this.value;
            var stored = SessionStorage.getValue(name);
            if (!editflow) {
                if (stored && typeof stored === 'string') {
                    return stored;
                }

                if (value != null && value != undefined) {
                    if (this.sim > 1 && tempName == 1) {
                        SessionStorage.storeValue(name,value);
                        return value;
                    } else if (this.sim == 1) {
                        SessionStorage.storeValue(name,value);
                        return value;
                    }
                } else {
                    SessionStorage.storeValue(name,defaultValue);
                    return defaultValue || '';
                }
            }
            else if (editflow) {
                if (value != null && value != undefined) {
                    SessionStorage.storeValue(name,value);
                    return value;
                } else {
                    return '';
                }
            }

        }
    }

    return UIQuestionBlockLineModel;
});
