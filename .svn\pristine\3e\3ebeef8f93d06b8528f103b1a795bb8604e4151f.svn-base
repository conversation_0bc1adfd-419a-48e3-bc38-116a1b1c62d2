define(function(require){

//automatically generated, do not edit!
//run `node build` instead
return {
    'WHITE_SPACES' : require('./string/WHITE_SPACES'),
    'camelCase' : require('./string/camelCase'),
    'contains' : require('./string/contains'),
    'crop' : require('./string/crop'),
    'endsWith' : require('./string/endsWith'),
    'escapeHtml' : require('./string/escapeHtml'),
    'escapeRegExp' : require('./string/escapeRegExp'),
    'escapeUnicode' : require('./string/escapeUnicode'),
    'hyphenate' : require('./string/hyphenate'),
    'insert' : require('./string/insert'),
    'interpolate' : require('./string/interpolate'),
    'lowerCase' : require('./string/lowerCase'),
    'lpad' : require('./string/lpad'),
    'ltrim' : require('./string/ltrim'),
    'makePath' : require('./string/makePath'),
    'normalizeLineBreaks' : require('./string/normalizeLineBreaks'),
    'pascalCase' : require('./string/pascalCase'),
    'properCase' : require('./string/properCase'),
    'removeNonASCII' : require('./string/removeNonASCII'),
    'removeNonWord' : require('./string/removeNonWord'),
    'repeat' : require('./string/repeat'),
    'replace' : require('./string/replace'),
    'replaceAccents' : require('./string/replaceAccents'),
    'rpad' : require('./string/rpad'),
    'rtrim' : require('./string/rtrim'),
    'sentenceCase' : require('./string/sentenceCase'),
    'slugify' : require('./string/slugify'),
    'startsWith' : require('./string/startsWith'),
    'stripHtmlTags' : require('./string/stripHtmlTags'),
    'trim' : require('./string/trim'),
    'truncate' : require('./string/truncate'),
    'typecast' : require('./string/typecast'),
    'unCamelCase' : require('./string/unCamelCase'),
    'underscore' : require('./string/underscore'),
    'unescapeHtml' : require('./string/unescapeHtml'),
    'unescapeUnicode' : require('./string/unescapeUnicode'),
    'unhyphenate' : require('./string/unhyphenate'),
    'upperCase' : require('./string/upperCase')
};

});
