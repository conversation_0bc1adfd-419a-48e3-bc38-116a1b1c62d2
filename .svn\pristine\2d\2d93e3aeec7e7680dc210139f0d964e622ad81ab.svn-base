/**
 * models/cart/products/collections/Surcharges.js
 */
define(function (require) { // jshint ignore:line
    'use strict';

    var AbstractCollection = require('../../../collections/Abstract');
    var CartProductSurcharge = require('../Surcharge');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.Cart.Product.Collections.Surcharges
     * @extends App.Models.Collections.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} surcharges
     */
    var CartProductSurchargesCollection = function (surcharges) {
        AbstractCollection.call(this, surcharges);
    };

    /**
     * UIQuestionBlocks extends AbstractCollection
     * @type {App.Models.Collections.Abstract}
     */
    var proto = inherits(CartProductSurchargesCollection, AbstractCollection);
    var base = AbstractCollection.prototype;

    /**
     * @method init
     * @param {Document|Object|undefined} surcharges
     */
    proto.init = function(surcharges) {
        /**
         * @property _xmlOutput
         * @default {null}
         * @type {string}
         * @private
         */
        this._xmlOutput = null;

        base.init.call(this, surcharges);

        this.setupHandlers().
            createChildren();
    };

    /**
     * @chainable
     * @method setupHandlers
     * @return {App.Models.Cart.Product.Collections.Surcharges}
     */
    proto.setupHandlers = function() {
        this.toXMLEachMethodHandler = this.toXMLEachMethod.bind(this);
        return this;
    };

    /**
     * Get total price of surcharges
     *
     * @method getTotalPrice
     * @returns {Number}
     */
    proto.getTotalPrice = function() {
        var price = 0;

        this.each(function(item) {
            price += item.price;
        });

        return price;
    };

    /**
     * @chainable
     * @method createChildren
     * @return {App.Models.Cart.Product.Collections.Surcharges}
     */
    proto.createChildren = function() {
        this._xmlOutput = '';
        return this;
    };

    /**
     * @property itemClass
     * @type {App.Models.Cart.Product.Surcharge}
     */
    proto.itemClass = CartProductSurcharge;

    /**
     * Converts all data into an XML string for sending
     * to the API
     * @method toXML
     * @return {string}
     */
    proto.toXML = function() {
        this._xmlOutput = '';
        this.each(this.toXMLEachMethodHandler);
        return this._xmlOutput;
    };

    /**
     * @method toXMLEachMethod
     * @param {App.Models.Cart.Product.Surcharge} surcharge
     */
    proto.toXMLEachMethod = function(surcharge) {
        this._xmlOutput += surcharge.toXML();
    };

    return CartProductSurchargesCollection;
});
