/*! Selectonic - v0.4.2 - 2014-02-02
* https://github.com/anovi/selectonic
* Copyright (c) 2014 <PERSON><PERSON>; Licensed MIT */

!function(a,b,c){"use strict";function d(a,b){if("object"!=typeof a)throw new TypeError("First argument must be an object with scheme of default options.");return this._schema=a,this._options={},this._callbacks={},this.set(b,!0),this}function e(b,c){this._name=e.pluginName,this.el=b,this.$el=a(b),this.ui={},this._selected=0,this._isEnable=!0,this._keyModes={},this.options=new d(i,c);var f=this;this.options.on("filter",function(a){return f._itemsSelector="."+f.options.get("listClass")+" "+a,a}),this.options.on("autoScroll",function(a){return f._setScrolledElem(a),a}),this._itemsSelector="."+this.options.get("listClass")+" "+this.options.get("filter"),this._setScrolledElem(this.options.get("autoScroll")),this._init()}var f=function(a,b,c){var d,e,f,g=null,h=0;c=c||{};var i=function(){h=c.leading===!1?0:new Date,g=null,f=a.apply(d,e)};return function(){var j=new Date;h||c.leading!==!1||(h=j);var k=b-(j-h);return d=this,e=arguments,0>=k?(clearTimeout(g),g=null,h=j,f=a.apply(d,e)):g||c.trailing===!1||(g=setTimeout(i,k)),f}},g=a(b.document),h=function(a,b){if(a instanceof Array&&a.length>0&&b!==c)for(var d=0;d<a.length;d++)if(b===a[d])return!0;return!1};d.checkType=function(a,b){var c=typeof a,d=null===a&&b.nullable;return b.type instanceof Array?h(b.type,c)||d:c===b.type||d},d.prototype.set=function(b,e){var f,g,i=this._schema,j=e?{}:this.get(),k={};b=b||{};for(f in b){var l=b[f],m=i[f];if(m!==c){if(m.unchangeable&&!e)throw new Error('Option "'+f+'" could be setted once at the begining.');if(!d.checkType(l,m)){var n='Option "'+f+'" must be '+(m.type instanceof Array?m.type.join(", "):m.type)+(m.nullable?" or null.":".");throw new TypeError(n)}if(m.values&&!h(m.values,l))throw new RangeError('Option "'+f+'" only could be in these values: "'+m.values.join('", "')+'".')}}if(e)for(f in i)i[f]["default"]!==c&&(k[f]=i[f]["default"]);j=e?a.extend(k,b):b;for(f in b)(g=this._callbacks[f])&&(b[f]=g.call(this,b[f]));this._options=a.extend(this._options,j)},d.prototype.get=function(b){return b?this._options[b]:a.extend({},this._options)},d.prototype.on=function(a,b){this._callbacks[a]=b},d.prototype.off=function(a){this._callbacks[a]&&delete this._callbacks[a]};var i={filter:{"default":"> *",type:"string"},multi:{"default":!0,type:"boolean"},mouseMode:{"default":"standard",type:"string",values:["standard","mouseup","toggle"]},focusBlur:{"default":!1,type:"boolean"},selectionBlur:{"default":!1,type:"boolean"},handle:{"default":null,type:"string",nullable:!0},textSelection:{"default":!1,type:"boolean"},focusOnHover:{"default":!1,type:"boolean"},keyboard:{"default":!1,type:"boolean"},keyboardMode:{"default":"select",type:"string",values:["select","toggle"]},autoScroll:{"default":!0,type:["boolean","string"]},loop:{"default":!1,type:"boolean"},preventInputs:{"default":!0,type:"boolean"},listClass:{"default":"j-selectable",type:"string",unchangeable:!0},focusClass:{"default":"j-focused",type:"string",unchangeable:!0},selectedClass:{"default":"j-selected",type:"string",unchangeable:!0},disabledClass:{"default":"j-disabled",type:"string",unchangeable:!0},create:{"default":null,type:"function",nullable:!0},before:{"default":null,type:"function",nullable:!0},focusLost:{"default":null,type:"function",nullable:!0},select:{"default":null,type:"function",nullable:!0},unselect:{"default":null,type:"function",nullable:!0},unselectAll:{"default":null,type:"function",nullable:!0},stop:{"default":null,type:"function",nullable:!0},destroy:{"default":null,type:"function",nullable:!0}};e.pluginName="selectonic",e.keyCode={DOWN:40,UP:38,SHIFT:16,END:35,HOME:36,PAGE_DOWN:34,PAGE_UP:33,A:65,SPACE:32,ENTER:13},e.getDataObject=function(b){return a(b).data("plugin_"+e.pluginName)},e.prototype._init=function(){this.$el.addClass(this.options.get("listClass")),this._bindEvents(),this.$el.data("plugin_"+e.pluginName,this),this._callEvent("create")},e.prototype._setScrolledElem=function(b){var c;if(null===b||!1===b)return void delete this._scrolledElem;if("string"==typeof b){if(c=a(b),!(c.length>0))throw new Error('There are no elements that matches to selector - "'+b+'"');return void(this._scrolledElem=c[0])}this._scrolledElem=this.el},e.prototype._cancel=function(b,c){if(!c.wasCancelled){c.isCancellation=this._isPrevented=!0;var d=this;a.each(a(c.changedItems),function(e,f){c.prevItemsStates[e]?d._select(b,c,a(f),!0):d._unselect(b,c,a(f),!0)}),c.prevFocus&&this._setFocus(c.prevFocus),delete c.isCancellation,c.wasCancelled=!0}},e.prototype._bindEvents=function(){var a=this,b=this._name;this._mouseEvent=function(b){a._isEnable&&a._mouseHandler.call(a,b)},this._keyboardEvent=function(b){a.options.get("keyboard")&&a._isEnable&&a._keyHandler.call(a,b)},this._selectstartEvent=function(){return a.options.get("textSelection")?void 0:!1},this._mousemoveEvent=f(function(b){a._isEnable&&a.options.get("focusOnHover")&&a._mousemoveHandler.call(a,b)},20),g.on("keydown."+b,this._keyboardEvent),g.on("keyup."+b,this._keyboardEvent),g.on("mousemove."+b,this._mousemoveEvent),g.on("click."+b,this._mouseEvent),g.on("mousedown."+b,this._mouseEvent),this.$el.on("mouseup."+b,this._mouseEvent),this.$el.on("selectstart."+b,this._selectstartEvent)},e.prototype._unbindEvents=function(){var a=this._name;g.off("keydown."+a,this._keyboardEvent),g.off("keyup."+a,this._keyboardEvent),g.off("mousemove."+a,this._mousemoveEvent),g.off("click."+a,this._mouseEvent),g.off("mousedown."+a,this._mouseEvent),this.$el.off("mouseup."+a,this._mouseEvent),this.$el.off("selectstart."+a,this._selectstartEvent)},e.prototype._getTarget=function(c){for(var d,e,f,g=c.target,h=this.options.get("handle");null!==g&&g!==this.el;)d=a(g),d.context=b.document,d.is(this._itemsSelector)&&(e=g),h&&d.is(h)&&(f=g),g=g.parentNode;return h&&g&&f?e:!h&&g?e:null},e.prototype._getItems=function(c,d,e){var f;switch(d){case"next":case"prev":for(var g=e.jquery?e:a(e),h=a.fn[d];;){if(g=h.call(g),0===g.length)break;if(g.context=b.document,g.is(this._itemsSelector))return g}return null;case"pageup":case"pagedown":return this._getNextPageElem(c,d,e);case"first":return f=c.allItems?c.allItems:this.$el.find(this.options.get("filter")),c.allItems=f,f.first();case"last":return f=c.allItems?c.allItems:this.$el.find(this.options.get("filter")),c.allItems=f,f.last();default:return f=c.allItems?c.allItems:this.$el.find(this.options.get("filter")),c.allItems=f,f}},e.prototype._getNextPageElem=function(c,d,e){var f,g,h,i,j,k=c.isShiftPageRange,l=this._scrolledElem||this.el,m=l.clientHeight,n=a(b).outerHeight(),o=a(e),p=m>n,q=p?n:m,r=o.outerHeight(),s=r,t=r,u="pageup"===d?"prev":"next";for(k&&(u="pageup"===d?-1:1,i=this._getItems(c),c.rangeStart=h=i.index(e));;){if(k?(h+=u,j=h>=0?i.eq(h):null,f=j&&j.length>0?j:null):f=this._getItems(c,u,o),!f&&o.is(e))break;if(!f)return k&&(c.rangeEnd=h-u),o;if(g=f.outerHeight(),t+=g,t>q)return s+g>q?(k&&(c.rangeEnd=h),f):(k&&(c.rangeEnd=h-u),o);s=g,o=f}return null},e.prototype._callEvent=function(a,b,c){var d,e=this.options.get(a);if(e){if("create"===a||"destroy"===a)return e.call(this.$el);switch(d={},c.target&&(d.target=c.target),this.ui.focus&&(d.focus=this.ui.focus),a){case"select":d.items=c.selected;break;case"unselectAll":case"unselect":d.items=c.unselected;break;case"stop":c.wasCancelled||(d.items=c.changedItems)}e.call(this.$el,b||null,d)}},e.prototype._controller=function(a,b){var d;return b.changedItems=[],b.prevItemsStates=[],delete this._isPrevented,this._callEvent("before",a,b),this._isPrevented?(this._cancel(a,b),void this._stop(a,b)):(b.wasSelected=this._selected>0,b.target&&b.isTargetWasSelected===c&&(b.isTargetWasSelected=this._getIsSelected(b.target)),b.isRangeSelect&&b.isTargetWasSelected&&b.target===this.ui.focus||(b.isRangeSelect?this._perfomRangeSelect(a,b):b.isMultiSelect?(d=b.isTargetWasSelected?this._unselect:this._select,d.call(this,a,b,b.items)):b.target&&!b.items&&"mouseover"===a.type||(b.target&&b.items?(this._selected&&1===this._selected&&this._getIsSelected(this.ui.focus)?this._unselect(a,b,this.ui.focus,b.isTargetWasSelected):this._selected&&this._unselectAll(a,b),this._select(a,b,b.items,b.isTargetWasSelected)):!b.target&&this._selected>0&&this.options.get("selectionBlur")&&this._unselectAll(a,b))),!this._selected&&b.wasSelected&&this._callEvent("unselectAll",a,b),b.prevFocus=this.ui.focus?this.ui.focus:null,!b.target&&this.options.get("focusBlur")?this._blur(a,b):b.target&&!b.wasCancelled&&this._setFocus(b.target),void this._stop(a,b))},e.prototype._perfomRangeSelect=function(a,b){var c,d,e,f,g,h,i,j=b.rangeStart<b.rangeEnd,k=this._getItems(b),l=j?b.rangeStart:b.rangeEnd,m=j?b.rangeEnd:b.rangeStart;b.isNewSolidSelection?(d=k.slice(0,l),d=d.add(k.slice(m+1)),this._unselect(a,b,d),this._select(a,b,b.items)):this.ui.solidInitialElem&&!b.isTargetWasSelected&&(e=b.items.index(this.ui.solidInitialElem))>=0?(e=j?b.rangeStart+e:b.rangeEnd+e,f=e<b.rangeStart,g=b.rangeStart<e,h=e<b.rangeEnd,i=b.rangeEnd<e,(!h&&f||!i&&g)&&(d=g?k.slice(l,e):k.slice(e+1,m+1),d.length>0&&this._unselect(a,b,d)),(i&&!g||h&&!f)&&(d=i?k.slice(l,e):k.slice(e+1,m+1),d.length>0&&this._select(a,b,d))):(c=b.isTargetWasSelected?this._unselect:this._select,c.call(this,a,b,b.items))},e.prototype._changeItemsStates=function(b,c,d){var e=c>0,f=[],g=this;a(b).each(function(b,h){var i=g._getIsSelected(h),j=e?!i:i,k=h===d.target&&d.isTargetWasSelected;(!k||e||d.isMultiSelect||d.isRangeSelect)&&(j&&(d.isCancellation||(f.push(h),d.prevItemsStates.push(i)),g._selected+=c),a(h).toggleClass(g.options.get("selectedClass"),e))}),d.isCancellation||(d[e?"selected":"unselected"]=a(f),d.changedItems=d.changedItems.concat(f))},e.prototype._select=function(a,b,c,d){this._changeItemsStates(c,1,b),d||this._callEvent("select",a,b),this._isPrevented&&!b.isCancellation&&this._cancel(a,b)},e.prototype._unselect=function(a,b,c,d){this._changeItemsStates(c,-1,b),d||this._callEvent("unselect",a,b),this._isPrevented&&!b.isCancellation&&this._cancel(a,b)},e.prototype._unselectAll=function(a,b){var c,d;this._selected&&0!==this._selected&&(d=this._getItems(b),c=b.target&&b.isTargetWasSelected&&1===this._selected,this._unselect(a,b,d,c))},e.prototype._multiSelect=function(b){return b.isMultiSelect=!0,a(b.target)},e.prototype._rangeSelect=function(b){if(b.isRangeSelect=!0,b.target===this.ui.focus)return a(b.target);var c=b.allItems?b.allItems:this._getItems(b),d=c.index(b.target),e=c.index(this.ui.focus),f=e>d?c.slice(d,e):c.slice(e,d);return f.push(e>d?c[e]:c[d]),b.allItems=c,b.rangeStart=e,b.rangeEnd=d,f},e.prototype._getIsSelected=function(b){var c=this.options.get();return a(b).length<=1?a(b).hasClass(c.selectedClass):a.map(a(b),function(b){return a(b).hasClass(c.selectedClass)})},e.prototype._blur=function(b,c,d){!d&&this.ui.focus&&this._callEvent("focusLost",b,c),this.ui.focus&&(a(this.ui.focus).removeClass(this.options.get("focusClass")),delete this.ui.focus)},e.prototype._setFocus=function(b){return b?(this.ui.focus&&a(this.ui.focus).removeClass(this.options.get("focusClass")),this.ui.focus=b,a(this.ui.focus).addClass(this.options.get("focusClass")),this.ui.focus):void 0},e.prototype._stop=function(a,b){this._callEvent("stop",a,b),this._isPrevented&&this._cancel(a,b)},e.prototype._checkIfElem=function(b){var c;return b&&(b.jquery||b.nodeType)?(b=b.jquery?b:a(b),c=b.filter(this._itemsSelector),c.length>0?c:null):!1},e.prototype._checkIfSelector=function(a){var b;return a&&"string"==typeof a?(b=this.$el.find(a).filter(this._itemsSelector),b.jquery&&b.length>0?b:null):!1},e.prototype._keyHandler=function(b){if(this.options.get("keyboard")&&!(this.options.get("preventInputs")&&"INPUT"===b.target.tagName||"TEXTAREA"===b.target.tagName)){var c,d,f,g,h=b.which,i={};if("keyup"===b.type)return void(h===e.keyCode.SHIFT&&(delete this._shiftModeAction,delete this._keyModes.shift));if(h===e.keyCode.A&&this._isMulti(b)&&this.options.get("multi"))c=this._getItems(i),d=!0;else switch(h){case e.keyCode.DOWN:f="next",c=this._findNextTarget("next",i);break;case e.keyCode.UP:f="prev",c=this._findNextTarget("prev",i);break;case e.keyCode.HOME:f="prev",c=this._getItems(i,"first");break;case e.keyCode.END:f="next",c=this._getItems(i,"last");break;case e.keyCode.PAGE_DOWN:case e.keyCode.PAGE_UP:var j=h===e.keyCode.PAGE_DOWN;f=j?"next":"prev",g=j?"pagedown":"pageup",i.isShiftPageRange=this.options.get("multi")&&b.shiftKey&&!d,c=this._findNextTarget(g,i);break;case e.keyCode.SPACE:c=a(this.ui.focus);break;case e.keyCode.ENTER:this.options.get("multi")||(c=a(this.ui.focus))}c&&c.length>0?(b.preventDefault(),i.target=c[0],i.items=c,"toggle"===this.options.get("keyboardMode")?(h===e.keyCode.SPACE||h===e.keyCode.ENTER&&!this.options.get("multi")||delete i.items,this.options.get("multi")&&(i.isMultiSelect=!0),delete this.ui.solidInitialElem):this.ui.focus&&this.options.get("multi")&&b.shiftKey&&!d?(h===e.keyCode.END||h===e.keyCode.HOME||h===e.keyCode.PAGE_UP||h===e.keyCode.PAGE_DOWN?this._rangeVariator(i):this._multiVariator(i,h,f,c),this.ui.solidInitialElem||i.target===this.ui.focus||(this.ui.solidInitialElem=this.ui.focus,i.isNewSolidSelection=!0),this._shiftModeAction||(this._shiftModeAction="select"),this._keyModes.shift||(this._keyModes.shift=h)):delete this.ui.solidInitialElem,this._controller(b,i),this.scroll()):(i.prevItemsStates=[],this._callEvent("before",b,i),this._callEvent("stop",b,i))}},e.prototype._rangeVariator=function(a){var b=void 0===a.isFocusSelected?this._getIsSelected(this.ui.focus):a.isFocusSelected,c=a.isTargetWasSelected=this._getIsSelected(a.target);b||c?(a.items=this._rangeSelect(a),c&&(a.items=a.rangeStart<a.rangeEnd?a.items.slice(0,a.items.length-1):a.items.slice(1))):(a.target=a.items=this.ui.focus,a.isMultiSelect=!0)},e.prototype._multiVariator=function(a,b,c,d){var e,f=void 0===a.isFocusSelected?this._getIsSelected(this.ui.focus):a.isFocusSelected,g=this._getIsSelected(a.target),h=this._getItems(a,c,d),i=this._getIsSelected(h);if(this._keyModes.shift&&this._keyModes.shift!==b&&(this._keyModes.shift=this._shiftModeAction=null),this._keyModes.shift&&"select"===this._shiftModeAction&&g){for(;this._getIsSelected(a.items)&&a.items.length>0;)e=a.items,a.items=this._getItems(a,c,a.items);a.target=a.items?a.items:e}else g&&f&&!i?(this._keyModes.shift=this._shiftModeAction=null,a.items=this.ui.focus):f&&g?(a.items=this.ui.focus,this._shiftModeAction||(this._shiftModeAction="unselect")):f||(a.target=a.items=this.ui.focus);a.isMultiSelect=!0},e.prototype._findNextTarget=function(a,b){var c="next"===a||"pagedown"===a?"first":"last",d=this.ui.focus?this._getItems(b,a,this.ui.focus):this._getItems(b,c);return null!==d&&0!==d.length||!this.options.get("loop")||(d=this._getItems(b,c)),d},e.prototype._refreshBoxScroll=function(c){var d=a(c),e=c===b,f=e?d.outerHeight():c.clientHeight,g=d.scrollTop(),h=e?0:d.offset().top,i=a(this.ui.focus),j=i.outerHeight(),k=e?i.offset().top:i.offset().top-h+g;g>k?d.scrollTop(k):k+j>g+f&&d.scrollTop(k+j-f)},e.prototype._isRange=function(a){return a.shiftKey||a.shiftKey&&a.ctrlKey||a.shiftKey&&a.metaKey},e.prototype._isMulti=function(a){return a.ctrlKey||a.metaKey},e.prototype._mouseHandler=function(b){var c,d=this.options.get(),e=b.type,f=this._isMulti(b),g=this._isRange(b),h={};if("mouseup"===d.mouseMode){if("mouseup"!==e)return"mousedown"===e||(c=this._getTarget(b))?void 0:void 0;c=this._getTarget(b)}else{if("click"===e&&!this._mousedownOnItem)return;if("mousedown"!==e&&"click"!==e)return;if(c=this._getTarget(b),"mousedown"===e&&c&&(!d.multi||!f&&!g||"standard"!==d.mouseMode))return void(this._mousedownOnItem=c);delete this._mousedownOnItem}h.target=c,d.multi&&h.target&&(g&&this.ui.focus?h.items=this._rangeSelect(h):(f||"toggle"===d.mouseMode)&&(h.items=this._multiSelect(h))),h.target&&!h.items&&(h.items=a(h.target)),delete this.ui.solidInitialElem,this._controller(b,h)},e.prototype._mousemoveHandler=function(a){if(!this._isFocusOnHoverPrevented){var b,c={};b=this._getTarget(a),b?(delete this.ui.solidInitialElem,this._isHovered=!0,b!==this.ui.focus&&(c.target=b,this._controller(a,c))):this._isHovered&&(this._isHovered=!1,this._controller(a,c))}},e.prototype._preventMouseMove=function(){var a=this;this._isFocusOnHoverPrevented=!0,this._focusHoverTimeout&&(clearTimeout(this._focusHoverTimeout),delete this._focusHoverTimeout),this._focusHoverTimeout=setTimeout(function(){delete a._isFocusOnHoverPrevented,delete a._focusHoverTimeout},250)},e._callPublicMethod=function(b){var c,d,f=e.getDataObject(this);if(null===f||void 0===f)throw new Error("Element "+this[0]+" has no plugin "+e.pluginName);if(f[b]&&a.isFunction(f[b])&&(c=f[b]),c&&a.isFunction(c)&&"_"!==b.charAt(0))return d=Array.prototype.slice.call(arguments),d.shift(),c.apply(f,d);throw new Error('Plugin "'+e.pluginName+'" has no method "'+b+'"')},e.prototype.isEnabled=function(){return this._isEnable},e.prototype.option=function(b,c){var d=arguments.length;if(d>0&&"string"==typeof b){if(d>1){var e={};return e[b]=c,this.options.set(e),this.$el}return this.options.get(b)}if(d>0&&a.isPlainObject(b))return this.options.set(b),this.$el;if(0===d)return this.options.get();throw new Error('Format of "option" could be: "option" or "option","name" or "option","name",val or "option",{}')},e.prototype.destroy=function(){this._callEvent("destroy"),this._unbindEvents(),this._focusHoverTimeout&&clearTimeout(this._focusHoverTimeout),this.ui.focus&&(a(this.ui.focus).removeClass(this.options.get("focusClass")),delete this.ui.focus),this._selected>0&&this.getSelected().removeClass(this.options.get("selectedClass")),this.$el.removeClass(this.options.get("disabledClass")),this.$el.removeClass(this.options.get("listClass")),this.options.off(),delete this.options,delete this._scrolledElem,delete this.ui.solidInitialElem,this.$el.removeData("plugin_"+e.pluginName),this.$el=null},e.prototype.select=function(b){var c;if(c=this._checkIfElem(b),c===!1&&(c=this._checkIfSelector(b)),c===!1)throw new Error('You shold pass DOM element or selector to "select" method.');return c&&(delete this.ui.solidInitialElem,this._controller(null,{items:c.addClass?c:a(c),target:c[0]||c})),this.$el},e.prototype.blur=function(){return this._controller(null,{target:null}),this.$el},e.prototype.getSelected=function(a){var b,c=this._getItems({}).filter("."+this.options.get("selectedClass"));if(a){b=[];for(var d=0;d<c.length;d++)b.push(c[d].id||null);return b&&b.length>0?b:null}return c},e.prototype.getSelectedId=function(){return this.getSelected(!0)},e.prototype.focus=function(a){var b;if(arguments.length>0){if(b=(b=this._checkIfElem(a))===!1?this._checkIfSelector(a):b,b&&b.jquery)this._setFocus(b[0]);else if(b===!1)throw new Error("You shold pass DOM element or CSS selector to set focus or nothing to get it.");return this.$el}return this.ui.focus?this.ui.focus:null},e.prototype.scroll=function(){this._preventMouseMove(),this.ui.focus&&(this._scrolledElem&&this._refreshBoxScroll(this._scrolledElem),this._refreshBoxScroll(b))},e.prototype.enable=function(){return this._isEnable=!0,this.$el.removeClass(this.options.get("disabledClass")),this.$el},e.prototype.disable=function(){return this._isEnable=!1,this._isHovered=!1,this.$el.addClass(this.options.get("disabledClass")),this.$el},e.prototype.cancel=function(){return this._isPrevented=!0,this.$el},e.prototype.refresh=function(){var b=this.ui.focus;return b&&!a(b).is(":visible")&&delete this.ui.focus,this._selected=this.getSelected().length,this.$el},a.fn[e.pluginName]=function(a){return a&&a.charAt?e._callPublicMethod.apply(this,arguments):this.each(function(b,c){e.getDataObject(c)||new e(c,a)})}}(jQuery,window);
//# sourceMappingURL=selectonic.min.map