define(function (require) {
    'use strict';

    var AbstractModel = require('./Abstract');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.Content
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} ContentValues
     */
    var ContentModel = function (ContentValues) {
        AbstractModel.call(this, ContentValues);
    };

    var proto = inherits(ContentModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} ContentValues
     * @chainable
     */
    proto.init = function(ContentValues) {

        /**
         * @property target
         * @default {null}
         * @type {string}
         */
        this.target = null;

        /**
         * @property type
         * @default {null}
         * @type {string}
         */
        this.type = null;

        /**
         * @property ipsname
         * @default {null}
         * @type {string}
         */
        this.value = null;

        /**
         * @property text
         * @default {null}
         * @type {string}
         */
        this.text = null;


        // run the parent init method to parse determine the data type
        base.init.call(this, ContentValues);

        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param json the original JSON data.
     */
    proto.fromJSON = function(json) {
        this.target = json.target;
        this.type = json.type;
        this.label = json.label;
        this.value = json.value;
        this.text = json._;
    };

    return ContentModel;
});
