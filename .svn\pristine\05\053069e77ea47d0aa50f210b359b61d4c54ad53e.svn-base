define(function (require) {
    'use strict';

    var $ = require('jquery');
    var AbstractProvider = require('./Abstract');
    var CustomerProfileModel = require('../models/CustomerProfile');
    var Settings = require ('../constants/Settings');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Providers.CustomerProfile
     * @extends App.Providers.Abstract
     *
     * @constructor
     */
    var CustomerProfileProvider = function (baseUrl, config) {
        bindAll(this, '_onResponseReceived');

        AbstractProvider.call(this, baseUrl, config);
    };

    var proto = inherits(CustomerProfileProvider, AbstractProvider);

    /**
     * Makes a call for the customer's profile, or returns the stored result.
     * The B2B model should use endUserProfileId first, else profileId.
     *
     * @method getProfile
     *
     * @param {string} profileId
     * @param {string} sessionId
     * @param {bool} flushCache
     *
     * @return {Promise}
     */
    proto.getProfile = function(profileId, sessionId, flushCache) {
        var params;
        var config = this.config;

        if (this.promise && !flushCache) {
            return this.promise;
        }

        if (!profileId) {
            throw new Error('missing required parameters');
        }

        //this.setBaseUrl(Settings.SITE_HOST);
        this.setBaseUrl('');

        params = {
            profile: profileId
        };

        this.promise = this
            .get(Settings.SVC_PROFILE, params)
            .then(this._onResponseReceived)
            .fail(this._onError);

        return this.promise;
    };

    /**
     * Handles the response from the ajax call
     *
     * @method _onResponseReceived
     * @param {HTMLDocument|Document} data
     * @private
     */
    proto._onResponseReceived = function(data) {
        // success! we have the data, store and resolve the promise;
        var model = new CustomerProfileModel(data);

        if (!data) {
            throw new Error(data);
        }

        return model;
    };


    return new CustomerProfileProvider();
});
