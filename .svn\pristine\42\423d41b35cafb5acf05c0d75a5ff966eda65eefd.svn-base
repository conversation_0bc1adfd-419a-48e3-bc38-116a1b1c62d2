/*jshint camelcase: false, maxlen: false */
define(function() {
    'use strict';

    /**
     * United States English content localizations.
     *
     * @class App.Constants.Localization.EN_US.Content
     * @static
     */
    var Content = {
        // -- Custom values ----------------------------------------------------

        ADDRESS_LINE_1: 'Address Line 1',
        ADDRESS_LINE_2: 'Address Line 2',
        BANK_NAME: 'Bank Name',
        CITY: 'City, State, Zip',
        
        // BI_1: 'Bank Name',
        // BI_2: 'Address Line 1',
        // BI_3: 'Address Line 2',
        // BI_4: 'City, State, Zip',
        // BI_5: 'Phone Number',
        
        BI_1: 'Bank Name',
        BI_2: 'Bank Address',
        BI_3: 'Bank City, State, Zip',

        CI_ML_1: 'Business Name 1',
        CI_ML_2: 'Business Name 2',
        CI_ML_3: 'Business Name 3',
        FONT_HEADING: 'Font of Company Name',
        INNER_TEXT: 'Updates to Font Type will show in the design, but may take a few seconds to display.',
        LOGO_ADDLOGO: 'Add Logo',
        LOGO_CUSTOM: 'Upload Options',
        LOGO_STANDARD: 'Change',
        LOGO_LOGOMX: 'Try Another',
        MATRIX1_PREMIUM_LABEL: 'Premium Designs',
        MATRIX1_PREMIUM_CHARITY_LABEL: 'Premium and Charitable Designs (+{{value}}):',
        MATRIX1_STANDARD_LABEL: 'Standard Designs',
        MATRIX2_STANDARD_LABEL: 'Color / Material:',
        STATE: 'State',
        ZIP: 'Zip Code',
        DEFAULT_COLOR: 'Most text will appear {{color}} by default.',
        FRONT_COLOR: 'Text on front of card will be printed in {{color}} {{ink}}',
        INK_2_SUBHEADING: 'Color',
        ROUTING_NUMBER_MAX_LENGTH: 9,
        ACCOUNT_NUMBER_MAX_LENGTH: '1-15',

        // -- Question overrides (by id) ---------------------------------------

        layout_ST_1: 'Voucher 1',
        layout_SH_1: 'Voucher 2',
        layout_LS_1: 'Text Above Signature Line',
        layout_SL_1: 'Add Another Signature Line',
        BI_BI_1: 'Bank Name',
        BI_BI_2: 'Address Line 1',
        BI_BI_4_city: 'City',
        BI_BI_4_state: 'State',
        BI_BI_4_zipCode: 'Zip Code',

        // -- Zoombox Label overrides --------------------------------------------------

        'zbox_routingNumber': 'Routing Number',
        'zbox_accountNumber': 'Account Number',
        'zbox_numbering': 'Start Number',
        'zbox_ST': 'Voucher 1',
        'zbox_SH': 'Voucher 2',

        // -- Label overrides --------------------------------------------------

        'Account Number': 'Account Number ({{range}} digits)',
        'Add To Cart': 'Add To Cart',
        'FBT Add To Cart': 'ADD TO CART & <br> CONTINUE',
        'KIT Add To Cart': 'CONTINUE',
        // 'skipNextPdt' : 'No thanks.&nbsp;Skip to next product.',
        'FBT Summary': 'ADD TO CART & <br> SEE SUMMARY',
        'KIT Summary': 'ADD TO CART',
        // 'skipToSummary' : 'No thanks.&nbsp;Skip to summary. &raquo',
        'BLACK': 'Black',
        'Business Name or Main Line of Text': 'Company Name',
        'CUSTOM': 'Upload Your Logo {{value}}',
        'Ink Color 1': 'Standard Color',
        'Ink Color 2': 'Extra Color (+{{value}})',
        'Logo': 'Logo',
        'NO LOGO': 'No Logo',
        'Prev Step': 'Previous Step',
        'Next Step': 'Next Step',
        'Numbering': 'Start Number ({{range}} digits)',
        'numbering': 'Start Number ({{range}} digits)',
        'Part': 'Copies',
        'Advertising Slogan (eg. 25 Years of Service).': 'Slogan (ex: 25 Years of Service)',
        //'Advertising Slogan (eg. 25 Years of Service).': 'Address Line 1',
        'P.O. Box or Street Address': 'Address Line 1',
        //'P.O. Box or Street Address': 'Address Line 2',
        'Phone, Fax or Email (Type "Fax" before Fax Number)': 'Phone, Fax or Email',
        'Previous Step': 'Previous',
        'Product ID': 'Copies',
        'Quantity': 'Quantity',
        'ReverseNumbering': 'Reverse Start Number Order',
        'reverseNumbering': 'Reverse Start Number Order',
        'Routing Number': 'Routing Number ({{range}} digits)',
        'ADDLOGO': 'Add a Logo',
        'STANDARD': 'Use Free Clip Art',
        'LOGOMX': 'Design free using Logo Maker',
        'Typestyle': 'Font:',
        'Second Ink Color Charge': 'Additional Color',
        'Premier Colors Laser Checks Charge': 'Premium Design',
        'Custom Logo Charge': 'Custom Logo',
        'Custom Logo w/ Chng': 'Custom Logo',
        'Custom Logo Charge (Reuse or with Change)': 'Custom Logo',
        'Custom Logo Charge (Reuse/or with Change)': 'Custom Logo',
        'Return Address Area': 'Return Address',
        'DIECUT_DESC': 'Die Cut Window',
        'DIECUT_DETAILS': 'These lines of text show through the window cut out of the front of the card.',
        'folding': 'Add Folding Services {{value}}',
        'Signatures': 'Add Signature(s) {{value}}',
        'ENVELOPE_RETURN': 'Print Return Address on Envelopes',
        'Folding Charge': 'Folding Service',
        'ENVPS': 'Envelope Upgrade  ',
        'ENVIM': 'Envelope Imprinting  ',
        'FOLD': 'Folding Charge ',
        'Foil Imprinting Charge': 'Text Color',
        'Ultimate Personalization': 'Ultimate Personalization package upgrade ',
        'Envelope': 'Envelope Options',
        'FBT_TITLE': 'Product Customizer',
        'FBT_SUB_TITLE': 'We\'ll Create them in this Order',
        'FBT_SUMMARY_TITLE': 'Customization Summary',
        'FBT_NEXT_TITLE': 'GENERATING NEXT PROOF',
        'FBT_HYBRID_TITLE': 'REVIEW PROOF',
        'FBT_PROOF_EDIT': 'Go to editor',
        'FBT_GOOD_HELP': 'Proof looks good!',
        'FBT_BAD_HELP': 'Proof needs editing.',
        'REORDER_STANDARD': 'Replace with Clipart',
        'REORDER_CUSTOM': 'Replace with Custom Logo {{value}}',
        
        // 'ADA Form Phone #': '',
        // 'Additional Signature Line': '',
        // 'Bank Name or Bank Address or Bank City/State': '',
        // 'Business Name, Title or Other Text': '',
        // 'City, State, Zip': '',
        // 'Claim Form Phone #': '',
        // 'Column Heading For Deduction Information': '',
        // 'Continuation Line': '',
        // 'Custom Line': '',
        // 'Descriptive or Advertising Line': '',
        // 'Doctor\'s Name': '',
        // 'E-Mail Address': '',
        // 'Effective Date': '',
        // 'Employee Name': '',
        // 'Employee Title (eg. President, etc.)': '',
        // 'Extra Lines of Text': '',
        // 'Forms Title': '',
        // 'I.D. #': '',
        // 'I.D. #, Serial # or Lic.#': '',
        // 'IMBC Code Line': '',
        // 'Lic. #': '',
        // 'Line 1 text': '',
        // 'Line 2 text': '',
        // 'MISC. BLOCKS': '',
        // 'MOTOR VEHICLE INDENTIFICATION NUMBER': '',
        // 'Number': '',
        // 'P.O. Box, Street Address, City-State-Zip, etc.': '',
        // 'Payment Terms': '',
        // 'Permit Number': '',
        // 'Privacy Officer': '',
        // 'Special Wording on Back of Form': '',
        // 'Special Wording': '',
        // 'State Where Shipping Company Resides': '',
        // 'Subheading': '',
        // 'T.I.N.': '',
        // 'Text in the Side Margin': '',

        //-- External content overrides (by target) ----------------------------

        'customLogoNotDisplay': '<span class="hdg hdg_h4">Please Note:</span>Custom logos are not displayed on the product preview.',
        'LogoLegalDisclaimerText': ' By clicking "Browse for a File," I confirm I have all the appropriate and necessary right and authority to use the uploaded text, images and/or designs and my use will not infringe on any intellectual property right of any other third party.',
        'uploadLogo': '<h3 class="hdg hdg_h4">How do I get my logo onto my phone or tablet?</h3> <span class="txtStd"> Is your logo available from a social media or cloud storage service?  </span> <ul class="vList vList_bullet"> <li>Your Website</li> <li>Facebook</li> <li>LinkedIn</li> <li>Dropbox</li> <li>Google Drive</li> </ul> <span class="txtStd"> Once you locate the logo, follow these steps to access it on your device </span> <ol class="vList vList_ordered"> <li>Download the Logo. (Try holding your finger on it for a few seconds)</li> <li>Save it to the device storage.</li> <li> <div class="vList-holdRight"> <p class="txtStd">Come back here and click:</p> {{> logo/browse}} </div> </li> <li>Find your Logo in device storage.</li> </ol>',

        DEPOSIT_WARNING_SKU: ['DLT600', 'DLT601', 'DLM600', 'DLM601', 'DLM602'],
        DEPOSIT_WARNING_HEADING: 'This is not a check',
        DEPOSIT_WARNING_MSG: 'This product you are about to configure is an Advice of Deposit form, which shows information for a Direct Deposit transaction. It is NOT a check.',
        DEPOSIT_WARNING_ACTIONS: {
            accept: {
                url: '#',
                text: 'CONTINUE'
            },
            cancel: {
                url: '/shopdeluxe/business-checks/cat80024/t1/?pscid=SD:T1_LNV0_cat80024',
                text: 'SHOP FOR CHECKS INSTEAD'
            }
        },
        DEPOSIT_WARNING_ACTIONS_DFS: {
            accept: {
                url: '#',
                text: 'CONTINUE'
            },
            cancel: {
                url: '/dfs/cl/business-checks-banking-products/business-checks/_/N-1sm9oo8?pscid=DFS:GBL_BusCks_BusinessChecks',
                text: 'SHOP FOR CHECKS INSTEAD'
            }
        },
        DEPOSIT_WARNING_ACTIONS_4PRINTING: {
            accept: {
                url: '#',
                text: 'CONTINUE'
            },
            cancel: {
                url: '/business-checks-banking-products/business-checks?s_facet_search=business-checks',
                text: 'SHOP FOR CHECKS INSTEAD'
            }
        },
       
        PROOF_MODEL_HEADING: 'Download Online Proof',
        PROOF_MODEL_MSG1: 'Avoid starting over!',
        PROOF_MODEL_MSG2: 'To retrieve this configured product later:' ,
        PROOF_MODEL_MSG3: 'I understand how to retrieve this configured product later.' ,
        PROOF_MODEL_ACTIONS: {
            //check: {
                //text: 'I understand that if I don\'t <span>\"Add to Cart\"</span> then <span>\"Save Order for Later\"</span>. I will not be able to retrieve my proof online'
            //},
            download: {
                text: 'Download PDF Proof',
                url: '#',
            },
            cancel: {
                url: '#',
                text: 'Cancel'
            },
        },

        //Branded Copy
        Deluxe_COPY_FOLDING: 'Save time and have us fold your cards!  Otherwise, cards are shipped unfolded.  Charge is $20 for up to 475 cards; $0.05 per card for 500 or more cards.',
        Deluxe_COPY_ENVELOPE_IMPRINT: 'Print your address on the back top-flap of your envelopes.  Imprint color will match the foil or ink color you selected for the card verse and company imprint.',
        Deluxe_PROOF_SAVE: 'Not Enabled.  Add copy if enabled.',
        Deluxe_COPY_REVIEW: 'I acknowledge that I have reviewed the information provided to personalize the product for accuracy. I approve this item for printing.',

        DFS_COPY_FOLDING: 'Save time and have us fold your cards!  Otherwise, cards are shipped unfolded.  Charge is {{value}} for up to 100 cards; ${{unitValue}} per card for 125 or more cards.',
        DFS_COPY_ENVELOPE_IMPRINT: 'Print your address on the back top-flap of your envelopes.  Imprint color will match the foil or ink color you selected for the card verse and company imprint.  Charge is $10 for up to 100 cards; $0.10 per card for 125 or more cards.',
        DFS_PROOF_SAVE: 'Save a .pdf to share with your customers.',
        DFS_COPY_REVIEW: 'I acknowledge that I have reviewed the information provided to personalize the product for accuracy. I approve this item for printing.',

        Staples_COPY_FOLDING: 'Save time and have us fold your cards!  Otherwise, cards are shipped unfolded.  Charge is $10 for up to 100 cards; $0.10 per card for 125 or more cards.',
        Staples_COPY_ENVELOPE_IMPRINT: 'Print your address on the back top-flap of your envelopes.  Imprint color will match the foil or ink color you selected for the card verse and company imprint.  Charge is $10 for up to 100 cards; $0.10 per card for 125 or more cards.',
        Staples_PROOF_SAVE: 'Save a .pdf for future reference.',
        Staples_COPY_REVIEW: 'I acknowledge that I have reviewed the information provided to personalize the product for accuracy. I approve this item for printing.',

        PrintEZ_COPY_FOLDING: 'Save time and have us fold your cards!  Otherwise, cards are shipped unfolded.  Charge is {{value}} for up to 100 cards; ${{unitValue}} per card for 125 or more cards.',
        PrintEZ_COPY_ENVELOPE_IMPRINT: 'Print your address on the back top-flap of your envelopes.  Imprint color will match the foil or ink color you selected for the card verse and company imprint.  Charge is $10 for up to 100 cards; $0.10 per card for 125 or more cards.',
        PrintEZ_PROOF_SAVE: 'Save a .pdf to share with your customers.',
        PrintEZ_COPY_REVIEW: 'I acknowledge that I have reviewed the information provided to personalize the product for accuracy. I approve this item for printing.',

        Polaroid_COPY_FOLDING: 'Save time and have us fold your cards!  Otherwise, cards are shipped unfolded.  Folding charge is {{value}}.',
        Polaroid_COPY_ENVELOPE_IMPRINT: 'Print your address on the back top-flap of your envelopes.  Imprint color will match the foil or ink color you selected for the card verse and company imprint.  Envelope imprinting charge is $15.',
        Polaroid_PROOF_SAVE: 'Save a .pdf to share with your customers.',
        Polaroid_COPY_REVIEW: 'I have reviewed my design.  Approve for print and acknowledge that it cannot be cancelled/modified after submission.',

        MSBC_COPY_FOLDING: 'Save time and have us fold your cards!  Otherwise, cards are shipped unfolded.  Charge is $10 for up to 100 cards; $0.10 per card for 125 or more cards.',
        MSBC_COPY_ENVELOPE_IMPRINT: 'Print your address on the back top-flap of your envelopes.  Imprint color will match the foil or ink color you selected for the card verse and company imprint.  Charge is $10 for up to 100 cards; $0.10 per card for 125 or more cards.',
        MSBC_PROOF_SAVE: 'Save a .pdf for future reference.',
        MSBC_COPY_REVIEW: 'I acknowledge that I have reviewed the information provided to personalize the product for accuracy. I approve this item for printing.',

        'SIG WHITE': {
            IMG_SM:'paper_sig_white_sm',
            IMG_LG:'paper_sig_white_lg',
            SIZE:'12 pt.',
            FEEL:'Standard card stock',
            COATING:'Coated on one side',
            PHOTOS:'Semi-Gloss finish',
            REC:false,
            COPY:'This card stock is the lighter version of our 130 lb. Premium White paper. It is coated on one side. Photo reproduction, if applicable, will result in a semi-gloss finish.'
        },
        'PREMIUM WHITE': {
            IMG_SM:'paper_premium_white_sm',
            IMG_LG:'paper_premium_white_lg',
            SIZE:'14 pt.',
            FEEL:'Heavy duty substantial stock',
            COATING:'Coated on one side',
            PHOTOS:'Semi-Gloss finish',
            REC:true,
            COPY:'This card stock is made from heavy duty paper with a substantial feel. It is coated on one side. Photo reproduction, if applicable, will result in a semi-gloss finish.'
        },
        'RECYCLED WHITE': {
            IMG_SM:'paper_rec_white_sm',
            IMG_LG:'paper_rec_white_lg',
            SIZE:'100 lb.',
            FEEL:'Standard card stock',
            COATING:'Coated on one side',
            PHOTOS:'Semi-Gloss finish',
            REC:false,
            COPY:'This substantial card stock is made from 100% post-consumer fibers and may contain some natural flecking. It is uncoated on both sides. Photo reproduction, if applicable, will result in a matte finish.'
        },
        'WHITE LINEN': {
            IMG_SM:'paper_white_linen_sm',
            IMG_LG:'paper_white_linen_lg',
            SIZE:'80 lb.',
            FEEL:'Linen Fabric',
            COATING:'Both sides uncoated',
            PHOTOS:'Not Recommended',
            REC:false,
            COPY:'This card stock is made with the distinctive look and feel of linen fabric. It is uncoated on both sides. Not recommended for photo reproduction.'
        },
        'WHITE FELT': {
            IMG_SM:'paper_white_felt_sm',
            IMG_LG:'paper_white_felt_lg',
            SIZE:'80 lb.',
            FEEL:'Texture Fabric',
            COATING:'Both sides uncoated',
            PHOTOS:'Canvas finish',
            REC:false,
            COPY:'This card stock is made with characteristics of textured fabric. It is uncoated on both sides. Photo reproduction, if applicable, will result in a canvas finish.'
        },
        'PEARL SHIMMER': {
            IMG_SM:'paper_pearl_shimmer_sm',
            IMG_LG:'paper_pearl_shimmer_lg',
            SIZE:'98 lb.',
            LOOK:'Luminous with subtle sparkle',
            COATING:'Coated on both side',
            PHOTOS:'Luster finish',
            REC:false,
            COPY:'This card stock is luminous with subtle sparkle. It is coated on both sides. Photo reproduction, if applicable, will result in a luster finish.'
        },

        //-- Locational --------------------------------------------------------
        STATE_OPTIONS:[
            { name: '0', abbreviation: '--Select--', value: '' },
            { name: 'Alabama', abbreviation: 'AL', value: 'AL' },
            { name: 'Alaska', abbreviation: 'AK', value: 'AK' },
            { name: 'American Samoa', abbreviation: 'AS', value: 'AS' },
            { name: 'Arizona', abbreviation: 'AZ', value: 'AZ' },
            { name: 'Arkansas', abbreviation: 'AR', value: 'AR' },
            { name: 'California', abbreviation: 'CA', value: 'CA' },
            { name: 'Colorado', abbreviation: 'CO', value: 'CO' },
            { name: 'Connecticut', abbreviation: 'CT', value: 'CT' },
            { name: 'Delaware', abbreviation: 'DE', value: 'DE' },
            { name: 'District Of Columbia', abbreviation: 'DC', value: 'DC' },
            { name: 'Florida', abbreviation: 'FL', value: 'FL' },
            { name: 'Georgia', abbreviation: 'GA', value: 'GA' },
            { name: 'Guam', abbreviation: 'GU', value: 'GU' },
            { name: 'Hawaii', abbreviation: 'HI', value: 'HI' },
            { name: 'Idaho', abbreviation: 'ID', value: 'ID' },
            { name: 'Illinois', abbreviation: 'IL', value: 'IL' },
            { name: 'Indiana', abbreviation: 'IN', value: 'IN' },
            { name: 'Iowa', abbreviation: 'IA', value: 'IA' },
            { name: 'Kansas', abbreviation: 'KS', value: 'KS' },
            { name: 'Kentucky', abbreviation: 'KY', value: 'KY' },
            { name: 'Louisiana', abbreviation: 'LA', value: 'LA' },
            { name: 'Maine', abbreviation: 'ME', value: 'ME' },
            { name: 'Marshall Islands', abbreviation: 'MH', value: 'MH' },
            { name: 'Maryland', abbreviation: 'MD', value: 'MD' },
            { name: 'Massachusetts', abbreviation: 'MA', value: 'MA' },
            { name: 'Michigan', abbreviation: 'MI', value: 'MI' },
            { name: 'Minnesota', abbreviation: 'MN', value: 'MN' },
            { name: 'Mississippi', abbreviation: 'MS', value: 'MS' },
            { name: 'Missouri', abbreviation: 'MO', value: 'MO' },
            { name: 'Montana', abbreviation: 'MT', value: 'MT' },
            { name: 'Nebraska', abbreviation: 'NE', value: 'NE' },
            { name: 'Nevada', abbreviation: 'NV', value: 'NV' },
            { name: 'New Hampshire', abbreviation: 'NH', value: 'NH' },
            { name: 'New Jersey', abbreviation: 'NJ', value: 'NJ' },
            { name: 'New Mexico', abbreviation: 'NM', value: 'NM' },
            { name: 'New York', abbreviation: 'NY', value: 'NY' },
            { name: 'North Carolina', abbreviation: 'NC', value: 'NC' },
            { name: 'North Dakota', abbreviation: 'ND', value: 'ND' },
            { name: 'Northern Mariana Islands', abbreviation: 'MP', value: 'MP' },
            { name: 'Ohio', abbreviation: 'OH', value: 'OH' },
            { name: 'Oklahoma', abbreviation: 'OK', value: 'OK' },
            { name: 'Oregon', abbreviation: 'OR', value: 'OR' },
            { name: 'Palau', abbreviation: 'PW', value: 'PW' },
            { name: 'Pennsylvania', abbreviation: 'PA', value: 'PA' },
            { name: 'Puerto Rico', abbreviation: 'PR', value: 'PR' },
            { name: 'Rhode Island', abbreviation: 'RI', value: 'RI' },
            { name: 'South Carolina', abbreviation: 'SC', value: 'SC' },
            { name: 'South Dakota', abbreviation: 'SD', value: 'SD' },
            { name: 'Tennessee', abbreviation: 'TN', value: 'TN' },
            { name: 'Texas', abbreviation: 'TX', value: 'TX' },
            { name: 'Utah', abbreviation: 'UT', value: 'UT' },
            { name: 'Vermont', abbreviation: 'VT', value: 'VT' },
            { name: 'Virgin Islands', abbreviation: 'VI', value: 'VI' },
            { name: 'Virginia', abbreviation: 'VA', value: 'VA' },
            { name: 'Washington', abbreviation: 'WA', value: 'WA' },
            { name: 'West Virginia', abbreviation: 'WV', value: 'WV' },
            { name: 'Wisconsin', abbreviation: 'WI', value: 'WI' },
            { name: 'Wyoming', abbreviation: 'WY', value: 'WY' },
            { name: 'Federated States Of Micronesia', abbreviation: 'FM', value: 'FM' }
            // Disabled 4/16/2014
            // { name: 'Armed Forces Africa', abbreviation: 'AE', value: 'AE' },
            // { name: 'Armed Forces Americas', abbreviation: 'AA', value: 'AA' },
            // { name: 'Armed Forces Canada', abbreviation: 'AE', value: 'AE' },
            // { name: 'Armed Forces Europe', abbreviation: 'AE', value: 'AE' },
            // { name: 'Armed Forces Middle East', abbreviation: 'AE', value: 'AE' },
            // { name: 'Armed Forces Pacific', abbreviation: 'AP', value: 'AP' }
        ],

        BROWSE_DIFFERENT_LOGO:"Upload a Different Logo"
    };

    return Content;
});
