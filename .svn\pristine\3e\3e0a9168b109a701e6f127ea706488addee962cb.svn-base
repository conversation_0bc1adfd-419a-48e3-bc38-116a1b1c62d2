define(function (require) {
    'use strict';

    var $ = require('jquery');
    var AbstractProvider = require('./Abstract');
    var ClipArtLogoModel = require('../models/clip-art/Logo');
    var Settings = require ('../constants/Settings');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Providers.RenderClipArt
     * @extends App.Providers.Abstract
     *
     * @constructor
     */
    var RenderClipArtProvider = function () {
        AbstractProvider.call(this);

        this.defaultParams = {
            wid: 150,
            layout: 'comp',
            fmt: 'png',
            'op_sharpen': '0',
            resMode: 'sharp'
        };
    };

    var proto = inherits(RenderClipArtProvider, AbstractProvider);

    /**
     * Get Image URL
     *
     * @method getClipArtUrl
     * @param {App.Models.ClipArt.Logo} clipArtLogo
     * @param {object} params list of query string parameters
     * @return {string}
     */
    proto.getClipArtUrl = function(clipArtLogo, params) {
        var endpoint;
        var serialized;

        params = $.extend(this.defaultParams, params);

        if (!clipArtLogo || !(clipArtLogo instanceof ClipArtLogoModel)) {
            throw new Error('Logo model required');
        }

        serialized = this.serializeParams(params);

        endpoint = Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE + clipArtLogo.image + '?' + serialized;

        return endpoint;
    };

    /**
     * Converts parameters into url string (recursive)
     *
     * @method serializeParams
     *
     * @param {object} obj The object to serialize
     *
     * @return {string}
     */
    proto.serializeParams = function(obj) {
        var str = [];
        var p;
        for(p in obj) {

            if (!obj.hasOwnProperty(p)) {
                // skip to the next p!
                continue;
            }

            if (typeof obj[p] === 'object') {
                str.push('{' + this.serializeParams(obj[p]) + '}');
            } else {
                str.push(encodeURIComponent(p) + '=' + encodeURIComponent(obj[p]));
            }

        }

        return str.join('&');
    };

    return new RenderClipArtProvider();
});
