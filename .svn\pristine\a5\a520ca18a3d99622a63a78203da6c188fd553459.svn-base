.modal-content{position:fixed;background-color:#fff;border: 40px solid #fff;-webkit-box-shadow:0 3px 9px rgba(0,0,0,.5);box-shadow:0 3px 9px rgba(0,0,0,.5);outline:0;left:50%;z-index:20;top:50%;vertical-align: middle !important;
  transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%) ;
  -webkit-transform: translate(-50%, -50%);
}
.modal-backdrop{position:fixed;top:0;right:0;bottom:0;left:0;opacity:.8;background-color:#000}
.modal-dialog{left:50%;right:auto}
.modal-body{position:relative;padding:20px;margin-left: 0px; margin-top: 0px;}
.modal-header{padding:15px;border-bottom:1px solid #e5e5e5;min-height:16.43px}
.modal-dialog{width:85%;padding:10px;height:50px;margin-top:-120%}
.js_image_close{position:fixed;z-index:21;cursor:pointer!important}
#lens{width:25px;float:right;padding:10px}
#mainImgLens {
	width:450px;
	position:absolute;
	margin-top:-110%;
	margin-left:12%;
	opacity:.8;
	display:none;
}

.js-zoombox-target:hover.js-zoombox-target:before{display:block}

.js-zoombox-target:hover{outline:5px solid #0070c9}
#backdrop{position:fixed;display:none;width:100%;height:1000px;min-width: 1002px;opacity:.5;z-index:12;background:#000;left:0}
.zoomContainer:hover,.zoomLens:hover,.zoomWindow:hover{cursor:pointer}
.lens{position:relative;cursor:pointer;padding-top:7px; font-family: Arial; margin-right: 20px; margin-left: 13px;}
.lens:before{font-family:fontAwesome;content:"\f00e"; padding-right: 3px;}

.lens span {transition: background-size .3s ease; text-decoration: underline !important; margin-left:4px; }
.lens span:hover { text-decoration: none !important; background-size: 100% 100%; }

/* #topLens:before{font-family:fontAwesome;content:"\f002";color:#fff;font-size:22px}
#topLens{position:relative;border-radius:5px;width:36px;height:34px;cursor:pointer;top: -5px}
.topLens{display:inline;left:15px;position:absolute;width:5%;font-size:12px} */
@font-face{font-family:FontAwesome;src:url(../../fonts/fontawesome-webfont.ttf?v=4.4.0) format('truetype');font-weight:400;font-style:normal} .fa{display:inline-block;font:normal normal normal 14px/1 FontAwesome;font-size:inherit;text-rendering:auto;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.fa-lg{font-size:1.33333333em;line-height:.75em;vertical-align:-15%}.fa-2x{font-size:2em}.fa-3x{font-size:3em}.fa-4x{font-size:4em}.fa-5x{font-size:5em}.fa-fw{width:1.28571429em;text-align:center}.fa-ul{padding-left:0;margin-left:2.14285714em;list-style-type:none}.fa-ul>li{position:relative}.fa-li{position:absolute;left:-2.14285714em;width:2.14285714em;top:.14285714em;text-align:center}.fa-li.fa-lg{left:-1.85714286em}.fa-border{padding:.2em .25em .15em;border:solid .08em #eee;border-radius:.1em}.fa-pull-left{float:left}.fa-pull-right{float:right}.fa.fa-pull-left{margin-right:.3em}.fa.fa-pull-right{margin-left:.3em}.pull-right{float:right}.pull-left{float:left}.fa.pull-left{margin-right:.3em}.fa.pull-right{margin-left:.3em}.fa-spin{-webkit-animation:fa-spin 2s infinite linear;animation:fa-spin 2s infinite linear}.fa-pulse{-webkit-animation:fa-spin 1s infinite steps(8);animation:fa-spin 1s infinite steps(8)}@-webkit-keyframes fa-spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}@keyframes fa-spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}.fa-rotate-90{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=1);-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg)}.fa-rotate-180{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=2);-webkit-transform:rotate(180deg);-ms-transform:rotate(180deg);transform:rotate(180deg)}.fa-rotate-270{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=3);-webkit-transform:rotate(270deg);-ms-transform:rotate(270deg);transform:rotate(270deg)}.fa-flip-horizontal{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1);-webkit-transform:scale(-1, 1);-ms-transform:scale(-1, 1);transform:scale(-1, 1)}.fa-flip-vertical{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1);-webkit-transform:scale(1, -1);-ms-transform:scale(1, -1);transform:scale(1, -1)}:root .fa-rotate-90,:root .fa-rotate-180,:root .fa-rotate-270,:root .fa-flip-horizontal,:root .fa-flip-vertical{filter:none}.fa-stack{position:relative;display:inline-block;width:2em;height:2em;line-height:2em;vertical-align:middle}.fa-stack-1x,.fa-stack-2x{position:absolute;left:0;width:100%;text-align:center}.fa-stack-1x{line-height:inherit}.fa-stack-2x{font-size:2em}.fa-inverse{color:#fff}.fa-search:before{content:"\f002"}



@media screen and (min-width: 1281px) {
    .zoomContainer{
        left: 22.5% !important;
    }
}
@media screen and (min-width:1920px) {.zoomContainer{left:29.5%!important}}
* {
-webkit-user-select: none;
-khtml-user-select: none;
-moz-user-select: -moz-none;
-o-user-select: none;
user-select: none;
}

input {
-webkit-user-select: text;
-khtml-user-select: text;
-moz-user-select: text;
-o-user-select: text;
user-select: text;
}
a#Zoom-ViewLarger-Left {
color: #0070c9;
font-size : 11px;
}

.js-zoomLarge-image
{
	cursor: -moz-zoom-in; 
    cursor: -webkit-zoom-in;
    cursor: zoom-in;
}
#img1_view{
	border-bottom-color: #D61120 !important;
	border-bottom-width:2px !important;  
	border-bottom-style:solid !important;
	}
#img1_tumb {
	border-left: 3px solid black;
    border-top: 3px solid black;
	}
.lens>span:hover {
	text-decoration:underline;
}
.lens {
	text-decoration:none;
}
#img1_tumb, #img1_over {
	padding:15px 30px;
}
.text-on-image {
	font-size: 7vw;
	font-family: Source Sans Pro;
	color: #666666;
	z-index: 1;
	position: absolute;
}
 #viewZoomText
 {
    width: 50px;
	text-align: center;
 }