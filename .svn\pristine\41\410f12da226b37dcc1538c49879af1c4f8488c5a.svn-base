define(function(require) {
    'use strict';

    var ActionEvents = require('../constants/ActionEvents');
    var Classes = require('../constants/Classes');
    var Notes = require('i18n!../constants/nls/en-us/Notes');
    var ConfigurationProvider = require('../providers/Configuration');
    var InkColorsProvider = require('../providers/InkColors');
    var Content = require('i18n!../constants/nls/en-us/Content');
    var Controller = require('./Controller');
    var DomEvents = require('../constants/DomEvents');
    var EventController = require('./Event');
    var ProductEvents = require('../constants/ProductEvents');
    var SessionStorage = require('../providers/SessionStorage');
    var FbtModel = require('../models/Fbt');
    var Settings = require('../constants/Settings');
    var StateEvents = require('../constants/StateEvents');
    var TrackEvents = require('../constants/TrackEvents');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var Query = require('models/Query');
    var currency = require('util/currencyFormat');
    var Surcharge = require('util/Surcharge');
    var Helper = require('util/helper');
    var q = require('q');
  
    /**
     * @class App.Controllers.LogoBrowse
     * @extends App.Controllers.Controller
     *
     * @constructor
     * @param {Object} config
     */
    function FbtHybridProofController(config) {
        bindAll(this,
            'onHideAction',
            'onShowAction',
            'onProductChange',
            'update',
            'retrieveImageUrl',
            'onChange',
            'onButtonClick',
            'validateNumber',
            'initColors',
            'setColors',
            'onEditLogoClick'
        );
        

        /**
         * @property query
         * @type {App.Models.Query}
         */
        this.query = Query.getInstance();

        Controller.call(this, config);
  
        /**
         * Omniture site prefix
         *
         * @type {String}
         */
        this.site_prefix = Settings.OMNITURE_PREFIX;
        
        $('html, body').animate({ scrollTop: 0 }, 'fast');
    }

    var proto = inherits(FbtHybridProofController, Controller);

    /**
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = require('hbs!templates/FbtHybridProof');


      /**
     * @method init
     * @return {Promise}
     */
    proto.init = function() {
         return ConfigurationProvider
            .getConfiguration()
            .then(this.initColors)
            .then(this.setColors);
    };

   /**
     * @method initColors
     * @return {Promise.<App.Models.Collections.Colors>}
     */
    proto.initColors = function(config) {
        return InkColorsProvider
            .setConfig(config)
            .getColors();
    };

    /**
     * @method setColors
     * @param {App.Models.Collections.Colors} colors
     * @chainable
     */
    proto.setColors = function(colors) {
        this.colors = colors;
        return this;
    };

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        this.$view
            .on(DomEvents.CLICK, Classes.PREVIEW_BUTTON_SELECTOR, this.retrieveImageUrl)
            .on(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick)
            .on(DomEvents.CHANGE, this.onChange)
            .on(DomEvents.KEY_UP, this.onChange)
            .on(DomEvents.CLICK,Classes.EDIT_LOGO_SELECTOR, this.onEditLogoClick);

        EventController
            .on(ActionEvents.HIDE_ALL, this.onHideAction)
            .on(ActionEvents.HIDE_HYBRID, this.onHideAction)
            .on(ActionEvents.FBT_HYBRID, this.onShowAction)
            .on(ProductEvents.CHANGE, this.onProductChange);
            //.on(StateEvents.CHANGE, this.onStateChange);
        return this;
    };

    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        this.$view
            .off(DomEvents.CLICK, Classes.PREVIEW_BUTTON_SELECTOR, this.retrieveImageUrl)
            .off(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick)
            .off(DomEvents.CHANGE, this.onChange)
            .off(DomEvents.KEY_UP, this.onChange)
            .off(DomEvents.CLICK,Classes.EDIT_LOGO_SELECTOR, this.onEditLogoClick);

        EventController
            .off(ActionEvents.HIDE_ALL, this.onHideAction)
            .off(ActionEvents.FBT_NEXT, this.onShowAction)
            .off(ProductEvents.CHANGE, this.onProductChange);
            //.off(StateEvents.CHANGE, this.onStateChange);
        return this;
    };

    /**
     * @method onHideAction
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onHideAction = function() {
        this.$view.hide();
    };

    /**
     * @method onShowAction
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onShowAction = function(event, done) {

        var state = this.state;
        var controller = state && state.controller;
        var model = (controller && controller.model) || state;
        var trackEvent = model && model.trackEvent;

        this.callback = done;

        this.$view.show();  
        
        var pageName = 'SD:MultiConfig:Multi Review Proof'+ (FbtModel.currentFbtProductIndex +1);
               
        EventController.emit(TrackEvents.CHANGE, {
            linkEvents: 'event131',
            linkVars: {
                pageName:  pageName,
                channel: 'MultiConfig',
                prop1: pageName,
                prop2: pageName,
                prop3: pageName,
                prop4: 'MultiConfig'
            }
        });
    };

	/**
     * @method validateNumber
     * @param {jQuery.Event} event
     * @callback
     */
    proto.validateNumber = function(val) {
        var result = true;
         if (val === null || val === '') {
            if (this.numbering_req) {
                 $('.fbt-js-error').html('Start Number is required');
                 $('.js-fbt-addtocart').addClass('button_grey btn_disable');
                 result = false;
            } else {
                $('.fbt-js-error').html('');
                $('.js-fbt-addtocart').removeClass('button_grey btn_disable');
                result = true;
            }
         } else {
             if(val.length < this.numbering_min) {
                 $('.fbt-js-error').html('Start Number  must be at least ' + this.numbering_min + ' digits.');
                 $('.js-fbt-addtocart').addClass('button_grey btn_disable');
             } else {
                $('.fbt-js-error').html('');
                $('.js-fbt-addtocart').removeClass('button_grey btn_disable');
                result = true;
             }
             
         }
        var pattern = /^\d+$/;
        var empty_pattern =/\S+/;
         if (empty_pattern.test(val) && !pattern.test(val)) {
            $('.fbt-js-error').html('Start Number must be numeric.');
            $('.js-fbt-addtocart').addClass('button_grey btn_disable');
            result = false;
         }
         return result;
    }

      /**
     * @method onChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onChange = function(event) {
        // console.log('FbtHybridProof.onChange');        
        var value = $(event.target).val().trim();

        if(event.target.defaultValue === 'fbtReviewProofCart') {
            if(this.product['reviewProof'] === 'isChecked') {
                this.product['reviewProof'] = '';
            } else {                
                this.product['reviewProof'] = 'isChecked';
            }
        }

        if ($(event.target).hasClass('numbering')) {
            $(event.target).val(value);
            var number_valid = this.validateNumber(value);
            // if(number_valid)
            FbtModel.fbtHybridProof.numbering.value = value;
        }
        if ($(event.target).hasClass('inputBox_select-input copies')) {
            FbtModel.fbtHybridProof.productId.value = value;
        } 
        if($(event.target)[0].id == 'copiesCombobox'){
         EventController.emit(ProductEvents.CHANGECOPIES, value);
        }
        if ($(event.target).hasClass('inputBox_select-input qty')) {
            FbtModel.fbtHybridProof.quantity.value = value;
        }        

        if($(event.target)[0].id == 'QuantityCombobox'){
         EventController.emit(ProductEvents.FBTQUANTITYCHANGE, value);
        }

        if($(event.target)[0].id == 'MC_numbering'){
            EventController.emit(ProductEvents.FBTSTARTNOCHANGE, value);
        }
        // console.log('jiby', $(event.target).attr('class'));
        // FbtModel.fbtHybridProof.quantity.value = value;
        // this.model.setValue(value);
        // this.isValid();
    };

     $(window).resize(function() {
        var a = $('#FbtCopiesHelp').offset();
        if (a) {
            var tp = a.top;
            var lt = a.left;
            $('.popover').offset({top: tp+32 ,left: lt-340});
            $('.arrow').offset({top: tp+25,left:lt-2});
        }
    });

    proto.updateConstruction = function(product) {
        this.construction = product.getConstruction() ;
        //console.log('FbtHybridProof.updateConstruction', this.construction);
    };

    /**
     * @method onButtonClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onButtonClick = function() {
        var model = this.model;
        var info = FbtModel.fbtHybridProof.productId.info;
        var options = FbtModel.fbtHybridProof.productId.options;
        var construction = this.construction;
        
        $('#FbtCopiesHelp').popover({
            html: true,
            trigger: "manual",
            content: function() {
                var img = [];
                var desc = [];
                var swatch = [];
                var table = "<table width='392px'><tr height='168px' style='padding-top:5px;'>";
                var val = '';
                var msg;
                var msg1;
                var msg2;
                for (var i = 0; i < options._items.length; i++) {
                    msg = '<div><img src="'+ host_url + '/webasset/w2p_mobile/' + baseAppUrl + 'assets/media/images/tooltip-close.png" id="FbtCopiesCloseIcon" alt="CopiesCloseIcon" class="close tooltip-close" style="position: absolute; top: 0; right: 7px;"/>';
                    msg1 = "<div class='popovertitle' >Number of Copies</div>";
                    msg2 = "<div class='popover-info' >Carbonless 2-, 3- or 4-copy forms stay tightly aligned for clear, clean copies.</div>";
                    if ($.inArray(construction, Settings.PART_ICON_CODES) != -1) {
                        msg2 = "<div class='popover-info' >Multi-part options offer color-coded duplicates on separate sheets.</div>";
                    }
                    var msg3 = "<div class='popover-heading' style='display: inline-block;'>" + info.option[i].desc + "</div>";
                    var msg5 = "<div  class='popover-heading' style='display: inline-block;'>" + translateLabel(info.option[i].swatch.slice(0, -1)) + "</div>";
                    var msg4 = "<div style='overflow: hidden;height:125px;'><img src='" + host_url + "/webasset/w2p_mobile/" + baseAppUrl + "assets/media/images/" + info.option[i].swatch + ".png' alt='"+info.option[i].swatch+"' style='display:inline-block;vertical-align: top;' />";
                    if ($.inArray(construction, Settings.PART_ICON_CODES) != -1) {
                        msg4 = "<div style='overflow: hidden;height:140px;'><img src='" + host_url + "/webasset/w2p_mobile/" + baseAppUrl + "assets/media/images/" + info.option[i].swatch + "-S.png' alt='"+info.option[i].swatch+"-S' style='display:inline-block;vertical-align: top;' />";
                    }
                    var flag = '';
                    if ((i + 1) % 3 === 0 && (i + 1) != options._items.length) {
                        flag = "</tr><tr class='tdsec';'>";
                    }
                    val = val + "<td style='text-align: center;' width='33%'>" + msg4 + "</div><div vertical-align='bottom'>" + msg3 + "<br/>" + msg5 + "</div></td>" + flag;
                }
                var table2 = "</tr></table>";
                return msg + msg1 + msg2 + table + val + table2 + "</div>";
            }
        });
        if (!Settings.COPIES_FLAG) {
            $('#FbtCopiesHelp').popover('show');
        } else {
            $('#FbtCopiesHelp').popover('hide');
        }
        Settings.COPIES_FLAG = !Settings.COPIES_FLAG;

        $('#FbtCopiesCloseIcon').click(function(e) {
            Settings.COPIES_FLAG = false;
            $('#FbtCopiesHelp').popover('hide');
        });

        function translateLabel(val) {
            var result = val;
            if (result.indexOf("1-") != -1) {
                result = result.replace(/Part/g, "Copy");
            } else {
                result = result.replace(/Part/g, "Copies");
            }
            return result;
        };
    };

	/**
     * @method update
     * @param {App.Models.Product} product
     * @chainable
     */
    proto.update = function(product) {        
        this.product = product;
        //console.log('Details.update.product', product);
        var i = 0;
        var details = [];
        var toolTips = [];
        var surcharge;

        var part = product.getProductDescription();
        var parts = product.questions.getById('productId');
        var quantity = product.getQuantityValue();

        EventController.emit(ProductEvents.SURCHARGE_CHANGE, product);

        var qty_options = null;
        var qty_desc = null
        var qty_value = null;
        var copy_desc = null;
        var copy_options = null;
        var copy_value = null

        var my_logo = false;
        var routing = false;
        var acc_number = null;
        var rout_number = null;
        var numbering = null;
        var acc_number_min = null;
        var rout_number_min = null;
        var numbering_val = null;
        var numbering_req = false;
        var numbering_min = null;
        var numbering_max = null;
        if (typeof FbtModel.fbtHybridProof != 'undefined') {
            if (typeof FbtModel.fbtHybridProof.stamp_account != 'undefined' && FbtModel.fbtHybridProof.stamp_account.lines) {
                 for (var i = 0; i < FbtModel.fbtHybridProof.stamp_account.lines._items.length; i++) {
                        if (FbtModel.fbtHybridProof.stamp_account.lines._items[i].blockId =='CI' &&
                            FbtModel.fbtHybridProof.stamp_account.lines._items[i].info &&
                            FbtModel.fbtHybridProof.stamp_account.lines._items[i].info.desc == 'Account Number') {
                        routing = true;
                        acc_number = FbtModel.fbtHybridProof.stamp_account.lines._items[i].value = SessionStorage.getValue('accountNumber');
                        if (acc_number) {
                            acc_number_min = acc_number.slice(-4);
                        }
                     }
                 }
            }
            if (typeof FbtModel.fbtHybridProof.stamp_rout != 'undefined' && FbtModel.fbtHybridProof.stamp_rout.lines) {
                 for (var i = 0; i < FbtModel.fbtHybridProof.stamp_rout.lines._items.length; i++) {
                        if (FbtModel.fbtHybridProof.stamp_rout.lines._items[i].blockId =='BI' &&
                            FbtModel.fbtHybridProof.stamp_rout.lines._items[i].info &&
                            FbtModel.fbtHybridProof.stamp_rout.lines._items[i].info.desc == 'Routing Number') {
                        routing = true;
                        rout_number = FbtModel.fbtHybridProof.stamp_rout.lines._items[i].value = SessionStorage.getValue('routingNumber');
                        if (rout_number) {
                            rout_number_min = rout_number.slice(-4);
                        }
                     }
                 }
            }

            if (typeof FbtModel.fbtHybridProof.quantity != 'undefined') {
                 qty_options = FbtModel.fbtHybridProof.quantity.options;
                 qty_desc = FbtModel.fbtHybridProof.quantity.desc;
                 qty_value = FbtModel.fbtHybridProof.quantity.value;
            }

            if (typeof FbtModel.fbtHybridProof.productId != 'undefined') {
                 copy_desc = FbtModel.fbtHybridProof.productId.desc;
                 copy_options = FbtModel.fbtHybridProof.productId.options;
                 copy_value = FbtModel.fbtHybridProof.productId.value;
                 if (copy_options._items.length <= 1) {
                    copy_options = false;
                 };
            }
            
            if (typeof FbtModel.fbtHybridProof.logo != 'undefined') {
                my_logo = true;
            }

            if (typeof FbtModel.fbtHybridProof.accountNumber != 'undefined' || typeof FbtModel.fbtHybridProof.routingNumber != 'undefined') {
                if (FbtModel.fbtHybridProof.accountNumber.value != null || FbtModel.fbtHybridProof.routingNumber.value != null || FbtModel.fbtHybridProof.numbering_flag) {
                    routing = true;
                    if (FbtModel.fbtHybridProof.accountNumber.value != null) {
                        acc_number = FbtModel.fbtHybridProof.accountNumber.value;
                        acc_number_min = acc_number.slice(-4);
                    }
                    if (FbtModel.fbtHybridProof.routingNumber.value != null) {
                        rout_number = FbtModel.fbtHybridProof.routingNumber.value;
                        rout_number_min = rout_number.slice(-4);
                    }
                    if(FbtModel.fbtHybridProof.numbering_flag) {
                        numbering = true;
                        if (typeof FbtModel.fbtHybridProof.numbering != 'undefined') {
                            this.numbering_min = numbering_min = FbtModel.fbtHybridProof.numbering.minLength;
                            numbering_max = FbtModel.fbtHybridProof.numbering.maxLength;                   
                            if (FbtModel.fbtHybridProof.numbering.value != null) {
                                numbering_val = FbtModel.fbtHybridProof.numbering.value;
                            }
                            if (FbtModel.fbtHybridProof.numbering.info.value != 'A') {
                                this.numbering_req = numbering_req = true;
                               
                            }
                        }
                    }

            }
            }
        }

        if ($('.js-fbt-skip').is(':visible') && !($('[data-controller="PreviewController"]').is(':visible'))) {
            $('[data-controller="PreviewController"]').attr("style", "display:flex !important;justify-content:center");
            $('.row.grid.flex-body').attr("style","flex-direction:column !important");
        }
        var msgCheck = (SessionStorage.getValue('logo') =="CUSTOM") ? true : false;
        this.$view
            .html(this.template({
                baseAppUrl: baseAppUrl,
                host_url: host_url,
                details: details,
                routing: routing,
                routing_desc: Notes['HIDDEN_ACCOUNT'],
                acc_number_min: acc_number_min,
                rout_number_min: rout_number_min,
                rout_number: rout_number,
                acc_number: acc_number,               
                numbering: numbering,
                numbering_val: numbering_val,
                numbering_req: numbering_req,
                numbering_min: numbering_min,
                numbering_max: numbering_max,
                my_logo: my_logo,
                copy_desc: copy_desc,
                'copy_default': copy_value,
                copy_options: copy_options && copy_options._items,
                qty_desc: qty_desc,
                'qty_default': qty_value,
                qty_options: qty_options && qty_options._items,
                id: 'fbtReviewProofCart',
                reviewProof: product['reviewProof'],
                approvalCopy: Content.get(Settings.SITE_CORP + '_COPY_REVIEW'),
                msgCheck: msgCheck
            }));

            // For logo
            var logoType = SessionStorage.getValue('logo');
            var logo = null;
            if (logoType) {
                logo = SessionStorage.getValue('logo_'+ logoType);
            }

            if (logo) {
                this.model.logo = logo;
            }
            
            // Preview the logo at last step
            var my_logo;
            var plogo = SessionStorage.getValue('logo_CUSTOM');
            if (typeof plogo != 'undefined') {
                my_logo = plogo.logoImageName;
                if (plogo.logoUrl) {
                    my_logo = plogo.logoUrl;
                }
            }
            var mix_logo;
            var mix_plogo = SessionStorage.getValue('logo_LOGOMX');
            var logomixLogocode;
            if (typeof mix_plogo != 'undefined') {
                logomixLogocode = mix_plogo.logoCode;
                if (mix_plogo.logoHash) {
                    mix_logo = mix_plogo.logoHash;
                }
                if (typeof mix_logo == 'undefined') {             
                  mix_logo = mix_plogo.data && mix_plogo.data.item.value
                }
            }
            var base = Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE;
            var imageExt = "?fmt=webp&hei=142&wid=142&qlt=85";
            var image1 = SessionStorage.getValue('logo_STANDARD');
            var image, clipart_logo1;
            var summaryCustomLogoStyle = '<img  id="summary-custom-logo" class="custom_logo" src="' + my_logo + '" alt="custom_logo"/>';
            if (typeof plogo != 'undefined') {
                if(plogo.logoCode && !plogo.logoCode.startsWith('U'))
                {
                    summaryCustomLogoStyle = '<img  id="summary-custom-logo" class="custom_logo_mset" src="' + my_logo + '" alt="custom_logo"/>'
                }
            }
            var editLogoUrl = host_url + '/webasset/w2p_mobile/' + baseAppUrl + 'assets/media/images/edit.png';
            if (typeof image1 != 'undefined') {
                image = image1.logoCode;
                clipart_logo1= base + image + imageExt;
            }
            if (my_logo && logoType == 'CUSTOM') {
                var log_image = '<div class="logo-preview"><h2 class="logo_name logo_left">Logo</h3><div class="logo_right"><img id="summary-edit-logo" class="edit_logo" src="'+ editLogoUrl +'"></img><i class="edit_text">Edit</i></div></div><div class="logo_border">' + summaryCustomLogoStyle + '</div> <div class="my_logo_message">Logo Applied Successfully!</div>';
                 $('.my_logo').html(log_image);
                 $('.my_logo').parent().show();
                 $('[data-id="HIDDEN_LOGO"]').show();
            } else if(mix_logo && logoType == 'LOGOMX') {

                   //  var base = Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE;
                   //  var ink2 = SessionStorage.getValue('inkColor2');
                   //  if (typeof ink2 == 'undefined' || ink2 == '' || ink2 == null) {
                   //      ink2 = SessionStorage.getValue('inkColor1');
                   //  }
                   //  // this.defaultInkColor = ink2;
                   //  var color = this.colors.getById(ink2);
                   //  var colorstring = color.getRGBString();

                   //  var imageExt = '&op_contrast=-50&wid=350&fit=constrain&fmt=png-alpha';
                   //  var imageUrl = base  + 'ugc/' + mix_logo + '.tif?op_colorize=' + colorstring + imageExt;

                   // var log_image = '<img class="mix_logo" src="' + imageUrl + '" /> <div class="my_logo_message">Logo Applied Successfully!</div>';
                   // $('.my_logo').addClass('logomix-summary');
                   // $('.my_logo').html(log_image);
                   // $('.my_logo').parent().show();
                   // $('[data-id="HIDDEN_LOGO"]').show();

                 var log_image = ' <div class="logo-preview"><h2 class="logo_name logo_left">Logo</h3><div class="logo_right"><img id="summary-edit-logo" class="edit_logo" src="'+ editLogoUrl +'"></img><i class="edit_text">Edit</i></div></div><div class="logo_border"><canvas id="review-canvas" width="150" height="150"></canvas></div>';
                 $('.my_logo').html(log_image);
                 $('.my_logo').parent().show();
                 var canvas = new fabric.Canvas('review-canvas');
                   
                    var ink2 = SessionStorage.getValue('inkColor2');
                    if (typeof ink2 == 'undefined') {
                     ink2 = SessionStorage.getValue('inkColor1');
                    }
                    // if(product.steps){
                    //   var values = product.steps.getValues().values;
                    //   var color = values.inkColor2 || {};

                    //   if (!color.value) {
                    //     ink2 = SessionStorage.getValue('inkColor1');
                    //   }
                    // }

                    // if (product.steps) {
                    //     var values = product.steps.getValues().values;
                    //     console.log('==== values  ==== ', values);
                    //     color1 = values.inkColor1.value;
                    //     color2 = values.inkColor2.value || color1;
                    // }

                    var color1;
                    var color2;
                    if (product.steps) {
                        var values = product.steps.getValues().values;
                        if(values.inkColor1) {
                            color1 = values.inkColor1.value;
                            color2 = values.inkColor2.value || color1;
                        }
                        else {
                            color1 = 'BLACK';
                            color2 = 'BLACK';
                        }
                        if (!color2) {
                            color2 = color1;
                        }
                    }

                    // var productQuestion = product.info.productInfo.question
                    // var inkColor1Obj;
                    // var inkColor2Obj;
                    // productQuestion.forEach( function(e) {
                    //     if(e.id === 'inkColor1') {
                    //         inkColor1Obj = e;
                    //     }
                    //     if(e.id === 'inkColor2') {
                    //         inkColor2Obj = e;
                    //     }
                    // });
                    // if(!inkColor2Obj) {
                    //     color2 = color1
                    // }

                    var color = this.colors.getById(color2);
                    var a = color.getRGBString();
                    a = a.split(",");

                    var b = a.map(function(x){             //For each array element
                        x = parseInt(x).toString(16);      //Convert to a base16 string
                        return (x.length==1) ? "0"+x : x;  //Add zero if we get only one character
                    });
                    b = "#"+b.join("");
                    var logoColor = b;
                    // var image = Settings.SVC_LOGOMIX_PATH + mix_logo + '/png/142';
                    var imageSize = 142;
                    var imageHeightWidth = 'hei:'+ imageSize + ',wid:' + imageSize + ',fit:constrain';
                    // logomixLogocode
                    var image = Settings.SVC_UPLOAD_DOMAIN + '?UID=' + Helper.isAlphaNumeric(logomixLogocode) ? logomixLogocode : "" + '&Custom=' + imageHeightWidth + '';

                    fabric.Image.fromURL(image, function(img) {
                      // Remove white space
                      canvas.clear();
                      
                       // // Gray scale
                       if(logoColor == '#000000') {
                          var grayscale = new fabric.Image.filters.Grayscale();
                          img.filters.push(grayscale);
                       } else {
                          var removeWhite = new fabric.Image.filters.RemoveColor({
                           color: '#ffffff',
                           distance: 0.1
                          });

                          // Add Red color
                          var addColor = new fabric.Image.filters.BlendColor({
                           color: logoColor,
                           mode: 'tint',
                           opacity: 0.5,
                           alpha: 0.9
                          });
                          img.filters.push(removeWhite);
                          img.filters.push(addColor);
                       }
                      

                      img.lockRotation = true;
                      img.lockMovementX = true;
                      img.lockMovementY = true;
                      img.lockScalingX = true;
                      img.lockScalingY = true;
                      img.lockUniScaling = true;
                      
                      img.selectable = false;
                      // apply filters and re-render canvas when done
                      img.applyFilters();
                      // add image onto canvas (it also re-render the canvas)
                      canvas.add(img);

                      canvas.renderAll();
                  }, { crossOrigin: 'Anonymous' });

            } else if(clipart_logo1 && logoType == 'STANDARD') {
                var color1;
                var color2;
                if (product.steps) {
                    var values = product.steps.getValues().values;
                    if(values.inkColor1) {
                        color1 = values.inkColor1.value;
                        color2 = values.inkColor2.value || color1;
                    }
                    else {
                        color1 = 'BLACK';
                        color2 = 'BLACK';
                    }
                    if (!color2) {
                        color2 = color1;
                    }
                }
                var color = this.colors.getById(color2);
                var a = color.getRGBString();
                clipart_logo1 = clipart_logo1 + '&op_colorize=' + a + '&layer=1';
                var log_image2 = '<div class="logo-preview"><h2 class="logo_name logo_left">Logo</h3><div class="logo_right"><img id="summary-edit-logo" class="edit_logo" src="'+ editLogoUrl +'"></img><i class="edit_text">Edit</i></div></div><div class="logo_border"><img id="summary-custom-logo" class="custom_logo" src="' + clipart_logo1 + '" alt="custom_logo"/></div> <div class="my_logo_message">Logo Applied Successfully!</div>';
                 $('.my_logo').html(log_image2);
                 $('.my_logo').parent().show();
                 $('[data-id="HIDDEN_LOGO"]').show();
            }
             else {
                 $('[data-id="HIDDEN_LOGO"]').hide();
                 $('.my_logo').html('');
                 $('.my_logo').parent().hide();
            }

            $(".fbt-hybrid-routing-val .js-reveal").bind("mousedown keydown touchstart", function(e){
                var $this = $(this);
                var $bcAc = $this.closest(".fbt-hybrid-routing-val");
                var eventName;
                $bcAc.addClass("active");

                // Bind to the mouseup
                $(document.body).one("mouseup keyup touchend", function(e){
                    $bcAc.removeClass("active");
                });
            });

            var number_val = $('.fbt_number').val();
            if (typeof number_val != 'undefined') {
                this.validateNumber(number_val);
            }

            if(!this.product['reviewProof'] || this.product['reviewProof'] !== 'isChecked') {
                $('.js-fbt-addtocart').addClass('button_grey btn_disable');
            } else {
                $('.js-fbt-addtocart').removeClass('button_grey btn_disable');
            }

            if (Settings.APPROVE_REVIEW_FBT === true) {
                $('.js-fbt-matte').removeClass('d-none');
            }
        return this;
    };

    /**
     * @method getQuestionsValue
     * @return {string}
     * product.questions._items[].options_items[].surcharge.id
     */
    proto.getQuestionValue = function(questionId) {
        var val = null;
        var questionModel = this.getQuestionModel(questionId);
        if (questionModel !== null) {
            val = questionModel.value;
        }
        return val;
    };

    /**
     * @method getQuestionModel
     * @return {QuestionModel}
     */
    proto.getQuestionModel = function(questionId) {
        var questionModel = null;
        for (var i=0; i<this.product.questions._items.length; i++) {
            if (this.product.questions._items[i].id == questionId) {
                questionModel = this.product.questions._items[i];
                break;
            }
        }
        return questionModel;
    };

    /**
     * @method onProductChange
     * @param {jQuery.Event} event
     * @param {App.Models.Product} product
     * @callback
     */
    proto.onProductChange = function(event, product) {
        this.updateConstruction(product);
        this.update(product);
    };

    proto.retrieveImageUrl = function() {

        $('#pop-up').fadeIn(300);

        // Add the mask to body
        $('body').append('<div id="mask-cart"></div>');
        $('#mask-cart').fadeIn(300);

        var orgURl = $(".js-preview-img").attr('src');
        var wid_url = orgURl.substr(0, orgURl.indexOf('&wid='));
        var img_url;
        if (wid_url == '') {
            // console.log('www', wid_url); 
            var hei_url = orgURl.substr(0, orgURl.indexOf('&hei='));
            if (hei_url == '') {
                hei_url = orgURl;
            }
            img_url = hei_url + '&wid=' + 1480;
        } else {
            img_url = wid_url + '&wid=' + 1480;
        }
        // var final_url = img_url + '&wid=' + orgWidth;
        // console.log(final_url);
        var urlSplitUid = orgURl.substr(0, orgURl.indexOf('?'));
        var tempuid = urlSplitUid.split("/");
        var uid = tempuid[tempuid.length - 1];


        var urlSplitAppUrl = orgURl.substr(orgURl.indexOf('?') + 1);
        var appUrl = urlSplitAppUrl.substr(0, urlSplitAppUrl.length);
        scene7url = urlSplitUid.substr(0, urlSplitUid.lastIndexOf('/')) + '/';

        zoom.initHeroImage();
        zoom.iviewer.settings.imgUrlPrefix = scene7url;
        zoom.iviewer.productPartNum = uid;
        zoom.iviewer.appendUrl = appUrl;
        zoom.iviewer.loadImage(img_url, 'sample', imgwidth);
        $('.drag_help').fadeIn(300);
        return false;
        // $(".js_preview_button").removeClass("js-include-zoom");
        // $(".modal-dialog").show();
        // $('.modal-content').css('height',$( window ).height()*0.8);
        // $('.modal-content').css('width',$( window ).width()*0.8);
        // $('.modal-body').css('height',$( window ).height()*0.8);
        // $('.modal-body').css('width',$( window ).width()*0.8);
        // $(".zoomingImage").css("display","block");
        // $(".zoomContainer").show();
        // $(".zoomWindowContainer").show();
        // $("#backdrop").css("display","block");
        // document.documentElement.style.overflow = 'hidden';
        // document.body.scroll = "no";
        // document.documentElement.scrollTop = "no";
        // if($('.text-on-image').is(':visible')) {
        //     $('.text-on-image').offset({left:$('.modal-content').offset().left + 15 + ($(".modal-content").width() - $('.text-on-image').width())/2, top:$('.modal-content').offset().top + 5 + ($(".modal-content").height() - $('.text-on-image').height())/2});
        //     $('.text-on-image').delay(1300).fadeOut(250);
        // }
        // $(".js_image_close").offset({top:$(".modal-content").offset().top - $(".js_image_close").height(), left:($(".modal-content").offset().left+6 + $(".modal-content").width()) - $(".js_image_close").width()});
    };

    $(window).resize(function() {
        var a = $('.modal-content').offset();
        if (a) {
            var tp = a.top;
            var lt = a.left;
            $('.zoomContainer').offset({top: tp+23 ,left: lt+20});
            $(".js_image_close").offset({top:$(".modal-content").offset().top - $(".js_image_close").height() + 50, left:($(".modal-content").offset().left + $(".modal-content").width()) - $(".js_image_close").width() + 60});
        }
    });

    $(window).on('resize', function() {
        var modalcontent = $( ".modal-content" ).offset();
        if (modalcontent) {
            var modalcontent_top = modalcontent.top;
            var modalcontent_left = modalcontent.left;
            $( ".zoomContainer" ).offset({top:modalcontent_top+10,left:modalcontent_left+10});
        }
    });

    /**
	 * @method onEditLogoClick
	 * @param {jQuery.Event} event
	 */

	proto.onEditLogoClick = function(event){
        EventController.emit(ActionEvents.GO_TO_FBT_EDIT_STEP,"Logo");
	}

    return FbtHybridProofController;
});
