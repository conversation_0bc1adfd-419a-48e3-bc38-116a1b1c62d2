define(function(require) {
	'use strict';

	var AbstractQuestionController = require('./Abstract');
	var inherits = require('mout/lang/inheritPrototype');
	var EventController = require('../Event');
	var DomEvents = require('../../constants/DomEvents');
	var ConfigurationProvider = require('../../providers/Configuration');
	var debounce = require('mout/function/debounce');
    var bindAll = require('mout/object/bindAll');
    var Settings = require('../../constants/Settings');

	/**
	 * @class App.Controllers.Question.ReqInk
	 * @extends App.Controllers.Question.Abstract
	 *
	 * @constructor
	 * @param {Object} config
	 * @param {jQuery} config.view
	 * @param {Models.Ui.Question} config.model
	 */
	function CommentController(config) {
		bindAll(this);

		/**
         * @method onChangeLazy
         * @param {jQuery.Event} event
         * @callback
         */
        this.onChangeLazy = debounce(this.onChange, Settings.DEBOUNCE_DELAY);

		AbstractQuestionController.call(this, config);
	}

	var proto = inherits(CommentController, AbstractQuestionController);

    /**
     * The following config call will be refactored to not
     * require an extra provider call.
     *
     * @method init
     * @return {Promise}
     */
    proto.init = function() {
        ConfigurationProvider.getConfiguration();
    };

	/**
	 * @method template
	 * @param {Object} model
	 * @return {String}
	 */
	proto.template = require('hbs!templates/question/comment');

	/**
	 * @method attachEvents
	 * @chainable
	 */
	proto.attachEvents = function() {
		this.$view
            .on(DomEvents.BLUR, this.onChange)
            .on(DomEvents.CHANGE, this.onChange)
            .on(DomEvents.KEY_DOWN, this.onChangeLazy);
		return this;
	};

	/**
	 * @method detachEvents
	 * @chainable
	 */
	proto.detachEvents = function() {
        this.$view
            .off(DomEvents.BLUR, this.onChange)
            .off(DomEvents.CHANGE, this.onChange)
            .off(DomEvents.KEY_DOWN, this.onChangeLazy);
		return this;
	};

	/**
	 * @method render
	 * @chainable
	 */
	proto.render = function() {
		//Only show this widget for DFS at this point.
		if (!Settings.LINEITEM_COMMENT) {
			this.$view.hide();
			return;
		}
		var comment = '';
		if (this.model.configuration &&
			this.model.configuration._details &&
			this.model.configuration._details.customizedOptions.availableValues.comment) {
			comment = this.model.configuration._details.customizedOptions.availableValues.comment;
			// comment = comment.split("<br/>").join("\n");
			// var commentWithBreaks = comment.split("\n").join("<br/>");
			this.model.setValue(comment, 'comment');
		}
		this.$view.html(
			this.template(
				{
					//commentCopy: inkColorData.getRGBString(),
					//commentHeading: inkColorData.description,
					value: comment
				}
			)
		);
		this.$view.css('margin-top', '10px');
		return this.start();
	};

	/**
     * @method onChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onChange = function(event) {
        var name = event.target.name;
        var $input = $(event.target);
        // var commentWithBreaks = $input.val().split("\n").join("<br/>");
		this.model.setValue($input.val(), name);
    };

	return CommentController;
});
