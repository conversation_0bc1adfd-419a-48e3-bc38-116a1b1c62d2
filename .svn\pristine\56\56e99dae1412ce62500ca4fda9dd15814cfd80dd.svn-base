<div class="grid">

    <div class="grid-col grid-col_7of10">

        <div class="design">

            <div class="design-hd">

                <div class="detailBox detailBox_1of3">
                    <div class="detailBox-header">
                        <span class="hdg hdg_h3">Routing Number</span>
                    </div>
                    <div class="detailBox-body">
                        {{! routing number }}
                        <span></span>
                    </div>
                </div>

                <div class="detailBox detailBox_1of3">
                    <div class="detailBox-header">
                        <span class="hdg hdg_h3">Account Number</span>
                    </div>
                    <div class="detailBox-body">
                        {{! account number }}
                        <span></span>
                    </div>
                </div>

                <div class="detailBox detailBox_1of3">
                    <div class="detailBox-header">
                        <span class="hdg hdg_h3">Start Number</span>
                    </div>
                    <div class="detailBox-body detailBox-body_check mix-detailBox-body_textRight">
                        {{! start number }}
                        <span></span>
                    </div>
                </div>

            </div> {{! /design-hd }}

            <div class="design-bd">

            </div>

        </div> {{! /design }}

    </div> {{! /grid-col }}

    <div class="grid-col grid-col_3of10">

        <div class="options">

            <p class="txtLarge">
                <span class="hdg hdg_h2">Note: </span>
                For security reasons, account information will not be shown on the product preview.
            </p>

            <ul class="vList vList_loose">

                <li>
                    <label class="txtLarge">
                        <span class="required" title="required"></span>
                        Routing Number (9 digits)
                        <input type="text" class="inputBox" />
                    </label>
                </li>

                <li>
                    <label class="txtLarge">
                        <span class="required" title="required"></span>
                        Account Number (1-15 digits)
                        <input type="text" class="inputBox inputBox_error" />
                    </label>
                    <div class="error">Some kind of an error</div>
                </li>

                <li>
                    <label class="txtLarge">
                        <span class="required" title="required"></span>
                        Start Number (at least 4 digits)
                        <input type="text" class="inputBox" />
                    </label>
                </li>

                <li>
                    <input type="checkbox" id="reverse" class="checkbox" />
                    <label for="reverse">
                        Reverse Start Number Order
                    </label>
                </li>

            </ul>

        </div> {{! /options }}

        {{> subtotal}}

    </div> {{! /grid-col }}

</div> {{! /grid }}
