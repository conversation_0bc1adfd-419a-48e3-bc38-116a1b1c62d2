define(function (require) {
    'use strict';

    var $ = require('jquery');
    var AbstractModel = require('./Abstract');
    var inherits = require('mout/lang/inheritPrototype');
    var Settings = require('../constants/Settings');

    /**
     * @class App.Models.Configuration
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} configurationValues
     */
    var ConfigurationModel = function (configurationValues) {
        AbstractModel.call(this, configurationValues);
    };

    var proto = inherits(ConfigurationModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} configurationValues
     * @chainable
     */
    proto.init = function(configurationValues) {

        /**
         * @parameter services
         * @default = null;
         * @type {Object}
         */
        this.services = null;

        // run the parent init method to parse determine the data type
        base.init.call(this, configurationValues);

        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param {object} json the original JSON data.
     */
    proto.fromJSON = function(json) {
        json = this.stripInvalidFields(json);

        var config = json.root.configuration;
        this.services = $.extend(true, config.services, {});
    };

    return ConfigurationModel;
});
