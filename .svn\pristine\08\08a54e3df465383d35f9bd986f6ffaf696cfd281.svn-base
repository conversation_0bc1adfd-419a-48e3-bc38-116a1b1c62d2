define(function (require) { // jshint ignore:line
    'use strict';

    var AbstractCollection = require('./Abstract');
    var InkColorModel = require('../InkColor');
    var inherits = require('mout/lang/inheritPrototype');
    /**
     * @class App.Models.Collections.InkColors
     * @extends App.Models.Collections.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} InkColorOptions
     */
    var InkColorsCollection = function (InkColorOptions) {
        AbstractCollection.call(this, InkColorOptions);
    };

    var proto = inherits(InkColorsCollection, AbstractCollection);
    var base = AbstractCollection.prototype;

    /**
     * @property itemClass
     * @type {App.Models.InkColor}
     */
    proto.itemClass = InkColorModel;

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param {object} json the original JSON data.
     */
    proto.fromJSON = function(json) {
        var items;

        json = this.stripInvalidFields(json).root;

        items = json;
        if (json.colors && json.colors.option) {
            items = json.colors.option;
        }

        base.fromJSON.call(this, items);

    };

    return InkColorsCollection;
});
