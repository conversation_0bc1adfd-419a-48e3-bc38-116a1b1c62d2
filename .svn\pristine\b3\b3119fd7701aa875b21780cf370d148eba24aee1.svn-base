define(function (require) { // jshint ignore:line
    'use strict';

    var AbstractCollection = require('./Abstract');
    var Content = require('i18n!../../constants/nls/en-us/Content');
    var ContentModel = require('../Content');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * Matches `$[[VAR]]` placeholders in strings.
     *
     * @type {RegExp}
     */
    var variablePattern = /\$\[\[VAR\]\]/g;

    /**
     * @class App.Models.Collections.Content
     * @extends App.Models.Collections.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} ContentOptions
     */
    var ContentCollection = function (ContentOptions) {
        AbstractCollection.call(this, ContentOptions);
    };

    var proto = inherits(ContentCollection, AbstractCollection);
    var base = AbstractCollection.prototype;

    /**
     * @property itemClass
     * @type {App.Models.Content}
     */
    proto.itemClass = ContentModel;

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param {object} json the original JSON data.
     */
    proto.fromJSON = function(json) {
        var items;

        json = this.stripInvalidFields(json);

        items = json.ExternalContent.Content;

        base.fromJSON.call(this, items);
    };

    /**
     * @method get
     * @param {String} id
     * @param {Object} data
     * @chainable
     */
    proto.get = function(id, data) {
        var item = this.getById(id);
        var text = item && Content.get(id, item.text, data);
        var variable = data && data.variable;

        if (text && typeof text === 'string') {
            return text.replace(variablePattern, variable);
        }

        return text;
    };

    return ContentCollection;
});
