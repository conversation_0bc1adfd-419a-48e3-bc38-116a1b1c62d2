define(function(require) {
  'use strict';

  var ActionEvents = require('../constants/ActionEvents');
  var Classes = require('../constants/Classes');
  var LogoMixProvider = require('../providers/LogoMix');
  var ConfigurationProvider = require('../providers/Configuration');
  var InkColorsProvider = require('../providers/InkColors');
  var Content = require('i18n!../constants/nls/en-us/Content');
  var Errors = require('i18n!../constants/nls/en-us/Errors');
  var Controller = require('./Controller');
  var DomEvents = require('../constants/DomEvents');
  var EventController = require('./Event');
  var ProductEvents = require('../constants/ProductEvents');
  var SessionStorage = require('../providers/SessionStorage');
  var Settings = require('../constants/Settings');
  var StateEvents = require('../constants/StateEvents');
  var bindAll = require('mout/object/bindAll');
  var inherits = require('mout/lang/inheritPrototype');
  var Query = require('models/Query');
  var q = require('q');

  var Registry = require('util/Registry');
    
  /**
   * @class App.Controllers.LogoBrowse
   * @extends App.Controllers.Controller
   *
   * @constructor
   * @param {Object} config
   */
  function LogoMixFilterController(config) {
      bindAll(this,
          'setCategories',
          'updateCategories',
          'updateTitle',
          'onHideAction',
          'onShowAction',
          'onNextClick',
          'onStateChange',
          'onProductChange',
          'update',
          'initColors',
          'setColors',
          'renderCanvas',
          'updateNext',
          'onRefreshLogos',
          'updateNextRight',
          'onCancelClick',
          'getLogoColor'
      );

      /**
       * @property categories
       * @type {App.Models.ClipArt.Collections.Categories}
       * @default null
       */
      this.categories = null;
      this.colors = null;

      this.setLogo(SessionStorage.getValue('logo_LOGOMX'));

      /**
       * @property query
       * @type {App.Models.Query}
       */
      this.query = Query.getInstance();

      Controller.call(this, config);

      // Start hidden
      this.$view.hide();
    
      /**
       * Omniture site prefix
       *
       * @type {String}
       */
      this.site_prefix = Settings.OMNITURE_PREFIX;
  
      /**
       * @property ready
       * @type {Promise}
       */
      this.ready = q
          .when(this.ready)
          .then(this.updateCategories);
  }

  var proto = inherits(LogoMixFilterController, Controller);

  /**
   * @method init
   * @return {Promise}
   */
  proto.init = function() {
      $('[data-controller="LogoMixFilterController"]').show();
      return ConfigurationProvider
          .getConfiguration()
          .then(this.initColors)
          .then(this.setColors);
  };

  /**
   * @method initColors
   * @return {Promise.<App.Models.Collections.Colors>}
   */
  proto.initColors = function(config) {
      return InkColorsProvider
          .setConfig(config)
          .getColors();
  };

  /**
   * @method setColors
   * @param {App.Models.Collections.Colors} colors
   * @chainable
   */
  proto.setColors = function(colors) {
      this.colors = colors;
      // this.model.options.each(this._setColor.bind(this, colors));
      return this;
  };

  // -- Accessors ------------------------------------------------------------

  /**
   * @method setCategories
   * @param {App.Models.ClipArt.Collections.Categories}
   * @chainable
   */
  proto.setCategories = function(categories) {
      this.categories = categories;

      // Set currently selected logo categories
      this._selectCategories();

      return this;
  };

  /**
   * @method getCategories
   * @return {App.Models.ClipArt.Collections.Categories}
   */
  proto.getCategories = function() {
      return this.categories;
  };

  /**
   * @method getCategory
   * @return {App.Models.ClipArt.Category}
   */
  proto.getCategory = function() {
      var id = this.$categories.find('select').val();
      var categories = this.getCategories();

      return categories && categories.getById(id);
  };

  /**
   * @method setLogo
   * @param {Object} data
   * @param {Boolean} [silent=false] If true, category dropdown will not be updated
   * @chainable
   */
  proto.setLogo = function(data, silent) {
      /**
       * @property logo
       * @type {Object}
       */
      this.logo = data;
      this.logourl = data && data.logoUrl;

      if (silent !== true && this.categories) {
          this._selectCategories();
      }

      return this;
  };

  /**
   * @method _selectCategories
   * @private
   */
  proto._selectCategories = function() {
      if (!this.logo || !this.logo.logoCode) {
          return;
      }

      var id = this.logo.logoCode;
      var data = Registry.get(Registry.LOGO, id);

      this.selectedCategory = data.category;
      this.selectedSubcategory = data.subcategory;

      if (this.$categories) {
          this.updateCategories();
      }
  };

  // -- Methods --------------------------------------------------------------

  /**
   * @method template
   * @param {Object} model
   * @return {String}
   */
  proto.template = require('hbs!templates/LogoMixFilter');

  /**
   * @method inputBoxTemplate
   * @param {Object} model
   * @return {String}
   */
  proto.inputBoxTemplate = require('hbs!templates/form/inputBox');

  /**
   * @method titleTemplate
   * @param {Object} model
   * @return {String}
   */
  proto.titleTemplate = require('hbs!templates/product/title');

  proto.render = function () {
    this.$view.html(
      this.template({
        line1: SessionStorage.getValue("CI_ML") ? SessionStorage.getValue("CI_ML") : "Enter Line One",
        line2: SessionStorage.getValue("CI_DE") ? SessionStorage.getValue("CI_DE") : "",
        next: Content.get("Next Step"),
        prev: Content.get("Previous Step"),
        brandLogoUrl: Settings.BRAND_LOGO_URL,
        siteCss: Settings.SITE_CSS,
        brandLogo: Settings.BRAND_LOGO,
        host_url: host_url,
        baseAppUrl: baseAppUrl,
      })
    );
    this.$filter = this.$view.find(Classes.MIX_LOGO_FILTER_SELECTOR);
    this.onRefreshLogos();
    return this;
  };

  /**
   * @method cacheElements
   * @chainable
   */
  proto.cacheElements = function() {
      var $view = this.$view;
      this.$filter = $view.find(Classes.MIX_LOGO_FILTER_SELECTOR);
      return this;
  };

  /**
   * @method attachEvents
   * @chainable
   */
  proto.attachEvents = function() {
      this.$view
          .on(DomEvents.CLICK, Classes.LOGO_MIX_SELECTOR, this.onRefreshLogos)
          .on(DomEvents.CLICK, Classes.NEXT_SELECTOR, this.onNextClick)
          .on(DomEvents.CLICK, Classes.CANCEL_UPLOAD_LOGO_BUTTON_SELECTOR, this.onCancelClick)
          .on(DomEvents.CHANGE, Classes.ALLSTYLES_SELECTOR, this.onRefreshLogos)
          .on(DomEvents.CHANGE, Classes.FILTERLAYOUT_SELECTOR, this.onRefreshLogos)
          .on(DomEvents.CLICK, Classes.SEARCHICON_SELECTOR, this.onRefreshLogos)
          .on(DomEvents.CHANGE, Classes.ALLSTYLES_SELECTOR, this.onStyleChange);

      EventController
          .on(ActionEvents.LOGOMIX_SELECTION, this.updateNextRight)
          .on(ActionEvents.LOGO_LOGOMX, this.onShowAction)
          .on(ActionEvents.LOGO_COLOR, this.getLogoColor);

      return this;
  };

  /**
   * @method detachEvents
   * @chainabledesignRadio 
   */
  proto.detachEvents = function() {
      this.$view
          .off(DomEvents.CLICK, Classes.LOGO_MIX_SELECTOR, this.onRefreshLogos)
          .off(DomEvents.CLICK, Classes.NEXT_SELECTOR, this.onNextClick)
          .off(DomEvents.CLICK, Classes.CANCEL_UPLOAD_LOGO_BUTTON_SELECTOR, this.onCancelClick)
          .off(DomEvents.CHANGE, Classes.ALLSTYLES_SELECTOR, this.onRefreshLogos)
          .off(DomEvents.CHANGE, Classes.FILTERLAYOUT_SELECTOR, this.onRefreshLogos)
          .off(DomEvents.CLICK, Classes.SEARCHICON_SELECTOR, this.onRefreshLogos)
          .off(DomEvents.CHANGE, Classes.ALLSTYLES_SELECTOR, this.onStyleChange);

      EventController
          .off(ActionEvents.LOGOMIX_SELECTION, this.updateNextRight)
          .off(ActionEvents.LOGO_LOGOMX, this.onShowAction)
          .off(ActionEvents.LOGO_COLOR, this.getLogoColor);

      return this;
  };

   /**
   * @method onStyleChange
   * @param {jQuery.Event} event
   * @callback
   */
  proto.onStyleChange = function(event) {
    var style = $( ".js-all-styles" ).val();
    if(style == '' || style == 'icon') {
      $( ".js-all-layout" ).addClass('showme');
    } else {
      $( ".js-all-layout" ).removeClass('showme');
    }      
  };

  /**
   * @method update
   * @chainable
   */
  proto.updateCategories = function() {
      var categories = this.getCategories();

      this.$categories.html(this.inputBoxTemplate({
          selected: this.selectedCategory,
          items: categories && categories._items,
          tlId: "StandardLogoMajorCategory"
      }));

      //return this.updateSubcategories();
  };

  function extractHostname(url) {
      var hostname;
      //find & remove protocol (http, ftp, etc.) and get hostname

      if (url.indexOf("//") > -1) {
          hostname = url.split('/')[2];
      } else {
          hostname = url.split('/')[0];
      }

      //find & remove port number
      hostname = hostname.split(':')[0];
      //find & remove "?"
      hostname = hostname.split('?')[0];

      return hostname;
  }

  /**
   * @method updateNext
   * @chainable
   */
  proto.updateNext = function(logourl) {
      if (this.logourl) {
          var host = extractHostname(this.logourl);
          var domain = host.split('.');
          if(domain[1] != 'deluxe') {
              this.uploadlogourl = this.logourl + '/png/800';
          } 
      } else {
          this.$nextLabel.html(Content.get('Proceed without Logo'));
          this.$next.addClass(Classes.BUTTON_NEUTRAL);
          this.$prev.attr("id", "Header-Previous-BrowseLogo");
      }
      // mahesh
      // this.updateSelected()
      this.onNextClick(event);
      return this;
  };

  /**
   * @method updateTitle
   * @chainable
   */
  proto.updateTitle = function() {
      var state = this.state;

      if (!state) {
          return this;
      }

      this.$title.html(this.titleTemplate({
          id: state.id,
          description: state.description
      }));

      return this;
  };

  // -- Event Handlers -------------------------------------------------------

  /**
   * @method onChange
   * @param {jQuery.Event} event
   * @callback
   */
  proto.onCategoryChange = function() {
      this.updateSubcategories();
  };


  /**
   * @method onHideAction
   * @param {jQuery.Event} event
   * @callback
   */
  proto.onHideAction = function() {
      this.$view.hide();
  };

  /**
   * @method onShowAction
   * @param {jQuery.Event} event
   * @callback
   */
  proto.onShowAction = function(event, done) {
    $('[data-controller="LogoMixFilterController"]').show();
    if(SessionStorage.getValue("CI_ML")) {
        this.$filter.find("#line_1").val(SessionStorage.getValue("CI_ML"));        
    }
    if(SessionStorage.getValue("CI_DE")) {
        this.$filter.find("#line_2").val(SessionStorage.getValue("CI_DE"));
    }
    let filterObj = {
        line1: this.$filter.find("#line_1").val(),
        line2: this.$filter.find("#line_2").val(),
        category: this.$filter.find("#category").val(),
        layout: this.$filter.find("#layout").val(),
        style: this.$filter.find("#style").val(),
        keyword: this.$filter.find("#keyword").val(),
    };
    EventController.emit(ActionEvents.UPDATELOGORESULTS, filterObj); 
  };

  proto.getImageFormUrl = function(url, callback) {
      var img = new Image();
      img.setAttribute('crossOrigin', 'anonymous');
      let $logosspinnercontainer = this.$logosspinnercontainer;
      img.onload = function (a) {
        var canvas = document.createElement("canvas");
        canvas.width = this.width;
        canvas.height = this.height;
        var ctx = canvas.getContext("2d");
        ctx.drawImage(this, 0, 0);
    
        var dataURI = canvas.toDataURL("image/jpg");
          
        // convert base64/URLEncoded data component to raw binary data held in a string
        var byteString;
        if (dataURI.split(',')[0].indexOf('base64') >= 0)
            byteString = atob(dataURI.split(',')[1]);
        else
            byteString = unescape(dataURI.split(',')[1]);
    
        // separate out the mime component
        var mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];
    
        // write the bytes of the string to a typed array
        var ia = new Uint8Array(byteString.length);
        for (var i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i);
        }
        return callback(new Blob([ia], { type: mimeString }));
      }
      img.onerror = function (error) {
        // console.log('logo download error', error);

        var logo = { logoType: 'NO LOGO' };     
        EventController
          .emit(ProductEvents.LOGO_CHANGE, null)
          .emit(StateEvents.CHANGE);
        alert('Our image service is taking an unusually long time to respond. Please reload this page and try again.')
    }
    img.src = url;
  }

  /**
   * @method onNextClick
   * @param {jQuery.Event} event
   * @callback
   */
  proto.onNextClick = function(event) {
    event.preventDefault();
    var logo = this.logo || { logoType: 'NO LOGO' };
     
    if( (event.target.textContent.indexOf("Proceed without Logo")>=0) || (typeof this.uploadlogourl == 'undefined')){
      this.track({
        pageName: this.site_prefix + ': W2P: Logo No Logo',
      });
      this.current = logo;
      delete this.callback;
 
      EventController
        .emit(ProductEvents.LOGO_CHANGE, logo)
        // .emit(ActionEvents.HIDE_ALL)
        .emit(StateEvents.CHANGE)
        .emit(ActionEvents.PRODUCT_STEP);
         
    } else {
      let $logosspinnercontainer = this.$logosspinnercontainer;
      this.getImageFormUrl(this.uploadlogourl, function (blobImage) {
          var file = blobImage;

          var myarr = file.type.split("/");
          var  filename ='filename.' + myarr[myarr.length - 1];
          var formData = new FormData();
          formData.append('source', '');
          formData.append('noScale', 'false');
          formData.append('file', file, filename);

          $.ajax({
              url: Settings.SVC_UPLOAD,
              type: 'POST',
              data: formData,
              cache: false,
              dataType: 'json',
              processData: false, // Don't process the files
              contentType: false, // Set content type to false as jQuery will tell the server its a query string request
              headers: {'Content-Type': undefined},
              xhrFields: {
                  withCredentials: false
              },
              success: function(data, textStatus, jqXHR) {
                  EventController.emit(ActionEvents.MIX_LOGO_UPLOAD_COMPLETE, data);
              },
              error: function(jqXHR, textStatus, errorThrown) {
                  var logo = { logoType: 'NO LOGO' };     
                  EventController
                      .emit(ProductEvents.LOGO_CHANGE, null)
                      .emit(StateEvents.CHANGE);
                  alert('Our image service is taking an unusually long time to respond. Please reload this page and try again.')
              }
          });
      });
    }
  };

  /**
   * @method onStateChange
   * @callback
   */
  proto.onStateChange = function(event, state) {
      this.state = state;
      this.updateTitle();
  };

  /**
   * @method updateNextRight
   * @chainable
   */
  proto.updateNextRight = function(logourl, logourl2) {
    this.logourl = logourl2; 
    if (this.logourl) {
         var host = extractHostname(this.logourl);
         var domain = host.split('.');
         if(domain[1] != 'deluxe') {
            // sizeremoved
            // this.uploadlogourl = this.logourl;
            this.uploadlogourl = this.logourl + '/png/800';
         } 
     } else {
         this.$nextLabel.html(Content.get('Proceed without Logo'));
         this.$next.addClass(Classes.BUTTON_NEUTRAL);
         this.$prev.attr("id", "Header-Previous-BrowseLogo");
     }
    //mahesh
    //  this.updateSelected()
    this.onNextClick(event);
    return this;
 };

  proto.onRefreshLogos = function () {
    EventController.emit(ActionEvents.UPDATELOGORESULTS, {});
  };

  proto.onCancelClick = function(event) {
    EventController.emit(ActionEvents.LOGO_CANCEL, event);
  }

  proto.getLogoColor = function(event, data) {
    this.defaultLogoColor = data;
  }

  return LogoMixFilterController;
});