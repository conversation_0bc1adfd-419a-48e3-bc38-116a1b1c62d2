define(function(require) {
    'use strict';

    var $ = require('jquery');
    var parse = require('mout/queryString/parse');
    var bindAll = require('mout/object/bindAll');
    var setParam = require('mout/queryString/setParam');

    var INSTANCE;

    /**
     * @class App.Models.Query
     * @constructor
     */
    var Query = function() {
        bindAll(this, 'onChange','parseQueryString');

        this._parse(true);

        $(window).on('popstate', this.onChange);
    };

    var proto = Query.prototype;

    /**
     * @method on
     * @chainable
     */
    proto.on = $.fn.on;

    /**
     * @method on
     * @chainable
     */
    proto.one = $.fn.one;

    /**
     * @method on
     * @chainable
     */
    proto.off = $.fn.off;

    /**
     * @method on
     * @chainable
     */
    proto.emit = $.fn.trigger;

    /**
     * Each of the above methods uses jQuery.each to call these methods
     * on all jQuery elements. This does not apply to this object, and
     * leaving the each method as is will iterate through all methods.
     * Instead, just call the callback with the context of EventController.
     *
     * @method each
     * @param {Function} callback
     * @param {Array} args
     * @chainable
     */
    proto.each = function(callback, args) {
        // IE8 requires args to be defined
        callback.apply(this, args || []);

        return this;
    };

    /**
     * Set value
     *
     * @method set
     * @param {String} key
     * @param {String} value
     * @param {Boolean} [silent=false]
     * @chainable
     */
    proto.set = function(key, value, silent) {
        var href = location.href.replace(location.origin, '');
        href = setParam(href, key, value);

        if (silent === true) {
            this[key] = value;
        }

        if ('state' in history) {
            history.replaceState(null, '', href);
            this.onChange(); // manually trigger change
        } else {
            location.replace(href);
        }

        return this;
    };

    /**
     * Parse query string parameters from a URL
     * 
     * @param {string} url - The URL containing query parameters
     * @returns {Object} - Object containing the parsed query parameters
     */
    proto.parseQueryString = function(url) {
        const queryParams = {};
        const parts = url.split('#');
        const queryString = parts.length > 1 ? parts[0].split('?')[1] : parts[0].split('?')[1];
        if (queryString) {
            const pairs = queryString.split('&');
            pairs.forEach(function (pair) {
                if(pair !== ""){
                    var pairArray = pair.split('=');
                    var key = pairArray[0];
                    var value = pairArray[1];                
                    const sanitizedKey = decodeURIComponent(key);
                    const sanitizedValue = decodeURIComponent(value.replace(/\+/g, ' '));
                    queryParams[sanitizedKey] = sanitizedValue !== undefined ? sanitizedValue : '';
                }
            });
        }
        return queryParams;
    }

    /**
     * @method _parse
     * @param {Boolean} [silent=false]
     * @private
     */
    proto._parse = function(silent) {
        var query = this.parseQueryString(location.href);
        var unChanged = true;
        var key;

        for (key in query) {
            if (query.hasOwnProperty(key)) {
                unChanged &= this[key] === query[key];
                this[key] = query[key];
                if(key == 'rec' || key == 'recSkuId'){
                   if(query[key].toString().indexOf(",") > -1)
                        query[key] = query[key].split(",");
                   
                }
            }
        }

        if (silent !== true && !unChanged) {
            this.emit(this.EVENT_CHANGE);
        }
    };

    /**
     * @method onChange
     * @callback
     */
    proto.onChange = function() {
        this._parse();
    };

    /**
     * @property EVENT_CHANGE
     * @type String
     * @event
     */
    proto.EVENT_CHANGE = 'change';

    /**
     * Get single instance of query
     *
     * @method getInstance
     * @returns {App.Models.Query}
     * @static
     */
    Query.getInstance = function() {
        if (!INSTANCE) {
            INSTANCE = new Query();
        }

        return INSTANCE;
    };

    return Query;

});