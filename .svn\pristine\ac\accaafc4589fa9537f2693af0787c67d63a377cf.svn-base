define(function(require) {
    'use strict';
    var Controller = require('./Controller');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var SessionStorage = require('../providers/SessionStorage');
    var Query = require('models/Query');

    /**
     * @class App.Controllers.PricingWidget
     * @extends App.Controllers.Controller
     *
     * @constructor
     * @param {Object} config
     * @param {jQuery} config.view
     * @param {Models.Ui.PricingWidget} config.model
     */
    
    var query = Query.getInstance();
    function PricingWidgetController(config) {
        bindAll(this,
            'onProductChange',
            'onSurchargeChange',
            'onNextClick',
            'onWidgetDetailsClick'
        );

        Controller.call(this, config);
    }
    var proto = inherits(PricingWidgetController, Controller);

    /**
     * @property registry
     * @type {Object.<String,App.Controllers.Controller>}
     */
    proto.registry = {
        QuestionController: require('./Question')
    };
    
    /**
     * @method init
     * @return {Promise}
     */
    proto.init = function() {
        SessionStorage.storeValue('pricingViewFlag', 0);
        var model = this.model;

        this.index = {
            QuestionController: model.questions
        };
        return this;
    }

    /**
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = require('hbs!templates/pricingwidget');


    /**
     * @method render
     * @chainable
     */
    proto.render = function() {
        var $view = this.$view;
        var model = this.model;
        var modelQuestions = model.questions.getDisplayable();
        var questionsIdsToShow = ['quantity', 'productId', 'DETAILS'];

        var filteredQuestions = modelQuestions.filter(function(modelQuestionItem){
            return questionsIdsToShow.indexOf(modelQuestionItem.id) > -1;
        });         

        $view.html(this.template({
            questions: filteredQuestions
        }));

        /**
         * DCOM-16523 start - removes unused DETAILS <div> in stepcontroller
         */
        ($('[data-id="DETAILS"]')[0]).remove();
        /**
         * DCOM-16523 end
         */

        if (!model.tabs.length()) {
            $view.hide();
        }

        return this.start();
    };

    /**
     * @method getProductValue
     * @return {string}
     * product.questions._items[].options_items[].surcharge.id
     */
    proto.getProductValue = function(product,productVal) {
        
        for(var j = 0; j < product.options._items.length; j++){
            if(product.options._items[j].id == productVal){
                var val = product.options._items[j].desc;
            }
        }
        return val;
    };  
    
    return PricingWidgetController;
});
