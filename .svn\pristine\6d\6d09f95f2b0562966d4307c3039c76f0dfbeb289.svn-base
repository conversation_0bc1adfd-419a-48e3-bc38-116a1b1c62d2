{{#if desc}}
    <h2 class="hdg hdg_h2 hdg_return ">
    	{{desc}}
    	{{#is id 'ST'}}<span id="voucherHelp" data-template='<div id="voucher-popup" class="custpop popover" role="tooltip"><div class="arrow"></div><div class="popover-body"></div></div>' data-container="body" data-placement="bottom" class="js-button-voucher-action pop-space" alt="voucherHelp"></span>{{/is}}
        {{#is id 'LS'}}<span id="signatureTextHelp" data-template='<div id="signature-popup" class="custpop popover" role="tooltip"><div class="arrow"></div><div class="popover-body"></div></div>' data-container="body" data-placement="bottom" class="js-button-signature-action pop-space" alt="signatureTextHelp"></span>{{/is}}
        {{#is id 'SM'}}<span id="sideMarginHelp" data-template='<div id="sidemargin-popup" class="custpop popover" role="tooltip"><div class="arrow"></div><div class="popover-body"></div></div>' data-container="body" data-placement="bottom" class="js-button-sidemargin-action pop-space" alt="sideMarginHelp"></span>{{/is}}
    </h2>  
{{/if}}
{{#if lines}}
    <ul class="vList vList_tight">
        {{#each lines}}
            <li data-controller="TextBlockLineQuestionController" data-id="{{id}}" data-parent-id="{{../id}}"></li>
        {{/each}}
    </ul>
{{/if}}