define(function (require) {
    'use strict';

    var AbstractModel = require('./Abstract');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.InkColor
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} InkColorValues
     */
    var InkColorModel = function (InkColorValues) {
        AbstractModel.call(this, InkColorValues);
    };

    var proto = inherits(InkColorModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} InkColorValues
     * @chainable
     */
    proto.init = function(InkColorValues) {

        /**
         * @property id
         * @default {null}
         * @type {string}
         */
        this.id = null;

        /**
         * @property code
         * @default {null}
         * @type {string}
         */
        this.code = null;

        /**
         * @property description
         * @default {null}
         * @type {string}
         */
        this.description = null;

        /**
         * @property type
         * @default {null}
         * @type {string}
         */
        this.type = null;

        /**
         * @property redLevel
         * @default {null}
         * @type {string}
         */
        this.redLevel = null;

        /**
         * @property greenLevel
         * @default {null}
         * @type {string}
         */
        this.greenLevel = null;

        /**
         * @property blueLevel
         * @default {null}
         * @type {string}
         */
        this.blueLevel = null;

        /**
         * @property swatch
         * @default {null}
         * @type {string}
         */
        this.swatch = null;
        
        /**
         * @property logo_rgb
         * @default {null}
         * @type {string}
         */
        this.logo_rgb = null;


        // run the parent init method to parse determine the data type
        base.init.call(this, InkColorValues);

        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param json the original JSON data.
     */
    proto.fromJSON = function(json) {
        this.id = json.id;
        this.code = json.code;
        this.type = json.type;
        // QA API uses desc, PROD uses descr
        this.description = json.descr || json.desc;
        this.blueLevel = parseInt(json.b, 10);
        this.redLevel = parseInt(json.r, 10);
        this.greenLevel = parseInt(json.g, 10);
        this.swatch = json.swatch;
        this.logo_rgb = json.logo_rgb;
    };

    /**
     * Returns the rgb values as a string
     *
     * @method getRGBString
     * @return {string}
     */
    proto.getRGBString = function() {
        return this.redLevel + ',' + this.greenLevel + ',' + this.blueLevel;
    };

    return InkColorModel;
});
