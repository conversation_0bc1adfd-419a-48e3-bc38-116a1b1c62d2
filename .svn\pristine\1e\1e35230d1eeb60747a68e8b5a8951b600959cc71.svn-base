define(function(require) {
    'use strict';

    var Handlebars = require('hbs/handlebars');
    var merge = require('mout/object/merge');

    /**
     * @type {Function}
     * @param {String} name Name of the partial to load.
     * @param {Object} options Handlebars partial options.
     * @return {String}
     */
    var partial = function (name, options) {
        var partials = Handlebars.partials;

        if (typeof partials[name] !== 'function') {
            partials[name] = Handlebars.compile(partials[name]);
        }

        return partials[name](merge(this, options && options.hash));
    };

    Handlebars.registerHelper('partial', partial);

    return partial;
});
