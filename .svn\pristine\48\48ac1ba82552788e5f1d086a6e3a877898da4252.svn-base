/* @import './base/elements.css'; */

body {
  background-color: #ffffff;
  overflow: unset;
}

/* @import './modules/radio.css'; */

.radio + label {
  color: #000f0c;
  font-size: 16px;
  text-decoration: underline;
}

.radio + label:before {
  background-position: 0px 0px;
}

.radio.isChecked + label {
  color: #3174d8 !important;
}

.radio.isChecked + label:before {
  background-position: 0 -24px;
}


/* @import './modules/button.css'; */

.button,
.button:hover {
  background: #d61120;
  color: #ffffff;
  font-family: 'Metro Sans-Bold', 'Source Sans Pro', sans-serif;
  text-transform: uppercase;
  height: auto;
  font-size: 13px;
  /* padding: 10px 26px; */
}

.button:focus {
  background: #b8dade !important;
  color: #000f0c;
  text-decoration: none;
}

@media (hover: hover) and (pointer: fine) {
  .button:hover {
    background: #b8dade !important;
    color: #000f0c;
    text-decoration: none;
  }
}

@media (pointer: coarse) {
  .button:active {
    background: #b8dade !important;
    color: #000f0c;
    text-decoration: none;
  }
}

.button_neutral {
  background: #ffffff !important;
  color: #d61120 !important;
  border: 2px solid !important;
  border-color: #d61120;
  font-family: 'Metro Sans-Bold', 'Source Sans Pro', sans-serif;
  text-transform: uppercase;
  height: auto;
  font-size: 13px;
  padding: 10px 26px !important; 
}

.button_neutral:focus,
.button_neutral:hover {
  background: #b8dade !important;
  border-color: #b8dade !important;
  color: #000f0c !important;
  text-decoration: none;
}

.button_grey {
  background: #757575 !important;
}

.button_blue {
  background: #802d73 !important;
  font-family: 'Source Sans Pro', sans-serif;
  text-transform: uppercase;
  height: 31px;
  font-size: 12px;
  border-radius: 3px;
  padding: 7px 7px;
}

.button_blue:hover,
.button_blue:focus {
  background: #4e1042 !important;
}

.button_file_browse {
  height: 15px;
}

.progressbar {
  background: #e9e9e9 none repeat scroll 0 0;
  border-radius: 11px;
  height: 13px;
  padding-left: 3px;
  padding-right: 3px;
  padding-top: 3px;
  width: 100%;
}

.progress {
  background: #c34924;
  border-radius: 11px;
  height: 10px;
}

.padding_4px {
  padding-top: 4px;
}

h2 {
  line-height: 1.3846153846153846em;
}

.button_prev {
  /* padding: 10px; */
  /* min-width: 106px !important; */
}

.button_next,
.button_prev {
  /* padding: 10px; */
  /* min-width: 48% !important; */
  /* width: 100% !important; */
}

.button_secondary {
  background: #ffffff !important;
  color: #d61120 !important;
  font-family: 'Metro Sans-Bold', 'Source Sans Pro', sans-serif;
  text-transform: uppercase;
  height: auto;
  font-size: 12px;
  padding: 13px 26px;
  border: 2px solid #d61120 !important;
  border-radius: 2px;
}

/* on hover - Teal */
.button_secondary:focus,
.button_secondary:hover {
  background: #b8dade !important;
  color: #000f0c;
  text-decoration: none;
  border: 2px solid #b8dade;
  border-radius: 2px;
}

.button_grey:focus,
.button_grey:hover {
  background: #757575 !important;
  color: #ffffff !important;
}

/* @import './modules/designSwatch.css'; */

/* design page */
.designRadio.isChecked + label > .designSwatchImage {
  border: 2px solid #d61120;
}

.designSwatchImage:hover {
  border: 2px solid #3178d4 !important;
}

/* color page */
.designRadio.isChecked + label > .designBox:before {
  border: 3px solid #3178d4 !important;
}

.choice-hd {
  background-color: #fff;
}

.choice-list label:hover {
  border-color: #b8dade;
  box-shadow: inset 2px 2px 0px #b8dade, inset -2px -2px 0px #b8dade; 
}

.choice-list .isChecked+label {
  border-color: #3178d4 !important;
  box-shadow: inset 2px 2px 0px #3178d4, inset -2px -2px 0px #3178d4 !important; 
}

@media (hover: hover) {
  .designBox_active:before {
    border: 2px solid #d61120 !important;
  }

  .designRadio.isChecked + label > .designBox:hover:before {
    border: 3px solid #b8dade !important;
  }

  .designBox_active:hover:before {
    border: 3px solid #b8dade !important;
  }

  .designBox:focus:before,
  .designBox:hover:before {
    border: 3px solid #b8dade !important;
  }
}

/* @import './modules/error.css'; */

.error {
  color: #d61120 !important;
}
.error-head {
  color: #d61120;
}

.logo_text_error {
  color: #d61120 !important;
}

/* @import './modules/hdg.css'; */

.hdg {
  color: #000f0c;
  margin-bottom: 8px;
  font-weight: bold;
}
.hdg_h2 {
  font-size: 22px;
  font-weight: normal;
}
.hdg_sub_h2 {
  font-size: 16px;
  margin-bottom: 8px;
}
.mix-hdg_caps_temp {
  text-transform: uppercase;
}
.hdg_h4 {
  margin-bottom: 4px !important;
}
.mix-hdg_red {
  color: #d61120;
}
.mix-hdg_blue {
  color: #0070c9 !important;
}
.font-size {
  font-size: 20px !important;
}
.mix-hdg_caps_temp h1 {
  margin-bottom: 5px !important;
}

/* @import './modules/link.css'; */

.link {
  transition: background-size 0.3s ease;
  text-decoration: underline !important;
  background: linear-gradient(to bottom, transparent 62%, #b8dade 0) left
    center/0 100% no-repeat;
}

.link_button {
  text-transform: uppercase;
}

/* @import './modules/site.css'; */

.site-hd-right {
  padding: 0px 15px 0px 0px;
}

.cfgSiteLogo {
  float: left;
  margin-right: 20px;
  padding-top: 20px;
}

.error-subtext {
  float: left;
}

.deluxe-file {
  font-size: 10px !important;
}

div#ax_paragraph {
  padding: 20px 0 15px 0;
  font-family: 'Arial Regular', 'Arial';
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  color: #000f0c;
  float: right;
  line-height: normal;
}

.currentPdt {
  font-weight: 700;
}

.js-product-nav {
  clear: both;
}

.js-product-nav:first-child {
  padding-top: 28px;
}

body {
  color: #000f0c !important;
}

.js-hide-price {
  color: #000f0c !important;
}

.js-price-text {
  transition: background-size 0.3s ease;
  color: #000f0c !important;
  text-decoration: underline;
  background: linear-gradient(to bottom, transparent 62%, #b8dade 0) left
    center/0 100% no-repeat;
}

.js-price-text:hover {
  color: #000f0c !important;
  text-decoration: none !important;
  background-size: 100% 100%;
}

.link-container {
  color: #3174d8 !important;
}

.progressBlocks .isActive {
  color: #3174d8 !important;
  text-decoration: underline;
}

.progressBlocks .progressCurrent {
  text-decoration: none;
}

.detailBox_active,
.detailBox_active .detailBox-header {
  border-color: #d61120 !important;
  background-color: #d61120 !important;
}

.detailBox_active .hdg {
  color: #ffffff;
}

.zoombox,
.zoombox_connector {
  background-color: #d61120 !important;
  opacity: 0.5;
}

.boldtxt {
  font-weight: bold;
}

.js-logo-subcategories {
  margin-left: 5px;
}

.filterLogos button {
  width: 100%;
  background-color: #d61120 !important;
  border: 0px !important;
  border-radius: 2px;
  padding: 12px;
  color: #fff;
  font-size: 16px;
  margin: 20px 0px;
}

.filterLogos button:hover {
  background: #b8dade !important;
  color: #000f0c;
  text-decoration: none;
}

.mix-selected-logo {
  border: 4px solid #d61120 !important;
}

.logoDiv input {
  background-color: white;
}

.cfgSiteLogo .siteTitle {
  display: none;
}

.whitetext {
  color: #ffffff !important;
}

.popover-body #personalization {
  color: #ffffff;
}

.mix-pages li .c-page {
  color: #000f0c !important;
}

.mix-pages .designBox {
  border-color: #0070c9 !important;
  color: #0070c9 !important;
}

.fbt-product {
  border: 2px solid #3174d8 !important;
}

.add_underline a {
  text-decoration: underline;
  transition: background-size 0.3s ease;
  background: linear-gradient(to bottom, transparent 62%, #b8dade 0) left
    center/0 100% no-repeat;
}

.add_underline a:hover {
  text-decoration: none !important;
  background-size: 100% 100%;
}

div#ax_paragraph {
  color: #000f0c !important;
}

.link_Skip p a {
  color: #000f0c !important;
  text-decoration: underline !important;
  transition: background-size 0.3s ease;
  background: linear-gradient(to bottom, transparent 62%, #b8dade 0) left
    center/0 100% no-repeat;
}

.link_Skip p a:hover {
  color: #000f0c !important;
  text-decoration: none !important;
  background-size: 100% 100%;
}

.minw-100 {
  min-width: 100px !important;
}

.fbt_hybrid_box .inputBox_select-input,
.fbt_hybrid_box .inputBox_select:after,
.fbt-hybrid-rout .inputBox {
  background-color: #ffffff;
}

.fbt-copy {
  background-image: url('../media/images/arrow-red.png') !important;
}

.mix-hdg_green {
  color: #00a76d !important;
}
.options h2.mix-hdg_green {
  color: #00a76d !important;
}

a.no_hover_underline:hover {
  background: none !important;
}

.media.media_logoSelect,
.js-logos {
  margin-right: 20px;
}

.subtotal_desc,
.subtotal_val {
  font-weight: bold;
}

/* @import './modules/step.css'; */

.step {
  line-height: 0.6;
  font-weight: 700;
  text-transform: uppercase;
  margin-bottom: 5px !important;
}

/* @import './modules/tooltip.css'; */

/* .popover {
	width:432px;
	background-color:#aedfe8;
	border:4px solid transparent !important;
	box-shadow: none !important;
} */

/* .popover .arrow,.popover .arrow:after {
	background-color:#aedfe8;
} */

.custpop {
  border: 4px solid transparent !important;
  box-shadow: none !important;
}

.popover .arrow1,
.popover .arrow1:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.popover .arrow1 {
  border-width: 11px;
}
.popover .arrow1:after {
  border-width: 10px;
  content: '';
}
.popover.bottom .arrow1 {
  top: -11px;
  left: 348px;
  margin-left: -11px;
  border-bottom-color: #fff;
  border-top-width: 0;
}
.popover.bottom .arrow1:after {
  top: 1px;
  margin-left: -10px;
  border-bottom-color: #3174d8;
  border-top-width: 0;
  content: ' ';
}

#CopiesHelp,
/* #NumberingHelp, */
#CVHelp,
#FbtCopiesHelp,
#ExtraColorHelp {
  padding-left: 5px;
  cursor: pointer;
  margin-bottom: -3px;
}

.custpop.bottom .arrow1 {
  margin-left: 363px !important;
}

.custpop.bottom .arrow1:after {
  border-bottom-color: #3174d8 !important;
}

.popover,
.custpop {
  background-color: #3174d8 !important;
}

.popover .othertxt,
.custpop .othertxt,
.popover .titletxt,
.custpop .titletxt {
  color: #ffffff;
  font-weight: normal;
}

#personalization_link.custpop.bottom {
  margin-top: 36px !important;
  margin-left: -96px !important;
}

.custpop.bottom {
  margin-top: 7px !important;
}

.popover-heading {
  color: #ffffff !important;
}

.popovertitle {
  color: #ffffff !important;
}

.foldSubhead {
  color: #ffffff !important;
}

.popover-info {
  color: #ffffff !important;
}

/* @import './modules/uploading.css'; */

.progressbar {
  background: #eaf1fb none repeat scroll 0 0;
  border-radius: 0px;
  height: 9px;
  padding: 0px;
  width: 100%;
}
.progress {
  background: #3174d8;
  border-radius: 0px;
  height: 9px;
}

/* @import './modules/zoomPreview.css'; */

/*DCOM-16528
 .topLens {
	top:42px;
}
 .lens {
	color: #3174D8 !important;
}

.lens span {
	transition: background-size .3s ease;
	text-decoration: underline !important;
	margin-left:4px;
	background: linear-gradient(to bottom, transparent 62%, #B8DADE 0) left center/0 100% no-repeat;
}

.lens span:hover {
	text-decoration: none !important;
	background-size: 100% 100%;
} DCOM-16528
*/

/* top left zoom button */

/*DCOM-16528
 #topLens {
	background: none;
	border: 0px !important;
	top: 0 !important;
}

#topLens:before {
	color:#3174D8 !important;
} DCOM-16528
*/

#viewZoomText {
  transition: background-size 0.3s ease;
  display: inline;
  background: linear-gradient(to bottom, transparent 62%, #b8dade 0) left
    center/0 100% no-repeat;
  text-decoration: underline !important;
}

#viewZoomText:hover {
  text-decoration: none !important;
  background-size: 100% 100%;
}

/* @import './modules/txt.css'; */

.txtLarge {
  font-size: 16px;
  cursor: auto !important;
}

/* @import './modules/blocks.css'; */

.blocks {
  margin-bottom: 10px;
}
.txt_style {
  font-weight: bold;
}

/* @import './modules/inputBox.css'; */

.inputBox {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 16px;
  border: solid #000f0c !important;
  border-width: 0px 0px 2px 0px !important;
  border-radius: 0 !important;
  background-color: #f1f1f1;
}

.inputBox.mix-inputBox_error {
  border: solid #d61120 !important;
  border-width: 0px 0px 2px 0px !important;
}

.inputBox.mix-inputBox_error:hover,
.inputBox.mix-inputBox_error:focus,
.inputBox_error:hover,
.inputBox_error:focus {
  border: solid #d61120 !important;
  border-width: 0px 0px 2px 0px !important;
}

.inputBox.mix-inputBox_error {
  border: solid #d61120 !important;
  border-width: 0px 0px 2px 0px !important;
}

.inputBox:hover,
.inputBox:focus {
  background-color: #f1f1f1 !important;
  border: solid #3174d8 !important;
  border-width: 0px 0px 2px 0px !important;
}

.inputBox_select {
  background-color: #f1f1f1 !important;
}

.inputBox_select-dropdown-item:hover,
.inputBox_select-dropdown-item_focused {
  background-color: #c7e3ef;
}

.inputBox_select:hover,
.inputBox_select:focus {
  background-color: #f1f1f1 !important;
  border: solid #3174d8 !important;
  border-width: 0px 0px 2px 0px !important;
}

/* @import './modules/comment.css'; */

.commentCopy {
  background-color: rgb(255, 255, 204);
  padding: 10px 12px 10px 12px;
  margin-bottom: 10px;
  font-size: 14px;
}
.commentLabel {
  color: #333;
}
.commentTextArea {
  width: 98%;
  height: 60px;
  resize: none;
  user-select: text;
  -moz-user-select: text;
  -webkit-user-select: text;
  -ms-user-select: text;
}

/* Storefront specific needed for footer */

.chat-badge {
  height: auto !important;
}
.third-party-links {
  display: inline-flex !important;
  top: -50% !important;
}
.footer .last-row .third-party-links img.badge {
  width: auto !important;
}
.footer .last-row .third-party-links img {
  height: 35px !important;
}
.footer .social-network {
  top: -75% !important;
}
.footer .last-row .col-container {
  padding-right: 90px !important;
}
.footer .social-network .badge.linkedin {
  background: transparent !important;
}
.badge.linkedin {
  background: transparent !important;
}

.pricing-widget-div:hover,
.pricing-widget-div:focus {
  background-color: #ffffff !important;
  border: solid #3174d8 !important;
  border-width: 1px 1px 2px 1px !important;
}
.footer{
  z-index: -1;
}
footer{
  max-width: 1350px;
  margin-left: auto;
  margin-right: auto;
}