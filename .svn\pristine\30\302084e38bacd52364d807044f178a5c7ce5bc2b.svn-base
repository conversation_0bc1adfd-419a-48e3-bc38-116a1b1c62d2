define(function(require){

//automatically generated, do not edit!
//run `node build` instead
return {
    'contains' : require('./queryString/contains'),
    'decode' : require('./queryString/decode'),
    'encode' : require('./queryString/encode'),
    'getParam' : require('./queryString/getParam'),
    'getQuery' : require('./queryString/getQuery'),
    'parse' : require('./queryString/parse'),
    'setParam' : require('./queryString/setParam')
};

});
