var imgwidth = 460 ;
var imgheight = 580 ;
var scene7url ;

(function($){

    // var imgwidth = 460 ;
    // var imgheight = 580;
    // var scene7url ;

    $.fn.iviewer  = function(o)
    {
        return this.each(function()
                        {
                           $(this).data('viewer', new $iv(this,o));
                        });
    }
    
    var defaults = {
        /**
        * start zoom value for image, not used now
        * may be equal to "fit" to fit image into container or scale in % 
        **/
        zoom: 100,
        /**
         * value to scale image while loading with zoom
         **/
        //load_zoom: 150,
        /**
        * base value to scale image
        **/
        zoom_base: 100,
        /**
        * maximum zoom
        **/
        zoom_max: 200,
        /**
        * minimum zoom
        **/
        zoom_min: 100,
        /**
        * value of pixel move on click (right,left,top,down)
        **/
        move_min: 10,
        /**
        * base of rate multiplier.
        * zoom is calculated by formula: zoom_base * zoom_delta^rate
        **/
        zoom_delta: 1.5,
        /**
        * if true plugin doesn't add its own controls
        **/
        ui_disabled: false,
        /**
        * if false, plugin doesn't bind resize event on window and this must 
        * be handled manually
        **/
        update_on_resize: true,
        /**
        * event is triggered when zoom value is changed
        * @param int new zoom value
        * @return boolean if false zoom action is aborted
        **/
        onZoom: null,
        /**
        * callback is fired after plugin setup
        **/
        initCallback: null,
        /**
        * event is fired on drag begin
        * @param object coords mouse coordinates on the image
        * @return boolean if false is returned, drag action is aborted
        **/
        onStartDrag: null,
        /**
        * event is fired on drag action
        * @param object coords mouse coordinates on the image
        **/
        onDrag: null,
        /**
        * event is fired when mouse moves over image
        * @param object coords mouse coordinates on the image
        **/
        onMouseMove: null,
        /**
        * mouse click event
        * @param object coords mouse coordinates on the image
        **/
        onClick: null,
        /**
        * event is fired when image starts to load
        */
        onStartLoad: null,
        /**
        * event is fired, when image is loaded and initially positioned
        */
        onFinishLoad: null,
        /**
         * 
        */        
        errorFlag : 0,
        /**
         * 
         */
        imgUrlPrefix : "",
        appUrlStatus : false,
        imgWidth : null,
        imgHeight : null,
        zoomNotAvailable: 0,
        maxDeviation: 40,
        imgLoad : 0,
        imgContainer: null,
        imgS7MaxWidth: null        
    };


  
    $.iviewer = function(e,o)
    {
        var me = this,
            last, diff, isDragged = false,
            clickDelay = 150; // millisecond delay;
    
        /* object containing actual information about image
        *   @img_object.object - jquery img object
        *   @img_object.orig_{width|height} - original dimensions
        *   @img_object.display_{width|height} - actual dimensions
        */
        this.img_object = {};
        this.zoom_object = {}; //object to show zoom status
        this.image_loaded = false;
        this.itemPartNum = null;
        this.productPartNum = null;
        
        //drag variables
        this.dx = 0; 
        this.dy = 0;
        this.dragged = false;    
        this.settings = $.extend({}, defaults, o || {});
        this.current_zoom = this.settings.zoom;
        if(this.settings.src === null){
            return;
        }
        this.container = $(e); 
        this.update_container_info();
        this.settings.imgContainer = ($("#Hero").length > 0) ? "#Hero" : "";
        //init container
        this.container.find(this.settings.imgContainer).css("overflow","hidden");
        if(this.settings.update_on_resize == true)
        {
            $(window).resize(function()
            {
                me.update_container_info();
            });
        }
        this.img_object.x = 0;
        this.img_object.y = 0;
        
        //init object
        this.img_object.object = this.container.find("#heroImage");
        touchmoveFlag = false;
        isTouch = navigator.userAgent.match(/iPad/i) != null;
        // isTouch = is_iPad;
        if(isTouch){
            this.img_object.object.css({ position: "absolute", top :"1px", left: "1px"});
            this.img_object.object.bind('touchstart', function (e) {
                // if(me.settings.imgWidth > me.settings.imgMinWidth){
                     return me.drag_start(e); 
                // }
            });
            this.img_object.object.bind('touchmove', function (e) {
                touchmoveFlag = true;
                return me.drag(e);
            });
            this.img_object.object.bind('touchend', function (e) {
                return me.drag_end(e);
            });                 
        } else {
             this.img_object.object.css({position: "absolute",top :"1px", left: "1px"}). 
            //bind mouse events
            mousedown(function(e) {         
                last = e.timeStamp;
                // if(me.settings.imgWidth > me.settings.imgMinWidth){
                    return me.drag_start(e);
                // }
            }).
            mousemove(function(e) {     
                return me.drag(e);
            }).
            mouseup(function(e){
                diff = e.timeStamp - last;
                isDragged = ( diff < clickDelay ) ? false : true;
                return me.drag_end(e);
            }).
            mouseleave(function(e) {            
                return me.drag_end(e);
            });
        }
        
        if(this.settings.initCallback){
            this.settings.initCallback.call(this);
        }
        
        $(this.container).on("click touchend","a.zoomIn, a.zoomOut, a#zoomImage, a.zoomRefresh,a.moveLeft,a.moveRight,a.moveTop,a.moveDown", function (e) { 
            e.preventDefault(); 
            var scope = this,
                elemID = $(scope).attr('id'),
                func = function(scope) {                    
                    if(me.settings.zoomCb) {                
                        me.zoomHandler(scope);              
                    }
                };
            if(!touchmoveFlag){
                if(elemID === "zoom-in" || elemID === "zoom-out" || elemID === "zoom-refresh" || elemID === "move-Left" || elemID === "move-right" || elemID === "move-top" || elemID === "move-down" ) {
                    func(scope);
                }else if(elemID == "zoomImage"){
                    if (!isDragged) { func(scope); }
                }
            }else{
                touchmoveFlag = false;
            }
            return false;           
        });
    }
    
    var $iv = $.iviewer;
    var top;
    var left;
    $iv.fn = $iv.prototype = {
        iviewer : "0.4.2"
    }
    $iv.fn.extend = $iv.extend = $.extend;
    $iv.fn.extend({
        reset: function() {
            var me = this;
            me.settings.imgWidth = me.settings.imgMinWidth;
            me.settings.imgHeight = me.settings.imgMinHeight; 
            me.settings.imgLoad = 0; 
            me.settings.errorFlag = 0;
            me.settings.zoomNotAvailable = 0;
        },
        setImageObj: function(id, title) {
            var me = this;
            me.settings.imageID = id;
            me.settings.imageTitle = title;
        },

        getImageObj: function() {
            var me = this;
            return {
                imgId:me.settings.imageID,
                imgTitle:me.settings.imageTitle
            };
        },

        getDefaultValues: function() {
            var me = this;
            return {
                width:me.settings.imgWidth,
                min:me.settings.imgMinWidth,
                max:me.settings.imgMaxWidth
            };
        }, 

        zoomHandler: function(scope)   {
            var   me          = this,itemPartNum,imgUrlPrefix,deviationWidth,imageUrl,
                  productId   = me.productPartNum,
                  itemPartNum = me.container.find('.zoomItemPartNum'),
                  imgObj      = me.getImageObj(),
                  imgId       = imgObj.imgId,
                  buttonID    = $(scope).attr('id');
                  
            me.settings.errorFlag = 0;
  
           if(buttonID == "zoom-in" || buttonID == "heroImageBundle" ||  buttonID == "heroZoomImage") {
              imgId = productId; me.setImageObj(imgId);
              if(me.settings.imgWidth < me.settings.imgMaxWidth && !me.settings.zoomNotAvailable) {
                  me.settings.imgWidth = (me.settings.imgWidth + me.settings.zoomRatio);
                  me.settings.imgHeight = (me.settings.imgHeight + me.settings.zoomRatio);// new addition for height
                  if (this.settings.appUrlStatus) {
                      imageUrl = me.settings.imgUrlPrefix + imgId + "?"+this.appendUrl+"&wid=" + me.settings.imgWidth + "&hei=" + me.settings.imgHeight;    
                  }else {
                      imageUrl = me.settings.imgUrlPrefix + imgId + "?wid=" + me.settings.imgWidth + "&hei=" + me.settings.imgHeight;    
                  }
              }else{ me.settings.errorFlag = 1; }
              
            } else if( buttonID == "zoom-out" ) {
              if(imgId == "" || typeof imgId === "undefined"){ imgId = productId; me.setImageObj(imgId);}
              if(me.settings.imgWidth > me.settings.imgMinWidth) {
                  me.settings.imgWidth = (me.settings.imgWidth - me.settings.zoomRatio);
                  me.settings.imgHeight = (me.settings.imgHeight - me.settings.zoomRatio);// new addition for height
                  if (this.settings.appUrlStatus) {
                      imageUrl = me.settings.imgUrlPrefix + imgId + "?"+this.appendUrl+"&wid=" + me.settings.imgWidth + "&hei=" + me.settings.imgHeight;    
                  }else {
                      imageUrl = me.settings.imgUrlPrefix + imgId + "?wid=" + me.settings.imgWidth + "&hei=" + me.settings.imgHeight;    
                  }
                  if ( me.settings.zoomNotAvailable == 1 ){
                      me.settings.zoomNotAvailable = 0; 
                  }
               } else { me.settings.errorFlag = 1; }
            } else if(buttonID == "zoom-refresh"){
              me.settings.imgWidth = me.settings.imgMinWidth;
              me.settings.imgHeight = me.settings.imgMinHeight// new addition for height
              if ( me.settings.zoomNotAvailable == 1 ){ me.settings.zoomNotAvailable = 0; }
              if (this.settings.appUrlStatus) {
                      imageUrl = me.settings.imgUrlPrefix + imgId + "?"+this.appendUrl+"&wid=" + me.settings.imgWidth + "&hei=" + me.settings.imgHeight;    
                  }else {
                      imageUrl = me.settings.imgUrlPrefix + imgId + "?wid=" + me.settings.imgWidth + "&hei=" + me.settings.imgHeight;    
                  }
              $("#zoom-in").focus();
            }else if( buttonID == "move-Left" ) { 
               this.img_object.x = this.img_object.x + me.settings.move_min;
               this.setCoords(this.img_object.x, this.img_object.y);
            }else if (buttonID == "move-right" ) { 
                this.img_object.x = this.img_object.x - me.settings.move_min;
                  this.setCoords(this.img_object.x, this.img_object.y);
             }else if (buttonID == "move-top" ) { 
              this.img_object.y = this.img_object.y + me.settings.move_min;
              this.setCoords(this.img_object.x, this.img_object.y);
             }else if (buttonID == "move-down" ) { 
               this.img_object.y = this.img_object.y - me.settings.move_min;
               this.setCoords(this.img_object.x, this.img_object.y);
             }
  
  
              if ( me.settings.errorFlag == 0 ) {
                  if( me.settings.imgLoad == 0  && buttonID != "zoom-refresh") {
                      //$.getScript(me.settings.imgUrlPrefix + imgId +"?req=imageprops,json", function(data) {
                          deviationWidth = (me.settings.imgWidth - me.settings.zoomRatio) + me.settings.maxDeviation;
                          imageMaxWidth = 1200;
                          me.settings.imgS7MaxWidth = imageMaxWidth;
                          if(me.settings.imgS7MaxWidth > deviationWidth){
                              me.checkAvailableZoom(me.settings.imgS7MaxWidth, deviationWidth, me.settings.zoomRatio,imageUrl, scope);
                          }else{
                              $(scope).parents(".primaryImgContainer").find(".zoomBox").hide();
                              $(scope).parents(".primaryImgContainer").find(".zoomMessage").show();
                              me.settings.zoomNotAvailable = 1;
                          }
                          me.settings.imgLoad = 1;
                      //});
                  }else if(buttonID == "move-Left" || buttonID == "move-right" ||  buttonID == "move-top" || buttonID == "move-down"  ){
                      if (this.settings.appUrlStatus) {
                           imageUrl = me.settings.imgUrlPrefix + imgId + "?"+this.appendUrl+"&wid=" + me.settings.imgWidth + "&hei=" + me.settings.imgHeight;    
                      }else {
                          imageUrl = me.settings.imgUrlPrefix + imgId + "?wid=" + me.settings.imgWidth + "&hei=" + me.settings.imgHeight;    
                      }
                  } else {
                      deviationWidth = (me.settings.imgWidth - me.settings.zoomRatio) + me.settings.maxDeviation;
                      me.checkAvailableZoom(me.settings.imgS7MaxWidth, deviationWidth, me.settings.zoomRatio,imageUrl, scope);
                  }
              }
          },
          
        checkAvailableZoom: function (maxWidth, devWidth, zoomRatio, imageUrl, scope) {
            var me = this;
            if(devWidth <= maxWidth) {
                me.loadImage(imageUrl, self.imgTitle, me.settings.imgWidth);
            }
            if(maxWidth <= (devWidth+zoomRatio)){
                me.settings.zoomNotAvailable = 1;
            }
            var obj = {
                width: me.settings.imgWidth,
                zoom: me.settings.zoomNotAvailable,
                min: me.settings.imgMinWidth,
                max: me.settings.imgMaxWidth            
            };
            if(me.settings.zoomCb) {
                me.settings.zoomCb.call(me.settings.context, scope, me, obj);
            }
        },
        
        loadImage: function(src, title, width)
        {
            var reAlignFactorLeft = "";
            this.current_zoom = this.settings.zoom;
            this.image_loaded = false;
            var me = this;   
            if(this.settings.onStartLoad)
            {
               this.settings.onStartLoad.call(this);
            }
            if(width > me.settings.imgMinWidth)
            {
                if(this.settings.imgContainer == "#hero-image"){
                   reAlignFactorLeft =(this.container.find(this.settings.imgContainer).width() - width) / 2;
                }
                reAlignFactor = (width - me.settings.imgMinWidth) / 2;
                top = "-"+reAlignFactor+"px";
                left = (reAlignFactorLeft != "") ? reAlignFactorLeft+"px" : "-"+reAlignFactor+"px" ;
            }
            else
            {
                top = 0;
                left = 0;
            }
            this.img_object.object.hide();
            this.img_object.object.unbind('load').
                removeAttr("src").
                removeAttr("width").
                removeAttr("height").
                on('load',function(){
                    me.image_loaded = true;
                    me.img_object.display_width = me.img_object.orig_width = me.img_object.object[0].naturalWidth;                    
                    me.img_object.display_height = me.img_object.orig_height =me.img_object.object[0].naturalHeight;
                    if(!me.container.hasClass("iviewer_cursor")){
                        me.container.addClass("iviewer_cursor");
                    }
                    if(me.settings.zoom == "fit"){
                        me.fit();
                    }
                    else {
                        me.set_zoom(me.settings.zoom);
                    }
                    
                    if(me.settings.onFinishLoad)
                    {
                       me.settings.onFinishLoad.call(me);
                    }
            }).attr("src",src);
            this.img_object.object.css({ top: top, left: left });
           
        },
               
         /**
        * fits image in the container
        **/
        fit: function()
        {
            var aspect_ratio = this.img_object.orig_width / this.img_object.orig_height;
            var window_ratio = this.settings.width /  this.settings.height;
            var choose_left = (aspect_ratio > window_ratio);
            var new_zoom = 0;
    
            if(choose_left){
                new_zoom = this.settings.width / this.img_object.orig_width * 100;
            }
            else {
                new_zoom = this.settings.height / this.img_object.orig_height * 100;
            }

          this.set_zoom(new_zoom);
        },
        
        
        /**
        * set coordinates of upper left corner of image object
        **/
        setCoords: function(x,y)
        {
            x = parseInt(x, 10);
            y = parseInt(y, 10);

            //do nothing while image is being loaded
            if(!this.image_loaded)
            {
                return;
            }
            
            if(y > 0){
                y = 0;
            }
            if(x > 0){
                x = 0;
            }
            
            if(y + this.img_object.display_height < this.settings.height){
                y = this.settings.height - this.img_object.display_height;  
            }

            if(x + this.img_object.display_width < this.settings.width){
                x =   this.settings.width - this.img_object.display_width ;
            }
        
            if(this.img_object.display_width <= this.settings.width){
                x = -(this.img_object.display_width - this.settings.width)/2;
            }
            if(this.img_object.display_height <= this.settings.height){
                y = -(this.img_object.display_height - this.settings.height)/2;
            }             
            this.img_object.x = x;
            this.img_object.y = y;
            this.img_object.object.css("top",y + "px")
                             .css("left",x + "px");

            this.img_object.object.show();
        },
        
        
        /**
        * get mouse coordinates on the image
        * @param e - object containing pageX and pageY fields, e.g. mouse event object
        *
        * @return object with fields x,y according to coordinates or false
        * if initial coords are not inside image
        **/
        getMouseCoords : function(e)
        {
            var img_offset = this.img_object.object.offset();

            return { x : $iv.descaleValue(e.pageX - img_offset.left, this.current_zoom),
                     y : $iv.descaleValue(e.pageY - img_offset.top, this.current_zoom)
            };
        },
        
        /**
        * set image scale to the new_zoom
        * @param new_zoom image scale in % 
        **/
        set_zoom: function(new_zoom)
        {
            if(this.settings.onZoom && this.settings.onZoom.call(this, new_zoom) == false){
                return;
            }
            
            //do nothing while image is being loaded
            if(!this.image_loaded){
                return;
            }
            
            if(new_zoom <  this.settings.zoom_min)
            {
                new_zoom = this.settings.zoom_min;
            }
            else if(new_zoom > this.settings.zoom_max)
            {
                new_zoom = this.settings.zoom_max;
            }

            /* we fake these values to make fit zoom properly work */
            if(this.current_zoom == "fit")
            {
                var old_x = Math.round(this.settings.width/2 + this.img_object.orig_width/2);
                var old_y = Math.round(this.settings.height/2 + this.img_object.orig_height/2);
                this.current_zoom = 100;
            }
            else {
                var old_x = -parseInt(this.img_object.object.css("left"),10) +
                                            Math.round(this.settings.width/2);
                var old_y = -parseInt(this.img_object.object.css("top"),10) + 
                                            Math.round(this.settings.height/2);
            }

            var new_width = $iv.scaleValue(this.img_object.orig_width, new_zoom);
            var new_height = $iv.scaleValue(this.img_object.orig_height, new_zoom);

            var new_x = $iv.scaleValue( $iv.descaleValue(old_x, this.current_zoom), new_zoom);
            var new_y = $iv.scaleValue( $iv.descaleValue(old_y, this.current_zoom), new_zoom);


            new_x = this.settings.width/2 - new_x;
            new_y = this.settings.height/2 - new_y;
            
            //img height & width
            // this.img_object.object.attr("width",new_width)
            //                  .attr("height",new_height);

            this.img_object.display_width = new_width;
            this.img_object.display_height = new_height;
            this.setCoords(new_x, new_y);
            this.current_zoom = new_zoom;
        },
        
        /**
        * changes zoom scale by delta
        * zoom is calculated by formula: zoom_base * zoom_delta^rate 
        * @param Integer delta number to add to the current multiplier rate number 
        **/
        zoom_by: function(delta)
        {
            var closest_rate = this.find_closest_zoom_rate(this.current_zoom);
            var next_rate = closest_rate + delta;
            var next_zoom = this.settings.zoom_base * Math.pow(this.settings.zoom_delta, next_rate)
            if(delta > 0 && next_zoom < this.current_zoom)
            {
                next_zoom *= this.settings.zoom_delta;
            }
            
            if(delta < 0 && next_zoom > this.current_zoom)
            {
                next_zoom /= this.settings.zoom_delta;
            }
            this.set_zoom(next_zoom);
        },
        
        /**
        * finds closest multiplier rate for value
        * basing on zoom_base and zoom_delta values from settings
        * @param Number value zoom value to examine
        **/
        find_closest_zoom_rate: function(value)
        {
            if(value == this.settings.zoom_base)
            {
                return 0;
            }
            
            function div(val1,val2) { return val1 / val2 };
            function mul(val1,val2) { return val1 * val2 };
            
            var func = (value > this.settings.zoom_base)?mul:div;
            var sgn = (value > this.settings.zoom_base)?1:-1;
            
            var mltplr = this.settings.zoom_delta;
            var rate = 1;
            
            while(Math.abs(func(this.settings.zoom_base, Math.pow(mltplr,rate)) - value) > 
                  Math.abs(func(this.settings.zoom_base, Math.pow(mltplr,rate+1)) - value))
            {
                rate++;
            }
            
            return sgn * rate;
        },
        
        
        update_container_info: function()
        {
            this.settings.height = this.container.find("#Hero").height();
            this.settings.width = this.container.find("#Hero").width();
        },
        
        /**
        *   callback for handling mousdown event to start dragging image
        **/
        drag_start: function(e)
        {
            if(this.settings.onStartDrag && 
               this.settings.onStartDrag.call(this,this.getMouseCoords(e)) == false)
            {
                return false;
            }
            $('.drag_help').fadeOut(300);
            $('#Hero').addClass("remove-spinner");
            /* start drag event*/
            this.dragged = true;
            this.container.addClass("iviewer_drag_cursor");
            this.dx = e.pageX - this.img_object.x;
            this.dy = e.pageY - this.img_object.y;
            return false;
        },
        
        /**
        *   callback for handling mousmove event to drag image
        **/
        drag: function(e)
        {
            this.settings.onMouseMove && 
                    this.settings.onMouseMove.call(this,this.getMouseCoords(e));
            if(this.dragged){
                this.settings.onDrag && 
                        this.settings.onDrag.call(this,this.getMouseCoords(e));
                        
                var ltop =  e.pageY -this.dy;
                var lleft = e.pageX -this.dx;
                this.setCoords(lleft, ltop);
                return false;
            }
        },
        
        /**
        *   callback for handling stop drag
        **/
        drag_end: function(e)
        {
            this.container.removeClass("iviewer_drag_cursor");
            this.dragged=false;
        },
        
        click: function(e)
        {
            this.settings.onClick && 
                    this.settings.onClick.call(this,this.getMouseCoords(e));
        }     
    });
    
    $iv.extend({
        scaleValue: function(value, toZoom)
        {
            return value * toZoom / 100;
        },
        
        descaleValue: function(value, fromZoom)
        {
            return value * 100 / fromZoom;
        }
    });

    zoom = {
        iviewer : {},
        init : function(){
        self = this;
        $("#primaryImgContainer").iviewer({
            initCallback: function () {
                self.iviewer = this;
            },
            imgWidth: imgwidth,
            imgHeight: imgheight,
            zoomRatio: 800,
            appUrlStatus : true,
            imgUrlPrefix : scene7url,
            imgMinWidth: imgwidth,
            imgMaxWidth: 1480,
            imgMinHeight: imgheight,
            imgMaxHeight: 1080,
            zoomCb: self.zoomCb,
            context: self
        });self.initHeroImage();
        },zoomCb : function(context, iviewerObj, dataObj) {
            var self = this,
            currentZoom = (dataObj) ? dataObj.width : 0,
            notAvailable = (dataObj) ? dataObj.zoom : 0,
            imgMinWidth = (dataObj) ? dataObj.min : 0,
            imgMaxWidth = (dataObj) ? dataObj.max : 0,
            zoomIn = iviewerObj.container.find(".zoomInEn"),
            zoomOut = iviewerObj.container.find(".zoomOutEn"),
            zoomRefresh = iviewerObj.container.find(".zoomRefreshEn"),
            zoomInOff = iviewerObj.container.find(".iviewer_zoom_in_off"),
            zoomOutOff = iviewerObj.container.find(".iviewer_zoom_out_off"),
            zoomMoveLeft = iviewerObj.container.find(".iviewer_move_Left"),
            zoomMoveLeftoff = iviewerObj.container.find(".iviewer_move_Left_off"),
            zoomMoveRight = iviewerObj.container.find(".iviewer_move_right"),
            zoomMoveRightoff = iviewerObj.container.find(".iviewer_move_right_off"),
            zoomMoveTop = iviewerObj.container.find(".iviewer_move_top"),
            zoomMoveTopoff = iviewerObj.container.find(".iviewer_move_top_off"),
            zoomMoveDown = iviewerObj.container.find(".iviewer_move_down"),
            zoomMoveDownoff = iviewerObj.container.find(".iviewer_move_down_off"),
            zoomRefreshOff = iviewerObj.container.find(".iviewer_zoom_refresh_off"),
            heroImage = (typeof iviewerObj.container.find('#Hero') !== "undefined") ? iviewerObj.container.find('#Hero') : false;
    
            if (typeof zoomIn !== "undefined" && typeof zoomOut !== "undefined" && typeof zoomRefreshOff !== "undefined" && heroImage) {
                if (currentZoom <= imgMinWidth) {
                    zoomInOff.hide();zoomIn.show();zoomOut.hide();zoomOutOff.show();zoomRefresh.hide();zoomRefreshOff.show();zoomMoveLeft.hide();zoomMoveLeftoff.show();zoomMoveRight.hide();  zoomMoveRightoff.show();zoomMoveTop.hide();zoomMoveTopoff.show();zoomMoveDown.hide();   zoomMoveDownoff.show();
                    zoomRefreshOff.show();zoomRefresh.hide();
                    heroImage.addClass("zoomImageBorder cursorMove");
                } else if (currentZoom >= imgMaxWidth || notAvailable) {
                    zoomIn.hide();zoomInOff.show();zoomOutOff.hide();zoomOut.show();zoomMoveLeft.show();      zoomMoveLeftoff.hide();zoomMoveRight.show();zoomMoveRightoff.hide();zoomMoveTop.show();  zoomMoveTopoff.hide();zoomMoveDown.show();zoomMoveDownoff.hide();
                    zoomRefreshOff.hide();  zoomRefresh.show();
                    heroImage.addClass("zoomImageBorder cursorMove");
                } else {
                    zoomInOff.hide();zoomIn.show();zoomOutOff.hide();zoomOut.show();zoomMoveLeft.show();   zoomMoveLeftoff.hide();zoomMoveRight.show();zoomMoveRightoff.hide();zoomMoveTop.show(); zoomMoveTopoff.hide();zoomMoveDown.show();zoomMoveDownoff.hide();
                    zoomRefreshOff.hide();zoomRefresh.show();
                    heroImage.addClass("zoomImageBorder cursorMove");
                }
                heroImage.focus();
            }
        },
        initHeroImage : function() {
            var self = this,
                dataObj = {},
                iviewerObj = self.iviewer.getDefaultValues(),
                iviewerObjFul = self.iviewer;
            dataObj.width = iviewerObj.min;
            dataObj.zoom = 0;
            dataObj.min = iviewerObj.min;
            dataObj.max = iviewerObj.max;
            self.iviewer.container.find('.zoomBox').show();
            self.iviewer.container.find('.zoomMessage').hide();
            self.iviewer.reset();
            self.zoomCb(false, iviewerObjFul, dataObj);
        }
    }


  
    
    
    $( document ).on( "click", "#mask-cart,a.cart-zoom-close", function(e) {
        e.preventDefault();
        $('#mask-cart , .pop-up').fadeOut(300 , function() {
        $('#mask-cart').remove();  
        }); 
        
    });
    
    

 })(jQuery);

 