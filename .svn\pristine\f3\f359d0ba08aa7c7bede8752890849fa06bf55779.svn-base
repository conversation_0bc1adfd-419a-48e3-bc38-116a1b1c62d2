/* ---------------------------------------------------------------------
Original Author: <PERSON>
Contributors: N/A

Target Browsers: All
Media Type: Screen, Projection
Width: All Sizes
------------------------------------------------------------------------ */

/* ---------------------------------------------------------------------
 Theme colors
------------------------------------------------------------------------ */

/* color_red d61120 */
/* color_blue 3174D8 */

/* ---------------------------------------------------------------------
 Site Object
------------------------------------------------------------------------ */
.container {
  padding: 0;
  overflow: visible;
  margin-right: 0px;
  margin-left: 0px;
}

.site {
  /* max-width: 1000px; */
  /* min-width: 980px; */
  width: 1000px;
  /* min-height: 100%; */
  margin: 0 auto;
  font-size: 14px;
}

/* ---------------------------------------------------------------------
 Site Object - Header
------------------------------------------------------------------------ */
.site-hd {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 10px 25px 0 18px;
  width: 100%;
  background: #fff;
  position: relative;
  z-index: 50;
}

.site-hd:before,
.site-hd:after {
  content: " ";
  display: table;
}

.site-hd:after {
  clear: both;
}

.site-hd-left {
  float: left;
}

.site-hd-right {
  float: right;
  position: relative;
  padding-left: 15px !important;
}

.cfgSiteLogo {
  float: left;
  margin-right: 0px !important;
  width: 182px !important;
  height: 80px !important;
  padding-top: 0px !important;
}

.siteTitle {
  float: left;
  padding-top: 10px;
  max-width: 675px;
  position: relative;
  padding-left: 30px;
}

.help_image {
  padding-top: 10px;
}

/* ---------------------------------------------------------------------
 Site Object - Body
------------------------------------------------------------------------ */
.site-bd {
  position: relative;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
  max-width: 996px;
  background: #fff;
  z-index: 50;

  border-top: 1px solid #e7e7e7;
  padding: 25px 20px 25px 20px;
  /*padding: 25px 20px 55px 60px; */ /* pol,deluxe.css */
}

.site-bd:before {
  content: "";
  display: block;
  width: 100%;
  top: 2px;
  left: 0;

  position: absolute;
  /* position: relative; */ /* staples,pez,msbc,bc4.css */
  /* height: 100%;*/ /* dfs,bc4.css */
}


.product-container .site-bd:has(.initial-spinner) {
  height: 60vh;
}

.product-container .site-bd:not(:has(.initial-spinner)) {
  min-height: 100%;
}


[data-controller="FbtNextController"][style*="display: none"] .site-bd {
  min-height: 100%;
}

[data-controller="FbtNextController"] .site-bd {
  height: 60vh;
}

.online-proof {
  display: none;
}
.my_logo {
  margin-top: 17px;
  margin-bottom: 10px;
}
.my_logo_message {
  font-weight: bold;
  color: #00a76d;
  font-size: 16px;
  margin: 5px 0px;
  display: none;
}
[data-id="SHOW_LOGO"] {
  margin-top: 10px !important;
}
[data-id="quantity"],
[data-id="productId"],
[data-id="DETAILS"] {
  background: #f1f1f1;
  margin-bottom: -27px;
  padding: 10px 20px 0px 20px;
}
.foldSubhead {
  font-weight: bold;
  margin: 10px 0px 15px 0px;
}
.withFold {
  display: inline;
  width: 107px;
}
.withoutFold {
  display: inline;
  height: 121px;
}
/* canBeRemoved - following class is no longer in use */
.foldLine {
  float: left;
  width: 1px;
  height: 170px;
  margin-left: 50px;
  margin-bottom: 20px;
}
.per_content {
  width: 152px;
  float: left;
  padding-left: 32px;
  box-sizing: content-box;
}

.per_image {
  width: 14px;
  float: left;
  cursor: pointer;
}
#personalization_link {
  margin-top: 39px;
  margin-left: -57px;
}

/* canBeRemoved - following class is no longer in use */
.envelope_img {
  float: left;
}
li[id^="PERSONNALIZATION"] {
  margin-top: -20px;
  cursor: pointer;
}

.personalization_custom {
  margin-top: -20px;
}


/* canBeRemoved - following class is no longer in use */
.per-heading {
  margin-top: 20px;
}

.CUSTOM {
  margin-left: -33px !important;
}
#foldingWorks {
  margin-left: -125px;
  margin-top: 11px;
}
#returnWorks {
  margin-left: -128px;
  margin-top: 11px;
}
#envelope_link {
  margin-left: -128px;
  margin-top: 11px;
}
.personalization_link_review {
  margin-top: 11px !important;
  margin-left: -149px !important;
}

.env-opts {
  margin-top: 4px;
  width: 135px;
}

.mt3 {
  margin-top: 3px;
}
.mt4 {
  margin-top: 4px;
}
.mt5 {
  margin-top: 5px;
}

/* .fbt-wrapper {
    padding-bottom: 20px;    
    padding-right: 75px;
    float: left;
} */

.fbt-product {
  border: 2px solid #3174d8;
  padding: 20px;
  margin: 0px 0px;
  float: left;
  width: 100%;
}
.fbt-product-low {
  border: 2px solid #ccc;
  padding: 20px;
  margin: 0px 0px;
  float: left;
  width: 100%;
  color: #666666;
}
.fbt-product-success {
  /* border: 2px solid #cccccc; */
  border-top: none;
  /* DCOM-14697 */
  /* padding: 20px; */
  margin: 20px 0px;
  float: left;
  width: 100%;
  background-color: white;
}
.fbt-product-skip {
  border: 1px solid #cccccc;
  /* padding: 20px; */
  margin: 20px 0px;
  float: left;
  width: 100%;
  padding-top: 20px;
}
.fbt-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 15px;
  margin-left: 15px;
}

/* DCOM-14697 */
.fbt-title-success {
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 15px;
  margin-left: 15px;

  margin: 0;
  background-color: #f2fbf8;
  border-bottom: 2px solid #00a76d;
  color: #000f0c;
  padding-right: 2px;
  padding-left: 2px;
  width: 100%;
}

.fbt-product-row {
  float: left;
  /* DCOM-14697 */
  padding: 20px;
}
.fbt-product-image,
.fbt-product-desc,
.fbt-product-action {
  float: left;
  padding: 0px 10px;
}
.fbt-product-desc {
  font-weight: bold;
  font-size: 18px;
  padding-top: 30px;
  min-width: 450px;
  max-width: 450px;
}
.fbt-product-action {
  padding-top: 20px;
}
.fbtTitle {
  float: left;
  padding: 10px 0px;
  font-size: 40px;
  font-style: normal;
  font-weight: 700;
  font-family: "Source Sans Pro Bold", "Source Sans Pro";
}
.subTitle {
  font-size: 24px;
}
.sub-desc {
  color: #666666;
  font-size: 16px;
  font-weight: normal;
  padding-bottom: 5px;
}
.fbt-help {
  padding: 12px 0px 0px 5px;
  float: left;
  font-size: 16px;
}
.fbt-help a:hover {
  text-decoration: underline;
}
.fbt-help-img {
  padding: 10px 5px 0px 5px;
  float: left;
}
.button_fbt {
  width: 190px;
  height: 40px !important;
  font-weight: 700;
  font-style: normal;
  font-size: 13px !important;
}
.fbt-copy {
  color: #3174d8;
  font-family: "Source Sans Pro Bold", "Source Sans Pro";
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 16px;
  text-align: center;
  float: left;
  height: 46px;
  padding-top: 8px;
  width: 100%;
  background-image: url("../../media/images/arrow.png");
  background-repeat: no-repeat;
  background-position: center;
}
.fbt-copy-low {
  color: #666666;
  font-family: "Source Sans Pro Bold", "Source Sans Pro";
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 16px;
  float: left;
  text-align: center;
  float: left;
  height: 46px;
  padding-top: 8px;
  width: 100%;
  background-image: url("../../media/images/arrow-grey.png");
  background-repeat: no-repeat;
  background-position: center;
}
/* .fbt-success{ */
/* color: #00A76D; */
/* } */
.fbt-success img {
  margin-bottom: -10px;
}
#fbt-popup {
  margin-left: -128px;
  margin-top: 11px;
}
div#ax_paragraph {
  padding: 20px 0 5px 0;
  font-family: "Arial Regular", "Arial";
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  color: #333333;
  float: right;
  line-height: normal;
}

.fbt-next-wrapper {
  font-family: "Source Sans Pro Bold", "Source Sans Pro";
  font-weight: 700;
  font-style: normal;
  font-size: 16px;
  text-align: center;
  width: 207px;
  position: absolute;
  left: 430px;
}
.fbt-next-product-image {
  text-align: center;
  padding-top: 20px;
}
.link_Skip.pt-20{
  margin-top: 20px;
}
.skip-editor-buttons{
  padding: 0 20px 20px;
}
.link_Skip p a {
  color: #005194;
  text-decoration: underline;
}
.link_Skip p a:hover {
  color: #005194;
  text-decoration: none;
  cursor: pointer;
}
.fbt-summary {
  display: block;
  border: 1px solid #d61120;
}

/* canBeRemoved - following class is no longer in use */
.good_proof {
  text-align: center;
  width: 100%;
  margin-bottom: 0px;
  margin-top: 10px;
}

/* canBeRemoved - following class is no longer in use */
.bad_proof {
  text-align: center;
  width: 100%;
  margin-bottom: 5px;
  margin-top: 5px;
}

.fbt-hybrid-rout {
  background: #f1f1f1;
  padding: 5px 0px 10px 10px;
}
#fbt-hybrid-routing {
  font-weight: 700;
  font-style: normal;
  font-size: 16px;
  margin: 10px 0px 5px 0px;
}
.fbt-hybrid-routing-val {
  overflow: hidden;
}
.fbt-hybrid-routing-val p {
  float: left;
  padding: 10px 0px;
  position: relative;
}
.fbt-hybrid-routing-val .reveal-container {
  float: right;
  padding: 10px 10px 0px 0px;
}
.btn-common {
  border: 1px solid #802d73;
  color: #fff;
  background: #802d73;
  border-radius: 5px;
  padding: 1px 7px;
  cursor: pointer;
}
.btn-common:hover,
.btn-common:focus {
  background: #4e1042;
}
.fbt-hybrid-rout .inputBox {
  width: 220px;
}
.numberin_help {
  font-weight: 400;
  font-size: 14px;
  color: #333333;
}
.fbt_hybrid_box {
  margin: 15px 0px;
}
.fbt-hybrid-routing-val .masked {
  opacity: 1;
  display: inline;
  transition: opacity 0.5s ease-in-out;
}
.fbt-hybrid-routing-val.active .masked {
  opacity: 0;
}
.fbt-hybrid-routing-val .unmasked {
  position: absolute;
  top: 10px;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
  display: inline;
  left: 0px;
  color: #d61120;
}
.fbt-hybrid-routing-val.active .unmasked {
  opacity: 1;
  display: inline;
}
.btn_disable {
  pointer-events: none;
}

/* canBeRemoved */
.logoFilerOptions {
  display: flex;
}

/* canBeRemoved */
.logoPortion {
  width: 70%;
  float: left;
}

/* canBeRemoved */
.logoPortionFirst {
  background-color: #f1f1f1;
  width: 100%;
  float: left;
}

/* canBeRemoved */
.logoPortionSecond {
  background-color: #fff;
  width: 35%;
  float: left;
  text-align: right;
}

/* canBeRemoved */
.logoPortionSecond ul {
  list-style: none;
  display: inline-flex;
}

/* canBeRemoved */
.logoPortionSecond ul label {
  font-weight: 600;
  font-size: 20px;
  color: #000;
}

/* canBeRemoved */
.logoPortionSecond ul img {
  width: 89px;
  border: solid 4px #f6b26b;
}

/* canBeRemoved */
.logoPortionSecond ul li.selectedLogo {
  padding: 10px 15px 0px;
}

/* canBeRemoved */
.logoPortionFirst .logoDiv {
  padding: 20px;
  float: left;
  width: 92%;
}

/* canBeRemoved */
.logoPortionFirst .logoDiv .logo-line {
  width: 48%;
  float: left;
  margin-top: 0px;
}

/* canBeRemoved */
.logoPortionFirst .logoDiv .txtLarge {
  font-weight: 600;
  font-size: 20px;
}

/* canBeRemoved */
.logoPortionFirst .logoDiv .firstInput {
  padding-right: 20px;
}

/* canBeRemoved */
.logoPortionFirst .inputBox {
  border-radius: 0px;
  border-color: #000;
  font-size: 18px;
}

/* canBeRemoved */
.mix-selected-logo {
  padding: 5px;
  width: 151px;
  float: right;
  border: 4px solid #fd9600;
  position: relative;
}

.filterLogos {
  width: 100%;
  /* float: left; */
  padding: 15px 0px 0px 0px;
}
.filterLogos label {
  font-weight: 600;
  font-size: 15px;
  color: #000;
}
.filterLogos ul {
  width: 100%;
  display: inline-flex;
}
.filterLogos li.allIndustries {
  width: 32%;
  padding-right: 15px;
}
.filterLogos li.allLayouts {
  width: 23.5%;
  padding: 0px 0 0 10px;
}
.filterLogos li.allStyles {
  width: 22%;
  padding: 0px 10px 0px 0px;
}
.filterLogos li.allSearch {
  width: 56%;
}
/* .filterLogos .inputBox_select::after{
    content: " ";
    position: absolute;
    right: 0;
    top: 0;
    width: 33px;
    height: 100%;
    pointer-events: none;
    background: #fff url(down-arrow.png) no-repeat right 6px;
    background: rgba(255,255,255,1)url(down-arrow.png) no-repeat right 6px;
    background-size: auto auto;
    background-size: 28px 19px;
} */
.filterLogos button {
  width: 100%;
  cursor: pointer;
  background-color: #802d73;
  border: 1px solid #000;
  border-radius: 4px;
  padding: 12px;
  color: #fff;
  font-size: 16px;
  margin: 20px 0px;
}
.filterLogos button:hover {
  background-color: #4e1042;
}
/*
*DCOM-11886 for iPad safari
*/
@media screen and (min-width: 767px) and (max-width: 1024px) {
  .filterLogos button:hover {
    background-color: #802d73 !important;
  }
}
.filterLogos .inputBox_select-input {
  font-weight: 300 !important;
}
.chooseALogo {
  float: left;
  width: 100%;
  padding: 15px 0px;
}
.chooseALogo .txtLarge {
  width: 100%;
  float: left;
  font-weight: 600;
  font-size: 20px;
  padding-bottom: 15px;
}
.mix-blocks li {
  margin-bottom: 0px;
}
.mix-logobox {
  margin: 0px !important;
  padding-top: 0px !important;
}

/* canBeRemoved */
.logoListItems {
  float: left;
  width: 100%;
}

/* canBeRemoved */
.logoListItems img {
  height: 158px;
  float: left;
  border: 1px solid #000;
  margin: 0px 13px 0px 0px;
}
.logo-container {
  position: relative;
}

.logo-container .js-logo-loading {
  display: none;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  z-index: 1;
}

.logo-container.loading .js-logo-loading {
  display: block;
}

.logo-container .js-logo-loading .uploading-spinner {
  margin-top: 200px;
}

.spinner-container {
  position: relative;
}

.spinner-container .js-logo-loading {
  display: none;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.5);
  z-index: 1;
}

.spinner-container.loading .js-logo-loading {
  display: block;
}

.spinner-container .js-logo-loading .uploading-spinner {
  margin-top: 200px;
}

.pages_container {
  width: 100%;
  text-align: center;
}

.mix-pages .designBox {
  padding: 10px;
  margin: 4px 4px 8px;
  border: 1px solid #0070c9;
  text-align: center;
  font-weight: 700;
  border-radius: 5px;
  cursor: pointer;
  padding: 9px 12px;
  color: #0070c9;
  font-size: 12px;
}

.mix-pages .designBox:focus:before,
.mix-pages .designBox:hover:before {
  border: none;
}
.mix-pages .designBox:before:focus,
.mix-pages .designBox:before:hover {
  border: none;
}

.mix-pages .designBox_active:before,
.mix-pages.designBox_active:hover:before {
  border: none;
}
.mix-pages .designRadio.isChecked + label > .designBox:before {
  border: none;
}

.mix-pages .designBox:before {
  content: "";
  display: block;
  border: none;
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  -webkit-transition: none;
  transition: none;
}

.mix-pages li .c-page {
  color: #000;
  border: none;
}
.logomix-preview {
  padding-top: 10px;
  overflow: hidden;
}

.logomix-preview .designBoxPreview {
  padding-top: 0px !important;
  height: 165px !important;
  width: 100% !important;
  padding-left: 10px;
  background-size: 160px 160px;
  background-repeat: no-repeat;
}
.logomix-preview .designBoxPreview-panel {
  padding-top: 0px !important;
  height: 165px !important;
  width: 100% !important;
  padding-left: 10px;
  background-size: 155px 160px;
  background-repeat: no-repeat;
}
/* .mix_logo {
    width: 165px !important;
    height: 165px !important;
} */
/* mahesh */
.mix_logo {
  width: 100px !important;
  height: 100px !important;
}

.mix-search-input {
  width: 100%;
  padding-right: 35px;
}
/* .canvas-overlay {
    width: 190px;
    height: 200px;
} */
/* mahesh */
.canvas-overlay {
  width: 150px;
  height: 150px;
}

.js-all-layout {
  display: none;
}
.showme {
  display: block;
}
.pop-space {
  padding-left: 5px;
}

.progressBlocks {
  width: 100%;
  float: left;
  margin: 0 0 0 0;
  font-weight: 600;
  font-size: 14px;
  font-family: "Source Sans Pro", sans-serif !important;
}
.progressRadio {
  display: none;
}
.progressBlocks li {
  float: left;
  color: #737373;
  margin: 0 0 0 0;
  padding-right: 22px !important;
  padding-top: 10px;
  border-bottom: 1px solid #737373 !important;
}

.progressItem {
  cursor: auto;
  font-weight: normal;
}
.progressBlocks .itemActive {
  cursor: pointer;
  color: #3174d8;
  font-weight: normal !important;
  text-decoration-color: transparent;
}
.progressBlocks .currentItem {
  color: #000 !important;
  font-weight: normal !important;
  text-decoration: underline 3px #3174d8 !important;
  text-underline-offset: 14px !important;
}

.progress-title {
  margin-bottom: 13px !important;
  font-size: 14px !important;
}

label.progressItem {
  margin-bottom: 0px !important;
}

.hide-widget {
  display: none;
}
.hide-link {
  display: none;
}

/* canBeRemoved */
.link-container {
  padding: 0px 20px 5px 20px;
  background-color: #f1f1f1;
  position: relative;
  color: #0070c9;
  cursor: pointer;
}

/* canBeRemoved */
.js-hide-price {
  width: 50%;
}

/* canBeRemoved */
.js-price-text {
  color: #005194;
}

/* canBeRemoved */
.js-price-text:hover {
  text-decoration: underline;
}

.js-product-next {
  padding: 10px 20px 20px;
  background-color: #f1f1f1;
}
.js-product-prev {
  padding: 10px 20px 20px;
  background-color: #f1f1f1;
}
.subtotal-widget {
  padding: 10px 20px 0px 20px;
  background-color: #f1f1f1;
  /* margin-top: 20px; */
  padding-top: 1px;
}
.clearMargin {
  clear: both;
  /* margin-bottom: 5px; */
}
.price-widget {
  padding: 5px 20px 15px 20px;
  background-color: #f1f1f1;
  padding-bottom: 1px;
  /* padding-top: 15px; */
}
.next_button {
  float: inherit !important;
}
.btn_widget_next,
.btn_widget_prev {
  width: 40% !important;
  padding: 10px;
  text-transform: uppercase;
}

.button_next,
.button_prev {
  padding: 10px;
  /* min-width: 48% !important; */
  /* width: 48% !important; */
  min-width:100% !important;
  max-width: 100% !important;
}
.button_prev.button_neutral {
  padding: 8px !important;
}
.button_prev.button_grey {
  padding: 10px !important;
}

.button_next {
  padding: 10px !important;

}


.price_seperator {
  border: 1px solid white;
  margin-top: 0px;
  margin-bottom: 10px;
}
.subtotal_desc {
  font-size: 14px !important;
  text-transform: inherit !important;
}
/* .echk_margin {
    margin-top: 20px;
} */

/* div[data-controller="StepController"] {
    border: 1px solid #D61120;
    margin-bottom: 20px;
} */
.add_margin {
  margin-top: -60px !important;
  padding: 0px 10px 0px 10px !important;
}

.price_term {
  font-weight: 200 !important;
}
.price_desc {
  font-weight: 200 !important;
}
.subtotal_val {
  font-size: 14px !important;
}
.icon-angle-up:after {
  font-family: fontAwesome;
  content: "\f106";
  padding-left: 5px;
}
.icon-angle-down:after {
  font-family: fontAwesome;
  content: "\f107";
  padding-left: 5px;
}
.productTotal {
  padding-top: 5px;
}
.rpf_hdg {
  float: left;
  clear: both;
}
.rpf_desc {
  float: right;
}
.rpf_desc {
  margin-bottom: 9px;
  font-size: 14px !important;
  font-weight: 400 !important;
  max-width: 185px;
  cursor: auto;
}
.rpf_hdg {
  /* margin-bottom: 9px; */
  margin-top: 8px;
  font-size: 14px !important;
  font-weight: 400 !important;
  max-width: 185px;
  cursor: auto;
}
.fbt_box_copy {
  height: 40px !important;
  margin: 0px !important;
  padding: 10px 20px 0px 20px;
  background: #f1f1f1;
}
.fbt_box_qty {
  height: 40px !important;
  margin: 0px !important;
  background: #f1f1f1;
  padding: 10px 20px 0px 20px;
}
.rpf_surcharge {
  background: #f1f1f1;
  padding: 0px 20px 0px 20px;
  margin-bottom: 0px !important;
}
.rpf_surcharge dl{
  margin-bottom: 0;
}
span#FbtCopiesHelp:after {
  font-size: 14px !important;
  background-color: #f1f1f1 !important;
  margin-left: 5px !important;
  cursor: pointer;
}
.fbt-cart-btn {
  background: #f1f1f1;
  padding-left: 20px;
  padding-right: 20px;
}

.textColorList {
  margin: 5px 0 0 15px;
  /* padding-bottom: 20px; */
}
.textColorLi {
  list-style-type: disc;
  line-height: 20px;
}

.popup-outer:after {
  border-radius: 0px !important;
}

.popup-outer {
  box-shadow: 0 0 0 1000px rgba(0, 0, 0, 0.5);
}

.cfgSiteLogo a:hover {
  background: none !important;
}

#envelope_content {
  color: #ffffff;
}

/* set input fields full width */
label.txtLarge {
  width: 100%;
}

.product_total_separator_cover {
  padding: 5px 20px 5px 20px;
  background-color: #f1f1f1;
}
.product_total_separator {
  margin-top: 7px;
  margin-bottom: 10px;
  width: 100%;
  background-color: #bababa !important;
  height: 1px !important;
}

/* respChange << */

/* .widget-container {
    display: block !important;
} */
/* respChange >> */

/* hide qty & copy dropdown, and additional charges added in REF_CONTROLS_mobile.xml as it is added now in each step */
.footer-widget{
  padding-top: 13px;
  background-color: #f1f1f1;
}

[data-controller="StepController"]
  [data-controller="QuestionController"][data-id="quantity"] {
    display: none;
}

[data-controller="StepController"]
  [data-controller="QuestionController"][data-id="quantity"]
  h2,
[data-controller="StepController"]
  [data-controller="QuestionController"][data-id="quantity"]
  label {
  display: none;
}

[data-controller="StepController"]
  [data-controller="QuestionController"][data-id="productId"] {
  display: none;
}

[data-controller="PricingWidgetController"] {
  margin-top: 0px;
  clear: both;
}

/* hide qty & copy dropdown as it is added now in each step */
.fbt_hybrid_box.fbt_box_copy,
.fbt_hybrid_box.fbt_box_qty {
  display: none;
}

.space30 {
  min-height: 30px;
  width: 100%;
}

hr {
  border: 0;
  padding: 0;
  margin: 0;
  height: 1px;
}

.js-logo-controls .hdg_h4 {
  background: #f4f7fc;
  display: flex;
  border-bottom: 2px solid #3174d8;
  padding: 10px;
  font-weight: normal;
  font-size: 14px;
  padding-right: 24px;
}

.js-logo-controls .txtStd {
  width: 100%;
  padding: 0 !important;
}

.js-logo-controls .hdg_h4::before {
  font-family: fontAwesome;
  content: "\f06a";
  width: 60px;
  font-size: 19px;
  margin-left: 5px;
  color: #3174d8;
  transform: rotate(180deg);
  text-align: end;
  position: relative;
  bottom: 15px;
  left: 0px;
}

.centper {
  width: 100%;
}

.logoButtonOptions button {
  margin-left: 0;
  margin-top: 10px;
}
.fbt-next-wrapperzz .fbt-next-product-desc {
  text-align: center;
}

.fbt-next-wrapperzz .fbt-next-product-title {
  text-align: center;
}

.logo-note {
  width: 100%;
  line-height: 1.4;
  padding: 4px 0 0 0 !important;
}

/* .logoButtonOptions input {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  } */

.radio-toolbar {
  margin: 10px;
}
.buttonlikeradio button_neutral,
.buttonlikeradio button {
  cursor: pointer;
}

.buttonlikeradio input[type="radio"] {
  opacity: 0;
  position: fixed;
  width: 0;
}

.radio-toolbar label {
  display: inline-block;
  background-color: #ddd;
  padding: 10px 20px;
  font-family: sans-serif, Arial;
  font-size: 16px;
  border: 2px solid #444;
  border-radius: 4px;
}

.radio-toolbar label:hover {
  background-color: #dfd;
}

.radio-toolbar input[type="radio"]:focus + label {
  border: 2px dashed #444;
}

.radio-toolbar input[type="radio"]:checked + label {
  background-color: #bfb;
  border-color: #4c4;
}

.logo-hdg {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  margin-left: 0px !important;
  padding-left: 0px !important;
}

.logo-condition {
  width: 100%;
  line-height: 1.4;
}

.js-logo-controls .vList form {
  margin: 0px !important;
  margin-top: 15px !important;
}

.js-logo-controls .vList form label {
  width: 100% !important;
  height: auto !important;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 10px;
  padding-bottom: 10px;
}

.js-logo-controls .vList form .button_file_browse {
  padding-left: 0px;
  padding-right: 0px;
}

.button_file_browse {
  margin-top: 0px !important;
}

.js-logo-controls .vList .txtStd .button {
  width: 100% !important;
  margin-top: 15px;
}

.js-logo-controls .mix-hdg_marginSmall {
  background: #f2fbf8;
  display: flex;
  border-bottom: 2px solid #00a76d;
  padding: 15px 10px;
  font-weight: normal;
  font-size: 14px;
  padding-right: 24px;
  color: #000f0c !important;
}

.js-logo-controls .mix-hdg_marginSmall::before {
  font-family: fontAwesome;
  content: "\f058";
  width: 40px;
  font-size: 20px;
  margin-left: 5px;
  color: #00a76d;
}

.logo-note span {
  background: #fdfcf2;
  display: flex;
  border-bottom: 2px solid #dfc700;
  padding: 10px;
  font-weight: normal;
  font-size: 14px;
  padding-right: 24px;
}

.logo-note span::before {
  font-family: fontAwesome;
  content: "\f0f3";
  width: 80px;
  font-size: 20px;
  margin-left: 5px;
  color: #dfc700;
}

.js-logo-upload .js-logo-preview .preview-custom-logo {
  text-align: center;
}

.js-logo-uploading .uploading .uploading-title {
  margin: 0;
  text-align: left;
}

.js-logo-uploading .uploading .uploading-title .hdg {
  font-size: 18px;
}

.success-logo-hdg {
  font-size: 18px;
}

.js-logo-uploading .uploading .uploading-title .progress_bar {
  font-size: 16px;
  margin-top: 15px;
}

.progressbar {
  background: #eaf1fb none repeat scroll 0 0 !important;
  border-radius: 0px !important;
  height: 9px !important;
  padding: 0px !important;
  width: 100% !important;
}
.progress {
  background: #3174d8 !important;
  border-radius: 0px !important;
  height: 9px !important;
}

.error-head {
  background: #fef8f8;
  display: flex;
  border-bottom: 2px solid #d61120;
  padding: 15px 10px;
  font-weight: normal;
  font-size: 14px !important;
  padding-right: 24px;
  color: #000 !important;
  line-height: 1.6 !important;
  margin-top: 15px;
  margin-bottom: 0px;
  font-family: inherit !important;
}

.error-head::before {
  font-family: fontAwesome;
  content: "\f06a";
  font-size: 20px;
  margin-left: 5px;
  color: #d61120;
  position: relative;
  bottom: 5px;
}

.error-head p {
  margin-left: 18px;
  font-size: 14px !important;
  color: #000f0c;
}

.error-head p .inline-link {
  color: #4480db;
  cursor: pointer;
}

.error-container {
  width: 100%;
}

hr {
  margin-top: 0px !important;
}

.cancel_upload_logo_button {
  width: 100%;
  margin-top: 10px !important;
}

fieldset p {
  padding: 0 !important;
}

#Header-BrandLogo {
  width: inherit;
  height: inherit;
  align-content: center;
  display: grid;
}

.brandLogo {
  width: auto !important;
  max-width: 100% !important;
  height: auto !important;
  max-height: 100% !important;
  object-position: left !important;
}

.logopreview-logomx {
  margin-top: 20px;
  min-height: 225px;
}

.logopreview-standard {
  margin-top: 20px;
  min-height: 250px;
}

.fullwidth {
  width: 100%;
  margin: 0 !important;
}

.logo-mix-site-hd {
  padding-left: 0px !important;
}

#logoMixResults {
  width: 100%;
  margin-top: 1.5em;
}

.js-logo-filter .logoMix-Bus-title {
  font-weight: 600;
  font-size: 20px;
}

.js-logo-filter .line1,
.js-logo-filter .line2,
.js-logo-filter p,
.js-logo-filter .filterLogos .filter-by-style,
.js-logo-filter .filterLogos .filter-by-layout,
.js-logo-filter .filterLogos .search-logos,
.js-logo-filter .logo-selection-section,
.site-bdzzzz .options_tight .grid > * {
  margin-top: 1em;
}

.search-logos {
  position: relative;
}

.site-bdzzzz .js-logo-subcategories {
  margin-left: 0px !important;
}

.searchicon {
  position: absolute;
  width: 25px;
  top: 24px;
  right: 4px;
  cursor: pointer;
}

.js-logo-filter .line1 label,
.js-logo-filter .line2 label {
  font-weight: 600;
  font-size: 15px;
}

.filterLogos .LogoMix-filter-title {
  font-size: 20px;
  font-weight: 600;
}

.site-bdzzzz .clipart-filter-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.logo-site-bd {
  padding-left: 18px !important;
}

.js-logo-filter .logo-selection-section {
  border: 4px solid #3174d8;
}

.logo-selection-section .logo-selection-title,
.clipart-selection p {
  margin: 0px;
  background: #3174d8;
  padding: 5px;
  font-size: 18px;
  text-align: center;
  color: #fff;
}

.clipart-selection {
  border: 4px solid #3174d8;
  margin-top: 1em;
  box-sizing: border-box;
  clear: both;
}

.clipartSelection-main {
  clear: both;
  padding-top: 5px;
}

.designBox_active:before,
.designBox_active:hover:before {
  border: 0px !important;
}

.logo-grid-col_preview {
  width: 642px !important;
}

.logo-mix-main-title {
  color: #000f0c;
  font-size: 18px;
  font-weight: 600;
}

.logo-mix-subTitle {
  font-size: 14px;
  margin-top: 6px;
  font-weight: normal;
}

.mix-pages .designBox {
  border-color: #0070c9 !important;
  color: #0070c9 !important;
}

[data-controller="FbtSummaryController"] .site-bd {
  padding: 25px 60px 55px 60px;
}

[data-controller="FbtSummaryController"] .fbt-wrapper {
  padding-right: 0;
}

.fbt-product-row-summary {
  float: left;
  background: white;
  border: 1px solid #cccccc;
  border-top: none;
  width: 100%;
  padding: 20px;
}

.p-20 {
  padding: 20px;
  display: table;
}
.p-10 {
  padding: 10px;
  display: table;
}

.fbt-success::before {
  font-family: fontAwesome;
  content: "\f058";
  width: 40px;
  font-size: 20px;
  color: #00a76d;
  margin-right: 5px;
}

.fbt-product-skip .fbt-product-row-summary {
  border: none;
  padding-right: 0;
}

.media_logoselect .designBox_active:before {
  border: none !important;
}

.js-logo-selected:focus:before,
.js-logo-selected:hover:before,
.media_logoselect .designBox:focus:before,
.media_logoselect .designBox:hover:before,
.media_logoselect .designBox_active:hover:before {
  border: none !important;
}

.logo-browse-subTitle {
  font-size: 14px;
  margin-top: 6px;
  font-weight: normal;
}

.clipart-page-container {
  margin-top: 30px !important;
}

.selected-logo {
  position: relative;
  padding: 10px;
}

.pricing-widget-list-item-label {
  margin-bottom: -20px !important;
}

.inputBox-note {
  margin-top: 8px;
  font-size: 11px;
  line-height: 16px;
  color: #6b6b6b;
  max-width: 280px;
}

.inputBox-note-Bold {
  font-weight: bold;
}

.highlight-envelope {
  background: white;
  font-weight: normal;
  border-radius: 3px;
  padding: 2px;
  color: #333333;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

.review_note {
  font-size: 13.5px !important;
}

.designSwatchDesc {
  display: block;
}

body {
  font-size: 14px !important;
  font-family: "Source Sans Pro", sans-serif !important;
  line-height: 1 !important;
}

/* Helper functions */

.uline {
  text-decoration-line: underline;
}
.flright {
  float: right;
}

/* mobile responsive */
.bottom-row {
  width: 100%;
  background: #ffffff;
}

.left-preview-panel .design-bd img {
  width: 100%;
}

.fbtProductHeader {
  display: flex;
  flex-direction: row;
  align-items: center;
}

p {
  margin-bottom: 0 !important;
}

.mb-0.txtLarge + .mb-0.txtLarge {
  margin-top: 0px !important;
}

/* DFS BBB logo in footer was hidden */
.badge:empty {
  display: block !important;
}

.cfgTopDivider {
  padding-top: 31px;
  margin-top: 36px;
}

.cfgTopDivider-proof {
  padding-top: 25px;
  margin-top: 40px;
}



.bank-name-loader {
  display: none;
  float: right;
  font-family: "Source Sans Pro";
  font-size: 12px;
  background-image: url(../../media/images/spinner-icon.gif);
  background-size: 60px 45px;
  height: 22px;
  background-repeat: no-repeat;
  background-position: 0px -15px;
  width: 148px;
  text-align: right;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
  margin-left: 15px;
  margin-right: 15px;
}

.popup-footer {
  margin-bottom: 15px;
  margin-left: 15px;
  margin-right: 15px;
}

#popup-preview {
  width: 100%;
}

.js_image_close_mobile {
  width: 100%;
}

.cart-zoom-close-mobile {
  margin: 0px !important;
}

#popup-mobile {
  display: none !important;
}
#popup-desktop {
  display: block !important;
}

.progress-title {
  margin-bottom: 7px !important;
  font-size: 13px !important;
}

.popup-content {
  margin: 15px 15px 15px 15px;
}

.mobile_info_wraper,
.mobile_logobrowse_info_wraper {
  font-size: 1.1rem;
  line-height: 22px;
}

.logo_border{
  border: 2px solid #000F0C;
  margin-top: 8px !important;
}
.custom_logo{
  display: block;
  margin: auto;
  padding: 10px 0;
}
.custom_logo_mset{
  display: block;
  margin: auto;
  padding: 10px 0;
  max-width: 100%;
}
.logo_name{
  color: #000f0c;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 0 !important;
  margin-top: 0px !important;
}
.info_icon_style::before {
  font-family: fontAwesome;
  content: "\f06a";
  width: 30px;
  font-size: 12px;
  color: #3174d8;
  text-align: left;
}

[data-id="HIDDEN_LOGO"] {
  margin-top: 5px !important;
}
[data-id="HIDDEN_LINK"] {
  display: none !important;
}
.info_icon_style {
  display: flex;
  font-size: 12px !important;
}

.wid30{
  width: 30% !important;
}

.logoButtonOptions-note {
  font-size: 10px;
  color:#666666;
  margin-top: 9px;
}

.upload_message {
  font-size: 18px;
  font-weight: 700;
}

.upload_message::before {
  font-family: fontAwesome;
  content: "\f058";
  margin-right: 9px;
  color: #00a76d;
}

.logo_message {
  margin-top: 8px;
}

.no-logo-logomix,.no-logo-logobrowse {
  width:auto;
  height:142px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.reorder-wraper {
  margin-top: 10px;
}
.reorder button {
  margin-left: 0;
  margin-top: 10px;
}
.reorder-logo-summary {
  margin-bottom: 5px;
}

.reorder-btn {
  margin-top: 10px;
}

.logo-preview {
  display: flex;
  justify-content: space-between;
}

.logo_left {
  align-self: flex-start;
}

.logo_right {
  align-self: flex-end;
  display:flex;
}

.logo_right:hover {
  cursor: pointer;
}

.edit_logo {
  width: 16px;
  height: 16px;
  margin-right: 1px;
}

.edit_text {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 19px;
  text-align: right;
}

.dropdown {
  overflow: hidden;
  width: 100%;
}

.dropdown-50-left {
  float: left !important;
}

.dropdown-50-right {
  float: right !important;
}

.dropdown-50-left,
.dropdown-50-right {
  width: 47.5% !important;
} 

.logo-title {
  padding-top:8px;
}

.pricing-widget-div{  
  margin-top: 0px !important;
  background-color: #ffffff !important;
  box-sizing: border-box;
  position: relative;
  font: inherit;
  color: inherit;
  line-height: 17px;
  text-indent: 0.01px;
  width: 100%;
  border: solid #000f0c !important;
  border-width: 1px 1px 2px 1px !important;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 16px;
  border-radius: 0 !important;
}

.mb0{
  margin-bottom: 0px !important;
}

.inputBox_select-input{
  padding: 5px 33px 5px 8px !important;
}

.padQuantity{
  padding: 1px 28px 5px 8px !important;
}

.dropFocus:focus{
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  box-shadow: none !important;
}

.dropFocus{
  text-align-last: right;
}

.inputBox_select-dropdown-item_selected {
  background: #0070c9 !important;
  color: #fff;
}

.dropdown-arrow {
  margin-left: 8px;
  margin-right: 8px;
}

.pricing-widget-dropdown-item {
  font-family: "Source Sans Pro";
  font-size: 14px;
}

.pricing-widget-vlist>.vList_loose>* {
  margin-bottom: 0px !important;
}

.pricing-widget-vlist>.vList_loose>*:not(:empty)+* {
  margin-top: 0px !important;
}


.pricing-widget-vlist>.vList_loose>*:last-child {
  margin-bottom: -27px !important;
}

.hide-second-color {
  display: none;
}
.hdg_sub_sp {
  font-size: 18px;
  font-weight: normal !important;
}
.hdg_sub_sp .checkbox-label {
  margin-left: 0px !important;
}
.checkbox-label {
  font-size: 18px !important;
}
.hide-me {
  visibility: hidden;
}
.custom-logo-loader {
  display: none;
  float: left;
  font-family: "Source Sans Pro";
  font-size: 12px;
  height: 22px;
  width: 148px;
  background-image: url(../../media/images/spinner-icon.gif);
  background-size: 60px 45px;  
  background-repeat: no-repeat;
  background-position: center;  
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.custom-logo-loader-container{
  display: inline-block;
  position: relative;
  height: 100%;
  width: 100%;
}
.custom-h2-style {
  margin: 0 0 .5rem 0;
}
.rmbox {
  margin: 0 auto;
  width: 80%;
  margin-top: 10px;
}
.nav-wrap {
  display: flex;
  justify-content: flex-end;
}


/* ============ Choice modal view css Start ============ */
.selected-img {
  width: 300px;
}

.choice-hd {
  border-bottom: 1px solid lightgrey;
  position: sticky;
  top: 0;
  z-index: 99;
  background: #fff;
  margin-top: -2px;
  background-color: #f9f9f9;
}

.choice-list-img {
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.verseSelector .choice-list-img{
  background-size: contain;
}

.choice-list {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 20px;
}

.choice-list label {
  display: block;
  height: 170px;
  border: 1px solid lightgrey;
  border-radius: 2px;
  cursor: pointer;
  position: relative;
}

.choice-list input {
  display: none;
}

.choice-list label:hover {
  border-color: #259cda;
  box-shadow: inset 2px 2px 0px #259cda, inset -2px -2px 0px #259cda; 
}

.choice-list .isChecked+label {
  border-color: #fd9600 !important;
  box-shadow: inset 2px 2px 0px #fd9600, inset -2px -2px 0px #fd9600 !important; 
}

.choice-list-id {
  color: #000000;
  padding: 3px 6px;
  border-radius: 3px;
  font-size: 12px;
  width: fit-content;
  position: absolute;
  bottom: 5px;
  right: 5px;
}

.cursor-pointer{
  cursor: pointer;
}

.help-icon-align{
  position: relative;
  top: -8px;
}

.checkbox-label-width {
  max-width: calc(100% - 25px);
}

.button_neutral.change-verse-btn{
  padding: 8px 12px !important;
  height: 34px !important;
}

.kit-quantity {
  display: flex;
  justify-content: space-between;
  /* border-bottom: 1px solid #bababa; */
  padding-top: 5px;
}

.js-prev-label {
  transition: background-size 0.3s ease;
  text-decoration: underline;
  color: #3174d8 !important;
  cursor: pointer;
  background: linear-gradient(to bottom, transparent 62%, #b8dade 0) left
    center/0 100% no-repeat;
}

.link:hover,
.js-prev-label:hover {
  text-decoration: none !important;
  color: #3174d8;
  cursor: pointer;
  background-size: 100% 100%;
}