define(function() {
    'use strict';

    /**
     * @class App.Constants.Routes
     * @static
     */
    var Routes = {
        /**
         * @property LOGO_BROWSE
         * @type {String}
         */
        /**
         * @property LOGO_BROWSE_LOAD
         * @type {String}
         */
        /**
         * @property LOGO_BROWSE_UNLOAD
         * @type {String}
         */
        LOGO_BROWSE: '/step/:id/logo/browse',

        /**
         * @property LOGO_UPLOAD
         * @type {String}
         */
        /**
         * @property LOGO_UPLOAD_LOAD
         * @type {String}
         */
        /**
         * @property LOGO_UPLOAD_UNLOAD
         * @type {String}
         */
        LOGO_UPLOAD: '/step/:id/logo/upload',

        /**
         * @property STEP
         * @type {String}
         */
        /**
         * @property STEP_LOAD
         * @type {String}
         */
        /**
         * @property STEP_UNLOAD
         * @type {String}
         */
        STEP: '/step/:id'
    };

    /** Generate `*_LOAD` and `*_UNLOAD` properties from routes: */
    Object.keys(Routes).forEach(function (key) {
        var route = Routes[key];
        Routes[key + '_LOAD'] = 'load' + route;
        Routes[key + '_UNLOAD'] = 'unload' + route;
    });

    return Routes;
});
