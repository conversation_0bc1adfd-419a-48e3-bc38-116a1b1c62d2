define(function (require) {
    'use strict';

    var AbstractModel = require('./Abstract');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.Verse
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} VerseValues
     */
    var CheckboxModel = function (CheckValues) {
        AbstractModel.call(this, checkValues);
    };

    var proto = inherits(CheckboxModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} VerseValues
     * @chainable
     */
    proto.init = function(CheckValues) {

        /**
         * @property folding
         * @default {null}
         * @type {string}
         */
        this.folding = null;

        /**
         * @property signature
         * @default {null}
         * @type {string}
         */
        this.signature = null;

          /**
         * @property envelope
         * @default {null}
         * @type {string}
         */
        this.envelopeReturn = null;


        // run the parent init method to parse determine the data type
        base.init.call(this, CheckValues);

        return this;
    };


    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param json the original JSON data.
     */
    proto.fromJSON = function(json) {
        this.folding = json.folding; 
        this.signature = json.signature;   
        this.envelopeReturn = json.envelopeReturn;       
    };

    return CheckboxModel;
});
