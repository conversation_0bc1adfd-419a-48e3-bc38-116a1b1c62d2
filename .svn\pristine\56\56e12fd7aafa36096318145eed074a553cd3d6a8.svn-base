define(function(require) {
    'use strict';

    var AbstractQuestionController = require('./Abstract');
    var Content = require('i18n!../../constants/nls/en-us/Content');
    var DomEvents = require('../../constants/DomEvents');
    var EventController = require('controllers/Event');
    var ProductEvents = require('constants/ProductEvents');
    var Settings = require('../../constants/Settings');
    var Product = require('../../models/Product');
    var Surcharge = require('util/Surcharge');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var Classes = require('../../constants/Classes');
    var Helper = require('../../util/helper');

    /**
     * @class App.Controllers.Question.RadioExt
     * @extends App.Controllers.Question.Abstract
     *
     * @constructor
     * @param {Object} config
     * @param {jQuery} config.view
     * @param {Models.Ui.Question} config.model
     */
    function RadioExtQuestionController(config) {
        bindAll(this,
            'onChange',
            'onProductChange',
            'onQtyChange',
            'onActionClick',
            'onButtonClick');

        AbstractQuestionController.call(this, config);
    }

    var proto = inherits(RadioExtQuestionController, AbstractQuestionController);

    var pdtQty = 0;

    /**
     * Do not display question if it only has a single option
     *
     * @property HIDE_SINGLE_ANSWER_QUESTIONS
     * @type Boolean
     */
    proto.HIDE_SINGLE_ANSWER_QUESTIONS = true;

    /**
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = require('hbs!templates/question/radioExt');

    var cv_flag = false;

    /**
     * @method render
     * @chainable
     */
    proto.render = function() {
        //console.log('JoeTest.radioExt.render');
        var model = this.model;
        //if (model.id === 'matrix1') {
            return this.renderMatrix1();
        //}
        //return this.renderDefault();
    };

    /**
     * @method renderDefault
     * @chainable
     */
     /*
    proto.renderDefault = function() {
        var model = this.model;
        console.log('JoeTest.radioExt.renderDefault.model: ', model);
        var id = model.id;
        var info = model.info;
        var options = model.options;
        var value = model.getValue() || (info && info['default']);
        //console.log('JoeTest.radioExt.renderDefault.value: ', value);

        this.$view
            .html(this.template({
                id: id,
                //layoutClass: 'blocks_3up',
                //layoutClass: '',
                //desc: model.desc,
                desc: 'Paper Choice',
                isRequired: model.isRequired,
                'default': value,
                options: options && options._items
            }));

        return this;
    };
    */

    /**
     * @method renderMatrix1
     * @chainable
     */
    proto.renderMatrix1 = function() {
        //console.log('JoeTest.RadioExt.renderMatrix1');
        var $view = this.$view;
        var model = this.model;
        var id = model.id;
        var info = model.info;
        var options = model.options;
        var paperOptions = [];
        var value = (model.getValue() || (info && info['default'])).split(' ').join('_');

        var matrixTitle = [];
        var matrixCode = [];
        var foundDefault = false;
        options.each(function(option) {
            var matrix = option.getMatrix();

            if(typeof matrix !== 'undefined') {

                // Expose specific matrix data
                option = Object.create(option);
                option.matrix = matrix;
                option.imgSrc = Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE + matrix.swatch + "?wid=215";
                option.code = matrix.code;
                option.uiDesc = matrix.desc;
                option.tlId = matrix.swatch;
                matrixCode.push(option.code);
                matrixTitle.push(option.tlId);
                window.matrixCodeValue = matrixCode;
                window.matrixTitleVal = matrixTitle;

                //console.log("JoeTest.RadioExt.renderMatrix1.option.matrix.uid: " + matrix.uid);
                if (option.code === matrix.uid) {
                    foundDefault = true;
                }

                if (option.surcharge) {
                    option.qtyCharge = Surcharge.getPrice(option.surcharge.id, false, pdtQty);
                }
            }

            paperOptions.push(option);
        });

        Array.from(paperOptions).forEach(function(option){
            option.id = (option.id).split(' ').join('_');
        });
        //console.log('jiby', options);
        $view.empty();

        // console.log('JoeTest.RadioExt.baseAppUrl', baseAppUrl);
        // console.log('JoeTest.RadioExt.host_url', host_url);

        $view.append(this.template({
            id: id,
            desc: 'Paper Choice',
            isRequired: model.isRequired,
            'default': value,
            baseAppUrl: baseAppUrl,
            host_url: host_url,
            options: paperOptions
        }));

        return this;
    };

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        //console.log('JoeTest.RadioExt.attachEvents');
        this.$view
            .on(DomEvents.CHANGE, this.onChange)
            .on(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick);

        if (this.model.id === 'matrix1') {
            EventController
                .on(ProductEvents.CHANGE, this.onProductChange)
                .on(ProductEvents.QTY_CHANGE, this.onQtyChange);
        }

        return this;
    };

    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        //console.log('JoeTest.RadioExt.detachEvents');
        this.$view
            .off(DomEvents.CHANGE, this.onChange)
            .off(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick);

        EventController
            .off(ProductEvents.CHANGE, this.onProductChange)
            .off(ProductEvents.QTY_CHANGE, this.onQtyChange);

        return this;
    };

        /**
     * @method onActionClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onActionClick = function(event) {
        var action = $(event.target).data('action');

        if (action) {
            event.preventDefault();
            event.stopPropagation();

            EventController
                .emit(ActionEvents.HIDE_ALL)
                .emit(action, this.update);
        }
    };

    $(window).resize(function() {
        //var model = this.model;
        var a = $('#personalization_logo').offset();
        if (a) {
            var tp = a.top;
            var lt = a.left;
            $('.popover').offset({top: tp+32 ,left: lt-340});
            $('.arrow').offset({top: tp+25,left:lt-2});
        }
    });

    /**
     * @method onButtonClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onButtonClick = function(e) {
        var model = this.model;
        var info = model.info;
        var options = model.options;
        var construction = this.construction;
               
        var thisId = $(e.currentTarget).attr('id');
        var thisName = $(e.currentTarget).attr('name');
        
        if (Helper.isSameTooltipClick(e)) {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
        } else {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }

            $('[id^=pcHelp_]').each(function () {
                var pop = $(this);
                var currentId = $(this).attr('id');
                if (thisId === currentId) {
                    thisId = (thisId.substring(thisId.indexOf('_') + 1)).split('_').join(' ');
                    $(this).popover({
                        html: true,
                        trigger: "manual",
                        placement: 'bottom',
                        content: function () {
                            var msg0 = '<div><img src="' + host_url + '/webasset/w2p_mobile/' + baseAppUrl + 'assets/media/images/tooltip-close.png" id="pchelpCloseIcon_' + thisId + '" class="close tooltip-close" style="position: absolute; top: 0; right: 7px;"/>';
                            var msg1 = "<div class='titletxt'>" + thisName + "</div><br/>";
                            var msg2 = "<div id='pcHelpTable' class='whitetext'>" + Content.get(thisId).COPY + "</div><br/>";
                            var msg3 = "<p><table id='pcHelpTable'><tr><td> #. </td> <td style='font-weight:bold;padding-left:20px;'>" + Content.get(thisId).SIZE + "</td></tr>";
                            var msg4;
                            if (Content.get(thisId).FEEL)
                                msg4 = "<tr><td> Feel: </td> <td style='font-weight:bold;padding-left:20px;'>" + Content.get(thisId).FEEL + "</td></tr>";
                            else
                                msg4 = "<tr><td> Look: </td> <td style='font-weight:bold;padding-left:20px;'>" + Content.get(thisId).LOOK + "</td></tr>";
                            var msg5 = "<tr><td> Coating: </td> <td style='font-weight:bold;padding-left:20px;'>" + Content.get(thisId).COATING + "</td></tr>";
                            var msg6 = "<tr><td> Photos: </td> <td style='font-weight:bold;padding-left:20px;'>" + Content.get(thisId).PHOTOS + "</td></tr>";
                            var msg7 = "</table></p><br/>";
                            var msg8 = "<img style='width:100%;' src='https://raptor.scene7.com/is/image/raptor/" + Content.get(thisId).IMG_LG + "' id='Matrix-Img-paper_sig_white_sm'></div>";
                            return msg0 + msg1 + msg2 + msg3 + msg4 + msg5 + msg6 + msg7 + msg8;
                        },
                    });
                    if (!cv_flag) {
                        $(this).popover('show');
                    } else {
                        $(this).popover('hide');
                    }
                    cv_flag = !cv_flag;

                    $('[id^=pchelpCloseIcon]').click(function (e) {
                        $('[id^=pchelpCloseIcon]').each(function () {
                            var currentId = $(this).attr('id');
                            currentId = currentId.substring(currentId.indexOf('_') + 1);
                            if (thisId === currentId) {
                                cv_flag = false;
                                pop.popover('hide');
                                $('[id="' + currentId + '"]').remove();
                            }
                        });
                    });

                }

            });
        }

   
        
    };

    /**
     * @method onChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onChange = function() {
        //console.log('JoeTest.RadioExt.onChange.previous: ' + this.model.getValue());
        var value = this.$view.find('input:checked').val();
        this.model.setValue(value);
        this.isValid();
    };

    /**
     * @method onProductChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onProductChange = function(event, product) {
        //console.log('JoeTest.RadioExt.onProductChange: ' + product.query.skuId);
        this.skuId = product.query.skuId;
        this.pdtQty = parseInt(product.getQuantityValue(), 10);
        this.render();
    };

    /**
     * @method onQtyChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onQtyChange = function() {
        this.render();
    };

    return RadioExtQuestionController;
});
