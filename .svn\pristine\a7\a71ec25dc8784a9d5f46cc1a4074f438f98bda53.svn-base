/* ---------------------------------------------------------------------
Original Author: <PERSON>
Contributors: N/A

Target Browsers: All
Media Type: Screen, Projection
Width: All Sizes
------------------------------------------------------------------------ */
/*csslint
    box-sizing: false
*/

/* ---------------------------------------------------------------------
 Grid (flexible width, fixed gutters)
------------------------------------------------------------------------ */
.grid {
    margin-left: 0px;
}

.grid:before,
.grid:after {
   content: " ";
   display: table;
}

.grid:after {
    clear: both;
}

.grid-col {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    float: left;
    /*padding-left: 18px;*/ /* positive equivalent of .grid negative margin-left */
}

.grid-col_1of10 { width: 10%; }
.grid-col_2of10 { width: 20%; }
.grid-col_3of10 { width: 30%; }
.grid-col_4of10 { width: 40%; }
.grid-col_5of10 { width: 50%; }
.grid-col_6of10 { width: 60%; }
.grid-col_7of10 { width: 70%; }
.grid-col_8of10 { width: 80%; }
.grid-col_9of10 { width: 90%; }
.grid-col_10of10 { width: 100%; }

.grid-col_prefix1of10 { margin-left: 10%; }
.grid-col_prefix2of10 { margin-left: 20%; }
.grid-col_prefix3of10 { margin-left: 30%; }
.grid-col_prefix4of10 { margin-left: 40%; }
.grid-col_prefix5of10 { margin-left: 50%; }
.grid-col_prefix6of10 { margin-left: 60%; }
.grid-col_prefix7of10 { margin-left: 70%; }
.grid-col_prefix8of10 { margin-left: 80%; }
.grid-col_prefix9of10 { margin-left: 90%; }
.grid-col_prefix10of10 { margin-left: 100%; }

.grid-col_suffix1of10 { margin-right: 10%; }
.grid-col_suffix2of10 { margin-right: 20%; }
.grid-col_suffix3of10 { margin-right: 30%; }
.grid-col_suffix4of10 { margin-right: 40%; }
.grid-col_suffix5of10 { margin-right: 50%; }
.grid-col_suffix6of10 { margin-right: 60%; }
.grid-col_suffix7of10 { margin-right: 70%; }
.grid-col_suffix8of10 { margin-right: 80%; }
.grid-col_suffix9of10 { margin-right: 90%; }
.grid-col_suffix10of10 { margin-right: 100%; }

.grid-col_nav { width: 300px; padding-left: 20px; }
.grid-col_preview { width: 600px; padding-left: 5px; }
