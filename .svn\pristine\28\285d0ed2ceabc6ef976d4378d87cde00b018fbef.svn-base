define(function() {
    'use strict';

    /**
     * @class App.Constants.RegExp
     * @static
     */
    var RegEx = {

        /**
         * @property IMAGE_EXT
         * @type {RegExp}
         */
        IMAGE_EXT: /\.(jpe?g|png|gif)/i,

        /**
         * @property FILE_NAME
         * @type {RegExp}
         */
        FILE_NAME: new RegExp(/^.*\/([\w\d]+)\.\w+$/),

        /**
         * @property FILE_NAME_NO_EXT
         * @type {RegExp}
         */
        FILE_NAME_NO_EXT: new RegExp(/([\w\d]+)\.\w+$/),

        /**
         * @property QUERY_VALUE
         * @type {RegExp}
         */
        QUERY_VALUE: new RegExp(/^\w+=/),

        /**
         * @property CITY_STATE
         * @type {RegExp}
         */
        CITY_STATE: new RegExp(/(.*), ([\w ]*) ([\w\-]*)$/),

        /**
         * @property NUMBERING_FIELD
         * @type {RegExp}
         */
        NUMBERING_FIELD: new RegExp(/numbering_(\d+)/),

        /**
         * @property MICR_FIELD
         * @type {RegExp}
         */
        MICR_FIELD: new RegExp(/micr_(\d+)/),

        /**
         * @property FXG_LABEL
         * @type {RegExp}
         */
        FXG_LABEL: new RegExp(/(\w+)_(\w+)_(\w+)/),

        /**
         * @property FXG_LABEL_BLOCK
         * @type {RegExp}
         */
        FXG_LABEL_BLOCK: new RegExp(/_block$/),

        /**
         * @property NUMERIC
         * @type {RegExp}
         */
        NUMERIC: new RegExp(/^\d+$/),

         /**
         * @property NUMERIC AND ASTERISK
         * @type {RegExp}
         */
        ASTERISK_NUMERIC: new RegExp(/^[0-9*]+$/),

         /**
         * @property ASTERISK ONLY
         * @type {RegExp}
         */
        ASTERISK_ONLY: new RegExp(/^[*,]+$/),

        /**
         * @property MUST BE NUMERIC AND ASTERISK
         * @type {RegExp}
         */
        ASTERISK_NUMERIC_MUST: new RegExp(/^(?=.*[0-9])(?=.*[*])/),

        /**
         * @property PARENTHESES
         * @type {RexExp}
         */
        PARENTHESES: new RegExp(/\([^\)]*\)/g)
    };

    return RegEx;
});
