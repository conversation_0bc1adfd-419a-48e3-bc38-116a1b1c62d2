define(function(require) {
    'use strict';

    var ActionEvents = require('../constants/ActionEvents');
    var Classes = require('../constants/Classes');
    var ClipArtProvider = require('../providers/ClipArt');
    var ConfigurationProvider = require('../providers/Configuration');
    var Content = require('i18n!../constants/nls/en-us/Content');
    var Controller = require('./Controller');
    var DomEvents = require('../constants/DomEvents');
    var EventController = require('./Event');
    var ProductEvents = require('../constants/ProductEvents');
     var ProductModel = require('../models/ProductInfo');
    var SessionStorage = require('../providers/SessionStorage');
    var Settings = require('../constants/Settings');
    var StateEvents = require('../constants/StateEvents');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var Query = require('models/Query');
    var q = require('q');

    var Registry = require('util/Registry');
    var InkColorsProvider = require('../providers/InkColors');

    /**
     * @class App.Controllers.LogoBrowse
     * @extends App.Controllers.Controller
     *
     * @constructor
     * @param {Object} config
     */
    function LogoBrowseController(config) {
        bindAll(this,
            'initClipArt',
            'setCategories',
            'updateCategories',
            'updateSubcategories',
            'updateLogos',
            'updateTitle',
            'onCategoryChange',
            'onSubcategoryChange',
            'onHideAction',
            'onShowAction',
            'onLogoChange',
            'onNextClickStandard',
            'onPrevClick',
            'onStateChange',
            'updateStandardLogoPreview',
            'onClickPages',
            'renderLogos',
            'renderPaginationInfo',
            'onPdtChange',
            'initColors',
            'setColors',
            'onColorChange'
        );

        /**
         * @property categories
         * @type {App.Models.ClipArt.Collections.Categories}
         * @default null
         */
        this.categories = null;
        this.colors = null;
        this.pdtModel = null;

        this.setLogo(SessionStorage.getValue('logo_STANDARD'));

        /**
         * @property query
         * @type {App.Models.Query}
         */
        this.query = Query.getInstance();

        Controller.call(this, config);

        // Start hidden
        this.$view.hide();
        
        /**
         * Omniture site prefix
         *
         * @type {String}
         */
        this.site_prefix = Settings.OMNITURE_PREFIX;


        /**
         * Current Page Location
         * 
         */
        this.currentPage = 1;
        
        /**
         * @property ready
         * @type {Promise}
         */
        this.ready = q
            .when(this.ready)
            .then(this.updateCategories);
    }

    var proto = inherits(LogoBrowseController, Controller);

    /**
     * @method init
     * @return {Promise}
     */
    proto.init = function() {
        return ConfigurationProvider
            .getConfiguration()
            .then(this.initClipArt)
            .then(this.setCategories)
            .then(this.initColors)
            .then(this.setColors)
            .fail(this.onError);
    };

    /**
     * @method initClipArt
     * @return {Promise}
     */
    proto.initClipArt = function(config) {
        return ClipArtProvider
            .setConfig(config)
            .getClipArt();
    };

    // -- Accessors ------------------------------------------------------------

    /**
     * @method setCategories
     * @param {App.Models.ClipArt.Collections.Categories}
     * @chainable
     */
    proto.setCategories = function(categories) {
        //ProductModel.www = 'jibyjohn';
        this.categories = categories;

        // Set currently selected logo categories (
        this._selectCategories();

        return this;
    };
    
    /**
     * @method initColors
     * @return {Promise.<App.Models.Collections.Colors>}
     */
    proto.initColors = function(config) {
        return InkColorsProvider
            .setConfig(config)
            .getColors();
    };

    /**
     * @method setColors
     * @param {App.Models.Collections.Colors} colors
     * @chainable
     */
    proto.setColors = function(colors) {
        this.colors = colors;
        return this;
    };

    /**
     * @method getCategories
     * @return {App.Models.ClipArt.Collections.Categories}
     */
    proto.getCategories = function() {
        return this.categories;
    };

    /**
     * @method getCategory
     * @return {App.Models.ClipArt.Category}
     */
    proto.getCategory = function() {
        var id = this.$categories.find('select').val();


        var categories = this.getCategories();

        return categories && categories.getById(id);
    };

    /**
     * @method getSubcategories
     * @return {App.Models.ClipArt.Collections.Subcategories}
     */
    proto.getSubcategories = function() {
        var category = this.getCategory();

        return category && category.subcategories;
    };

    /**
     * @method getSubcategory
     * @return {App.Models.ClipArt.Subcategory}
     */
    proto.getSubcategory = function() {
        var id = this.$subcategories.find('select').val();
        var subcategories = this.getSubcategories();

        return subcategories && subcategories.getById(id);
    };

    /**
     * @method getLogos
     * @return {App.Models.ClipArt.Collections.Logos}
     */
    proto.getLogos = function() {
        var subcategory = this.getSubcategory();

        return subcategory && subcategory.logos;
    };

    /**
     * @method getLogo
     * @return {App.Models.ClipArt.Logo}
     */
    proto.getLogo = function() {
        var id = this.$logos.find('input:checked').val();
        var logos = this.getLogos();
        var logo = logos && logos.getById(id);

        return logo && {
            logoType: 'STANDARD',
            logoCode: logo.id,
            description: logo.description
        };
    };

    /**
     * @method setLogo
     * @param {Object} data
     * @param {Boolean} [silent=false] If true, category dropdown will not be updated
     * @chainable
     */
    proto.setLogo = function(data, silent) {

        /**
         * @property logo
         * @type {Object}
         */
        this.logo = data;

        if (silent !== true && this.categories) {
            this._selectCategories();
        }

        return this;
    };

    /**
     * @method _selectCategories
     * @private
     */
    proto._selectCategories = function() {
        if (!this.logo || !this.logo.logoCode) {
            return;
        }

        var id = this.logo.logoCode;
        var data = Registry.get(Registry.LOGO, id);

        this.selectedCategory = data.category;
        this.selectedSubcategory = data.subcategory;

        if (this.$categories) {
            this.updateCategories();
        }
    };

    // -- Methods --------------------------------------------------------------

    /**
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = require('hbs!templates/logoBrowse');

    /**
     * @method designRadioTemplate
     * @param {Object} model
     * @return {String}
     */
    proto.designRadioTemplate = require('hbs!templates/form/designRadio');

    /**
     * @method inputBoxTemplate
     * @param {Object} model
     * @return {String}
     */
    proto.inputBoxTemplate = require('hbs!templates/form/inputBox');
    // var inputBoxTemplate = require('hbs!templates/form/inputBox');

    /**
     * @method titleTemplate
     * @param {Object} model
     * @return {String}
     */
    proto.titleTemplate = require('hbs!templates/product/title');

    /**
     * TODO: refactor this
     *
     * @method render
     * @chainable
     */
    proto.render = function() {
        var desc = $('.mobile_info_wraper')[0].innerText;
        this.$view
            .html(this.template({
                description: desc,
                next: Content.get('Next Step'),
                prev: Content.get('Previous Step'),
                brandLogoUrl: Settings.BRAND_LOGO_URL,
                siteCss: Settings.SITE_CSS,
                brandLogo: Settings.BRAND_LOGO
            }));

        return this;
    };

    /**
     * @method cacheElements
     * @chainable
     */
    proto.cacheElements = function() {
        var $view = this.$view;

        this.$title = $view.find(Classes.PRODUCT_TITLE_SELECTOR);
        this.$categories = $view.find(Classes.LOGO_CATEGORIES_SELECTOR);
        // this.$categories = $('.js-logo-categories');
        this.$logos = $view.find(Classes.LOGOS_SELECTOR);
        // this.$logos = $('.js-logos');
        this.$prev = $view.find(Classes.PREV_SELECTOR);
        this.$next = $view.find(Classes.NEXT_SELECTOR);
        this.$nextLabel = $view.find(Classes.NEXT_LABEL_SELECTOR);
        this.$selected = $view.find(Classes.LOGO_SELECTED_SELECTOR);
        this.$subcategories = $view.find(Classes.LOGO_SUBCATEGORIES_SELECTOR);
        // this.$subcategories = $('.js-logo-subcategories');

        return this;
    };

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        this.$view
            .on(DomEvents.CHANGE, Classes.LOGO_CATEGORIES_SELECTOR, this.onCategoryChange)
            .on(DomEvents.CHANGE, Classes.LOGO_SUBCATEGORIES_SELECTOR, this.onSubcategoryChange)
            .on(DomEvents.CHANGE, Classes.LOGOS_SELECTOR, this.onLogoChange)
            .on(DomEvents.CLICK, Classes.NEXT_SELECTOR, this.onNextClickStandard)
            .on(DomEvents.CLICK, Classes.PREV_SELECTOR, this.onPrevClick)
            .on(DomEvents.CHANGE, Classes.PAGE_SELECTOR, this.onClickPages)
            ;

        EventController
            .on(ActionEvents.HIDE_ALL, this.onHideAction)
            .on(ActionEvents.LOGO_STANDARD, this.onShowAction)
            .on(StateEvents.CHANGE, this.onStateChange)
            .on(ProductEvents.UPDATESTANDARDLOGOPREVIEW, this.updateStandardLogoPreview)
            .on(ProductEvents.CHANGE, this.onPdtChange)
            .on(ProductEvents.COLOR_CHANGE, this.onColorChange)
            ;

        return this;
    };

    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        this.$view
            .off(DomEvents.CHANGE, Classes.LOGO_CATEGORIES_SELECTOR, this.onCategoryChange)
            .off(DomEvents.CHANGE, Classes.LOGO_SUBCATEGORIES_SELECTOR, this.onSubcategoryChange)
            .off(DomEvents.CHANGE, Classes.LOGOS_SELECTOR, this.onLogoChange)
            .off(DomEvents.CLICK, Classes.NEXT_SELECTOR, this.onNextClickStandard)
            .off(DomEvents.CLICK, Classes.PREV_SELECTOR, this.onPrevClick)
            .off(DomEvents.CHANGE, Classes.PAGE_SELECTOR, this.onClickPages);

        EventController
            .off(ActionEvents.HIDE_ALL, this.onHideAction)
            .off(ActionEvents.LOGO_STANDARD, this.onShowAction)
            .off(StateEvents.CHANGE, this.onStateChange)
            .off(ProductEvents.UPDATESTANDARDLOGOPREVIEW, this.updateStandardLogoPreview)
            .off(ProductEvents.CHANGE, this.onPdtChange)
            .off(ProductEvents.COLOR_CHANGE, this.onColorChange)
            ;;

        return this;
    };

    /**
     * @method update
     * @chainable
     */
    proto.updateCategories = function() {
        var categories = this.getCategories();



        this.$categories.html(this.inputBoxTemplate({
            selected: this.selectedCategory,
            items: categories && categories._items,
            tlId: "StandardLogoMajorCategory"
        }));

        return this.updateSubcategories();
    };

    /**
     * @method update
     * @chainable
     */
    proto.updateSubcategories = function() {
        var subcategories = this.getSubcategories();

        // console.log('zzzz ==== this.selectedSubcategory', this.selectedSubcategory);


        this.$subcategories.html(this.inputBoxTemplate({
            selected: this.selectedSubcategory,
            items: subcategories && subcategories._items,
            tlId: "StandardLogoSubCategory"
        }));

        return this.updateLogos();
    };

    /**
     * @method updateLogos
     * @chainable
     */
    proto.updateLogos = function() {
        var category = this.getCategory() || {};
        var subcategory = this.getSubcategory() || {};
        var logos = this.getLogos() || {};
        var selected = this.logo || {};
        const total_logos = logos.length() < 140 ? logos.length() : 140;
        const per_page = 20;
        var total_pages = Math.ceil(total_logos/per_page);
        var pages = [];
        for (var index = 1; index <= total_pages; index++) {
            var t = {id: index};
            pages.push(t);
            
        }

        if(this.pdtModel){
            var productQuestion = this.pdtModel.info.productInfo.question
            var inkColor1Obj;
            var inkColor2Obj;
            productQuestion.forEach( function(e) {
                if(e.id === 'inkColor1') {
                    inkColor1Obj = e;
                }
                if(e.id === 'inkColor2') {
                    inkColor2Obj = e;
                }
            });
            var color1;
            var color2;
            
            if(this.pdtModel.steps){
                var values = this.pdtModel.steps.getValues().values;
                if(values.inkColor1) {
                    color1 = values.inkColor1.value;
                    color2 = values.inkColor2.value || color1;
                }
                else {
                    color1 = 'BLACK';
                    color2 = 'BLACK';
                }
            }
            if(!inkColor2Obj) {
                color2 = color1;
            }
            var requiredInkColor = this.pdtModel.getRequiredInkForBlock('LO');
            if (requiredInkColor) {
                color2 = requiredInkColor;
            }
            var color = this.colors.getById(color2);
            var a2 = color.logo_rgb || color.getRGBString();

            SessionStorage.storeValue('logo_browse_page', 1);
            var currentPage = SessionStorage.getValue('logo_browse_page');
            this.$logos.html(this.designRadioTemplate({
                name: 'LOGO_STANDARD',
                id: category.id + '_' + subcategory.id,
                imageBase: Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE,
                imageExt: '?fmt=webp&hei=142&wid=142&qlt=85&op_colorize=' + a2 + '&layer=1',
                items: this.generatePaginatedLogos(logos),
                'default': selected.logoCode,
                pages: pages,
                current_page: currentPage,
            }));
        }else{
            SessionStorage.storeValue('logo_browse_page', 1);
            var currentPage = SessionStorage.getValue('logo_browse_page');
            this.$logos.html(this.designRadioTemplate({
                name: 'LOGO_STANDARD',
                id: category.id + '_' + subcategory.id,
                imageBase: Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE,
                imageExt: '?fmt=webp&hei=142&wid=142&qlt=85',
                items: this.generatePaginatedLogos(logos),
                'default': selected.logoCode,
                pages: pages,
                current_page: currentPage,
            }));
        }
        

        // mahesh
        // show selected logo on reload
        EventController.emit(ActionEvents.CLIPART_SELECTION, this.logo);
        // auto select previously selected logo
        if( this.logo && SessionStorage.getValue('logo') === 'STANDARD' ) {
            EventController.emit(ProductEvents.LOGO_CHANGE, this.logo);
        }
        return this;
    };

    /**
     * @method updateSelected
     * @chainable
     */
    proto.updateSelected = function() {

        if(this.pdtModel){
            var productQuestion = this.pdtModel.info.productInfo.question
            var inkColor1Obj;
            var inkColor2Obj;
            productQuestion.forEach( function(e) {
                if(e.id === 'inkColor1') {
                    inkColor1Obj = e;
                }
                if(e.id === 'inkColor2') {
                    inkColor2Obj = e;
                }
            });
            var color1;
            var color2;
            
            if(this.pdtModel.steps){
                var values = this.pdtModel.steps.getValues().values;
                if(values.inkColor1) {
                    color1 = values.inkColor1.value;
                    color2 = values.inkColor2.value || color1;
                }
                else {
                    color1 = 'BLACK';
                    color2 = 'BLACK';
                }
            }
            if(!inkColor2Obj) {
                color2 = color1;
            }
            var requiredInkColor = this.pdtModel.getRequiredInkForBlock('LO');
            if (requiredInkColor) {
                color2 = requiredInkColor;
            }
            var color = this.colors.getById(color2);
            var a2 = color.logo_rgb || color.getRGBString();

            var imageExt = '?fmt=webp&hei=142&wid=142&qlt=85&op_colorize=' + a2 + '&layer=1';
        }else{
            var imageExt = '?fmt=webp&hei=142&wid=142&qlt=85';
        }
        
        var $selected = this.$selected;
        var selected = this.logo;
        var image = selected && selected.logoCode;
        var base = Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE;

        if (!image) {
            $selected.css('background-image', '');
            return this;
        }

        $selected.css('background-image', 'url(' + base + image + imageExt + ')');
        return this;
    };

    /**
     * @method updateNext
     * @chainable
     */
    proto.updateNext = function() {
       if (this.logo) {
            this.$nextLabel.html(Content.get('Select'));
            this.$next
                .removeClass(Classes.BUTTON_GREY)
                .removeClass(Classes.BUTTON_NEUTRAL);
            // this.$next.attr("id", "Header-Next-BrowseLogoNextButton");
            // this.$nextLabel.attr("id", "Header-Next-BrowseLogoNextButtonLabel");

            this.$prev.html('<span class="js-prev-label">' + Content.get('Proceed without Logo') + '</span>');
            
        } else {
            this.$nextLabel.html(Content.get('Proceed without Logo'));
            this.$next.addClass(Classes.BUTTON_NEUTRAL);
            // this.$next.attr("id", "Header-Next-BrowseLogoProceedButton");
            // this.$nextLabel.attr("id", "Header-Next-BrowseLogoProceedButtonLabel");

            this.$prev.attr("id", "Header-Previous-BrowseLogo");
        }
        return this;
    };

    /**
     * @method updateTitle
     * @chainable
     */
    proto.updateTitle = function() {
        var state = this.state;

        if (!state) {
            return this;
        }

        this.$title.html(this.titleTemplate({
            id: state.id,
            description: state.description
        }));

        return this;
    };

    // -- Event Handlers -------------------------------------------------------

    /**
     * @method onChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onCategoryChange = function(event, newdata) {
        var id = this.$categories.find('select').val();
        EventController.emit(ActionEvents.CLIPART_CAT_CHANGE, id);
        this.updateSubcategories();
    };

    /**
     * @method onSubcategoryChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onSubcategoryChange = function(event, newsubcat) {
        var id = this.$subcategories.find('select').val();
        EventController.emit(ActionEvents.CLIPART_SUBCAT_CHANGE, id);
        this.updateLogos();
    };

    /**
     * @method onLogoChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onLogoChange = function(event) {
        if(event.target.name !== "page"){
            this.setLogo(this.getLogo(), true);


            this
                .updateNext()
                .updateSelected();
    
            // mahesh - modified
            EventController.emit(ActionEvents.CLIPART_SELECTION, this.getLogo());
            // mahesh - added
            this.onNextClickStandard(event);
        }
    };

    /**
     * @method onHideAction
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onHideAction = function() {
        this.$view.hide();
    };

    /**
     * @method onShowAction
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onShowAction = function(event, done) {
        var state = this.state;
        var controller = state && state.controller;
        var model = (controller && controller.model) || state;
        var trackEvent = model && model.trackEvent;
        var omniturePageName = model && model.description;

        // On edit/re-order of a product this controller
        // is instantiated before the logo data is fetched
        // from the order and save to session.
        if (!this.logo) {
            this.setLogo(SessionStorage.getValue('logo_STANDARD'));
        }

        this.callback = done;
        if (omniturePageName && omniturePageName.indexOf("Inside of Card - Logo") > -1) {
            omniturePageName = omniturePageName.replace("Inside of Card - Logo", "Inside of Card:Logo");
        }
         this.track({
            pageName:  this.site_prefix + ': W2P :' + omniturePageName,
            eventName: 'event115'
        });

        this
            .updateNext()
            .updateLogos()
            .updateSelected()
            .$view
            .show();
    };

    /**
     * @method onNextClickStandard
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onNextClickStandard = function(event) {
       event.preventDefault();
        // var logo = this.logo || { logoType: 'NO LOGO' };
        // mahesh
        let logo = this.getLogo() || { logoType: 'NO LOGO' };
        var callback = this.callback;
        // console.log("logo",logo);
        if (typeof callback === 'function') {
            callback(logo);
        }

       if( event.target.textContent.indexOf("Proceed without Logo")>=0){
           this.track({
               pageName: this.site_prefix + ': W2P: Logo No Logo',
               //eventName: 'event78'
           });
           
       }else{
        this.track({
            pageName: this.site_prefix + ': W2P: Logo Library',
            //eventName: 'event77'
        });
       }

        this.current = logo;
        delete this.callback;

        EventController
            .emit(ProductEvents.LOGO_CHANGE, logo)
            // mahesh
            // .emit(ActionEvents.HIDE_ALL)
            .emit(StateEvents.CHANGE)
            .emit(ActionEvents.PRODUCT_STEP);

    };

     /**
     * @method onPrevClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onPrevClick = function(event) {
         event.preventDefault();
        if(this.current == undefined && this.logo != undefined ){
            var logo = { logoType: 'NO LOGO' };
            var callback = this.callback;
            if (typeof callback === 'function') {
                callback(logo);
            }
            this.track({
                pageName: this.site_prefix + ': W2P: Logo No Logo',
                //eventName: 'event77'
            });
            this.current = logo;
            $( ".button_next" ).removeClass("button_grey" );
            delete this.callback;
            
        }else {
            var logo = { logoType: 'NO LOGO' };
            var callback = this.callback;
            if (typeof callback === 'function') {
                callback(logo);
            }
           // this.setLogo(this.current);
            delete this.callback; 
        }
        $('.logopreview').removeClass('show-logopreview');
         EventController
            .emit(ProductEvents.LOGO_CHANGE, null)
            .emit(ActionEvents.HIDE_ALL)
            .emit(StateEvents.CHANGE)
            .emit(ActionEvents.PRODUCT_STEP);
    };

    /**
     * @method onStateChange
     * @callback
     */
    proto.onStateChange = function(event, state) {
        this.state = state;
        this.updateTitle();
    };

    /**
     * @method updateStandardLogoPreview
     * @chainable
     */
    proto.updateStandardLogoPreview = function(event,logo) {
        if(this.pdtModel){
            var productQuestion = this.pdtModel.info.productInfo.question
            var inkColor1Obj;
            var inkColor2Obj;
            productQuestion.forEach( function(e) {
                if(e.id === 'inkColor1') {
                    inkColor1Obj = e;
                }
                if(e.id === 'inkColor2') {
                    inkColor2Obj = e;
                }
            });
            var color1;
            var color2;
            
            if(this.pdtModel.steps){
                var values = this.pdtModel.steps.getValues().values;
                if(values.inkColor1) {
                    color1 = values.inkColor1.value;
                    color2 = values.inkColor2.value || color1;
                }
                else {
                    color1 = 'BLACK';
                    color2 = 'BLACK';
                }
            }
            if(!inkColor2Obj) {
                color2 = color1;
            }
            var requiredInkColor = this.pdtModel.getRequiredInkForBlock('LO');
            if (requiredInkColor) {
                color2 = requiredInkColor;
            }
            var color = this.colors.getById(color2);
            var a2 = color.logo_rgb || color.getRGBString();

            var imageExt = '?fmt=webp&hei=142&wid=142&qlt=85&op_colorize=' + a2 + '&layer=1';
        }else{
            var imageExt = '?fmt=webp&hei=142&wid=142&qlt=85';
        }
        
        
        var selected = logo;
        var image = selected && selected.logoCode;
        var base = Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE;
        

        if (!image) {
            $('.designBoxPreview').empty();
            return this;
        }

        $('.mix-radio-preview').empty();
        $('.mix-radio-preview').hide();

        $('.logopreview').addClass('show-logopreview');
        $('.designBoxPreview').css('background-image', 'url(' + base + image + imageExt + ')');
        return this;
    };


    /**
     * @method generatePaginatedLogos
     */
    proto.generatePaginatedLogos = function (logoArray) {
        var currentPage = SessionStorage.getValue('logo_browse_page');
        var logos = [];
        Object.assign(logos, logoArray._items);
        var perPage = logoArray._items.length < 20 ? logoArray._items.length : 20;
        var startCount = (currentPage - 1) * perPage;
        var endCount = startCount + perPage;
        if (Math.ceil(logoArray._items.length / 20) === currentPage) {
           endCount = startCount + logoArray._items.length % 20;
           if(((startCount + logoArray._items.length) % 20) === 0) {
               endCount = logoArray._items.length;
           }
        }
        if (Math.sign(startCount) === -1) {
            startCount = Math.abs(startCount);
        }
        if (Math.sign(endCount) === -1) {
            endCount = Math.abs(endCount);
        }
        var paginatedLogos = logos.slice(startCount, endCount);
        return paginatedLogos;
    }

    /**
     * @method onClickPages
     */
     proto.onClickPages = function(event) {

        // console.log("On click pages");
      
        // pagination change
        var pages = this.$logos.find(Classes.LOGOS_PAGES_SELECTOR);
        var p_page = SessionStorage.getValue('logo_browse_page');
        var id = pages.find('input:checked').val();
    
         if (id !== undefined) {
             if ((typeof p_page != 'undefined') && (p_page != id)) {
                 SessionStorage.storeValue('logo_browse_page', parseInt(id));
                 this.renderLogos();
                 return;
             }
         }
    };


    /**
     * @member renderLogos
     */
    proto.renderLogos = function() {
        //console.log('rendering inside...')
        var category = this.getCategory() || {};
        var subcategory = this.getSubcategory() || {};
        var logos = this.getLogos() || {};
        var selected = this.logo || {};
        if(!Object.keys(logos).length) {
            return this;
        } 
        const total_logos = logos.length() < 140 ? logos.length() : 140;
        const per_page = 20;
        var total_pages = Math.ceil(total_logos/per_page);
        var pages = [];
        for (var index = 1; index <= total_pages; index++) {
            var t = {id: index};
            pages.push(t);
            
        }
        var currentPage = SessionStorage.getValue('logo_browse_page');
        if(this.pdtModel){
            var productQuestion = this.pdtModel.info.productInfo.question
            var inkColor1Obj;
            var inkColor2Obj;
            productQuestion.forEach( function(e) {
                if(e.id === 'inkColor1') {
                    inkColor1Obj = e;
                }
                if(e.id === 'inkColor2') {
                    inkColor2Obj = e;
                }
            });
            var color1;
            var color2;
            
            if(this.pdtModel.steps){
                var values = this.pdtModel.steps.getValues().values;
                if(values.inkColor1) {
                    color1 = values.inkColor1.value;
                    color2 = values.inkColor2.value || color1;
                }
                else {
                    color1 = 'BLACK';
                    color2 = 'BLACK';
                }
            }
            if(!inkColor2Obj) {
                color2 = color1;
            }
            var requiredInkColor = this.pdtModel.getRequiredInkForBlock('LO');
            if (requiredInkColor) {
                color2 = requiredInkColor;
            }
            var color = this.colors.getById(color2);
            var a2 = color.logo_rgb || color.getRGBString();
            
            this.$logos.html(this.designRadioTemplate({
                name: 'LOGO_STANDARD',
                id: category.id + '_' + subcategory.id,
                imageBase: Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE,
                imageExt: '?fmt=webp&hei=142&wid=142&qlt=85&op_colorize=' + a2 + '&layer=1',
                items: this.generatePaginatedLogos(logos),
                'default': selected.logoCode,
                pages: pages,
                current_page: currentPage,
            }));
        }else{
            this.$logos.html(this.designRadioTemplate({
                name: 'LOGO_STANDARD',
                id: category.id + '_' + subcategory.id,
                imageBase: Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE,
                imageExt: '?fmt=webp&hei=142&wid=142&qlt=85',
                items: this.generatePaginatedLogos(logos),
                'default': selected.logoCode,
                pages: pages,
                current_page: currentPage,
            }));
        }
        
        // mahesh
        // show selected logo on reload
        EventController.emit(ActionEvents.CLIPART_SELECTION, this.logo);
        return this;
    }

    /**
     * @method renderPaginationInfo
     * @param {String} startCount
     * @param {String} endCount
     * @param {String} totalCount
     */
     proto.renderPaginationInfo = function(startCount, endCount, totalCount) {
        // console.log("renderPaginationInfo");
        var subTitle = "Showing "+ startCount +" - " + endCount + " of " + totalCount;
        ($('.logo-browse-subTitle')[0]).innerHTML = subTitle;
    }

    /**
     * @method onPdtChange
     * @param {jQuery.Event} event
     * @param {App.Models.Product} product
     * @callback
     */
     proto.onPdtChange = function (event, product) {
       this.pdtModel = product;
       if (this.pdtModel && this.pdtModel.logo) {
        if (this.pdtModel.logo.logoType === "STANDARD") {
            this.renderLogos();
        }
      }
     };

    /**
     * @method onColorChange
     * @param {jQuery.Event} event
     * @callback
     */
     proto.onColorChange = function (event) {
       if (event && event.type === "colorChange") {
         if (this.pdtModel && this.pdtModel.logo) {
           if (this.pdtModel.logo.logoType === "STANDARD") {
             this.updateLogos();
           }
         }
       }
     };
    return LogoBrowseController;
    
});
