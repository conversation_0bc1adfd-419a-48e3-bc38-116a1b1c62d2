define(function (require) {
    'use strict';

    var AbstractCollection = require('../../collections/Abstract');
    var ClipArtLogoModel = require('../Logo');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.ClipArt.Collections.Logos
     * @extends App.Models.Collections.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} ClipArtData
     */
    var ClipArtLogosCollection = function (ClipArtData) {
        AbstractCollection.call(this, ClipArtData);
    };

    var proto = inherits(ClipArtLogosCollection, AbstractCollection);

    /**
     * @property itemClass
     * @type {App.Models.ClipArt.Logo}
     */
    proto.itemClass = ClipArtLogoModel;

    return ClipArtLogosCollection;
});
