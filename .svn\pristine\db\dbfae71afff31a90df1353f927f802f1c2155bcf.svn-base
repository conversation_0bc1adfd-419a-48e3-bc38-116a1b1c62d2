define(function(require) {
    'use strict';

    var Content = require('i18n!../constants/nls/en-us/Content');
    var Controller = require('./Controller');
    var EventController = require('./Event');
    var ProductEvents = require('../constants/ProductEvents');
    var bindAll = require('mout/object/bindAll');
    var currency = require('mout/number/currencyFormat');
    var inherits = require('mout/lang/inheritPrototype');
    var FbtModel = require('../models/Fbt');
    var Query = require('models/Query');

    // respChange >>
    var Settings = require('../constants/Settings');
    var Classes = require('../constants/Classes');
    var DomEvents = require('../constants/DomEvents');
    // respChange <<
    var ConfigurationProvider = require('../providers/Configuration');
    var PriceProvider = require('../providers/Price');
    var Helper = require('../util/helper');
    var cv_flag = false;

    /**
     * @class App.Controllers.Subtotal
     * @extends App.Controllers.Controller
     *
     * @constructor
     * @param {Object} config
     * @param {jQuery} config.view
     * @param {Models.Ui.Subtotal} config.model
     */
    function SubtotalController(config) {
        bindAll(this,
            'fetchPrice',
            'update',
            'renderUpdate',
            'onProductChange',
            'onSurchargeChange',
            'onViewDetailsClick'
        );

        Controller.call(this, config);
    }

    var proto = inherits(SubtotalController, Controller);

    // -- Accessors ------------------------------------------------------------

    // -- Methods --------------------------------------------------------------

    /**
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = require('hbs!templates/subtotal');

    /**
     * @method subtotalTemplate
     * @param {Object} model
     * @return {String}
     */
    proto.subtotalTemplate = require('hbs!templates/_subtotal');

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        // respChange >>
        this.$view.on(DomEvents.CLICK, Classes.BUTTON_VIEW_DETAILS_ACTION_SELECTOR, this.onViewDetailsClick)
            .on(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick);
        // respChange <<
        EventController.on(ProductEvents.CHANGE, this.onProductChange);
        EventController.on(ProductEvents.SURCHARGE_CHANGE, this.onSurchargeChange);
        return this;
    };

    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        // respChange >>
        this.$view.off(DomEvents.CLICK, Classes.BUTTON_VIEW_DETAILS_ACTION_SELECTOR, this.onViewDetailsClick)
            .off(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick);
        // respChange <<
        EventController.off(ProductEvents.CHANGE, this.onProductChange);
        EventController.off(ProductEvents.SURCHARGE_CHANGE, this.onPriceChange);
        return this;
    };

    /**
     * @method update
     * @param {App.Models.Product} product
     * @chainable
     */
    proto.update = function(product) {
        // console.log('==========================================================================================');
        // console.log('Subtotal.update.product', product);
        var querydata = Query.getInstance();
        const pushValuesToArray = function(inputObj) {
            const outputArray = [];
            for (let key in inputObj) {
                outputArray.push(inputObj[key]);
            }
            return outputArray;
        };

        
        //console.log('Subtotal.update.product.surcharges', product.surcharges);
        //console.log('Subtotal.update.product.priceKey', product.toPriceKey());
        var surchargeCacheKey = JSON.stringify(product.surcharges.concat(product.toPriceKey()), null, 4);
        //console.log('Subtotal.update.surchargeCacheKey', surchargeCacheKey);
        if (surchargeCacheKey === this.surchargeCacheKey) {
            return this;
        }
        this.surchargeCacheKey = surchargeCacheKey;
        this.product = product;

        if(typeof FbtModel.kitSkuId != 'undefined') {
            if(typeof FbtModel.kitPriceXml == 'undefined') {
                FbtModel.kitPriceXml = [];
            }
            let productXml = product.toXML(false);
            FbtModel.kitPriceXml[product.info.productId] = productXml.split('<customizedOptions>')[1].split('</customizedOptions>')[0]
            var kitItems = pushValuesToArray(FbtModel.kitPriceXml)
            this.productXml = product.kitToPriceXML(false, kitItems)
        } else {
            this.productXml = product.toXML(false);
        }
        

       
        
        

        ConfigurationProvider
            .getConfiguration()
            .then(this.fetchPrice)
            .then(this.renderUpdate);

        return this;
    };

    /**
     * Render view
     * @param product
     * @chainable
     */
    proto.renderUpdate = function(product) {
        var pricing = product.getPricing();
        var total = pricing && currency(pricing.total);
        var discounted = typeof pricing.discounted === 'number' ? currency(pricing.discounted) : false;
        /**
         * DCOM-14567 Product Pricing Object Emitted from Subtotal.js to data\assets\scripts\models\Product.js
         */
        
        this.$view.html(this.template({
            desc: Content.get('Product Total'),
            subtotal: Content.get('$') + total,
            discountedTotal: (discounted === false || discounted === total) ?
                false :
                Content.get('$') + discounted
        }));
        EventController.emit(ProductEvents.SUBTOTAL_CHANGE, pricing);


        return this;
    };

    /**
     * @method fetchPrice
     * @param {App.Models.Configuration} config
     */
    proto.fetchPrice = function(config) {
        //console.log('Subtotal.fetchPrice.this', this);
        //console.log('Subtotal.fetchPrice.this.productXML', this.productXml);
        //console.log('Subtotal.fetchPrice.config', this);
        return PriceProvider
            .setConfig(config)
            .getPrice(this.productXml);
    };

    /**
     * @method onProductChange
     * @param {jQuery.Event} event
     * @param {App.Models.Product} product
     * @callback
     */
    proto.onProductChange = function(event, product) {
        //console.log('Subtotal.onProductChange');
        this.update(product);
    };

    /**
     * @method onPriceChange
     * @param {jQuery.Event} event
     * @param {App.Models.Product} product
     * @callback
     */
    proto.onSurchargeChange = function(event, product) {
        //console.log('Subtotal.onSurchargeChange', product);
        this.update(product);
    };

    // respChange <<
    /**
     * @method onButtonClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onViewDetailsClick = function (event) {
        event.preventDefault();
        var myDefaultWhiteList = $.fn.tooltip.Constructor.Default.whiteList;
        myDefaultWhiteList.dd = [];
        myDefaultWhiteList.dt = [];
        myDefaultWhiteList.dl = [];
        myDefaultWhiteList.table = [];


        if (event.target.id == "subTotalPopUp") {
            var quantity = this.product.getQuantityValue() === undefined ? "" : "<dl><dt class='dList-term review-desc'>" + "Quantity:" + "</dt><dd class='dList-desc'>" + this.product.getQuantityValue() + "</dd></dl>";
            var copies = this.product.getCopiesValue() === "" ? "" : "<dl><dt class='dList-term review-desc'>" + "Copies:" + "</dt><dd class='dList-desc'>" + this.product.getCopiesValue() + "</dd></dl>";
            var price_widget = $(".options ul [data-id='DETAILS'] dl").html();
            price_widget = price_widget.replaceAll('Review"', 'Review_1"');
            var subtotal_text = $(".subtotal-widget .productTotal .split .split-left").html();
            var subtotal_price = $(".subtotal-widget .productTotal .split .split-right").html();
            if ($('.price-summary-tooltip').is(':visible')) {
                $('.price-summary-tooltip').css('display', 'none');
            } else {
                $('.price-summary-tooltip').html(this.subtotalTemplate({
                    host_url: host_url + '/webasset/w2p_mobile/' + baseAppUrl + 'assets/media/images/subtotal-close.png',
                    quantity: quantity,
                    copies: copies,
                    price_widget: price_widget,
                    subtotal_text: subtotal_text,
                    subtotal_price: subtotal_price
                }));

                $('#subTotalPopUpCloseIcon.close.popover-close').on('click', function () {
                    $('.price-summary-tooltip').css('display', 'none');
                });
                $('.price-summary-tooltip').css('display', 'block');
            }
            return this;
        }

    }


    proto.onButtonClick = function (e) {
        if ($(e.currentTarget).attr('id') == 'envelope_helpReview_1') {
            popupProcess(e.currentTarget, '#CVCloseIcon.close.tooltip-close', '#envelope_popup_model', e);
        } else if ($(e.currentTarget).attr('id') == 'foldingHelpReview_1') {
            popupProcess(e.currentTarget, '#foldingCloseIcon.close.tooltip-close', '#foldingPopup', e);
        } else if ($(e.currentTarget).attr('id') == 'returnHelpReview_1') {
            popupProcess(e.currentTarget, '#foldingCloseIcon.close.tooltip-close', '#returnPopup', e);
        } else {
            popupProcess(e.currentTarget, '#CVCloseIcon.close.tooltip-close', '#popup_model', e);
        }
    };

    function popupProcess(id, closeId, popupModel, event) {
        if (Helper.isSameTooltipClick(event)) {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
        } else {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
            $(id).popover({
                html: true,
                trigger: "manual",
                content: function () {
                    return $(popupModel).html();
                }
            });
            if (!cv_flag) {
                $(id).popover('show');
                if ($(id).attr('id') === "personalizationReview_1") {
                    var popup_style = $('#personalization_link').attr('style');
                    $('#personalization_link').attr('style', popup_style + "margin-top: 7px !important; margin-left: -170px !important");
                }
            } else {
                $(id).popover('hide');
            }
            cv_flag = !cv_flag;

            $(closeId).on('click', function (e) {
                cv_flag = false;
                $(id).popover('hide');
            });
        }
    }

    // respChange <<

    return SubtotalController;
});
