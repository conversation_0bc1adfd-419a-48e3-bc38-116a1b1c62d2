/* ---------------------------------------------------------------------
Original Author: <PERSON>
Contributors: N/A

Target Browsers: All
Media Type: Screen, Projection
Width: All Sizes
------------------------------------------------------------------------ */
/*csslint
    box-sizing: false
*/

/* ---------------------------------------------------------------------
 Input Styles
------------------------------------------------------------------------ */
.inputBox {
    display: block;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
    height: 34px;
    padding: 0 8px;
    margin: 3px 0 4px;
    border: 1px solid #cccccc;
    position: relative;
    border-radius: 3px;
    color: #000f0c;    
    border-color: #dadbdd;
}

.inputBox_select {
    overflow: hidden;
    padding: 0;
    background: #ffffff url(../../media/images/select-input-btn-new.png) no-repeat right 6px;
    background: rgba(255,255,255,1) url(../../media/images/<EMAIL>) no-repeat right 6px;
    background-size: 28px 19px;
    background-color: #f1f1f1;
}

.inputBox_select:after {
    content: " ";
    position: absolute;
    right: 0;
    top: 0;
    width: 33px;
    height: 100%;
    pointer-events: none;
    background: #ffffff url(../../media/images/select-input-btn-new.png) no-repeat right 6px;
    background: rgba(255,255,255,1) url(../../media/images/<EMAIL>) no-repeat right 6px;
    background-color: #f1f1f1;
    background-size: 26px 17px;
}

.inputBox_select_hasDropdown {
    overflow: visible;
}

.inputBox_select_review {
    font-size:14px;
    margin-top: -1px !important;
    height: 30px !important;
}

/* select input is nested for the sake of hiding native arrow in IE9 down */
.inputBox_select-input {
    -webkit-appearance: none; /* webkit - hide native select arrow */
    -moz-appearance: none; /* moz - hide native select arrow */
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
    padding: 5px 33px 5px 8px;
    border: none;
    position: relative;
    background: transparent;
    font: inherit;
    color: inherit;
    line-height: 20px;
    text-align: left;
    text-indent: 0.01px;
    text-overflow: '';
    font-size: 14px;
}

.inputBox_select-input > img {
    margin-top: 1px;
}

/* ie - hide native select arrow */
.inputBox_select > select::-ms-expand {
    display: none;
}

.inputBox_select:focus,
.inputBox_select-input:focus {
    -webkit-box-shadow: 0 0 3px 0 #259cda;
    -moz-box-shadow: 0 0 3px 0 #259cda;
    box-shadow: 0 0 3px 0 #259cda;
}

.inputBox_select-dropdown {
    display: none;
    overflow: auto;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    max-height: 300px;
    padding: 4px 0;
    border: 1px solid #7f9db9;
    position: absolute;
    top: 100%;
    top: calc(100% + 1px); /* as designed */
    left: -1px;
    right: -1px;
    background-color: #ffffff;
    z-index: 100;
}

/* Always display scroll bar on OSX and iOS */
.inputBox_select-dropdown::-webkit-scrollbar {
    width: 12px;
    background-color: #f2f2f2;
}

.inputBox_select-dropdown::-webkit-scrollbar-thumb {
    border: 3px solid #f2f2f2;
    border-radius: 4px;
    background-color: #d9d9d9;
}

.inputBox_select-dropdown_isOpen {
    display: block;
}

.inputBox_select-dropdown-item {
    padding: 8px 8px 3px 8px;
    font-family: Arial;
    font-size: 10px;
    padding-bottom: 5px;
}

.inputBox_select-dropdown-item:hover,
.inputBox_select-dropdown-item_focused {
    background-color: #c7e3ef;
}

.inputBox_select-dropdown-item_selected {
    background: #ff9900 url(../../media/images/button-bg.png) repeat-x 0 -64px;
    background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#ff9900), to(#e77401));
    background: -webkit-linear-gradient(top, #ff9900, #e77401);
    background: -moz-linear-gradient(top, #ff9900, #e77401);
    background: -ms-linear-gradient(top, #ff9900, #e77401);
    background: -o-linear-gradient(top, #ff9900, #e77401);
    background: linear-gradient(to bottom, #ff9900, #e77401);
}

.typestyle_img {
    display:block;
    height:25px;
}

.mix-inputBox_error {
    border-color: #D61120;
}

.inputBox_select.mix-inputBox_error {border-color: #dadbdd;}

.inputBox_select_review,
.inputBox_select_review:after, 
.inputBox_select_review .inputBox_select-input
{
    background-color: #ffffff !important;
}
