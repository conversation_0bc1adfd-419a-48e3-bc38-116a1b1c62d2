<div class="grid">

    <div class="grid-col grid-col_7of10">

        <div class="design">

            <div class="design-hd">
                <div class="detailBox detailBox_1of3">
                    <div class="detailBox-header">
                        <span class="hdg hdg_h3">Text on Voucher 1</span>
                    </div>
                    <div class="detailBox-body">

                    </div>
                </div>

                <div class="detailBox detailBox_1of3">
                    <div class="detailBox-header">
                        <span class="hdg hdg_h3">Text on Voucher 2</span>
                    </div>
                    <div class="detailBox-body">

                    </div>
                </div>

                <div class="detailBox detailBox_1of3">
                    <div class="detailBox-header">
                        <span class="hdg hdg_h3">Text Above Signature Line</span>
                    </div>
                    <div class="detailBox-body detailBox-body_check">

                    </div>
                </div>
            </div> {{! /design-hd }}

            <div class="design-bd">

            </div>

        </div> {{! /design }}

    </div> {{! /grid-col }}

    <div class="grid-col grid-col_3of10">

        <div class="options">

            <p class="txtLarge">
                <span class="hdg hdg_h2">Note: </span>
                Blank lines will not print
            </p>

            <ul class="vList vList_loose">

                <li>
                    <label class="txtLarge">
                        Text Above Signature Line
                        <input type="text" class="inputBox" />
                    </label>
                </li>

                <li>
                    <label class="txtLarge">
                        Text on Voucher 1
                        <input type="text" class="inputBox" />
                    </label>
                </li>

                <li>
                    <label class="txtLarge">
                        Text on Voucher 2
                        <input type="text" class="inputBox" />
                    </label>
                </li>

                <li>
                    <input type="checkbox" id="signatureLine" class="checkbox" />
                    <label for="signatureLine">
                        Add Another Signature Line
                    </label>
                </li>

            </ul>

        </div> {{! /options }}

        {{> subtotal}}

    </div> {{! /grid-col }}

</div> {{! /grid }}
