<div class="grid">

    <div class="grid-col grid-col_3of10 grid-col_prefix2of10">
        <div class="logoUpload {{! logoUpload_error }}"></div>
    </div>

    <div class="grid-col grid-col_3of10 grid-col-suffix2of10">

        <div class="options">

            <h2 class="hdg hdg_h2">Upload Your Own Logo: </h2>

            <div class="vList vList_std">

                {{! Errors go here. Toggle this whole bit
                <div>
                    <div class="error">1337, Logo Failed To Upload, 0, 0, null, null, null</div>
                </div>
                }}

                <div>
                    <input type="button" class="button" value="Browse for a File" />
                </div> {{! /vList-child }}

                <div>
                    <div class="media">
                        <div class="media-img media-img_small">
                            <div class="designBox designBox_active"></div>
                        </div>
                        <div class="media-body">
                            <span class="txtStd">
                                Logo will be printed in the selected ink color.
                            </span>
                            <h3 class="hdg hdg_h4">
                                Black
                            </h3>
                        </div>
                    </div>
                </div> {{! /vList-child }}

                <div>
                    <a href="#" class="link">Proceed Without Logo</a>
                </div> {{! /vList-child }}

            </div> {{! /vList }}

        </div> {{! /options }}

    </div> {{! /grid-col }}

</div> {{! /grid }}

<div class="grid">

    <div class="grid-col grid-col_6of10 grid-col_prefix2of10 grid-col_suffix2of10">

        <p class="txtStd">
            <span class="hdg hdg_h4"> Please Note: </span>
            Custom logos are not displayed on the product preview.
        </p>
        <p class="txtStd">
            By clicking browse for a file, I confirm I have all the appropriate and necessary right<br />
            and authority to use the uploaded text, images and/or designs and my use will not<br />
            infringe on any intellectual property right of any other third party.
        </p>

    </div>

    <div class="grid-col grid-col_6of10 grid-col_prefix2of10 grid-col_suffix2of10">

        <div class="cfgTopDivider">

            <h3 class="hdg hdg_h4">How do I get my logo onto my phone or tablet?</h3>

            <span class="txtStd">
                Is your logo available from a social media or cloud storage service?
            </span>

            <ul class="vList vList_bullet">
                <li>Your Website</li>
                <li>Facebook</li>
                <li>LinkedIn</li>
                <li>Dropbox</li>
                <li>Google Drive</li>
            </ul>

            <span class="txtStd">
                Once you locate the logo, follow these steps to access it on your device
            </span>

            <ol class="vList vList_ordered">
                <li>Download the Logo. (Try holding your finger on it for a few seconds)</li>
                <li>Save it to the device storage.</li>
                <li>
                    <div class="vList-holdRight">
                        <p class="txtStd">Come back here and click:</p>
                        <input type="button" class="button" value="Browse for a File" />
                    </div>
                </li>
                <li>Find your Logo in device storage.</li>
            </ol>

        </div> {{! /cfgTopDivider }}

    </div> {{! /grid-col }}

</div> {{! /grid }}