define(function(require) {
    'use strict';

    var $ = require('jquery');
    var Classes = require('../constants/Classes');
    var Controller = require('./Controller');
    var EventController = require('./Event');
    var ActionEvents = require('../constants/ActionEvents');
    var DomEvents = require('../constants/DomEvents');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');

    require('fancybox');

    /**
     * @class App.Controllers.Message
     * @extends App.Controllers.Controller
     *
     * @constructor
     * @param {Object} config
     */
    function MessageController(config) {
        bindAll(this,
            'onUpdateModal'
        );

        Controller.call(this, config);
    }

    var proto = inherits(MessageController, Controller);

    // -- Methods --------------------------------------------------------------

    /**
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = require('hbs!templates/message');

    /**
     * @method render
     * @chainable
     */
    proto.render = function() {
        this.$view
            .html(this.template());

        return this.start();
    };

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        this.$view
            .on(DomEvents.CLICK, Classes.MODAL_ACTION_ACCEPT_SELECTOR, this.onCancelClick);

        EventController
            .on(ActionEvents.MODAL_OPEN, this.onUpdateModal);

        return this;
    };

    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        this.$view
            .off(DomEvents.CLICK, Classes.MODAL_ACTION_ACCEPT_SELECTOR, this.onCancelClick);

        EventController
            .off(ActionEvents.MODAL_OPEN, this.onUpdateModal);

        return this;
    };

    //-- Event Handlers --------------------------------------------------------

    /**
     * @method onUpdateModal
     * @param {jQuery} event
     * @param {Object} args
     * @callback
     */
    proto.onUpdateModal = function(event, args) {
        this.$view
            .html(this.template({
                heading: args.heading,
                content: args.content,
                actions: args.actions
            }));

        $.fancybox.defaults.modal = true;
        $.fancybox.defaults.autoSize = false;
        $.fancybox.defaults.autoHeight = true;
        $.fancybox.defaults.width = 450;

        $.fancybox(this.$view);
        $.fancybox.open();
    };

    /**
     * @method onCancelClick
     *
     * @param {jQuery} event
     * @param {Object} button
     * @callback
     */
    proto.onCancelClick = function() {
        $.fancybox.close();
    };

    return MessageController;
});
