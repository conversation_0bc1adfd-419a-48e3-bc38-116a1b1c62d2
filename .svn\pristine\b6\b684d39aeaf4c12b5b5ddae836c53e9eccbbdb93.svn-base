define(function (require) {
    'use strict';

    var $ = require('jquery');
    var AbstractProvider = require('./Abstract');
    var Settings = require ('../constants/Settings');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Providers.FbtMicro
     * @extends App.Providers.Abstract
     *
     * @constructor
     */
    var KitMicroProvider = function (baseUrl, config) {
        bindAll(this, '_onResponseReceived',
                      '_processMicroData');

        AbstractProvider.apply(this, arguments);
    };

    var proto = inherits(KitMicroProvider, AbstractProvider);

    /**
     * Makes a call to get all FBT product details
     *
     * @method getClipArt
     * @param {bool} flushCache
     * @return {Promise}
     */
    proto.getKitDetails = function(params) {

        params.cfg = 'W2P'; 
        this.promise = this
            .get(Settings.SVC_PRODUCT, params)
            // .get('/proxy/ProductInfoWrapper.xml', params)
            .then(this._onResponseReceived)
            .fail(this._onError);

        return this.promise;
    };

    /**
     * Handles the response from the configuration call
     *
     * @method _onResponseReceived
     * @param {string} data
     * @private
     */
    proto._onResponseReceived = function(data) {
        // success! we have the data, store and resolve the promise;
        if (!data) {
            this.promise.reject(data);
        }
        var response = $.xml2json(data);

        if (response.error) {
            throw new Error(response.error);
        }

        // console.log('kiiiit', response)
        var processedData = this._processMicroData(response);
        // console.log('gggggg', processedData)
        return processedData;
    };

    /**
     * Handles the response from the configuration call
     *
     * @method _onResponseReceived
     * @param {string} data
     * @private
     */
    proto._processMicroData = function(kitData) {
        try {
          var result = {};

          if (!kitData.configuration.productInfo.kit) {
              return {};
          }
          if (Array.isArray( kitData.configuration.priceInfo.price)) {
             result.surchargeInfo = kitData.configuration.priceInfo.price;
          } else {
            result.surchargeInfo = [];
            result.surchargeInfo.push(kitData.configuration.priceInfo.price)
          }
          // kit return 2 skus, default to 1st one
          if (Array.isArray(kitData.configuration.productInfo.kit)) {
            kitData.configuration.productInfo.kit = kitData.configuration.productInfo.kit[0]
          }
          result.desc = kitData.configuration.productInfo.kit.$.desc;
          result.sku = kitData.configuration.productInfo.kit.$.id;
          // result.price = kitData.configuration.priceInfo.price.find(function(item){
          //   return item.$.id == result.sku;
          // }).option.$.price;
          const components = kitData.configuration.productInfo.kit.component;
          const optionIds = [];
      
          // components.forEach((component) => {
          //   if (Array.isArray(component.option)) {
          //     // Handle array of options
          //     component.option.forEach((option) => {
          //       optionIds.push({ recId: option.$.swatch, recSkuId:option.$.id, qty:option.$.qty });
          //     });
          //   } else if (component.option) {
          //     // Handle single option
          //     optionIds.push({ recId: component.option.$.swatch, recSkuId: component.option.$.id, qty: component.option.$.qty });
          //   }
          // });

          components.forEach(function(component) {
            if (Array.isArray(component.option)) {
              // Handle array of options

              // console.log('component.option', component.option)
              optionIds.push({ recId: component.option[0].$.swatch, recSkuId: component.option[0].$.id, qty: component.option[0].$.qty });
              // component.option.forEach(function(option) {
              //   optionIds.push({ recId: option.$.swatch, recSkuId: option.$.id, qty: option.$.qty });
              // });
            } else if (component.option) {
              // Handle single option
              optionIds.push({ recId: component.option.$.swatch, recSkuId: component.option.$.id, qty: component.option.$.qty });
            }
          });
          result.optionIds = optionIds
          return result;
        } catch (error) {
          console.error("Error getting kit option IDs:", error);
          return [];
        }
    };

    return new KitMicroProvider();
});
