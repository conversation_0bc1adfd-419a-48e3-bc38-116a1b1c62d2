<dl>
    {{#each details}}
    <dt class="dList-term">{{{desc}}}:</dt>
    <dd id="item-{{desc}}" class="dList-desc">{{value}}</dd>
    {{/each}}
    {{#each surcharges}}
    <dt class="dList-term review-desc">{{{desc}}}:
        {{#if isFoldingExist}}
        <img id="foldingHelpReview" alt="foldingHelpReview"
            src="{{host_url}}/webasset/w2p_mobile/{{baseAppUrl}}assets/media/images/helpText.png"
            class="js-button-action"
            data-template='<div id="foldingWorks" class="custpop popover" role="tooltip"><div class="arrow"></div><div class="popover-body"></div></div>'
            data-container="body" data-placement="bottom" data-content="" data-original-title="" title=""></img>
        {{/if}}
        {{#if isEnvUpgradeExist}}
        <img id="envelope_helpReview" alt="envelope_helpReview" class="js-button-action"
            src="{{host_url}}/webasset/w2p_mobile/{{baseAppUrl}}assets/media/images/helpText.png" data-container="body"
            data-template='<div id="envelope_link" class="custpop popover" role="tooltip"><div class="arrow"></div><div class="popover-body"></div></div>'
            data-placement="bottom" data-content="" data-original-title="" title=""></img>
        <div class="env-opts">
            {{!-- <div class="optionSelected">Option Selected: </div> --}}
            <div class="selectedOption"> {{swatchSel}} </div>
        </div>
        {{/if}}
        {{#if isEnvImprintExist}}
        <img id="returnHelpReview" alt="returnHelpReview"
            src="{{host_url}}/webasset/w2p_mobile/{{baseAppUrl}}assets/media/images/helpText.png"
            class="js-button-action" data-template='<div id="returnWorks" class="custpop popover" role="tooltip"><div class="arrow"></div><div
                        class="popover-body"></div></div>' data-container="body" data-placement="bottom"
            data-content="" data-original-title="" title=""></img>
        {{/if}}
        {{#if isInkcolorExist}}
        &nbsp;{{inkColor}}
        {{/if}}
        {{#if isPersonalizationExist}}
        <img id="personalizationReview" alt="personalizationReview"
            src="{{host_url}}/webasset/w2p_mobile/{{baseAppUrl}}assets/media/images/helpText.png"
            class="js-button-action"
            data-template='<div id="personalization_link" class=" custpop popover personalization_link_review " role="tooltip"><div class="arrow"></div><div class="popover-body"></div></div>'
            data-container="body" data-placement="bottom" data-content="" data-original-title="" title=""></img>
            <div id="popup_model" style="display:none">
                <div id="personalization"><img
                        src="{{host_url}}/webasset/w2p_mobile/{{baseAppUrl}}/assets/media/images/tooltip-close.png" id="CVCloseIcon"
                        alt="CVCloseIcon" class="close tooltip-close" style="position: absolute; top: 0; right: 7px;" />
                    <div class='popovertitle'> Ultimate Personalization Package - ${{amount}}</div></br>
                    <div>For just ${{amount}}, you get <span class='popover-heading'> ALL </span>of the following:</div></br>
                    <div><span class='popover-heading'>Create Your Own Verse:</span>&nbsp;&nbsp;Use up to 5 lines, 35 characters and
                        spaces per line. The layout and type size of your verse will be determined by our designers.</div></br>
                    <div><span class='popover-heading'>Custom Logo:</span>&nbsp;&nbsp;Upload your company's custom logo. Its layout
                        among the card's messaging and signatures (if applicable) will be carefully crafted by our designers.</div>
                    </br>
                    <div><span class='popover-heading'>Signatures:</span>&nbsp;&nbsp;If you select to include signature(s) on your
                        card, then after submitting your order you will receive an email with instructions describing how to submit
                        signatures to Deluxe.</div>
                </div>
            </div>
        {{!-- <br><br>
        <div class="optionsCont">
            <div class="optionSelected">Options Selected: </div> --}}
            {{#if customVerse}}
            <div class="selectedOption">{{customVerse}} </div>
            {{/if}}
            {{#if customLogo}}
            <div class="selectedOption">{{customLogo}} </div>
            {{/if}}
            {{#if signature}}
            <div class="selectedOption">{{signature}} </div>
            {{/if}}
        </div>
        {{/if}}
    </dt>
    {{#if priceFormatted}}
    <dd class="dList-desc">+${{priceFormatted}}</dd>
    {{/if}}
    {{/each}}
</dl>
