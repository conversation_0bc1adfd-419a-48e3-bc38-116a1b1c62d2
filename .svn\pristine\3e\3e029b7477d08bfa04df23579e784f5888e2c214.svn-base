define(function(require) {
    'use strict';

    var Controller = require('./Controller');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Controllers.Question
     * @extends App.Controllers.Controller
     *
     * @constructor
     * @param {Object} config
     * @param {jQuery} config.view
     * @param {App.Models.Ui.Question} config.model
     */
    function QuestionController(config) {
        bindAll(
            this,
            'onProductChange',
            'updateErrors'
        );

        Controller.call(this, config);
    }

    var proto = inherits(Question<PERSON><PERSON><PERSON><PERSON>, Controller);

    /**
     * @property registry
     * @type {Object.<String,App.Controllers.Controller>}
     */
    proto.registry = {
        checkbox: require('./question/Checkbox'),
        dropdown: require('./question/Dropdown'),
        radio: require('./question/Radio'),
        radioExt: require('./question/RadioExt'),
        verse: require('./question/Verse'),
        rgb: require('./question/Rgb'),
        swatch: require('./question/Swatch'),
        text: require('./question/Text'),
        reqInk: require('./question/ReqInk'),
        model: require('./question/Model'),
        comment: require('./question/Comment'),
        'x-details': require('./question/Details'),
        'x-note': require('./question/Note'),
    };

    /**
     * @method init
     * @chainable
     */
    proto.init = function() {
        return this.start();
    };

    /**
     * @method start
     * @chainable
     */
    proto.start = function() {
        var $view = this.$view;
        var model = this.model;
        var Controller = this.registry[model.control];

        if (!Controller) {
            return;
        }

        this.started = [
            new Controller({
                view: $view,
                model: model
            })
        ];

        return this;
    };

    /**
     * @method isValid
     * @return {Boolean}
     */
    proto.isValid = function() {
        return this.started[0].isValid();
    };

    /**
     * Determine if question should be displayed
     *
     * @method isDisplayable
     * @returns {Boolean}
     */
    proto.isDisplayable = function() {
        return this.started[0].isDisplayable();
    };

    /**
     * @method updateErrors
     * @param {jQuery} $input
     * @return {App.Controllers.Questions.Abstract}
     */
    proto.updateErrors = function($input) {
        return this.started[0].updateErrors($input);
    };

    return QuestionController;
});
