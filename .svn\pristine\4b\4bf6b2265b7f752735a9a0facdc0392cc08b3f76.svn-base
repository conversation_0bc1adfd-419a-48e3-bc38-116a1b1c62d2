define(function(require){

//automatically generated, do not edit!
//run `node build` instead
return {
    'append' : require('./array/append'),
    'collect' : require('./array/collect'),
    'combine' : require('./array/combine'),
    'compact' : require('./array/compact'),
    'contains' : require('./array/contains'),
    'difference' : require('./array/difference'),
    'every' : require('./array/every'),
    'filter' : require('./array/filter'),
    'find' : require('./array/find'),
    'findIndex' : require('./array/findIndex'),
    'findLast' : require('./array/findLast'),
    'findLastIndex' : require('./array/findLastIndex'),
    'flatten' : require('./array/flatten'),
    'forEach' : require('./array/forEach'),
    'indexOf' : require('./array/indexOf'),
    'insert' : require('./array/insert'),
    'intersection' : require('./array/intersection'),
    'invoke' : require('./array/invoke'),
    'join' : require('./array/join'),
    'lastIndexOf' : require('./array/lastIndexOf'),
    'map' : require('./array/map'),
    'max' : require('./array/max'),
    'min' : require('./array/min'),
    'pick' : require('./array/pick'),
    'pluck' : require('./array/pluck'),
    'range' : require('./array/range'),
    'reduce' : require('./array/reduce'),
    'reduceRight' : require('./array/reduceRight'),
    'reject' : require('./array/reject'),
    'remove' : require('./array/remove'),
    'removeAll' : require('./array/removeAll'),
    'shuffle' : require('./array/shuffle'),
    'slice' : require('./array/slice'),
    'some' : require('./array/some'),
    'sort' : require('./array/sort'),
    'sortBy' : require('./array/sortBy'),
    'split' : require('./array/split'),
    'toLookup' : require('./array/toLookup'),
    'union' : require('./array/union'),
    'unique' : require('./array/unique'),
    'xor' : require('./array/xor'),
    'zip' : require('./array/zip')
};

});
