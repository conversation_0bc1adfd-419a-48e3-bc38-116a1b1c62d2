define(function (require) { // jshint ignore:line
    'use strict';

    var $ = require('jquery');
    var AbstractProvider = require('./Abstract');
    var RegionCollection = require('../models/collections/Regions');
    var Settings = require ('../constants/Settings');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Providers.Region
     * @extends App.Providers.Abstract
     *
     * @constructor
     */
    var RegionProvider = function () {
        bindAll(this, '_onResponseReceived');

        AbstractProvider.apply(this, arguments);
    };

    var proto = inherits(RegionProvider, AbstractProvider);

    /**
     *
     * @method getRegions
     *
     * @param {bool} flushCache
     *
     * @return {Promise}
     */
    proto.getRegions = function(flushCache) {
        if (this.promise && flushCache !== true) {
            // data will be cached after the first call
            return this.promise;
        }

        this.promise = this
            .getConfig()
            .then(this._onResponseReceived)
            .fail(this._onError);

        return this.promise;
    };

    /**
     * Handles the response from the ajax call
     *
     * @method _onResponseReceived
     *
     * @param {HTMLDocument} data
     * @private
     */
    proto._onResponseReceived = function(data) {
        // success! we have the data, store and resolve the promise;
        //console.log('gggg', data);
        var response = $.xml2json(data);

        if (response.error) {
            throw new Error(response.error);
        }

        return new RegionCollection(data);
    };

    return new RegionProvider();
});
