define(function(require) {
    'use strict';

    var $ = require('jquery');
    var Classes = require('../constants/Classes');
    var Controller = require('./Controller');
    var EventController = require('./Event');
    var ActionEvents = require('../constants/ActionEvents');
    var Query = require('models/Query');
    var DomEvents = require('../constants/DomEvents');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var Settings = require('../constants/Settings');

    require('fancybox');

    /**
     * @class App.Controllers.Message
     * @extends App.Controllers.Controller
     *
     * @constructor
     * @param {Object} config
     */
    function ProofController(config) {
        bindAll(this,
            'onProofModal'
        );

        /**
		 * @property query
		 * @type {App.Models.Query}
		 */
		this.query = Query.getInstance();

        Controller.call(this, config);
    }

    var proto = inherits(<PERSON>of<PERSON><PERSON><PERSON><PERSON>, Controller);

    // -- Methods --------------------------------------------------------------

    /**
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = require('hbs!templates/proof');

    /**
     * @method render
     * @chainable
     */
    proto.render = function() {
        this.$view
            .html(this.template({baseAppUrl: baseAppUrl, host_url: host_url}));

        return this.start();
    };

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        this.$view
            .on(DomEvents.CLICK, Classes.PROOF_DOWNLOAD_SELECTOR, this.onProofDownload)
            .on(DomEvents.CLICK, Classes.PROOF_CHECK_SELECTOR, this.onProofClick)
            .on(DomEvents.CLICK, Classes.PROOF_CLOSE_SELECTOR, this.onCancelClick);

        EventController
            .on(ActionEvents.PROOF_OPEN, this.onProofModal);

        return this;
    };

    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        this.$view
            .off(DomEvents.CLICK, Classes.PROOF_DOWNLOAD_SELECTOR, this.onProofDownload)
            .off(DomEvents.CLICK, Classes.PROOF_CHECK_SELECTOR, this.onProofClick)
            .off(DomEvents.CLICK, Classes.PROOF_CLOSE_SELECTOR, this.onCancelClick);

        EventController
            .off(ActionEvents.PROOF_OPEN, this.onProofModal);

        return this;
    };

    //-- Event Handlers --------------------------------------------------------

    /**
     * @method onUpdateModal
     * @param {jQuery} event
     * @param {Object} args
     * @callback
     */
    proto.onProofModal = function(event, args) {

        // let urlInfo = $('#imageLocation').val().toString();
        let url = args.loc.replace('fmt=jpg', 'fmt=pdf');
        let waterMarkTxt;
        if(args.isMicr)
        {
            waterMarkTxt = '&insertAfter.group_insertionPoint=%3CGroup%3E%3CBitmapImage%20source=%22@Embed(proofOverlay15pctMicr)%22%20s7:fit=%22fit%22/%3E%3C/Group%3E';
        }
        else {
            waterMarkTxt = '&insertAfter.group_insertionPoint=%3CGroup%3E%3CBitmapImage%20source=%22@Embed(proofOverlay15pct)%22%20s7:fit=%22fit%22/%3E%3C/Group%3E';
        }

        url = url + waterMarkTxt;
        url = url.replace('%5B', '[');
        url = url.replace('%5D', ']');
        // url = Settings.SVC_PROOF + encodeURIComponent(url);

        this.$view
            .html(this.template({
                heading: args.heading,
                msg1: args.msg1,
                msg2: args.msg2,
                msg3: args.msg3,
                loc: url,
                fname: this.query.productId,
                actions: args.actions,
                baseAppUrl: baseAppUrl,
                host_url: host_url,
                proofDownload: Settings.SVC_PROOF,
            }));

        $.fancybox.defaults.modal = true;
        $.fancybox.defaults.autoSize = false;
        $.fancybox.defaults.autoHeight = true;
        $.fancybox.defaults.width = 480;

        $.fancybox(this.$view);
        $.fancybox.open();
    };

    /**
     * @method onCancelClick
     *
     * @param {jQuery} event
     * @param {Object} button
     * @callback
     */
    proto.onCancelClick = function() {
        $.fancybox.close();
    };
    
    /**
     * @method onProofClick
     *
     * @param {jQuery} event
     * @param {Object} button
     * @callback
     */
    proto.onProofClick = function() {
        var loc = $("#mainImg").prop("src") ;
        console.log('fff', loc);
        $('.btn-download').toggleClass("btn-active");
        $('.link-download').toggleClass("link-download-active");
    };
    
    /**
     * @method onProofDownload
     *
     * @param {jQuery} event
     * @param {Object} button
     * @callback
     */
    proto.onProofDownload = function() {
        // document.getElementById("myForm").submit();
        let urlInfo = $('#imageLocation').val().toString();
        let url = urlInfo.replace('fmt=jpg', 'fmt=pdf');
        url = url.replace('%5B', '[');
        url = url.replace('%5D', ']');
        // console.log('xxxxx', url)
        url = Settings.SVC_PROOF + encodeURIComponent(url);
        // console.log('xxxxxx', url)
        
        const a = document.createElement('a');
        a.href = url;
        a.target = '_blank';
        // console.log('cccc', url.split('/').pop())
        a.download = 'proof-download.pdf';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        $.fancybox.close();
    };

    return ProofController;
});
