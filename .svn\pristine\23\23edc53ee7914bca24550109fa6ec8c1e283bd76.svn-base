define(function(require){

//automatically generated, do not edit!
//run `node build` instead
return {
    'dayOfTheYear' : require('./date/dayOfTheYear'),
    'diff' : require('./date/diff'),
    'i18n_' : require('./date/i18n_'),
    'isLeapYear' : require('./date/isLeapYear'),
    'isSame' : require('./date/isSame'),
    'parseIso' : require('./date/parseIso'),
    'quarter' : require('./date/quarter'),
    'startOf' : require('./date/startOf'),
    'strftime' : require('./date/strftime'),
    'timezoneAbbr' : require('./date/timezoneAbbr'),
    'timezoneOffset' : require('./date/timezoneOffset'),
    'totalDaysInMonth' : require('./date/totalDaysInMonth'),
    'totalDaysInYear' : require('./date/totalDaysInYear'),
    'weekOfTheYear' : require('./date/weekOfTheYear')
};

});
