define(function() {
    'use strict';

    var Handlebars = require('hbs/handlebars');

    /**
     * @type {Function}
     * @param {Number} count
     * @param {Object} options
     * @return {String}
     */
    var repeat = function(count, options) {
        options = options || {};

        var priv;
        var data = options.data || {};
        var retval = '';
        var i = 0;

        if (count === null || count === undefined) {
            return options.fn(this, { data: data });
        }

        for (; i < count; i++) {
            priv = Handlebars.createFrame(data);

            priv.index = i;
            priv.indexPlusOne = i + 1;
            priv.length = count;

            retval = retval + options.fn(this, { data: priv });
        }

        return retval;
    };

    Handlebars.registerHelper('repeat', repeat);

    return repeat;
});
