define(function (require) {
    'use strict';

    var $ = require('jquery');
    var AbstractCollection = require('../../collections/Abstract');
    var UIQuestionModel = require('../Question');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var toLookup = require('mout/array/toLookup');

    /**
     * @class App.Models.Ui.Collections.Questions
     * @extends App.Models.Collections.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} questions
     */
    function UIQuestionsCollection(questions) {
        bindAll(this,
            '_getDisplayable',
            '_hasImportant',
            '_affectsPrice'
        );

        AbstractCollection.call(this, questions);
    }

    var proto = inherits(UIQuestionsCollection, AbstractCollection);

    /**
     * @property itemClass
     * @type {App.Models.Ui.Question}
     */
    proto.itemClass = UIQuestionModel;

    /**
     * Recursively sets product info on all steps.
     *
     * @method setInfo
     * @param {Object} info
     * @param {App.Models.FXG} fxg
     * @param {App.Models.CustomerProfile} customer
     * @chainable
     */
    proto.setInfo = function(info, fxg, customer) {
        var productInfo = info && info.productInfo;
        var questionInfo = productInfo && productInfo.question;
        var questionIndex = toLookup(questionInfo, 'id');

        this.each(this._setInfo.bind(this, questionIndex, info, fxg, customer));

        return this;
    };

    /**
     * @method _setInfo
     * @param {Object} questionIndex
     * @param {Object} product
     * @param {App.Models.FXG} fxg
     * @param {App.Models.CustomerProfile} customer
     * @param {App.Models.Ui.Question} question
     * @callback
     */
    proto._setInfo = function(questionIndex, product, fxg, customer, question) {
        var info = questionIndex[question.id];

        question.setInfo(info, fxg, product, customer);
    };

    /**
     * Returns an object containing all selections made by
     * the user for all contained questions
     *
     * @method getValues
     * @return {Object}
     */
    proto.getValues = function() {
        var results = {
            blocks: [],
            values: {},
            surcharges: []
        };

        this.each(this._getValue.bind(this, results));

        return results;
    };

    /**
     * Appends a single question's value to an existing object
     *
     * @method _getValue
     * @param {object} results Result set to append to
     * @param {App.Models.Ui.Question} item
     * @private
     */
    proto._getValue = function(results, item) {
        var blocks = item.blocks;
        var values = item.getValues();

        results.values[item.id] = values;

        if (blocks.length()) {
            results.blocks = $.merge(results.blocks, blocks.getValues());
        }

        if (values && values.surcharges) {
            results.surcharges = $.merge(results.surcharges, results.values[item.id].surcharges);
        }
    };

    proto.getPriceAffected = function() {
        return new UIQuestionsCollection(
            this._items
                .filter(this._affectsPrice)
        );
    };

    /**
     * Returns questions worth being displayed.
     *
     * @method getDisplayable
     * @return {Boolean}
     */
    proto.getDisplayable = function() {
        return this._items
            .filter(this._getDisplayable);
    };

    /**
     * Whether a question should be displayed.
     *
     * @method _getDisplayable
     * @return {Boolean}
     */
    proto._getDisplayable = function(question) {
        return question.isDisplayable();
    };

    /**
     * Whether any of the questions are worth being displayed on their own.
     *
     * @method hasImportant
     * @return {Boolean}
     */
    proto.hasImportant = function() {
        return this._items
            .some(this._hasImportant);
    };

    /**
     * Whether a question is important enough to display on its own.
     *
     * @method _hasImportant
     * @param {App.Models.Ui.Question} item
     * @return {Boolean}
     */
    proto._hasImportant = function(item) {
        return item.isImportant();
    };

    /**
     * Whether a question when changed affects the price
     *
     * @method _affectsPrice
     * @param {App.Models.Ui.Question} item
     * @return {Boolean}
     */
    proto._affectsPrice = function(item) {
        return item.price === true && item.id !== 'logo';
    };

    /**
     * @method isValid
     * @return {Boolean}
     */
    proto.isValid = function() {
        var questions = this._items;
        var length = questions.length;
        var i = 0;

        for (; i < length; i++) {
            if (questions[i].isValid() !== true) {
                return false;
            }
        }

        return true;
    };

    return UIQuestionsCollection;
});
