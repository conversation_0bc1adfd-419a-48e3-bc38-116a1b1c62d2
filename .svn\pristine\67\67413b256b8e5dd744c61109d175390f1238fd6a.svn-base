define(function (require) {
    'use strict';

    var AbstractCollection = require('../../collections/Abstract');
    var UIQuestionBlockLineModel = require('../QuestionBlockLine');
    var inherits = require('mout/lang/inheritPrototype');
    var toLookup = require('mout/array/toLookup');

    /**
     * @class App.Models.Ui.Collections.QuestionBlockLines
     * @extends App.Models.Collections.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} lines
     */
    var UIQuestionBlockLinesCollection = function (lines) {
        AbstractCollection.call(this, lines);
    };

    /**
     * UIQuestionBlocks extends AbstractCollection
     * @type {App.Models.Collections.Abstract}
     */
    var proto = inherits(UIQuestionBlockLinesCollection, AbstractCollection);

    /**
     * @property itemClass
     * @type {App.Models.Ui.QuestionBlockLine}
     */
    proto.itemClass = UIQuestionBlockLineModel;

    /**
     * Recursively sets product info on all steps.
     *
     * @method setInfo
     * @param {Object} block
     * @param {Object} question
     * @param {Object} product
     * @param {Object} fxg
     * @param {Object} customer
     * @chainable
     */
    proto.setInfo = function(block, question, product, fxg, customer) {
        var lineInfo = block && block.line;
        var lineIndex = toLookup(lineInfo, 'id');

        // Set fxg object to the current block, then
        // the children node data correlates to the
        // individual line items in the block.
        fxg = fxg[block.id] || null;

        this.each(this._setInfo.bind(this, lineIndex, block, question, product, fxg, customer));

        return this;
    };

    /**
     * @method _setInfo
     * @param {Object} lineIndex
     * @param {Object} question
     * @param {Object} fxg
     * @param {Object} product
     * @param {App.Models.CustomerProfile} customer
     * @param {App.Models.Ui.QuestionBlockLine} line
     * @callback
     */
    proto._setInfo = function(lineIndex, block, question, product, fxg, customer, line) {
        if(line.id == 'BX') {
            line.setInfo(block.line, block, question, product, fxg, customer);
        } else {
            line.setInfo(lineIndex[line.id], block, question, product, fxg, customer);
        }
        
    };

    /**
     * Returns an object containing all selections made by
     * the user for all contained questions
     *
     * @method getValues
     * @returns {Object}
     */
    proto.getValues = function() {
        var results = [];
        this.each(this._getValue.bind(this, results));
        return results;
    };

    /**
     * Appends a single question's value to an existing object
     *
     * @method _getValue
     * @param {object} results result set to append to
     * @param {App.Models.Ui.Line} line
     * @private
     */
    proto._getValue = function(results, line) {
        var value = [];
        var i = 1;
        var city = '';
        var state = '';
        var zip = '';
        var str = '';
        var cityState = [];

        // BI and CI blocks need special treatment
        // as they have multiple lines within them.
        switch (line.id) {
            case 'BI':
                if(line.sim > 3) {
                    line.sim = 3;
                }
                
                for (; i <= line.sim; i++) {
                    value.push(line.getSimValue(i));
                }

               /* city = line.getValue('4_city');
                state = line.getValue('4_state');
                zip = line.getValue('4_zipCode');

                if (city) {
                    cityState.push(city);
                }

                if (state) {
                    cityState.push(state);
                }

                str = cityState.join(', ') + ' ' + zip;

                value.push(str);*/
                break;

            /*case 'CI':
                city = line.getValue('city');
                state = line.getValue('state');
                zip = line.getValue('zipCode');

                if (city) {
                    cityState.push(city);
                }

                if (state) {
                    cityState.push(state);
                }

                str = cityState.join(', ') + ' ' + zip;

                value.push(str);
                break;*/
            default:
                if (line.sim && line.sim > 1) {
                    for (; i <= line.sim; i++) {
                        value.push(line.getSimValue(i));
                    }
                } else {
                    value.push(line.getValue());
                }

                break;
        }

        results.push({
            id: line.id,
            description: line.desc,
            input: value
        });
    };


    proto.getPrefixs = function() {
        var prefixresults = [];
        this.each(this._getPrefixs.bind(this, prefixresults));
        return prefixresults;
    }

    proto._getPrefixs = function(prefixResults, line) {
        prefixResults.push({
            id:line.blockId+"_"+line.id,
            prefix: line.prefix
        });
    }

    /**
     * @method isValid
     * @return {Boolean}
     */
    proto.isValid = function () {
        var lines = this._items;
        var length = lines.length;
        var i = 0;

        for (; i < length; i++) {
            if (lines[i].isValid() !== true) {
                return false;
            }
        }

        return true;
    };

    return UIQuestionBlockLinesCollection;
});
