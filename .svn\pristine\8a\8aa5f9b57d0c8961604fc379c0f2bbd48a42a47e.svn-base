define(function (require) {
	'use strict';

	var ActionEvents = require('../constants/ActionEvents');
	var AddToCart = require('../providers/AddToCart');
	var Classes = require('../constants/Classes');
	var ConfigurationProvider = require('../providers/Configuration');
	var InkColorsProvider = require('../providers/InkColors');
	var Content = require('i18n!../constants/nls/en-us/Content');
	var Controller = require('./Controller');
	var DomEvents = require('../constants/DomEvents');
	var EventController = require('./Event');
	var ProductEvents = require('../constants/ProductEvents');
	var ProductModel = require('../models/Product');
	var FbtModel = require('../models/Fbt');
	var Settings = require('../constants/Settings');
	var SessionStorage = require('../providers/SessionStorage');
	var StateEvents = require('../constants/StateEvents');
	var TrackEvents = require('../constants/TrackEvents');
	var StateModel = require('../models/State');
	var ZoomEvents = require('../constants/ZoomEvents');
	var bindAll = require('mout/object/bindAll');
	var debounce = require('mout/function/debounce');
	var inherits = require('mout/lang/inheritPrototype');
	var Query = require('models/Query');
	var toLookup = require('mout/array/toLookup');
	var SessionIdProvider = require('../providers/SessionId');
	var ProductInfoProvider = require('../providers/ProductInfo');
	var q = require('q');
	var x = null; //TODO : Can be removed? this var is not used anywhere
	var y = null; //TODO : Can be removed? this var is not used anywhere
    var Surcharge = require('../util/Surcharge');

	//require('simplemodel');
	/**
	 * Determines whether a value is defined and not null. Useful for filtering
	 * useless items out of arrays.
	 *
	 * @type {Function}
	 * @param {Any} value
	 * @return {Boolean}
	 */
	var isDefined = function (value) {
		return value != null;
	};


	var initReorderFlag = true;
	
	/**
	 * @class App.Controllers.Product
	 * @extends App.Controllers.Controller
	 *
	 * @constructor
	 * @param {Object} config
	 */
	function ProductController(config) {
		bindAll(this,
			'initInfo',
			'initModel',
			'initSession',
			'setState',
			'openModal',
			'update',
			'onHideAction',
			'onShowAction',
			'onLogoChange',
			'onVerseOptionChange',
			'onFoldingChange',
			'onSignatureChange',
			'onEnvelopeUpgradeChange',
			'onEnvelopeReturnChange',
			'onNextClick',
			'onPrevClick',
			'onProductStepAction',
			'onQuestionChange',
			'onQuestionRoutingNumChange',
			'onQuestionKeyUp',
			'onStateChange',
			'onSubmitToCart',
			'takeEvasiveAction',
			'saveConfig',
			'shouldEject',
			'shouldKitEject',
			'saveKitConfig',
			'saveKitItems',
			'closeModal',
			'openProofModal',
			'checkProduct',
			'checkRequiredFields',
			'skipProduct',
			'checkProductID',
			'fbtEditProof',
			'fbtAddtoCArt',
			'initColors',
			'setColors',
			'progressClick',
			'onLogoQuestionChange',
			'showLogoLeftSection',
			'hideLogoLeftSection',
			'logoRenew',
			'hidePreviewController',
			'applyLocationUsageSurcharge',
			'locationUsageSurchargeUpdate',
			'isAnyLogoSelected',
			'updateLogoData',
			'onPreviewClick',
			'isBankNameServiceRequired',
			'setBankName',
			'onBankExpandClick',
			'checkBankAddrCity',
			'gotoStep',
			'fbtLogoEdit',
			'prodLogoStateEmit',
			'onSubtotalChange',
			'updateImprintPrice'
		);

		
		/**
		 * @property query
		 * @type {App.Models.Query}
		 */
		this.query = Query.getInstance();

		/**
		 * @property stateModel
		 * @type {Models.State}
		 */
		this.stateModel = new StateModel();

		/**
		 * @property load
		 * @type {Models.State}
		 */

		this.initLoad = true;

		/**
		 * @property productModel
		 * @type {ProductModel}
		 */
		this.productModel = new ProductModel();

		/**
		 * Omniture site prefix
		 *
		 * @type {String}
		 */
		this.site_prefix = Settings.OMNITURE_PREFIX;

		/**
		 * Progressbar steps
		 *
		 * @type {String}
		 */
		this.progress_steps = [];

		/**
		 * currentStep
		 *
		 * @type {Number}
		 */
		this.currentStep = 0;

		/**
		 * Progressbar current state
		 *
		 * @type {String}
		 */
		this.currentState = '';

		

		/**
		 * @method onQuestionKeyUpLazy
		 * @param {jQuery.Event} event
		 * @callback
		 */

		this.onQuestionKeyUpLazy = debounce(this.onQuestionKeyUp, Settings.DEBOUNCE_DELAY);

		Controller.call(this, config);

		/**
		 * @property ready
		 * @type {Promise}
		 */
		this.ready = q
			.when(this.ready)
			.then(this.setState)
			.then(this.openModal)
			.then(this.update)
			.fail(this.onError);
	}

	var proto = inherits(ProductController, Controller);

	/**
	 * @property registry
	 * @type {Object.<String,App.Controllers.Controller>}
	 */
	proto.registry = {
		LogoMixController: require('./LogoMix'),
		LogoBrowseController: require('./LogoBrowse'),
		LogoBrowseRightController: require('./LogoBrowseRight'),
		StepController: require('./Step'),
		SubtotalController: require('./Subtotal'),
		PricingWidgetController: require('./PricingWidget'),
		PreviewController: require('./Preview'),
		MessageController: require('./Message'),
		ProofController: require('./Proof'),
		FbtHybridProofController: require('./FbtHybridProof')
	};

	/**
	 * @method init
	 * @return {Promise}
	 */
	proto.init = function () {
		// ConfigurationProvider
		//           .getConfiguration()
		//           .then(this.initColors)
		//           .then(this.setColors);
		return this.productModel.ready
			.then(this.initModel)
			// .then(this.prodLogoStateEmit)
			.fail(this.onError);
	};

	/**
  	* @method initColors
  	* @return {Promise.<App.Models.Collections.Colors>}
  	*/
	// proto.initColors = function(config) {
	//     return InkColorsProvider
	//         .setConfig(config)
	//         .getColors();
	// };

	/**
	 * @method setColors
	 * @param {App.Models.Collections.Colors} colors
	 * @chainable
	 */
	// proto.setColors = function(colors) {
	//     this.colors = colors;
    // 	   this.model.options.each(this._setColor.bind(this, colors));
	//     return this;
	// };

	/**
	 * @method initModel
	 * @param {App.Models.Product} model
	 * @chainable
	 */
	proto.initModel = function (model) {
		this.model = model;

		this.index = {
			StepController: model.steps,
			PricingWidgetController: model.steps
		};

		// Identify the FBT product
		var query = this.query;
		var rec = query.rec;
		var recSkuId = query.recSkuId;
		var prodId = query.productId;
		var skuId = query.skuId;
		if (typeof rec != 'undefined' && typeof recSkuId != 'undefined') {
			if (FbtModel.currentFbtProductIndex == null) {
				if (rec !== '' && recSkuId !== '' && rec != null) {
					if (!(rec.toString().indexOf(",") > -1)) {
						if (rec === prodId) {
							query.rec = '';
							return this;
						}
					}

					this.$view.hide();
					EventController
						.emit(ActionEvents.FBT_INDEX);
				}
			} else {
				EventController
					.emit(ActionEvents.HIDE_ALL)
			}
		}
		return this;
	};

	/**
	 * @method prodLogoStateEmit
	 * @chainable
	 */
	proto.prodLogoStateEmit = function () {
		// setTimeout(function() {
		EventController.emit(ActionEvents.LOGO_STATE_REP, this.stateModel);
		// }.bind(this), 1000)
		
		return this;
	};
	// -- Accessors ------------------------------------------------------------

	/**
	 * Get all states and child states.
	 *
	 * @method getStates
	 * @return {Array}
	 */
	proto.getStates = function () {

		ProductModel.totalUISteps = this.started
			.reduce(this._getStates, []).length;

		return this.started
			.reduce(this._getStates, [])
			.filter(isDefined);
	};

	/**
	 * @method _getStates
	 * @return {Array}
	 */
	proto._getStates = function (states, component) {
		if (typeof component.getStates !== 'function') {
			return states;
		}
		return states.concat(component.getStates());
	};

	/**
	 * @method setState
	 * @chainable
	 */
	proto.setState = function () {

		var stateModel = this.stateModel;

		this.getStates()
			.forEach(stateModel.add);

		stateModel.start();

		return this;
	};

	/**
	 * @method openModal
	 * @chainable
	 */
	proto.openModal = function () {
		var skus = Content.DEPOSIT_WARNING_SKU;
		var sku = this.model.info && this.model.info.productCode;
		if (skus.indexOf(sku) !== -1) {
			var actionsValue = Content.DEPOSIT_WARNING_ACTIONS;
			if (Settings.SITE_CORP === 'BC Corp') {

				actionsValue = Content.DEPOSIT_WARNING_ACTIONS_4PRINTING;

				// EventController.emit(ActionEvents.MODAL_OPEN, {
				// 	heading: Content.DEPOSIT_WARNING_HEADING,
				// 	content: Content.DEPOSIT_WARNING_MSG,
				// 	actions: Content.DEPOSIT_WARNING_ACTIONS_4PRINTING
				// });

			} else if (Settings.SALES_BRAND === '203') {

				actionsValue = Content.DEPOSIT_WARNING_ACTIONS_DFS;

				// EventController.emit(ActionEvents.MODAL_OPEN, {
				// 	heading: Content.DEPOSIT_WARNING_HEADING,
				// 	content: Content.DEPOSIT_WARNING_MSG,
				// 	actions: Content.DEPOSIT_WARNING_ACTIONS_DFS
				// });

			} //else {
				// EventController.emit(ActionEvents.MODAL_OPEN, {
				// 	heading: Content.DEPOSIT_WARNING_HEADING,
				// 	content: Content.DEPOSIT_WARNING_MSG,
				// 	actions: Content.DEPOSIT_WARNING_ACTIONS
				// });
			// }

			EventController.emit(ActionEvents.MODAL_OPEN, {
				heading: Content.DEPOSIT_WARNING_HEADING,
				content: Content.DEPOSIT_WARNING_MSG,
				actions: actionsValue
			});

		}
		return this;
	};


	/**
	 * @method openModal
	 * @chainable
	 */
	proto.openProofModal = function () {
		var productInfo = this.model.info && this.model.info.productInfo;
		let micr_accNum = false, micr_routingNum = false;
		for (var i = 0; i < productInfo.question.length; i++) {
			if (productInfo.question[i].id == 'accountNumber') {
				micr_accNum = true;
			}
			if (productInfo.question[i].id == 'routingNumber') {
				micr_routingNum = true;
			}
		};
		var loc = $("#mainImg").prop("src");
		var res = loc.replace("fmt=jpg", "fmt=pdf");

		EventController.emit(ActionEvents.PROOF_OPEN, {
			heading: Content.PROOF_MODEL_HEADING,
			msg1: Content.PROOF_MODEL_MSG1,
			msg2: Content.PROOF_MODEL_MSG2,
			msg3: Content.PROOF_MODEL_MSG3,
			loc: loc,
			actions: Content.PROOF_MODEL_ACTIONS,
			isMicr: micr_accNum && micr_routingNum
		});

		return this;
	};

	// -- Methods --------------------------------------------------------------

	/**
	 * @method template
	 * @param {Object} model
	 * @return {String}
	 */
	proto.template = require('hbs!templates/product');

	/**
	 * @method titleTemplate
	 * @param {Object} model
	 * @return {String}
	 */
	proto.titleTemplate = require('hbs!templates/product/title');

	/**
	 * @method progressTemplate
	 * @param {Object} model
	 * @return {String}
	 */
	proto.progressTemplate = require('hbs!templates/product/progress');

	/**
	 * @method navTemplate
	 * @param {Object} model
	 * @return {String}
	 */
	proto.navTemplate = require('hbs!templates/product/nav');

	/**
	 * @method nextTemplate
	 * @param {Object} model
	 * @return {String}
	 */
	proto.nextTemplate = require('hbs!templates/product/next');

	/**
	 * @method fbtnextTemplate
	 * @param {Object} model
	 * @return {String}
	 */
	proto.fbtnextTemplate = require('hbs!templates/product/fbt');

	/**
	 * @method render
	 * @chainable
	 */
	proto.render = function () {
		var model = this.model;
		var lastStep = model.steps.length() - 1;
		var priceStep = model.steps._items[lastStep].id;
		window.desc = model.info.productInfo.desc;
		var pdtIndex = 0;
		var isMobileMenu = window.innerWidth > 768;
		var stepCountFlag = model.steps.length() == 1 ? true : false
		if (typeof FbtModel.fbtProducts != 'undefined') {
			for (var i = 0; i < FbtModel.fbtProducts.length; i++) {
				if (FbtModel.fbtProducts[i].pc === model.info.productInfo.pc)
					pdtIndex = i + 1;
			}
		}
		this.$view
			.html(this.template({
				description: model.info.productInfo.desc,
				steps: model.steps._items,
				brandLogoUrl: Settings.BRAND_LOGO_URL,
				siteCss: Settings.SITE_CSS,
				brandLogo: Settings.BRAND_LOGO,
				uiGroup: model.info.productInfo.group,
				proofSaveCopy: Content[Settings.SITE_CORP + '_PROOF_SAVE'],
				totalPdts: FbtModel.fbtProducts ? (FbtModel.fbtProducts.length > 1 ? FbtModel.fbtProducts.length : '') : '',
				pdtIndex: pdtIndex,
				priceStep: priceStep,
				stepCountFlag: stepCountFlag,
				isMobileMenu: isMobileMenu 
			}));
		if (Settings.APPROVE_REVIEW_FBT === false) {
			setTimeout(function() {
				$('#fbtReviewProofCart').prop('checked', true);
				$('#fbtReviewProofCart').addClass('isChecked');
				$('#fbtReviewProofCart').change();
			}, 0);
		}
		return this.start();
	};

	/**
	 * @method cacheElements
	 * @chainable
	 */
	proto.cacheElements = function () {
		var $view = this.$view;

		this.$title = $view.find(Classes.PRODUCT_TITLE_SELECTOR);
		this.$nav = $view.find(Classes.PRODUCT_NAV_SELECTOR);
		this.$next = $view.find(Classes.PRODUCT_NEXT_SELECTOR);
		this.$fbt = $view.find(Classes.FBT_PRODUCT_NEXT_SELECTOR);

		return this;
	};

	/**
	 * @method attachEvents
	 * @chainable
	 */
	proto.attachEvents = function () {
		this.$view
			.on(DomEvents.CHANGE, this.onQuestionChange)
			.on(DomEvents.KEY_UP, this.onQuestionRoutingNumChange)
			.on(DomEvents.KEY_UP, this.onQuestionKeyUpLazy)
			.on(DomEvents.CLICK, Classes.PREV_SELECTOR, this.onPrevClick)
			.on(DomEvents.CLICK, Classes.NEXT_SELECTOR, this.onNextClick)
			.on(DomEvents.CLICK, Classes.PROOF_SELECTOR, this.openProofModal)
			.on(DomEvents.CLICK, Classes.BACKDROP_SELECTOR, this.closeModal)
			.on(DomEvents.CLICK, Classes.FBT_SKIP_SELECTOR, this.skipProduct)
			.on(DomEvents.CLICK, Classes.FBT_EDIT_PROOF_SELECTOR, this.fbtEditProof)
			.on(DomEvents.CLICK, Classes.FBT_ADDTOCART_SELECTOR, this.fbtAddtoCArt)
			.on(DomEvents.CLICK, Classes.QUESTION_OPTION_SELECTOR, this.onQuestionChange)
			// respChange >>
			.on(DomEvents.CLICK, Classes.PREVIEW_BUTTON_SELECTOR, this.onPreviewClick)
			// respChange <<
			.on(DomEvents.SHOW, Classes.FBT_CONTROLLER, this.fbtTest)
			.on(DomEvents.CLICK, Classes.BUTTON_BANK_INFO_SELECTOR, this.onBankExpandClick);


		this.$title.on(DomEvents.CHANGE, this.progressClick);

		EventController
			.on(ProductEvents.LOGO_CHANGE_SELECTOR, Classes.LOGO_BUTTONS, this.onLogoQuestionChange)
			.on(ActionEvents.HIDE_ALL, this.onHideAction)
			.on(ActionEvents.SHOW_PRODUCT, this.onShowAction)
			.on(ActionEvents.PRODUCT_STEP, this.onProductStepAction)
			.on(ActionEvents.REORDERACTION, this.onReorderAction)
			.on(ProductEvents.LOGO_CHANGE, this.onLogoChange)
			.on(ProductEvents.VERSE_CHANGE, this.onVerseOptionChange)
			.on(ProductEvents.FOLDING_CHANGE, this.onFoldingChange)
			.on(ProductEvents.SIGNATURE_CHANGE, this.onSignatureChange)
			.on(ProductEvents.ENVELOPE_UPGRADE_CHANGE, this.onEnvelopeUpgradeChange)
			.on(ProductEvents.ENVELOPE_RETURN_CHANGE, this.onEnvelopeReturnChange)
			.on(ProductEvents.MISSLE_LOCK, this.onSubmitToCart)
			.on(StateEvents.CHANGE, this.onStateChange)
			.on(ActionEvents.GO_TO_FBT_EDIT_STEP,this.fbtLogoEdit)
			// .on(ProductEvents.FBT_NEXT_FLOW, this.onNewProduct)
			.on(ProductEvents.LOGO_BUTTONS_CLICK, this.onLogoQuestionChange)
			.on(ActionEvents.GO_TO_EDIT_STEP,this.gotoStep)
			.on(ActionEvents.REQUEST_LOGO_STATE_REP,this.prodLogoStateEmit)
			// .on(ProductEvents.SURCHARGE_CHANGE, this.onSurchargeChange);
			.on(ProductEvents.SUBTOTAL_CHANGE, this.onSubtotalChange)
			.on(ProductEvents.QNTY_CHANGE, this.updateImprintPrice)
			;

		return this;
	};


	/**
	 * @method detachEvents
	 * @chainable
	 */
	proto.detachEvents = function () {

		this.$view
			.off(DomEvents.CHANGE, this.onQuestionChange)
			.off(DomEvents.KEY_UP, this.onQuestionRoutingNumChange)
			.off(DomEvents.KEY_UP, this.onQuestionKeyUpLazy)
			.off(DomEvents.CLICK, Classes.PREV_SELECTOR, this.onPrevClick)
			.off(DomEvents.CLICK, Classes.NEXT_SELECTOR, this.onNextClick)
			.off(DomEvents.CLICK, Classes.PROOF_SELECTOR, this.openProofModal)
			.off(DomEvents.CLICK, Classes.BACKDROP_SELECTOR, this.closeModal)
			.off(DomEvents.CLICK, Classes.FBT_SKIP_SELECTOR, this.skipProduct)
			.off(DomEvents.CLICK, Classes.FBT_EDIT_PROOF_SELECTOR, this.fbtEditProof)
			.off(DomEvents.CLICK, Classes.FBT_ADDTOCART_SELECTOR, this.fbtAddtoCArt)
			.off(DomEvents.CLICK, Classes.QUESTION_OPTION_SELECTOR, this.onQuestionChange)
			// respChange >>
			.off(DomEvents.CLICK, Classes.PREVIEW_BUTTON_SELECTOR, this.onPreviewClick)
			// respChange <<
			.off(DomEvents.CLICK, Classes.BUTTON_BANK_INFO_SELECTOR, this.onBankExpandClick)

		this.$title.off(DomEvents.CHANGE, this.progressClick);

		EventController
			.off(ActionEvents.HIDE_ALL, this.onHideAction)
			.off(ActionEvents.SHOW_PRODUCT, this.onShowAction)
			.off(ActionEvents.PRODUCT_STEP, this.onProductStepAction)
			.off(ProductEvents.LOGO_CHANGE, this.onLogoChange)
			.off(ProductEvents.VERSE_CHANGE, this.onVerseOptionChange)
			.off(ProductEvents.FOLDING_CHANGE, this.onFoldingChange)
			.off(ProductEvents.SIGNATURE_CHANGE, this.onSignatureChange)
			.off(ProductEvents.ENVELOPE_UPGRADE_CHANGE, this.onEnvelopeUpgradeChange)
			.off(ProductEvents.ENVELOPE_RETURN_CHANGE, this.onEnvelopeReturnChange)
			.off(ProductEvents.MISSLE_LOCK, this.onSubmitToCart)
			.off(StateEvents.CHANGE, this.onStateChange)
			.off(ActionEvents.GO_TO_FBT_EDIT_STEP,this.fbtLogoEdit)
			// .off(ProductEvents.FBT_NEXT_FLOW, this.onNewProduct)
			.off(ProductEvents.LOGO_BUTTONS_CLICK, this.onLogoQuestionChange)
			.off(ActionEvents.GO_TO_EDIT_STEP,this.gotoStep)
			.off(ActionEvents.REQUEST_LOGO_STATE_REP,this.prodLogoStateEmit)
			// .off(ProductEvents.SURCHARGE_CHANGE, this.onSurchargeChange)
			.off(ProductEvents.SUBTOTAL_CHANGE, this.onSubtotalChange)
			.off(ProductEvents.QNTY_CHANGE, this.updateImprintPrice);
			;

		return this;
	};


	/**
 	* @method progressClick
 	* @return {Boolean}
 	*/
	proto.progressClick = function () {
		var value = this.$title.find('input:checked').val();
		var stateModel = this.stateModel;

		// $(".logopreview-custom").hide();
		
		if (this.progress_steps[value - 1].active == 1) {
			if (value != this.currentState + 1) {
				if ($('[data-controller="PreviewController"]').is(":hidden")) {
					$('[data-controller="LogoBrowseController"]').hide();
					$('[data-controller="LogoBrowseRightController"]').hide();
					$('[class="site-bd logo-site-bd"]').removeClass("logo-site-bd");
					$('[class="grid-col grid-col_preview logo-grid-col_preview"]').removeClass("logo-grid-col_preview");
					$('[data-controller="LogoMixController"]').hide();
					$('[data-controller="LogoMixFilterController"]').hide();
					$('[data-controller="PreviewController"]').show();
				}
				this.checkBankAddrCity();
				var logoIndex = stateModel.states.findIndex(function (obj) { return obj.tlId === "Logo"; });

				if (logoIndex === value - 1) {
					this.showLogoLeftSection()
				} else {
					this.hideLogoLeftSection()
				}

				var logoStatus = SessionStorage.getValue('logo');

				var logoInfoKey = "logo_" + (logoStatus === "" ? "LOGOMX" : logoStatus);
				var logoInfoValue = SessionStorage.getValue(logoInfoKey);

				if (logoInfoValue === null || logoInfoValue === undefined || logoInfoValue.logoUrl === "") {
					EventController.emit(ActionEvents.LOGO_CANCEL, event);
				}

				this.changeInRadioBtn = false;
				this.changeInQtyDropdown = false;
				Settings.VOUCHER_FLAG = false;
				Settings.SIGNATURE_FLAG = false;
				Settings.ANOTHER_SIGNATURE_FLAG = false;
				Settings.ROUTING_FLAG = false;
				Settings.COPIES_FLAG = false;
				Settings.REVERSE_FLAG = false;
				Settings.PRIMARY_COLOR_FLAG = false;
				Settings.SIDEMARGIN_FLAG = false;

				var arrayOfElementsToHideIfVisible = [];
				arrayOfElementsToHideIfVisible.push('#fbt-popup', '#reverse,#Copy', '#reverse,#personalization_link');
				arrayOfElementsToHideIfVisible.push('#reverse,#returnWorks', '#reverse,#foldingWorks', '#reverse,#envelope_link');
				arrayOfElementsToHideIfVisible.push('.sub-pop');
				this.hideIfVisible(arrayOfElementsToHideIfVisible);
	
				var arrayOfElementsToRemoveIfVisible = [];
				arrayOfElementsToRemoveIfVisible.push('#voucher-popup', '#signature-popup', '#sidemargin-popup', '#primary-popup', '#signatureLine-popup');
				this.removeIfVisible(arrayOfElementsToRemoveIfVisible);

				// Removes any Popups that are still present in page
				if ($("div[class*='custpop']").is(':visible')) {
					$('.close.tooltip-close').trigger('click');
				}

				if($('.mobile_info_wraper_container').is(":hidden")){
					$('.mobile_info_wraper_container').removeClass('d-none');
					$('.logo_heading').removeClass('d-none');
				}

				if (value == stateModel.states.length) {
					if($('.site-hd-mobile').is(':visible')){
						$('[data-controller="PreviewController"]').attr("style", "display:flex !important; justify-content: center");
						$('.row.grid.flex-body').attr("style","flex-direction:column !important");
					}
					if (Settings.SITE_CORP != 'BC Corp') {
						$('.online-proof').addClass("make-visible"); // Show proof download btn
					}
					$('.widget-container').addClass("hide-link"); //hide widget expansion link in last step

					$('.clearMargin').hide();
					//Do not show the Approve checkbox for DFS via Settings.APPROVE_REVIEW indicator
					// if (Settings.APPROVE_REVIEW === false &&  !($("#approvalCheckbox").is(":checked")) ) {
					// 	$('#approvalCheckbox').prop('checked', true);
					// 	$('#approvalCheckbox').addClass('isChecked');
					// 	$('#approvalCheckbox').change();
					// 	$('.matte').hide();
					// }
					// steps = toLookup(stateModel.states, 'description');

					var logoType = SessionStorage.getValue('logo');
					var logo = null;
					if (logoType) {
						logo = SessionStorage.getValue('logo_' + logoType);
					}

					if (logo) {
						this.model.logo = logo;
					}

					var detailsHtml = $('[data-controller="StepController"] [data-controller="QuestionController"][data-id="DETAILS"] ');
					if (detailsHtml.length) detailsHtml.hide();

				} else {
					if($('.site-hd-mobile').is(':visible')){
						$('[data-controller="PreviewController"]').attr("style", "display:none");
						$('.row.grid.flex-body').removeAttr("style");
					}
					var detailsHtml = $('[data-controller="StepController"] [data-controller="QuestionController"][data-id="DETAILS"] ');
					if (detailsHtml.length) detailsHtml.show();

					$('.clearMargin').show();
					$('.online-proof').removeClass("make-visible"); // hide proof btn
					$('.widget-container').removeClass("hide-link"); // show widget details expansion link
				}
				if ($('.site-hd-mobile').is(':visible')) {
					$('.closePreview').trigger('click');
				}

				this.stateModel.go(value, true);
			}
		}
	};

	/**
	 * @method isStateValid
	 * @return {Boolean}
	 */
	proto.isStateValid = function () {
		if (typeof this.stateModel.getState() != 'undefined') {
			return this.stateModel
				.getState()
				.controller
				.isValid();
		}
	};

	proto.closeModal = function () {
		$("#backdrop").css("display", "none");
		$(".js_preview_button").addClass("js-include-zoom");
		$(".modal-dialog").hide();
		$(".zoomWindowContainer").hide();
		$(".zoomContainer").hide();
		document.documentElement.style.overflow = 'auto';
		document.body.scroll = "yes";
		document.documentElement.scrollTop = "yes";
	};

	/**
	 * @method update
	 * @chainable
	 */
	proto.update = function () {
		var state = this.stateModel.getState();

		var prevState = this.prevState;
		var $title = this.$title;
		var $nav = this.$nav;
		var $next = this.$next;
		var $fbt = this.$fbt;
		var query = this.query;
		var checkValid = false;
		this.progress_steps = this.stateModel.getStates();
		this.currentState = state.index;
		this.currentStep = state.index + 1;
		var widthval=74*(this.progress_steps.length-1);
		var leftval='48px';
		if(this.progress_steps.length>6){
			widthval=widthval+8;
			if(this.progress_steps.length == 7){
				leftval='68px';
			}else{
				leftval='45px';
			}
		}else if(this.progress_steps.length<6){
			widthval=widthval+3;
			leftval='38px';
		}else{
			leftval='38px';
		}
		widthval=widthval+'px';
		
		var root = document.querySelector(':root');
		root.style.setProperty('--widthvalue', widthval);
		root.style.setProperty('--leftvalue', leftval);



		for (let index = 0; index < this.progress_steps.length; index++) {
			this.progress_steps[index].current = 0;
		}

		//Enable progress bar for edit from cart option.
		if (SessionStorage.getValue('EDIT_CART')) {
			for (var len = 0; len < this.stateModel.getStates().length; len++) {
				//If 'No Logo' is selected, it will return false on validation.
				//To avoid that, change it to true even if it returns false
				if (this.stateModel.states[len].tlId == 'Logo' && this.stateModel.getStates()[len].controller.isValid() == false) {
					checkValid = true;
				} else if (this.stateModel.states[len].tlId == 'FontTextColor' && this.stateModel.getStates()[len].controller.isValid() == false) {
					checkValid = true;
				} else {
					checkValid = this.stateModel.getStates()[len].controller.isValid()
				}
				if (typeof this.stateModel.getStates()[len] != 'undefined') {
					if (!checkValid) {
						this.progress_steps[len].active = 1;
						break;
					} else {
						this.progress_steps[len].active = 1;
					}
				}
			}
		}

		this.progress_steps[this.currentState].active = 1;
		this.progress_steps[this.currentState].current = 1;
		// var progress_steps = this.stateModel.getState();

		var showSkip = false;
		var skipText = null;
		var tlIdPrefix = "";
		if (typeof FbtModel.fbtProducts != 'undefined') {
			tlIdPrefix = 'MC_';
			if (state.next == 'ADD TO CART & <br> SEE NEXT PRODUCT') {
				showSkip = true;
				skipText = 'No thanks.&nbsp;Skip to next product.';
			}
			if (state.next == 'ADD TO CART & <br> SEE SUMMARY') {
				showSkip = true;
				skipText = 'No thanks.&nbsp;Skip to summary.';
			}
		}

		if (!state || !$title || !$nav) {
			return this;
		}

		if (prevState) {
			prevState.controller.hide();
			if (prevState.tlId !== state.tlId && state.tlId != "Logo") {
				if ($('[data-controller="PreviewController"]').is(":hidden")) {
					$('[data-controller="LogoBrowseController"]').hide();
					$('[class="site-bd logo-site-bd"]').removeClass("logo-site-bd");
					$('[class="grid-col grid-col_preview logo-grid-col_preview"]').removeClass("logo-grid-col_preview");
					$('[data-controller="LogoMixController"]').hide();
					var value = state.id;
					if($('.site-hd-mobile').is(':visible') && value == this.stateModel.states.length){
						$('[data-controller="PreviewController"]').attr("style", "display:flex !important;justify-content:center");
						$('.row.grid.flex-body').attr("style","flex-direction:column !important");
					}
				}else {
					if($('.site-hd-mobile').is(':visible') && state.id !== this.stateModel.states.length){
						$('[data-controller="PreviewController"]').attr("style", "display:none");
						$('.row.grid.flex-body').removeAttr("style");
					}
				}
			}

			if (prevState.tlId === "Logo") {
				var logoStatus = SessionStorage.getValue('logo');
				var logoInfoKey = "logo_" + (logoStatus === "" ? "LOGOMX" : logoStatus);
				var logoInfoValue = SessionStorage.getValue(logoInfoKey);

				if (logoInfoValue === null || logoInfoValue === undefined) {
					EventController.emit(ActionEvents.LOGO_CANCEL, event);
				}
			}
		}

		// Show stock flow product
		if (Array.isArray(FbtModel.fbtProducts) && FbtModel.fbtRequiredFields === undefined) {
			var configType = '';
			
			for (var i = 0; i < FbtModel.fbtProducts.length; i++) {
				var item = FbtModel.fbtProducts[i];
				if (this.model.info.productCode === item.pc) {
					configType = item.configType;
					break;
				}
			}

			// Set fbtRequiredFields based on configType
			FbtModel.fbtRequiredFields = configType === 'N' ? true : false;
		}

		var fbtFlow = false
		if(typeof FbtModel.kitSkuId == 'undefined') {
			fbtFlow = true;
		}
		if (!FbtModel.fbtRequiredFields) {
			// reorder non imprinted version of stock flow
			var questionObj = this.model.questions._items.find(function(item) {
				return item.id === "productId";
			});
			
			let isImprintTypeN = false;
			if (questionObj && questionObj.info && Array.isArray(questionObj.info.option)) {
				const optionItem = questionObj.info.option.find(function(item) {
					return item.id === query.skuId;
				});
			
				if (optionItem && optionItem.type === 'N') {
					isImprintTypeN = true;
				}
			}

			if (FbtModel.fbtRequiredFields == undefined && isImprintTypeN) {
				$title.html(this.titleTemplate({
					id: Content.get('FBT_HYBRID_TITLE'),
					description: state.description,
					fbtHybrid: true
				}));

				var isStock = this.model.info ? (this.model.info.productInfo.type === 'N' ? true : false) : false;
				var imprintType = false;
				var questionObj = this.model.questions._items.filter(function (item) {
					return item.id == "productId";
				});

				if (Array.isArray(questionObj) && questionObj.length) {
					imprintType = questionObj[0].info.imprintType;
					if (imprintType) {
						var selectedProduct = this.productModel.info.priceInfo.filter(
							function (product) {
								return product.id == this.productModel.info.productId;
							},
							this
						)[0];
						var unselectedProduct = this.productModel.info.priceInfo.filter(
							function (product) {
								return product.id !== this.productModel.info.productId;
							},
							this
						)[0];
						var selectedQty = this.model.getQuantityValue();
						var imprintPrice =
							parseFloat(
								unselectedProduct.option.filter(function (item) {
									return item.qty == selectedQty;
								}, this)[0].price
							) -
							parseFloat(
								selectedProduct.option.filter(function (item) {
									return item.qty == selectedQty;
								}, this)[0].price
							);
					}
				}

				$fbt.html(this.fbtnextTemplate({
					next: Content.get('FBT Summary'),
					back: imprintType ? "ADD IMPRINT (+$" + imprintPrice.toFixed(2) + ")" : Content.get('FBT_PROOF_EDIT'),
					isStock: isStock,
					fbtFlow: fbtFlow,
					isLast: true,
					tlId: 'AddToCartSummary',
					skipText: true
				}));

				$('.widget-container').addClass("hide-link");
				$('.js-product-next,.clearMargin').hide();
				$('[data-controller="FbtHybridProofController"]').css("display", "block");
			}
			else {
				// Show normal flow
				EventController
					.emit(ActionEvents.HIDE_HYBRID);

				$title.html(this.titleTemplate({
					id: state.id,
					description: state.description
				}));
				$title.html(this.progressTemplate({
					progress_steps: this.progress_steps
				}));

				$nav.html(this.navTemplate({
					tlIdPrefix: tlIdPrefix,
					tlId: state.tlId,
					// prev: state.prev,
					next: state.next
				}));

				$next.html(this.nextTemplate({
					tlIdPrefix: tlIdPrefix,
					tlId: state.tlId,
					next: state.next,
					showSkip: showSkip,
					skipText: Content.get(skipText)
				}));
				state.controller.show();
				$fbt.hide();

				if (this.stateModel.current != this.stateModel.states.length) {
					$('.widget-container').removeClass("hide-link");
					$('.clearMargin').show();
				} else {
					$('.widget-container').addClass("hide-link");
					$('.clearMargin').hide();
				}
				$('.js-product-next').show();
			}


		} else {
			// Mange htbrid proof display
			$title.html(this.titleTemplate({
				id: Content.get('FBT_HYBRID_TITLE'),
				description: FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].desc,
				fbtHybrid: true
			}));

			var isStock = this.model.info ? (this.model.info.productInfo.type === 'N' ? true : false) : false;
			var isLast = (typeof FbtModel.fbtProducts[FbtModel.currentFbtProductIndex + 1] === 'undefined') ? true : false;
			if(typeof FbtModel.kitSkuId != 'undefined') {
				var buttonText = isLast ? Content.get('KIT Summary') : Content.get('KIT Add To Cart');
			} else {
				var buttonText = isLast ? Content.get('FBT Summary') : Content.get('FBT Add To Cart');
			}
			
			var tlId = isLast ? 'AddToCartSummary' : 'AddToCartProduct';
			var imprintType = false;
			var questionObj = this.model.questions._items.filter(function(item){
				return item.id == "productId";
			});

			if (Array.isArray(questionObj) && questionObj.length) {
				imprintType = questionObj[0].info.imprintType;
				if(typeof FbtModel.kitSkuId != 'undefined') {
					imprintType = false;
				}
				if (imprintType) {
					var selectedProductId = this.productModel.info.productId;
					var selectedProduct = this.productModel.info.priceInfo.filter(
						function (product) {
							return product.id == this.productModel.info.productId;
						},
						this
					)[0];
					var unselectedProduct = this.productModel.info.priceInfo.filter(
						function (product) {
							return product.id !== this.productModel.info.productId;
						},
						this
					)[0];
					var unselectedProductId = unselectedProduct.id;
					var selectedQty = this.model.getQuantityValue();
					var imprintPrice =
						parseFloat(
							unselectedProduct.option.filter(function (item) {
								return item.qty == selectedQty;
							}, this)[0].price
						) -
						parseFloat(
							selectedProduct.option.filter(function (item) {
								return item.qty == selectedQty;
							}, this)[0].price
						);
				}
			}

			$fbt.html(this.fbtnextTemplate({
				// good_help: Content.get('FBT_GOOD_HELP'),
				// bad_help: Content.get('FBT_BAD_HELP'),
				next: buttonText,
				back: imprintType ? "ADD IMPRINT (+$"+imprintPrice.toFixed(2)+")":Content.get('FBT_PROOF_EDIT'),
				isStock: isStock,
				fbtFlow: fbtFlow,
				isLast: isLast,
				tlId: tlId
			}));
			// $('.clearMargin').hide(); 
			$('.widget-container').addClass("hide-link");
			$('.js-product-next,.clearMargin').hide();

			if (FbtModel.currentFbtProductIndex === 0 && FbtModel.fbtRequiredFields){
				$('[data-controller="FbtHybridProofController"]').css("display", "block");
			}
		}

		if (this.prevState) {
			EventController.emit(ZoomEvents.RESET, {
				mode: this.model,
				state: state
			});
		}

		this.prevState = state;
		this.updateNext();
		EventController.emit(ProductEvents.CHANGE, this.model);

		if (this.productModel.locationSurcharge.flag) {
			this.locationUsageSurchargeUpdate();
		}
		if (Settings.APPROVE_REVIEW === false && !($("#approvalCheckbox").is(":checked"))) {
			$('#approvalCheckbox').prop('checked', true);
			$('#approvalCheckbox').addClass('isChecked');
			$('#approvalCheckbox').change();
			$('.matte').hide();
		}

		if( this.currentStep === 1 && $('#button_prev').hasClass('button_neutral') ) {
			$('#button_prev').removeClass('button_neutral').addClass('button_grey');
			$('#button_prev').prop('disabled', true);
			$('#button_prev').hide();
			$('.next_button').removeClass('split-50-right');
		} else if (this.currentStep !== 1 && $('#button_prev').hasClass('button_grey')) {
			$('#button_prev').removeClass('button_grey').addClass('button_neutral');
			$('#button_prev').prop('disabled', false);
			$('.next_button').addClass('split-50-right');
			$('#button_prev').show();
		}
		return this;
	};

	/**
	 * @method updateNext
	 * @chainable
	 */
	proto.updateNext = function () {
		this.$view
			.find(Classes.NEXT_SELECTOR)
			.toggleClass(Classes.BUTTON_GREY, this.isStateValid() !== true);

		return this;
	};

	// -- Event Handlers -------------------------------------------------------

	/**
	 * @method onHideAction
	 * @callback
	 */
	proto.onHideAction = function () {
		var stepControllerIndex = this.stateModel.current - 1;
		if (stepControllerIndex === this.index.StepController._items.length) {
			stepControllerIndex = stepControllerIndex - 1;
		}
		var hidePreviewForFBTStatus = this.index.StepController._items[stepControllerIndex] === undefined ? false : (this.index.StepController._items[stepControllerIndex]).tlId === "Logo";
		if (this.state !== undefined && this.state.tlId === "Logo") {
			this.hidePreviewController();
		} else if (this.state === undefined && hidePreviewForFBTStatus) {
			this.hidePreviewController();
		} else {
			this.$view.hide();
		}
	};

	/**
	 * @method onLogoChange
	 * @param {Object} logo
	 * @callback
	 */
	proto.onLogoChange = function (event, logo) {
		if (logo && logo.logoCode) {
			this.productModel.setLogo(logo, false);
		}
		this.productModel.logo = logo;
	};

	/**
	 * @method onVerseChange
	 * @param {jQuery} event
	 * @param {Object} verse
	 * @callback
	 */
	proto.onVerseOptionChange = function (event, verse) {
		this.productModel.verse = verse;
	};

	/**
	 * @method onfoldingChange
	 * @param {jQuery} event
	 * @param {Object} folding
	 * @callback
	 */
	proto.onFoldingChange = function (event, folding) {
		this.productModel.folding = folding;
	};

	/**
	 * @method onsignatureChange
	 * @param {jQuery} event
	 * @param {Object} signature
	 * @callback
	 */
	proto.onSignatureChange = function (event, signature) {
		this.productModel.signature = signature;
	};

	/**
	 * @method onEnvelopeUpgradeChange
	 * @param {jQuery} event
	 * @param {Object} upgradeOption
	 * @callback
	 */
	proto.onEnvelopeUpgradeChange = function (event, upgradeOption) {
		this.productModel.peelAndSeal = upgradeOption;
	};

	/**
	 * @method onEnvelopeReturnChange
	 * @param {jQuery} event
	 * @param {Object} returnValue
	 * @callback
	 */
	proto.onEnvelopeReturnChange = function (event, returnValue) {
		this.productModel.envelopeReturn = returnValue;
		if (!returnValue) {
			SessionStorage.storeValue('ENVELOPE_RETURN', returnValue);
			let personalization_array = ['EI_ML', 'EI_DE', 'EI_AD', 'EI_CI'];
			personalization_array.forEach(function (ele) {
				SessionStorage.storeValue(ele, '')
			});
		}
	};

	/**
	 * @method onShowAction
	 * @callback
	 */
	proto.onShowAction = function (event, advance) {
		this.$view.show();
	};

	/**
	 * @method onProductStepAction
	 * @callback
	 */
	proto.onProductStepAction = function (event, advance) {
		if (advance) {
			this.onNextClick(event);
		}

		// this.$view.show.bind(this.$view)

		// Next tick
		setTimeout(this.$view.show.bind(this.$view), 0);
	};

	/**
	 * @method onNextClick
	 * @param {jQuery.Event} event
	 * @callback
	 */
	proto.onNextClick = function (event) {
		$('.cart-zoom-close').trigger('click');
		if ($('.site-hd-mobile').is(':visible')) {
			$('.closePreview').trigger('click');
		}

		if($('.mobile_info_wraper_container').is(":hidden") && this.model.logo){
			$('.mobile_info_wraper_container').removeClass('d-none');
			$('.logo_heading').removeClass('d-none');
		}

		// $(".logopreview-custom").hide();
	

		Settings.onPreviousClick = false;
		event.preventDefault();
		this.changeInRadioBtn = false;
		this.changeInQtyDropdown = false;
		Settings.VOUCHER_FLAG = false;
		Settings.SIGNATURE_FLAG = false;
		Settings.ANOTHER_SIGNATURE_FLAG = false;
		Settings.ROUTING_FLAG = false;
		Settings.COPIES_FLAG = false;
		Settings.REVERSE_FLAG = false;
		Settings.PRIMARY_COLOR_FLAG = false;
		Settings.SIDEMARGIN_FLAG = false;

		$('#basic-modal-content').modal();
		var steps;
		var stateModel = this.stateModel;
		if (!this.isStateValid()) {
			if (typeof stateModel.getState() != 'undefined') {
				stateModel
					.getState()
					.controller
					.updateErrors();

				return;
			}
		}
		if (stateModel.states && stateModel.current === stateModel.states.length) {
			EventController.emit(ProductEvents.MISSLE_LOCK, this.model);
		} else {
			var arrayOfElementsToHideIfVisible = [];
			arrayOfElementsToHideIfVisible.push('#fbt-popup', '#reverse,#Copy', '#reverse,#personalization_link');
			arrayOfElementsToHideIfVisible.push('#reverse,#returnWorks', '#reverse,#foldingWorks', '#reverse,#envelope_link');
			arrayOfElementsToHideIfVisible.push('.sub-pop');
			this.hideIfVisible(arrayOfElementsToHideIfVisible);

			var arrayOfElementsToRemoveIfVisible = [];
			arrayOfElementsToRemoveIfVisible.push('#voucher-popup', '#signature-popup', '#sidemargin-popup', '#primary-popup', '#signatureLine-popup');
			this.removeIfVisible(arrayOfElementsToRemoveIfVisible);

			// Removes any Popups that are still present in page
			if ($("div[class*='custpop']").is(':visible')) {
				$('.close.tooltip-close').trigger('click');
			}

			var logoIndex = stateModel.states.findIndex(function (obj) { return obj.tlId === "Logo"; });

			if (logoIndex === stateModel.current) {
				this.showLogoLeftSection()
			} else {
				this.hideLogoLeftSection()
			}
			this.checkBankAddrCity();

			// Track the Bank info editing 
			if(stateModel.states[stateModel.current - 1].tlId == 'BankInfo' && typeof _satellite != 'undefined') {
				let bankCode = SessionStorage.getValue('routingNumber');
				let bankName = SessionStorage.getValue('BI_BI_1');
				let satObj = {"BankroutingNumber": bankCode, "BankName": bankName}
				_satellite.track('Edit_Bank_Name', satObj);
			}
			// Second to last step, e.g. Review Proof?
			if (stateModel.states && stateModel.current === stateModel.states.length - 1) {

				if (Settings.SITE_CORP != 'BC Corp') {
					$('.online-proof').addClass("make-visible"); // Show proof download btn
				}
				$('.clearMargin').hide();
				$('.price-widget').addClass("hide-widget");	//hide price widget				
				$('.widget-container').addClass("hide-link");  //hide widget  in last step

				//Do not show the Approve checkbox for DFS via Settings.APPROVE_REVIEW indicator
				// if (Settings.APPROVE_REVIEW === false &&  !($("#approvalCheckbox").is(":checked")) ) {
				// 	console.log('================= 11111 ');
				// 	$('#approvalCheckbox').prop('checked', true);
				// 	$('#approvalCheckbox').addClass('isChecked');
				// 	$('#approvalCheckbox').change();
				// 	$('.matte').hide();
				// }
				steps = toLookup(stateModel.states, 'description');
				// Make sure there is a logo step before we track
				// that the user did not choose a logo.
				/*if ('Logo' in steps && !this.model.logo) { 
					this.track({
						pageName:  this.site_prefix + ': W2P : Logo No Logo',
						eventName: 'event78'
					});
				}
				*/
				// If reloading the app and logo data is in session
				// and when user gets to logo screen and does not
				// change their selection, it does not update the model.
				var logoType = SessionStorage.getValue('logo');
				var logo = null;
				if (logoType) {
					logo = SessionStorage.getValue('logo_' + logoType);
				}
				if (logo) {
					this.model.logo = logo;
				}
				
				var detailsHtml = $('[data-controller="StepController"] [data-controller="QuestionController"][data-id="DETAILS"] ');
				if (detailsHtml.length) detailsHtml.hide();

			} else {

				var detailsHtml = $('[data-controller="StepController"] [data-controller="QuestionController"][data-id="DETAILS"] ');
				if (detailsHtml.length) detailsHtml.show();

				$('.clearMargin').show();
				$('.online-proof').removeClass("make-visible"); // hide proof btn
				$('.widget-container').removeClass("hide-link"); // show widget details  in all steps except last
				if( (stateModel.states && stateModel.current === stateModel.states.length) && $('.site-hd-mobile').is(':visible')){
					$('[data-controller="PreviewController"]').attr("style", "display:block !important");
					$('.row.grid.flex-body').attr("style","flex-direction : column !important");
				}
				
			}

			// Step forward
			EventController.emit(ProductEvents.NEXT_CLICK, this.model);

			//EventController.emit(ActionEvents.MODAL_OPEN);
			stateModel.forward();
		}
	};

	/**
	 * @method onPrevClick
	 * @param {jQuery.Event} event
	 * @callback
	 */
	proto.onPrevClick = function (event) {
		Settings.onPreviousClick = true;
		event.preventDefault();
		Settings.COPIES_FLAG = false;
		Settings.REVERSE_FLAG = false;
		Settings.ROUTING_FLAG = false;
		Settings.VOUCHER_FLAG = false;
		Settings.SIGNATURE_FLAG = false;
		Settings.ANOTHER_SIGNATURE_FLAG = false;
		Settings.PRIMARY_COLOR_FLAG = false;
		Settings.SIDEMARGIN_FLAG = false;

		var stateModel = this.stateModel;

		// hide/show proof download btn
		if (stateModel.states && stateModel.current === stateModel.states.length + 1) {
			$('.online-proof').addClass("make-visible");
		} else {
			$('.online-proof').removeClass("make-visible");
		}

		var logoIndex = stateModel.states.findIndex(function (obj) { return obj.tlId === "Logo"; });
		if (logoIndex+1 === this.currentStep - 1) {
			this.showLogoLeftSection()
		} else {
			this.hideLogoLeftSection()
		}
		var arrayOfElementsToHideIfVisible = [];
		arrayOfElementsToHideIfVisible.push('#fbt-popup', '#reverse,#Copy', '#reverse,#personalization_link');
		arrayOfElementsToHideIfVisible.push('#reverse,#returnWorks', '#reverse,#foldingWorks', '#reverse,#envelope_link');
		this.hideIfVisible(arrayOfElementsToHideIfVisible);

		var arrayOfElementsToRemoveIfVisible = [];
		arrayOfElementsToRemoveIfVisible.push('#voucher-popup', '#signature-popup', '#sidemargin-popup', '#primary-popup', '#signatureLine-popup');
		this.removeIfVisible(arrayOfElementsToRemoveIfVisible);

		// Step back
		EventController.emit(ProductEvents.PREV_CLICK, this.model);
		stateModel.back();
	};

	/**
	 * @method onQuestionChange
	 * @callback
	 */
	proto.onQuestionChange = function (event) {

		this.updateNext();

		if (event.target.name === 'logo' && event.target.value === 'NO LOGO') {
			this.onLogoChange(event, null);
		}

		if (this.productModel.locationSurcharge.flag) {
			this.applyLocationUsageSurcharge(event);
		}

		EventController.emit(ProductEvents.CHANGE, this.model);

		this.updateLogoData();
		// this.model.updateQuantity();
	};

	/**
	 * @method onQuestionRoutingNumChange
	 * @callback
	 */
	proto.onQuestionRoutingNumChange = function (event) {
		
		if(this.isBankNameServiceRequired(event)) {
			var bankRountingNumber = event.target.value;
			this.setBankName(bankRountingNumber);
		}

		this.updateNext();
	}

	/**
	 * @method onQuestionChange
	 * @callback
	 */
	proto.updateLogoData = function () {
		var model = this.model;
		setTimeout(function () {
			EventController.emit(ProductEvents.CHANGE, model);
		}, 10);
	};

	/**
	 * @method onLogoQuestionChange
	 * @callback
	 */
	proto.onLogoQuestionChange = function (event, clickevent) {
		this.updateNext();
		// this is important. not to remove.
		this.onLogoChange(event, clickevent);
		EventController.emit(ProductEvents.CHANGE, this.model);
		if (this.productModel.locationSurcharge.flag) {
			this.locationUsageSurchargeUpdate();
		}
	};

	/**
	 * @method onQuestionKeyUp
	 * @callback
	 */
	proto.onQuestionKeyUp = function () {
		this.updateNext();
	};

	/**
	 * @method onStateChange
	 * @callback
	 */
	proto.onStateChange = function () {
		if (typeof FbtModel.fbtProducts != 'undefined' && Object.keys(FbtModel.fbtProducts).length > 0) {
			if ((Object.keys(FbtModel.fbtProducts).length > 0) && (typeof FbtModel.currentFbtProductIndex != 'undefined')) {
				if (this.model.info && (this.model.info.productCode !== FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].pc)) {
					this.stateModel.states = null;
				}
			}
		}
		if (this.stateModel && typeof this.stateModel.getState() != 'undefined') {
			if (this.stateModel.getState().controller.model.questions._items[0]) {
				// TODO: can be removed following line? There is no use of variable "x" assigned in following line
				x = this.stateModel.getState().controller.model.questions._items[0].value;
			}
			if (this.stateModel.getState().controller.model.questions._items[1]) {
				// TODO: can be removed following line? There is no use of variable "y" assigned in following line
				y = this.stateModel.getState().controller.model.questions._items[1].value;
			}
			this.update();
		}
		this.state = this.stateModel.getState();

		if (this.$view.is(':hidden')) {
			if (!this.initLoad) {
				EventController
					.emit(ActionEvents.HIDE_ALL)
					.emit(ActionEvents.PRODUCT_STEP);
			}
		}
	};

	/**
	 * @method onSubmitToCart
	 * @callback
	 */
	proto.onSubmitToCart = function () {

		var query = this.query;
		var prodId = query.productId;
		var skuId = query.skuId;
		if (typeof BrTrk !== 'undefined') {
			BrTrk.getTracker().logEvent('cart', 'click-add', { 'prod_id': prodId, 'sku': skuId });
		}

		this._disableSubmit();

		if (typeof FbtModel.fbtProducts != 'undefined' && Object.keys(FbtModel.fbtProducts).length > 0) {
			if (Object.keys(FbtModel.fbtProducts).length > 0) {
				if($('.site-hd-mobile').is(':visible')){
					$('[data-controller="PreviewController"]').attr("style", "display:none");
					$('.row.grid.flex-body').removeAttr("style");
				}
				if (typeof FbtModel.currentFbtProductIndex == 'undefined') {
					FbtModel.currentFbtProductIndex = 0;
				}
				if (typeof FbtModel.fbtProducts[FbtModel.currentFbtProductIndex + 1] === 'undefined') {
					if(typeof FbtModel.kitSkuId == 'undefined') {
						EventController
							.emit(ActionEvents.HIDE_ALL)
							.emit(ActionEvents.FBT_SUMMARY);
						var pageName = 'SD:MultiConfig:Customization Summary';
						var event;
						var alreadyAdded = false;
						for (var i = 0; i < FbtModel.currentFbtProductIndex; i++) {
							if (FbtModel.fbtProducts[i].addtocart === true)
								alreadyAdded = true;
						}

						if ((this.query.cartItems && this.query.cartItems > 0) || alreadyAdded) {
							event = 'scAdd';
						} else {
							event = 'scAdd,scOpen';
						}

						if (FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].pc != FbtModel.pdpPdt) {
							event = event + ', event58'
						} else if(this.query.allComps == true) {
							event = event + ', event58';
						}
						EventController.emit(TrackEvents.CHANGE, {
							linkEvents: event + ', event130',
							linkVars: {
								products: ';' + FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].pid,
								pageName: pageName,
								channel: 'MultiConfig',
								prop1: pageName,
								prop2: pageName,
								prop3: pageName,
								prop4: 'MultiConfig'
							}
						});
					}
				} else {
					EventController
						.emit(ActionEvents.HIDE_ALL)
						.emit(ActionEvents.FBT_NEXT);

					var event;
					var alreadyAdded = false;
					for (var i = 0; i < FbtModel.currentFbtProductIndex; i++) {
						if (FbtModel.fbtProducts[i].addtocart === true)
							alreadyAdded = true;
					}
					if ((this.query.cartItems && this.query.cartItems > 0) || alreadyAdded) {
						event = 'scAdd';
					} else {
						event = 'scAdd,scOpen';
					}
					if (FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].pc != FbtModel.pdpPdt) {
						event = event + ', event58';
					} else if(this.query.allComps == true) {
						event = event + ', event58';
					}
					var pageName = 'SD:MultiConfig:Generating Proof';

					EventController.emit(TrackEvents.CHANGE, {
						linkEvents: event,
						linkVars: {
							products: ';' + FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].pid,
							pageName: pageName,
							channel: 'MultiConfig',
							prop1: pageName,
							prop2: pageName,
							prop3: pageName,
							prop4: 'MultiConfig'
						}
					});

				}
				this.ga4AddtoCart(this.model)
			}
		}
		 // For reorder with single companion product
		 let recStatus = typeof this.query.recSkuId == 'undefined' || this.query.recSkuId == '' ? true : false
		 if(this.query.allComps == true && recStatus) {
			var pageName = 'SD:MultiConfig:Generating Proof';
			EventController.emit(TrackEvents.CHANGE, {
				linkEvents: 'scAdd,event58',
				linkVars: {
					products: ';' + prodId,
					pageName: pageName,
					channel: 'MultiConfig',
					prop1: pageName,
					prop2: pageName,
					prop3: pageName,
					prop4: 'MultiConfig'
				}
			});

			this.ga4AddtoCart(this.model)

		 }
		// let qtyModel = this.model.questions._items.filter(checkQuantityQuestion);
		// let qty = qtyModel[0].options._items.map(mapQuantity);
		// if(!qty.includes(qtyModel[0].value)) {
		// 	qtyModel[0].value = qty[0];			
		// }
		
		// function checkQuantityQuestion(item) {
		// 	return item.id == 'quantity'
		// }
		// function mapQuantity(item) {
		// 	return item.id;
		// }
		if(typeof FbtModel.kitSkuId != 'undefined') {
			ConfigurationProvider
				.getConfiguration()
				.then(this.saveKitConfig)
				.then(this.saveKitItems)
				.then(this.shouldKitEject);

		} else if (Settings.SVC_ADDTOCART_FLAG != 3) {
			ConfigurationProvider
				.getConfiguration()
				.then(this.takeEvasiveAction)
				.then(this.shouldEject);
		} else {
			ConfigurationProvider
				.getConfiguration()
				.then(this.saveConfig)
				.then(this.takeEvasiveAction)
				.then(this.shouldEject);
		}

	};

	/**
	 * Add to cart tagging using GA4
	 * @param {*} model 
	 */
	proto.ga4AddtoCart = function(model) {
		if (typeof dataLayer != "undefined") {
			let qtyModel = model.questions._items.filter(checkQuantityQuestion);
			let qty;
			if(typeof qtyModel[0] == 'undefined') {
				 qty = model.query.qty;
			} else {
				 qty = qtyModel[0].value
			}
			function checkQuantityQuestion(item) {
				return item.id == 'quantity'
			}

			let ga4Obj = this.createGa4Obj(model, qty)
			dataLayer.push(ga4Obj)
		}
	}

	/**
	 * Create the GA4 Obj
	 * @param {*} model 
	 * @param {*} qtyModel 
	 * @returns 
	 */
	proto.createGa4Obj = function(model, qty) {
		let ga4Obj = {
			event: "add_to_cart",
			ecommerce: {
				items: [
					{
					item_id: model.info.productId,
					item_name: model.info.productInfo.desc,
					affiliation: "Shop Deluxe",	
					coupon: null,
					currency: "USD",
					discount: 0,
					index: 0,
					item_brand: "Deluxe",
					item_category: "",
					location_id: "",
					item_list_id: "",
					item_list_name: "",
					item_variant: model.info.productId,								
					price: model.totalPrice,								
					quantity:qty
					},
				],
			},
		};
		return ga4Obj;
	}
	/**
	 * First save config to call add to cart
	 * @method saveConfig
	 * @return {Promise}
	 */
	proto.saveConfig = function (config) {
		return AddToCart
			.setConfig(config)
			.saveConfig(this.model);
	};

	/**
	 * Save individual kit items
	 * @method saveConfig
	 * @return {Promise}
	 */
	proto.saveKitItems = function (config) {
		return AddToCart
			.setConfig(config)
			.saveKitItems(this.model);
	};

		/**
	 * Save individual kit items
	 * @method saveConfig
	 * @return {Promise}
	 */
	proto.saveKitConfig = function (config) {
		return AddToCart
			.setConfig(config)
			.saveKitConfig(this.model);
	};

	/**
	 * @method takeEvasiveAction
	 * @return {Promise}
	 */
	proto.takeEvasiveAction = function (config) {
		return AddToCart
			.setConfig(config)
			.query(this.model);
	};



	/**
	 * @method shouldKitEject
	 * @param {Boolean} addToCart
	 * @chainable
	 */
	proto.shouldKitEject = function (addToCart) {

		if (typeof FbtModel.fbtProducts != 'undefined' && Object.keys(FbtModel.fbtProducts).length > 0) {
			var addToCartStatus = true;
			FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].addtocart = addToCartStatus;
			FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].status = addToCartStatus;
			if (typeof FbtModel.fbtProducts[FbtModel.currentFbtProductIndex + 1] === 'undefined') {
				var query = this.query;
				var productId = query.productId;

				var params = '';
				if (Settings.SVC_ADDTOCART_REDIRECT.indexOf('?') == -1) {
					params = '?';
				}
				if (Settings.SVC_ADDTOCART_REDIRECT_PRODID) {
					params += '&prodId=' + productId;
				}
				if (Settings.SVC_ADDTOCART_REDIRECT_PAGENAME) {
					params += '&pageName=' + Settings.SVC_ADDTOCART_REDIRECT_PAGENAME;
				}

				if (Settings.SVC_ADDTOCART_FLAG == 1) {
					if (addToCart === true && productId) {
						window.location.href = Settings.SVC_ADDTOCART_REDIRECT + params;
					} else {
						this._enableSubmit();
					}
				} else if (Settings.SVC_ADDTOCART_FLAG == 2 || Settings.SVC_ADDTOCART_FLAG == 3) {
					if (addToCart.status === true && productId && addToCart.uid) {
						window.location.href = Settings.SVC_ADDTOCART_REDIRECT + params + '&configId=' + addToCart.uid;
					} else {
						this._enableSubmit();
					}
				}

				return this;
			} else {
				this.checkProductID(addToCartStatus);
			}
		}
	}

	/**
	 * @method shouldEject
	 * @param {Boolean} addToCart
	 * @chainable
	 */
	proto.shouldEject = function (addToCart) {

		if (typeof FbtModel.fbtProducts != 'undefined' && Object.keys(FbtModel.fbtProducts).length > 0) {
			var addToCartStatus = true;
			FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].addtocart = addToCartStatus;
			FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].status = addToCartStatus;
			if (typeof FbtModel.fbtProducts[FbtModel.currentFbtProductIndex + 1] === 'undefined') {
				EventController
					.emit(ActionEvents.HIDE_ALL)
					.emit(ActionEvents.FBT_SUMMARY);
			} else {
				this.checkProductID(addToCartStatus);
			}
		}
		else {

			var query = this.query;
			var productId = query.productId;

			var params = '';
			if (Settings.SVC_ADDTOCART_REDIRECT.indexOf('?') == -1) {
				params = '?';
			}
			if (Settings.SVC_ADDTOCART_REDIRECT_PRODID) {
				params += '&prodId=' + productId;
			}
			if (Settings.SVC_ADDTOCART_REDIRECT_PAGENAME) {
				params += '&pageName=' + Settings.SVC_ADDTOCART_REDIRECT_PAGENAME;
			}

			if (Settings.SVC_ADDTOCART_FLAG == 1) {
				if (addToCart === true && productId) {
					window.location.href = Settings.SVC_ADDTOCART_REDIRECT + params;
				} else {
					this._enableSubmit();
				}
			} else if (Settings.SVC_ADDTOCART_FLAG == 2 || Settings.SVC_ADDTOCART_FLAG == 3) {
				if (addToCart.status === true && productId && addToCart.uid) {
					window.location.href = Settings.SVC_ADDTOCART_REDIRECT + params + '&configId=' + addToCart.uid;
				} else {
					this._enableSubmit();
				}
			}

			return this;
		}
	};

	/**
	 * Find the next product in FBT flow
	 *
	 * @method onProductChange
	 * @param {jQuery.Event} event
	 * @callback
	 */
	proto.checkProductID = function (status) {
		// First product
		if (FbtModel.currentFbtProductIndex == null) {
			FbtModel.currentFbtProductIndex = 0;
		}
		FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].status = 1;
		FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].addtocart = status;
		// console.log(FbtModel.fbtProducts[FbtModel.currentFbtProductIndex]);
		// Check not last product and load next product
		if (typeof FbtModel.fbtProducts[FbtModel.currentFbtProductIndex + 1] != 'undefined') {
			FbtModel.currentFbtProductIndex = FbtModel.currentFbtProductIndex + 1;
			FbtModel.resetStep = true;
			this.checkProduct();
		} else {
			// Last Product new view?
			EventController
				.emit(ActionEvents.HIDE_ALL)
				.emit(ActionEvents.FBT_SUMMARY);

			window.location.href = window.location.href + '?pscid=SD:Multi_Config-No_Thanks_Skip_For_Now_&_See_Summary';
			var pageName = 'SD:MultiConfig:Customization Summary';

			EventController.emit(TrackEvents.CHANGE, {
				linkEvents: 'event130',
				linkVars: {
					pageName: pageName,
					channel: 'MultiConfig',
					prop1: pageName,
					prop2: pageName,
					prop3: pageName,
					prop4: 'MultiConfig'
				}
			});
		}
		return this;
	}

	/**
	 * Get the next productinfo of fbt flow
	 *
	 * @method onProductChange
	 * @param {jQuery.Event} event
	 * @callback
	 */
	proto.checkProduct = function () {
		var query = this.query;
		var productInfoParams = {
			productId: FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].pc,
			skuId: FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].pid,
			fulfillmentId: query.fulfillmentId,
			orderId: query.orderId,
			atgOrderId: query.atgOrderId,
			qty: query.qty
		};
		var info = ProductInfoProvider
			.getProductInfo(productInfoParams)
			.then(this.checkRequiredFields);
	};

	/**
	 * Checking all the required product
	 *
	 * @method onProductChange
	 * @param {jQuery.Event} event
	 * @callback
	 */
	proto.checkRequiredFields = function (info) {
		ProductModel.fbtNextProdInfo = info;
		var productInfo = info && info.productInfo;
		var type = FbtModel.types.getById(productInfo.group);
		var steps = type.steps;
		var questions = steps.getQuestions();
		var questionsItem = questions._items;
		var requireFields = {};
		var NavFlag = false;
		FbtModel.fbtHybridProof = [];
		FbtModel.fbtHybridProof.numbering_flag = false;

		for (var i = 0; i < productInfo.question.length; i++) {
			if (productInfo.question[i].id == 'numbering') {
				FbtModel.fbtHybridProof.numbering_flag = true;
			}
		};

		for (var i = 0; i < questionsItem.length; i++) {

			// For Hybrid proof step
			if (questionsItem[i].id == 'quantity') {
				FbtModel.fbtHybridProof.quantity = questionsItem[i];
			}
			if (questionsItem[i].id == 'productId') {
				FbtModel.fbtHybridProof.productId = questionsItem[i];
			}
			if (questionsItem[i].id == 'logo') {
				FbtModel.fbtHybridProof.logo = questionsItem[i];
			}
			if (questionsItem[i].id == 'accountNumber') {
				FbtModel.fbtHybridProof.accountNumber = questionsItem[i];
			}
			if (questionsItem[i].id == 'routingNumber') {
				FbtModel.fbtHybridProof.routingNumber = questionsItem[i];
			}
			if (questionsItem[i].id == 'numbering') {
				FbtModel.fbtHybridProof.numbering = questionsItem[i];
				FbtModel.fbtHybridProof.numbering.value = null;
				if (SessionStorage.getValue(questionsItem[i].id) === undefined && questionsItem[i].isRequired) {
					NavFlag = true;
				}
			}
			if(questionsItem[i].id == "inkColor1"){
                if(SessionStorage.getValue(questionsItem[i].id) == undefined){
                    if(!Settings.PERSIST_IMPRINT && typeof FbtModel.fbtProducts != 'undefined'){
                        var defaultInkColor1 = this.model.info.productInfo.question.filter(function(item){return item.id === "inkColor1";})[0].default;
                        SessionStorage.storeValue('inkColor1',defaultInkColor1);
                    }
                }
            }

			// For stamper VIP seat        	
			if (questionsItem[i].id == 'layout') {
				if (questionsItem[i].blocks._items.length > 0) {
					for (var x = 0; x < questionsItem[i].blocks._items.length; x++) {
						if (questionsItem[i].blocks._items[x].id == 'CI') {
							FbtModel.fbtHybridProof.stamp_account = questionsItem[i].blocks._items[x];
						}
						if (questionsItem[i].blocks._items[x].id == 'BI') {
							FbtModel.fbtHybridProof.stamp_rout = questionsItem[i].blocks._items[x];
						}
					};
				}
			}

			if (questionsItem[i].isRequired == true && questionsItem[i].id != 'addToCart' && questionsItem[i].id != 'numbering') {
				if (typeof questionsItem[i].blocks._items != 'undefined' && questionsItem[i].blocks._items.length > 0) {
					if ($.inArray(productInfo.pc, Settings.CUSTOM_VALIDATION_PRODUCTS) > -1) {
						for (var k = 0; k < Settings.CUSTOM_VALIDATION_LINES.length; k++) {
							if (SessionStorage.getValue(Settings.CUSTOM_VALIDATION_BLOCKS + '_' + Settings.CUSTOM_VALIDATION_LINES[k]) == undefined) {
								NavFlag = true;
								break;
							}
						}
					}

					for (var j = 0; j < questionsItem[i].blocks._items.length; j++) {
						if (questionsItem[i].blocks._items[j].isRequired == true) {
							if (questionsItem[i].blocks._items[j].id == 'BI') {
								var biLimit = 5;
								var biFlag = false;
								for (var k = 0; k < biLimit; k++) {
									if (SessionStorage.getValue('BI_BI_' + k) != undefined) {
										biFlag = true;
										requireFields[questionsItem[i].blocks._items[j].id] = SessionStorage.getValue('BI_BI_' + k);
										break;
									}
								}
								if (!biFlag) {
									NavFlag = true;
								}
							} else {
								requireFields[questionsItem[i].blocks._items[j].id] = SessionStorage.getValue(questionsItem[i].blocks._items[j].id);
								if (SessionStorage.getValue(questionsItem[i].blocks._items[j].id) === undefined) {
									NavFlag = true;
								}
							}
						}
					}

				} else {
					requireFields[questionsItem[i].id] = SessionStorage.getValue(questionsItem[i].id);
					if (SessionStorage.getValue(questionsItem[i].id) === undefined) {
						NavFlag = true;
					}
				}

			}
		}

		if (NavFlag) {
			FbtModel.fbtRequiredFields = false;
		} else {
			FbtModel.fbtRequiredFields = true;
			var pageName = 'SD:MultiConfig:Multi Review Proof' + FbtModel.currentFbtProductIndex;
			var event = null;
			let prodId = info.productId
			if (FbtModel.currentFbtProductIndex == 1)
				event = 'event131';
			if (FbtModel.currentFbtProductIndex == 2)
				event = 'event132';
			if (FbtModel.currentFbtProductIndex == 3)
				event = 'event133';
			EventController.emit(TrackEvents.CHANGE, {
				linkEvents: event,
				linkVars: {
					products: ';' + prodId,
					pageName: pageName,
					channel: 'MultiConfig',
					prop1: pageName,
					prop2: pageName,
					prop3: pageName,
					prop4: 'MultiConfig'
				}
			});
		}

		EventController
			.emit(ProductEvents.FBT_NEXT_FLOW);
	};

	/**
	 * @method _disableSubmit
	 * @chainable
	 */
	proto._disableSubmit = function () {
		this.$view.find(Classes.NEXT_SELECTOR)
			.addClass(Classes.BUTTON_GREY)
			.off(DomEvents.CLICK, Classes.NEXT_SELECTOR, this.onNextClick)
		EventController.off(ProductEvents.MISSLE_LOCK);

		return this;
	};

	/**
	 * @method _enableSubmit
	 * @chainable
	 */
	proto._enableSubmit = function () {
		this.$view.find(Classes.NEXT_SELECTOR)
			.removeClass(Classes.BUTTON_GREY)
			.on(DomEvents.CLICK, Classes.NEXT_SELECTOR, this.onNextClick);

		EventController.on(ProductEvents.MISSLE_LOCK, this.onSubmitToCart);
	};

	/**
	 * @method skipProduct
	 * @chainable
	 */
	proto.skipProduct = function () {
		$('#FbtCopiesHelp').popover('hide');
		this._disableSubmit();
		var addToCartStatus = false;
		if (typeof FbtModel.fbtProducts != 'undefined' && Object.keys(FbtModel.fbtProducts).length > 0) {
			if (Object.keys(FbtModel.fbtProducts).length > 0) {
				EventController
					.emit(ActionEvents.HIDE_ALL)
					.emit(ActionEvents.FBT_NEXT);
			}
			if (FbtModel.currentFbtProductIndex !== (Object.keys(FbtModel.fbtProducts).length - 1))
				window.location.href = window.location.href + '?pscid=SD:Multi_Config-No_Thanks_Skip_to_Next_Product';
			var event = null;

			var pageName = 'SD:MultiConfig:Generating Proof';

			EventController.emit(TrackEvents.CHANGE, {
				linkEvents: event,
				linkVars: {
					products: ';' + FbtModel.fbtProducts[FbtModel.currentFbtProductIndex].pid,
					pageName: pageName,
					channel: 'MultiConfig',
					prop1: pageName,
					prop2: pageName,
					prop3: pageName,
					prop4: 'MultiConfig'
				}
			});

			if (typeof FbtModel.fbtProducts != 'undefined' && Object.keys(FbtModel.fbtProducts).length > 0) {
				var isLast = (typeof FbtModel.fbtProducts[FbtModel.currentFbtProductIndex + 1] === 'undefined') ? true : false;
				if (isLast) {
					window.onbeforeunload = null;
				}
			}
		}
		$('html, body').animate({ scrollTop: 0 }, 'fast');

		this.checkProductID(addToCartStatus);
	};



	/**
	 * @method fbtEditProof
	 * @chainable
	 */
	proto.fbtEditProof = function () {
		Settings.COPIES_FLAG = false;

		if($('.site-hd-mobile').is(':visible')){
			$('[data-controller="PreviewController"]').attr("style", "display:none");
			$('.row.grid.flex-body').removeAttr("style");
		}		

		if ($('.hyb-copy').is(':visible')) {
			$('.hyb-copy').hide();
		}

		$('html, body').animate({ scrollTop: 0 }, 'fast');
		FbtModel.fbtRequiredFields = false;
		this.update();
		EventController.emit(StateEvents.CHANGE, this.state);
		window.location.href = window.location.href + '?pscid=SD:Multi_Config-Go_to_Editor';
		if(this.model.questions._items.filter(function(item){
				return item.id == "productId";
			})[0].info.imprintType) {
				var dropdownModel = this.model.questions._items.filter(function(item){
					return item.id == "productId";
				})[0];
				var imprintValue = dropdownModel.info.option.filter(function(item){return item.id !== dropdownModel.value;})[0].id;
				dropdownModel.setValue(imprintValue);
		}
		// var step = this.productModel.info.productInfo.group;
		//EventController.emit(TrackEvents.TRACK_TEALEAF, {
		//	name: 'MPC-#step/1',
		//	step: step
		//});
		EventController.emit(ActionEvents.RELOAD_ADDTOCART_REVIEW_TEMPLATE);
	};

	/**
	 * @method fbtAddtoCArt
	 * @chainable
	 */
	proto.fbtAddtoCArt = function () {
		EventController.emit(ProductEvents.MISSLE_LOCK, this.model);
	};

	/**
	 * @method onReorderAction
	 * @param {Object} logo
	 * @callback
	 */
	proto.onReorderAction = function (event) {
		initReorderFlag = false;
	};


	proto.showLogoLeftSection = function () {
		var sessionLogoStatus = SessionStorage.getValue('logo');
		var logoStatus = sessionLogoStatus;
		if (this.model.logo) {
			logoStatus = sessionLogoStatus || this.model.logo.logoType;
		}
		if (logoStatus !== "NO LOGO" && !(this.query.fulfillmentId || this.query.orderId)) {
			if (Array.from(this.model.info.productInfo.question[this.model.info.productInfo.question.findIndex(function (item) { if (item.id === "logo") { return item; } })].logoButtonOption
			).find(function (item) { if (item.id === logoStatus) { return item; } }) === undefined) {

				if(this.query.orderId && logoStatus != 'LOGOMX') {
					EventController.emit(ActionEvents.LOGO_CANCEL, event);
					logoStatus = "NO LOGO";
				} 
			}
		}

		if ((($('[data-controller="LogoBrowseRightController"]').is(":hidden") || $('[data-controller="LogoMixFilterController"]').is(":hidden")) && logoStatus !== "NO LOGO" )  ) {
			if (logoStatus !== "CUSTOM" && logoStatus !== "LOGOMX" && (logoStatus === "STANDARD" && SessionStorage.getValue('logo_STANDARD')) ) {
				$('[data-controller="PreviewController"]').hide();
			}
			$('[class="site-bd"]').addClass("logo-site-bd");
			$('[class="grid-col grid-col_preview"]').addClass("logo-grid-col_preview");
			if (logoStatus === "STANDARD" && SessionStorage.getValue('logo_STANDARD')) {
				this.hideLogoLeftSection();
				$("#reorder-wraper").removeClass('d-none');
				$(".logopreview-custom").hide();
				// $('[data-controller="LogoBrowseRightController"]').show();
				// $('[data-controller="LogoBrowseController"]').show();
			} else if (logoStatus === "CUSTOM") {
				$("#reorder-wraper").removeClass('d-none');
			} else if (logoStatus === "LOGOMX") {
				$("#reorder-wraper").removeClass('d-none');
			} else if (logoStatus === "") {
				$('[data-controller="LogoMixFilterController"]').show();
				$('[data-controller="LogoMixController"]').show();
			}
			
			$(".logoButtonOptions").hide();
		}
		this.logoRenew();
		if ((this.query.fulfillmentId || this.query.orderId) && logoStatus != 'NO LOGO') {
			this.hideLogoLeftSection();
			$("#reorder-wraper").removeClass('d-none');
			$(".logopreview-custom").hide();
		}
	}

	proto.hideLogoLeftSection = function () {
		$('[data-controller="LogoBrowseRightController"]').hide();
		$('[data-controller="LogoBrowseController"]').hide();
		$('[data-controller="LogoMixController"]').hide();
		$('[data-controller="LogoMixFilterController"]').hide();
		$('[class="site-bd logo-site-bd"]').removeClass("logo-site-bd");
		$('[class="grid-col grid-col_preview logo-grid-col_preview"]').removeClass("logo-grid-col_preview");
		// $('[data-controller="PreviewController"]').show();
		$('[data-controller="PreviewController"]').attr("style", "content-visibility:visible");
		$(".logoButtonOptions").hide();
	}

	proto.logoRenew = function (logo) {
		var sessionLogoType = SessionStorage.getValue('logo');
		var logoType = sessionLogoType;
		if (!sessionLogoType && this.model.logo) {
			logoType = this.model.logo.logoType
		}
		if (logoType === 'STANDARD' || logoType === 'LOGOMX') {
			if (this.productModel.logo) {
				EventController
					.emit(ActionEvents.LOGO_RENEW, this.productModel.logo)
			} else {
				EventController
					.emit(ActionEvents.LOGO_RENEW)
			}
		}
	}

	/**
	 * @method hidePreviewController
	 */
	proto.hidePreviewController = function () {
		// $('[data-controller="PreviewController"]').hide();
		$('[data-controller="PreviewController"]').attr("style", "content-visibility:hidden");
		$('[class="site-bd"]').addClass("logo-site-bd");
		$('[class="grid-col grid-col_preview"]').addClass("logo-grid-col_preview");
	}

	proto.isAnyLogoSelected = function () {
		var response = false;
		var selectedLogo = SessionStorage.getValue('logo');
		if (selectedLogo === 'STANDARD' || selectedLogo === 'CUSTOM' || selectedLogo === 'LOGOMX') {
			response = true;
		}
		return response;
	}

	proto.applyLocationUsageSurcharge = function (event) {

		var locationLogo = this.isAnyLogoSelected();
		var locationText = false;
		var applyCharges = false;

		if (this.productModel.locationSurcharge.questionIds.includes(event.target.name)) {
			var questionIds = this.productModel.locationSurcharge.questionIds;
			for (var i = 0; i < questionIds.length; i++) {
				var qId = questionIds[i];
				if ($('#' + qId)[0].value.trim()) {
					locationText = true;
				}
			}

			if (locationLogo || locationText) {
				applyCharges = true;
			}

			this.productModel.locationSurcharge.applicable = applyCharges;
		}
	}

	proto.locationUsageSurchargeUpdate = function () {

		var locationLogo = this.isAnyLogoSelected();
		var locationText = false;
		var applyCharges = false;

		var productQuestions = this.productModel.info.productInfo.question;
		if (productQuestions.length) {
			for (var i = 0; i < productQuestions.length; i++) {
				if (productQuestions[i].id === 'layout') {
					if (productQuestions[i].option.location.block.length) {
						if (productQuestions[i].option.location.block[0].lines.length) {
							var lines = productQuestions[i].option.location.block[0].lines;
							for (var j = 0; j < lines.length; j++) {
								if (lines[j].input[0]) {
									locationText = true;
								}
							}
						}
					}
				}
			}
		}

		if (locationLogo || locationText) {
			applyCharges = true;
		}

		this.productModel.locationSurcharge.applicable = applyCharges;
		EventController.emit(ProductEvents.CHANGE, this.model);

	}

	// respChange >>
	/**
	 * @method onPreviewClick
	 * @chainable
	 */
	proto.onPreviewClick = function () {
		if (this.stateModel.states && this.stateModel.current === this.stateModel.states.length) {
			if ($(".previewMobile").hasClass("closePreview")) {
				$(".online-proof").addClass("make-visible");
			} else {
				$(".online-proof").removeClass("make-visible");
			}
		}
		if ($("div[class*='custpop']").is(':visible')) {
			$('.close.tooltip-close').trigger('click');
		}
		if ($(".previewMobile").hasClass("closePreview")) {
			$('[data-controller="PreviewController"]').attr("style", "display:none");
			if ($('[data-controller="FbtHybridProofController"]').hasClass("d-none")) {
				$('[data-controller="FbtHybridProofController"]').removeClass("d-none");
			} else {
				$('[data-controller="StepController"]').removeClass("d-none");
				if ($('[data-controller="LogoBrowseController"]').hasClass('d-none')) {
					$('[data-controller="LogoBrowseController"]').removeClass('d-none');
					$('[data-controller="LogoBrowseRightController"]').removeClass('d-none');
				} else if ($('[data-controller="LogoMixController"]').hasClass('d-none')) {
					$('[data-controller="LogoMixController"]').removeClass('d-none');
					$('[data-controller="LogoMixFilterController"]').removeClass('d-none');
				}
			}
			$(".previewMobile").removeClass("closePreview").html('</br>Preview');
		} else {
			$('[data-controller="PreviewController"]').attr("style", "display:block !important");
			if ($('[data-controller="FbtHybridProofController"]').css('display') == "block") {
				$('[data-controller="FbtHybridProofController"]').addClass("d-none");
			} else {
				$('[data-controller="StepController"]').addClass("d-none");
				if ($('[data-controller="LogoBrowseController"]').css('display') == "block") {
					$('[data-controller="LogoBrowseController"]').addClass('d-none');
					$('[data-controller="LogoBrowseRightController"]').addClass('d-none');
				} else if ($('[data-controller="LogoMixController"]').css('display') == "block") {
					$('[data-controller="LogoMixController"]').addClass('d-none');
					$('[data-controller="LogoMixFilterController"]').addClass('d-none');
				}
			}
			$(".previewMobile").addClass("closePreview").html('</br>Close');
		}
	};
	// respChange <<

	proto.isBankNameServiceRequired = function(event) {
		return ( ( ( event.target.name === 'routingNumber' || event.target.name ===  'BI_NO' )  && event.target.value.length === 9) && Settings.BANK_INFO_SERVICE_USE  ); 
	}

	proto.setBankName = function(bankCode) {
		$('.bank-name-loader').css('display', 'inline-block');
		$.ajax({
			url: Settings.BANK_INFO_SERVICE_URL + bankCode ,
			type: 'GET',
			cache: false,
		    dataType: 'text',
			success: function(data) {
				var jsondata = $.xml2json(data);
				var bankName = "";
				if( jsondata.hasOwnProperty('BankInfo') && jsondata.BankInfo.BankCode === bankCode) {
					if(Array.isArray(jsondata.BankInfo.BankLines.Line)) {
						bankName = jsondata.BankInfo.BankLines.Line[0];
					} else {
						bankName = jsondata.BankInfo.BankLines.Line;
					}
				}
				$('#BI_BI_1')[0].value = bankName;
				$('#BI_BI_1').change();	

				if(typeof _satellite != 'undefined') {
					let satObj = {"BankroutingNumber": bankCode, "BankName": bankName}
					_satellite.track('Capture_Bank_Details', satObj);
				}
				$('.bank-name-loader').css('display', 'none');
			},
			error: function(jqXHR, textStatus, errorThrown) {
				// console.log("BankName.error: " + textStatus);
				$('.bank-name-loader').css('display', 'none');
			}
		});
	}

	proto.hideIfVisible = function (arr) {
		if(arr.length) {
			arr.forEach(function(element) {
				if ($(element).is(':visible')) {
					$(element).hide();
				}
			});
		}
	}

	proto.removeIfVisible = function (arr) {
		if(arr.length) {
			arr.forEach(function(element) {
				if ($(element).is(':visible')) {
					$(element).remove();
				}
			});
		}
	}

	proto.onBankExpandClick = function (event) {
		var buttonText = '- <span id="hide-bank-details" class="td_underline pointer">Hide Optional Bank Fields</span>';
		var bankInfoVisible = $('#bank-info').is(':visible');
		if (bankInfoVisible) {
			buttonText = '+ <span id="show-bank-details" class="td_underline pointer">Show Optional Bank Fields</span>';
		} 
		$('.button_bank_info').html(buttonText);
		$('#bank-info').toggle(150);
	};

	proto.checkBankAddrCity = function () {
		var bankAddr = SessionStorage.getValue('BI_BI_2');
		var bankCity = SessionStorage.getValue('BI_BI_3'); 
		if (bankAddr || bankCity) {
			var buttonText = '- <span id="hide-bank-details" class="td_underline pointer">Hide Optional Bank Fields</span>';
			$('.button_bank_info').html(buttonText);
			$('#bank-info').show(150);
		}
	}

	/**
	 * @method findStepIndex
	 * @param {String} stepTitleId 
	 * @returns 
	 */
	proto.findStepIndex = function (stepTitleId,stateModel) {
		var stepIndex;
		if(stateModel.states!==null){
			stepIndex = stateModel.states.findIndex(function (obj) { return obj.tlId === stepTitleId; })+1;
		}else{
			stepIndex = this.progress_steps.findIndex(function (obj) { return obj.tlId === stepTitleId; });
		}
		return stepIndex;
	};
	  

	/**
	 * @method onEditLogoClick
	 * @param {String} stepTitleId
	 * @callback
	 */
	proto.gotoStep = function (event,stepTitleId) {
		Settings.onPreviousClick = true;
		Settings.COPIES_FLAG = false;
		Settings.REVERSE_FLAG = false;
		Settings.ROUTING_FLAG = false;
		Settings.VOUCHER_FLAG = false;
		Settings.SIGNATURE_FLAG = false;
		Settings.ANOTHER_SIGNATURE_FLAG = false;
		Settings.PRIMARY_COLOR_FLAG = false;
		Settings.SIDEMARGIN_FLAG = false;

		var stateModel = this.stateModel;

		// hide/show proof download btn
		if (stateModel.states && stateModel.current === stateModel.states.length + 1) {
			$('.online-proof').addClass("make-visible");
		} else {
			$('.online-proof').removeClass("make-visible");
		}
		var index = this.findStepIndex(stepTitleId,stateModel);
		if (index==-1) {
			return;
		}
		if (stepTitleId === "Logo") {
			this.showLogoLeftSection();
		}
		var arrayOfElementsToHideIfVisible = [];
		arrayOfElementsToHideIfVisible.push('#fbt-popup', '#reverse,#Copy', '#reverse,#personalization_link');
		arrayOfElementsToHideIfVisible.push('#reverse,#returnWorks', '#reverse,#foldingWorks', '#reverse,#envelope_link');
		this.hideIfVisible(arrayOfElementsToHideIfVisible);

		var arrayOfElementsToRemoveIfVisible = [];
		arrayOfElementsToRemoveIfVisible.push('#voucher-popup', '#signature-popup', '#sidemargin-popup', '#primary-popup', '#signatureLine-popup');
		this.removeIfVisible(arrayOfElementsToRemoveIfVisible);

		EventController.emit(ProductEvents.PREV_CLICK, this.model);
		stateModel.go(index);
	};

	/**
	 * @method fbtLogoEdit
	 * @param {jQuery.event} event 
	 * @param {String} stepTitleId 
	 */
	proto.fbtLogoEdit = function(event,stepTitleId) {
		$(Classes.FBT_EDIT_PROOF_SELECTOR).trigger('click');
		this.gotoStep(event,stepTitleId);	
	}

	proto.onSubtotalChange = function () {
		const findKeyVal = function (key, value) {
			return function (item) {
				return item[key] === value;
			}
		}
		
		if (Settings.ULTIMATE_PERSONALIZATION === 'true' && Surcharge.get(Settings.PERSONALIZATION_SURCHARGEID )) {
			var surchargeObj = Surcharge.get(Settings.PERSONALIZATION_SURCHARGEID);

			var optionSurcharge = Number(surchargeObj.option.price);
			var optionSurchargeFloor = Math.floor(optionSurcharge);
			
			if(optionSurcharge === optionSurchargeFloor) {
				optionSurcharge = optionSurcharge.toFixed(0);
			} else {
				optionSurcharge = optionSurcharge.toFixed(2);
			}

			if(this.model.surcharges.length && this.model.surcharges.find(findKeyVal('id', 'LAYCHG'))) {
				optionSurcharge = '(INCLUDED)';
			} else {
				optionSurcharge = '(+$' + optionSurcharge + ')';
			}
			this.model.steps._items.find(findKeyVal('progressTitle', 'Logo')).questions._items.find(findKeyVal('id', 'logo')).logoButtonOptions._items.find(findKeyVal('id', 'CUSTOM') ).desc = Content.get('CUSTOM', desc, { value: optionSurcharge })  ;
			this.model.steps._items.find(findKeyVal('progressTitle', 'Logo')).questions._items.find(findKeyVal('id', 'logo')).logoButtonOptions._items.find(findKeyVal('id', 'CUSTOM') ).reorderDesc = Content.get('REORDER_CUSTOM', desc, { value: optionSurcharge }) ;
			EventController.emit(ActionEvents.LOGO_DESC_UPDATE);
		}
	}

	proto.updateImprintPrice = function () {
		var productIdObj = this.model.questions._items.filter(function (item) {
			return item.id == "productId";
		});
		if (Array.isArray(productIdObj) && productIdObj.length) {
			var imprintType = productIdObj[0].info.imprintType;
			if (imprintType && FbtModel.fbtRequiredFields) {
				var selectedProductId = this.productModel.info.productId;
				var selectedProduct = this.productModel.info.priceInfo.filter(function (product) { return product.id == this.productModel.info.productId; }, this)[0];
				var unselectedProduct = this.productModel.info.priceInfo.filter(function (product) { return product.id !== this.productModel.info.productId; }, this)[0];
				var unselectedProductId = unselectedProduct.id;
				var selectedQty = this.model.getQuantityValue();
				var imprintPrice = parseFloat(unselectedProduct.option.filter(function (item) { return item.qty == selectedQty; }, this)[0].price) -
					parseFloat(selectedProduct.option.filter(function (item) { return item.qty == selectedQty; }, this)[0].price);
				$("#MC_GoToEditor .js-next-label").text("ADD IMPRINT (+$" + imprintPrice.toFixed(2) + ")");
			}
		}
	};

	return ProductController;
});
