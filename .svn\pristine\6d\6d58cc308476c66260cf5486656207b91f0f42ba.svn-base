define(function(require) {
	'use strict';

	var AbstractQuestionController = require('./Abstract');
	var Classes = require('../../constants/Classes');
	var DomEvents = require('../../constants/DomEvents');
	var bindAll = require('mout/object/bindAll');
	var inherits = require('mout/lang/inheritPrototype');
	var Settings = require('../../constants/Settings');
    var currency = require('mout/number/currencyFormat');
    var Helper = require('../../util/helper');
	/**
	 * @class App.Controllers.Question.Note
	 * @extends App.Controllers.Question.Abstract
	 *
	 * @constructor
	 * @param {Object} config
	 * @param {jQuery} config.view
	 * @param {Models.Ui.Question} config.model
	 */
	function ModelQuestionController(config) {
		bindAll(this,
				'onActionClick',
            	'onButtonClick'
		);

		AbstractQuestionController.call(this, config);
	}

	var proto = inherits(ModelQuestionController, AbstractQuestionController);

	/**
	 * @method template
	 * @param {Object} model
	 * @return {String}
	 */
	proto.template = require('hbs!templates/question/model');

	var cv_flag = false;

	/**
	 * @method attachEvents
	 * @chainable
	 */
	// proto.attachEvents = function() {
	// 	this.$view
	// 	.on(DomEvents.CLICK,Classes.PREVIEW_BUTTON_SELECTOR, this.retrieveImageUrl);
	// 	this.$view
	// 	.on(DomEvents.CLICK,Classes.CLOSE_BUTTON_SELECTOR, this.closeModalPopup);
	// 	return this;
	// };

	/**
	 * @method detachEvents
	 * @chainable
	 */
	// proto.detachEvents = function() {
	// 	this.$view
	// 	.off(DomEvents.CLICK,Classes.PREVIEW_BUTTON_SELECTOR, this.retrieveImageUrl);
	// 	this.$view
	// 	.off(DomEvents.CLICK,Classes.CLOSE_BUTTON_SELECTOR, this.closeModalPopup);
	// 	return this;
	// };

	/**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        this.$view
            .on(DomEvents.CLICK, Classes.verse_ACTION_SELECTOR, this.onActionClick)
            .on(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick)
            .on(DomEvents.CHANGE, this.onVerseChange);
       // EventController.on(ProductEvents.CHANGE, this.onProductChange);

        return this;
    };


    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        this.$view
           .off(DomEvents.CLICK, Classes.verse_ACTION_SELECTOR, this.onActionClick)
            .off(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick)
            .off(DomEvents.CHANGE, this.onVerseChange);
       // EventController.off(ProductEvents.CHANGE, this.onProductChange);

        return this;
    };


	/**
	 * @method render
	 * @chainable
	 */
	proto.render = function() {
		var model = this.model;
		this.$view
		.html(this.template({
			show : 'text',
			company: Settings.SITE_CORP,
			baseAppUrl:baseAppUrl,	
			id: model.id,
            amount: currency(model.value, 0)
			
		}));

		return this.start();
	};


	/**
     * @method onActionClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onActionClick = function(event) {
        var action = $(event.target).data('action');

        if (action) {
            event.preventDefault();
            event.stopPropagation();

            EventController
                .emit(ActionEvents.HIDE_ALL)
                .emit(action, this.update);
        }
    };

     $(window).resize(function() {
     //	var model = this.model;
        var a = $('#personalization_logo').offset();
    if(a){
        var tp = a.top;
        var lt = a.left;
        $('.popover').offset({top: tp+32 ,left: lt-340});
        $('.arrow').offset({top: tp+25,left:lt-2});
    }
    });

     /**
     * @method onButtonClick
     * @param {jQuery.Event} event
     * @callback
     */
    
    proto.onButtonClick = function(event) {
        var model = this.model;
        var info = model.info;
        var options = model.options;
        var construction = this.construction;
        
        if (Helper.isSameTooltipClick(event)) {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
        } else {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }

            $('#' + model.id).popover({
                html: true,
                trigger: "manual",
                content: function () {
                    return $('#popup_model').html();
                }
                // content: function() {
                //     return $('#per_popup').html();
                // },
            });
            if (!cv_flag) {
                $('#' + model.id).popover('show');
            } else {
                $('#' + model.id).popover('hide');
            }
            cv_flag = !cv_flag;

            $('#personalization #CVCloseIcon').on('click', function (e) {
                cv_flag = false;
                $('#' + model.id).popover('hide');
            });
        }
    };



	return ModelQuestionController;
});
