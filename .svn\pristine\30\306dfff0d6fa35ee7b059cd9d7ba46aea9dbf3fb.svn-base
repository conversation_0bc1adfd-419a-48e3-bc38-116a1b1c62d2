{{#if options}}
<div class="logo_heading">
    <h2 class="hdg hdg_h2">{{desc}}{{#if noc}}<span alt="CopiesHelp" id="CopiesHelp" class="js-button-action"
            data-template='<div id="fbt-popup" class="custpop popover pop-copy" role="tooltip"><div class="arrow"></div><div class="popover-body"></div></div>'
            data-container="body" data-placement="bottom" data-content="" data-original-title="" title=""></span>{{/if}}
    </h2>
</div>

<div class="ie-display logoButtonOptions btnTogglezz">
    {{#each logoButtonOptions}}
    <div class="ie-display btnToggle" id="logoSelectionButtonDiv_{{id}}">
        {{#if isFormButton}}
        <button value="{{id}}"
            class="js-logo-buttons button_neutral button js-radio-action centper js-question-option logo-option-button-{{id}} d-none"
            data-action="{{action.event}}" id="{{slug}}">{{desc}}
        </button>
        {{else}}
        <button value="{{id}}"
            class="js-logo-buttons button_neutral button js-radio-action centper js-question-option logo-option-button-{{id}}"
            data-action="{{action.event}}" id="js-logo-buttons_{{slug}}">{{desc}}
        </button>
        {{/if}}
    </div>
    {{#if isFormButton}}
    <form class="file logo-upload" action="{{../../uploadUrl}}" method="POST" enctype="multipart/form-data" id="file-browse-logo-upload">
            <label for="file-control"  id="logo-upload_{{slug}}" class="button_neutral button centper js-question-option logo-option-button-{{id}}" id="UploadLogo-BrowseForFile">
                {{desc}}
            </label>
            <input id="file-control_UploadLogo" type="file" class="deluxe-file file-control" name="image" />
    </form>
    {{/if}}
    {{/each}}
    <span class="logoButtonOptions-note">
    Recommended file formats: EPS, AI, PNG or JPG.<br>We recommend a transparent background.<br>Max file size: 10 MB
    </span>
</div>


{{!-- <div class="reorder-wraper  {{#if isNoLogo}}d-none{{/if}} " id="reorder-wraper"> --}}
<div class="reorder-wraper  d-none " id="reorder-wraper">

    {{!-- For image uploading placeholder --}}
    <div class="reorder-logo-summary" id="js-logo-summary"> </div> 

    <div class="no-logo-wraper" >
        <button value=""
                class="button_neutral button centper logo-option-button-none cancel_upload_logo_button"
                data-action="" id="">Remove Logo
        </button>
    </div>
    {{#each logoButtonOptions}}

        <div class="" >
            {{#is id 'STANDARD'}}
                <button value="{{id}}"
                class="reorder-btn js-logo-buttons button_neutral button js-radio-action centper js-question-option logo-option-button-{{id}}"
                data-action="{{action.event}}" id="{{slug}}">{{reorderDesc}}
                </button>
            {{/is}}              

        </div>
         {{#is id 'CUSTOM'}}               

                <form class="file" action="{{../../uploadUrl}}" method="POST" enctype="multipart/form-data" id="file-browse">
                    <label for="file-control" class="button button_neutral  button_file_reorder" id="UploadLogo-BrowseForFile">
                       {{reorderDesc}}
                    </label>
                    <input id="file-control" type="file" class="deluxe-file file-control" name="image"/>
                </form>
        {{/is}}
    {{/each}}
    <span class="logoButtonOptions-note">
        Recommended file formats: EPS, AI, PNG or JPG.<br>We recommend a transparent background.<br>Max file size: 10 MB
    </span>

    
    {{!-- {{#each logoButtonOptions}}
    <div class="" >
        {{#if isFormButton}}
        <button value="{{id}}"
            class="reorder-btn js-logo-buttons button_neutral button js-radio-action centper js-question-option logo-option-button-{{id}} d-none"
            data-action="{{action.event}}" id="{{slug}}">{{reorderDesc}}
        </button>
        {{else}}
        <button value="{{id}}"
            class="reorder-btn js-logo-buttons button_neutral button js-radio-action centper js-question-option logo-option-button-{{id}}"
            data-action="{{action.event}}" id="{{slug}}">{{reorderDesc}}
        </button>
        {{/if}}
    </div>
    {{#if isFormButton}}
    <form class="file logo-upload" action="{{../../uploadUrl}}" method="POST" enctype="multipart/form-data" id="file-browse">
            <label for="file-control"  id="{{slug}}" class="button_neutral button centper js-question-option logo-option-button-{{id}}" id="UploadLogo-BrowseForFile">
                {{reorderDesc}}
            </label>
            <input id="file-control" type="file" class="deluxe-file file-control" name="image" />
    </form>
    {{/if}}
    {{/each}}
    <span class="logoButtonOptions-note">
    Recommended file formats: EPS, AI, PNG or JPG.<br>We recommend a transparent background.<br>Max file size: 10 MB
    </span> --}}


</div>

<div class="ie-display btnTogglezz">
    {{#each logoButtonOptions}}

    {{#if ../../isCustomLogo}}
    {{#is id 'CUSTOM'}}
    <div class="btnToggle logopreview-custom">
        <div class="js-logo-upload">
            <div class="js-logo-preview"></div>
            <div class="js-logo-controls">
            </div>
        </div>

        <div class="js-logo-uploading" style="display:none;">
            <div class="uploading">
                <div class="uploading-title">
                    <h2 class="hdg hdg_h1 progress_bar">Uploading Your Logo&hellip;</h2>
                </div>

                <div class="progressbar">
                    <div class="progress" style="width:0%"></div>
                </div>
            </div>
        </div>
    </div>
    {{/is}}
    {{/if}}

    {{#if ../../isStandardLogo}}
    {{#is id 'STANDARD'}}
    {{/is}}
    {{/if}}
    {{#if ../../isLogoMx}}

    {{#is id 'LOGOMX'}}
    <div data-controller="LogoMixFilterController"></div>
    {{/is}}
    {{/if}}

    {{/each}}
    </div>

    <fieldset>
        <legend> </legend>
        <ul class="vList vList_std isPseudoSibling">
        </ul>
    </fieldset>
    {{/if}}