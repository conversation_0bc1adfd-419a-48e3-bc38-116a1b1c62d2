define(function(require) {
    'use strict';

    var ImprintOverlay = {
        /**
         * Get the width of the block based on the widest child.
         *
         * @method _getWidth
         * @param {Object} group
         * @return {Integer}
         */
        getWidth: function(group) {
            var width = 0;
            var children = group.children;
            var child;
            var childWidth;
            var logoWidth = 0;
            var logoX = 0;
            var logoY = 0;
            var lineX = 0;

            for (var i=0; i<children.length; i++) {
                child = children[i];
                childWidth =+ child.w;

                //Only logos in blocks have scaleX property.
                if (child.scaleX) {
                    logoWidth = child.w * child.scaleX;
                    logoX = child.x;
                    logoY = child.y
                } else {
                    lineX = child.x;
                }

                //Logos do not have the id property.
                //Only calculate line widths here.
                if (child.id && childWidth > 0 && childWidth > width) {
                    //Set our new widest width
                    width = childWidth;
                }
            }

            //console.log('ImprintOverlay.getWidth().group: ', group);
            //console.log('ImprintOverlay.getWidth().width: ' + width);
            //console.log('ImprintOverlay.getWidth().childWidth: ' + childWidth);
            //console.log('ImprintOverlay.getWidth().logoWidth: ' + logoWidth);
            //console.log('ImprintOverlay.getWidth().logoX: ' + logoX);
            //console.log('ImprintOverlay.getWidth().logoY: ' + logoY);
            //console.log('ImprintOverlay.getWidth().lineX: ' + lineX);

            //We have a logo in the 2 position, above other text.
            if (logoX > lineX) {
                return width;
            }

            //Logo is in the 4 or 6 position, left or right of the text.
            return width + logoWidth;
        },

        /**
         * Calculates the height of the block based on the sum of the children.
         *
         * @method _getHeight
         * @param {Object} group
         * @return {Integer}
         */
        getHeight: function(group, rotation) {
            var children = group.children;
            var child;
            var logoHeight = 0;
            var logoX = 0;
            var logoY = 0;
            var lineHeight = 0;
            var lineX = 0;
            var blockHeight = 0;

            rotation = parseInt(rotation, 10) || 0;
            var yMult = Math.abs(Math.cos(rotation* Math.PI / 180))
            var xMult = 1 - yMult;
            //console.log('ImprintOverlay ----------------------------------------------');

            for (var i=0; i<children.length; i++) {
                child = children[i];
                //console.log('Imprintoverlay.child.h[' + child.id + ']: ' + child.h);

                //Only logos in blocks have scaleY property.
                if (child.scaleY) {
                    //console.log('ImprintOverlay.child.scaleY: ' + child.scaleY);
                    logoHeight = child.h;
                    logoX = child.x;
                    logoY = child.y;
                } else {
                    if (group.label == 'BI' || group.label == 'CI' || group.label == 'LS') {
                        if (!(child.label.includes('_block'))) {
                            lineHeight += child.h;
                            lineX = child.x;
                        }
                    } else {
                        lineHeight += child.h;
                        lineX = child.x;
                    }
                }
            }

            //console.log('Imprintoverlay.getHeight().lineX: ' + lineX);
            //console.log('Imprintoverlay.getHeight().lineHeight: ' + lineHeight);
            //console.log('Imprintoverlay.getHeight().logoHeight: ' + logoHeight);
            //console.log('Imprintoverlay.getHeight().logoX: ' + logoX);
            //console.log('Imprintoverlay.getHeight().logoY: ' + logoY);

            if (logoHeight > 0 && lineHeight == 0) {
                //Logo Only
                blockHeight = logoHeight;
            } else if (logoHeight == 0 && lineHeight > 0) {
                //Text Only
                blockHeight = lineHeight;
            } else if (logoHeight > 0 && lineHeight > 0 && logoX < lineX) {
                //Logo Position 4 or 6
                if (logoHeight > lineHeight) {
                    blockHeight = logoHeight;
                } else {
                    blockHeight = lineHeight;
                }
            } else if (logoHeight > 0 && lineHeight > 0 && logoX > lineX) {
                //Logo Position 2
                blockHeight = logoHeight + lineHeight;
            } else {
                //console.log('WARNING: Set Default Block Height! [' + group.label + ']');
                blockHeight = lineHeight;
            }
            //console.log('Imprintoverlay.getHeight().blockHeight: ' + blockHeight);

            return blockHeight;
        },

        /**
         * Calculate bounding box of rotated field
         *
         * @method _applyRotation
         * @param  {Object} pos      Position values
         * @param  {Number} rotation Degrees to rotate
         * @return {Object}
         */
        applyRotation: function(pos, rotation) {
            //console.log('Imprintoverlay.applyRotation()');
            if (rotation == 90) {
                var actualH = pos.h;
                var rad = 2 * Math.PI * parseInt(rotation, 10) / 360;
                var w = Math.abs( pos.w * Math.cos(rad) + pos.h * Math.sin(rad) );
                var h = Math.abs( pos.w * Math.sin(rad) + pos.h * Math.cos(rad) );
                pos.w = w;
                pos.h = h;
                pos.x -= actualH * pos.scale;
            } else if (rotation == -90) {
                var actualW = pos.w;
                var rad = 2 * Math.PI * parseInt(rotation, 10) / 360;
                var w = Math.abs( pos.w * Math.cos(rad) + pos.h * Math.sin(rad) );
                var h = Math.abs( pos.w * Math.sin(rad) + pos.h * Math.cos(rad) );
                pos.w = w;
                pos.h = h;
                pos.y -= actualW * pos.scale;
            } else {
                var rad = 2 * Math.PI * parseInt(rotation, 10) / 360;
                var w = Math.abs( pos.w * Math.cos(rad) + pos.h * Math.sin(rad) );
                var h = Math.abs( pos.w * Math.sin(rad) + pos.h * Math.cos(rad) );
                pos.w = w;
                pos.h = h;
                pos.y -= Math.abs(Math.sin(rad)) * w/2;
            }
            return pos;
        },

        /**
         * Deprecated
         *
         * @method _calculateLogoOffsets
         * @private
         * @param {Object} group
         * @return {Object}
         */
        calculateLogoOffsets: function(group) {
            var noOffset = {x: 0, y: 0};
            var logoPosition = 4;
            var children = group.children;
            var i = 0;

            // disable method.
            return noOffset;

            if ( !children) {
                return noOffset;
            }

            var logoChild;
            var firstChild = children[0] || null;

            for (; i < children.length; i++) {
                if (children[i].label === 'logo1') {
                    logoChild = children[i];
                    // If not present in xml, then they are 0
                    logoChild.y = logoChild.y || 0;
                    logoChild.x = logoChild.x || 0;
                }
            }

            if ( !firstChild || !logoChild) {
                return noOffset;
            }

            // Positions. 5 being the text position in the block.
            // 1 2 3
            // 4 5 6
            // 7 8 9

            if (logoChild.y < firstChild.y && logoChild.x > firstChild.x) {
                logoPosition = 2;
            }

            // Is there extra space on the top for a logo? Position 2
            if (logoPosition === 2) {
                var logoX = logoChild.x || 0;
                var logoY = logoChild.w * logoChild.scaleY;
                logoY = parseInt(group.y, 10) + parseInt(logoY, 10);

                return {x: logoX, y: logoY};
            }

            //Is there extra space on the left for a logo? Position 4
            if (logoPosition === 4) {
                var logoY = logoChild.y || 0;
                var logoX = logoChild.w * logoChild.scaleX;
                logoX = parseInt(group.x, 10) + parseInt(logoX, 10);

                return {x: logoX, y: logoY};
            }

            return noOffset;
        }
    };

    return ImprintOverlay;
});
