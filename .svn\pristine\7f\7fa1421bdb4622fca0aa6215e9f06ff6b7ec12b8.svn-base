define(function(require) {
    'use strict';

    var ActionEvents = require('../constants/ActionEvents');
    var Classes = require('../constants/Classes');
    var ConfigurationProvider = require('../providers/Configuration');
    var Content = require('i18n!../constants/nls/en-us/Content');
    var Controller = require('./Controller');
    var DomEvents = require('../constants/DomEvents');
    var EventController = require('./Event');
    var ProductEvents = require('../constants/ProductEvents');
    var FbtModel = require('../models/Fbt');
    var Settings = require('../constants/Settings');
    var StateEvents = require('../constants/StateEvents');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var Query = require('models/Query');
    var q = require('q');
    // var Handlebars = require('handlebars');


    /**
     * @class App.Controllers.LogoBrowse
     * @extends App.Controllers.Controller
     *
     * @constructor
     * @param {Object} config
     */
    function FbtSummaryController(config) {
        bindAll(this,
            'getFbtProducts',
            'setIndex',
            'onHideAction',
            'onShowAction',
            'onStateChange',
            'onButtonClick',
            'onSummaryButtonClick'
        );
       

        /**
         * @property query
         * @type {App.Models.Query}
         */
        this.query = Query.getInstance();

        Controller.call(this, config);

        // Start hidden
        this.$view.hide();
        
        /**
         * Omniture site prefix
         *
         * @type {String}
         */
        this.site_prefix = Settings.OMNITURE_PREFIX;
        
        
    }

    var proto = inherits(FbtSummaryController, Controller);

    

    /**
     * @method initCall
     * @return {Promise}
     */
    proto.initCall = function() {
        return ConfigurationProvider
            .getConfiguration()
            .then(this.getFbtProducts)
            .then(this.setIndex)
            .then(this.render)
            .fail(this.onError);
    };

    /**
     * @method getFbtProducts
     * @return {Promise}
     */
    proto.getFbtProducts = function() {
       this.products = FbtModel.fbtProducts;
       return this.products;
    };

    // -- Accessors ------------------------------------------------------------

    /**
     * @method setIndex
     * @param {fbtProducts}
     * @chainable
     */
    proto.setIndex = function(products) {
        this.products = products;
        return this;
    };

  

  
    // -- Methods --------------------------------------------------------------

    /**
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = require('hbs!templates/fbtSummary');

    /**
     * TODO: refactor this
     *
     * @method render
     * @chainable
     */
    proto.render = function() {
        var fbtCartPath = Settings.SVC_ADDTOCART_REDIRECT;
        if (Settings.SVC_ADDTOCART_FBT_REDIRECT) {
            fbtCartPath = fbtCartPath + '?pageName=' + Settings.SVC_ADDTOCART_FBT_REDIRECT;
        }
        this.$view
            .html(this.template({
                baseAppUrl: baseAppUrl,
                host_url: host_url,
                title: Content.get('FBT_SUMMARY_TITLE'),
                imagePath: Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE,
                brandLogoUrl: Settings.BRAND_LOGO_URL,
                siteCss: Settings.SITE_CSS,
                items: this.products,
                brandLogo: Settings.BRAND_LOGO,
				cartPath: fbtCartPath
            }));

        return this;
    };

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        this.$view
            .on(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick)
            .on(DomEvents.CLICK, Classes.FBT_REVIEWCART_SELECTOR, this.onReviewCartClick)
            .on(DomEvents.CLICK, Classes.FBT_SUMMARY_BUTTON, this.onSummaryButtonClick);

        EventController
            .on(ActionEvents.HIDE_ALL, this.onHideAction)
            .on(ActionEvents.FBT_SUMMARY, this.onShowAction)
            .on(StateEvents.CHANGE, this.onStateChange);

        return this;
    };

    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        this.$view
            .off(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick)
            .off(DomEvents.CLICK, Classes.FBT_REVIEWCART_SELECTOR, this.onReviewCartClick)
            .off(DomEvents.CLICK, Classes.FBT_SUMMARY_BUTTON, this.onSummaryButtonClick);

        EventController
            .off(ActionEvents.HIDE_ALL, this.onHideAction)
            .off(ActionEvents.FBT_SUMMARY, this.onShowAction)
            .off(StateEvents.CHANGE, this.onStateChange);

        return this;
    };

    /**
     * @method onButtonClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onButtonClick = function() {
      $('#fbtHelp').popover({
                html: true,
                trigger: "manual",
                placement: 'bottom',
                content: function() {
                    return $('#fbtHelpPopup').html();
                },
        });
        if (!cv_flag) {
            $('#fbtHelp').popover('show');
        } else {
            $('#fbtHelp').popover('hide');
        }
        cv_flag = !cv_flag;

        $("#fbt-popup #fbtCloseIcon").on("click", function() {
            cv_flag = false;
            $('#fbtHelp').popover('hide');
        });
    };
   
    proto.onReviewCartClick = function() {
      EventController.emit(ActionEvents.FBT_REVIEWCART);
    };

    proto.onSummaryButtonClick = function() {
      window.onbeforeunload = null;
    };
   
    /**
     * @method onHideAction
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onHideAction = function() {
        this.$view.hide();
    };

    /**
     * @method onShowAction
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onShowAction = function(event, done) {

        var state = this.state;
        var controller = state && state.controller;
        var model = (controller && controller.model) || state;
        var trackEvent = model && model.trackEvent;

        this.callback = done;

        this.initCall();
        this.render();
        this.$view.show();
        
    };

   
    /**
     * @method onStateChange
     * @callback
     */
    proto.onStateChange = function(event, state) {
        this.state = state;
    };

    return FbtSummaryController;
});
