define(function (require) { // jshint ignore:line
    'use strict';

    var AbstractProvider = require('./Abstract');
    var Settings = require ('../constants/Settings');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var xmlToObject = require('../util/xmlToObject');
    var Query = require('models/Query');
    var FbtModel = require('models/Fbt');

    /**
     * @class App.Providers.AddToCart
     * @extends App.Providers.Abstract
     *
     * @constructor
     */
    var AddToCartProvider = function () {
        // bindAll(this, '_onResponseReceived');
        bindAll(this, '_onSaveResponseReceived', 'saveKitConfig', '_onResponseReceived', 'kitAddToCart');
        
        AbstractProvider.apply(this, arguments);
    };

    var proto = inherits(AddToCartProvider, AbstractProvider);

    /**
     * Save individual kit items for processing last
     *
     * @method saveKitItems
     * @param {App.Models.Cart.Product} configuredProduct A product with custom configuration options set
     * @return {Promise}
     */
    proto.saveKitItems = function(configuredProduct) {
        var params = {};
        var querydata = Query.getInstance();
          if(typeof FbtModel.kitSkuId != 'undefined') {           
            if(typeof FbtModel.cartItems == 'undefined') {
                FbtModel.cartItems = [];
            }
            var product = configuredProduct.toXML(true, false);
            FbtModel.cartItems.push(product.split('<customizedOptions>')[1].split('</customizedOptions>')[0])

            // final product
            if (typeof FbtModel.fbtProducts[FbtModel.currentFbtProductIndex + 1] === 'undefined') {
                
               return this.kitAddToCart(configuredProduct)
                
			} else {
                
                this.promise = new Promise(function(resolve){
                    resolve(true)
                })
                return this.promise;

			}


          
            // FbtModel.cartItems.push(configuredProduct)
            // return {"status": true}
        }


    }


    /**
     * Handles the response from the save config in kit
     *
     * @method kitAddToCart
     *
     * @param {HTMLDocument} data
     * @private
     */
    proto.kitAddToCart = function(configuredProduct) {
        var params = {};
        var querydata = Query.getInstance();
        var endPoint;
        var product = configuredProduct.kitToXML(false)
        this.setBaseUrl('');
        if (Settings.SVC_ADDTOCART_FLAG == 1 || Settings.SVC_ADDTOCART_FLAG == 3) {
            
            endPoint = Settings.SVC_ADDTOCART + this.serializeParams(params);

            window.onbeforeunload = null;

            //Storefront may require the X-CSRF-Token header (Tenenz.com)
            var _headers;
            var csrf_meta_value = $(Settings.X_CSRF_SELECTOR).attr('content');
            if (csrf_meta_value != undefined) {
                _headers = {'X-CSRF-TOKEN':csrf_meta_value};
            }
            this.promise = this
                .post(endPoint, product, 'text', {
                    processData: false,
                    contentType: 'text/xml',
                    headers: _headers
                })
                .then(this._onResponseReceived)
                .fail(this._onError);
            
            return this.promise;
        } else if (Settings.SVC_ADDTOCART_FLAG == 2) {
        
            var configId = querydata.configId || querydata.configId;
            if (configId) {
                endPoint = Settings.SVC_SAVE_CONFIG + '?configId=' + configId;
            } else {
                endPoint = Settings.SVC_SAVE_CONFIG + this.serializeParams(params);
            }

            window.onbeforeunload = null;
            this.promise = this
                .post(endPoint, product, 'text', {
                    processData: false,
                    contentType: 'multipart/form-data',
                })
                .then(this._onSaveResponseReceived)
                .fail(this._onError);
            return this.promise;
        }
    };
     

    /**
     * Adds the specified product (and configuration) to the user's
     * cart (NO RESPONSE CACHING)
     *
     * @method addToCart
     * @param {App.Models.Cart.Product} configuredProduct A product with custom configuration options set
     * @return {Promise}
     */
    proto.query = function(configuredProduct) {
        var params = {};
        var querydata = Query.getInstance();
        var product = configuredProduct.toXML(true);
        var endPoint;
        
        
        this.setBaseUrl('');
        if (Settings.SVC_ADDTOCART_FLAG == 1 || Settings.SVC_ADDTOCART_FLAG == 3) {
            endPoint = Settings.SVC_ADDTOCART + this.serializeParams(params);

            // For FBT products
            if (typeof FbtModel.fbtProducts != 'undefined' && Object.keys(FbtModel.fbtProducts).length > 0) {
                var isLast = (typeof FbtModel.fbtProducts[FbtModel.currentFbtProductIndex + 1] === 'undefined') ? true : false;
                if(isLast) {
                    window.onbeforeunload = null;
                }                
            } else {
               window.onbeforeunload = null;   
            }

            //Storefront may require the X-CSRF-Token header (Tenenz.com)
            var _headers;
            var csrf_meta_value = $(Settings.X_CSRF_SELECTOR).attr('content');
            if (csrf_meta_value != undefined) {
                _headers = {'X-CSRF-TOKEN':csrf_meta_value};
            }
           
            this.promise = this
                .post(endPoint, product, 'text', {
                    processData: false,
                    contentType: 'text/xml',
                    headers: _headers
                })
                .then(this._onResponseReceived)
                .fail(this._onError);

            return this.promise;
        } else if (Settings.SVC_ADDTOCART_FLAG == 2) {
            var product = configuredProduct.toXML(true, true);
           
            var configId = querydata.configId || querydata.configId;
            if (configId) {
                endPoint = Settings.SVC_SAVE_CONFIG + '?configId=' + configId;
            } else {
                endPoint = Settings.SVC_SAVE_CONFIG + this.serializeParams(params);
            }

            // For FBT products
            if (typeof FbtModel.fbtProducts != 'undefined' && Object.keys(FbtModel.fbtProducts).length > 0) {
                var isLast = (typeof FbtModel.fbtProducts[FbtModel.currentFbtProductIndex + 1] === 'undefined') ? true : false;
                if(isLast) {
                    window.onbeforeunload = null;
                }
            } else {
               window.onbeforeunload = null;   
            }
            this.promise = this
                .post(endPoint, product, 'text', {
                    processData: false,
                    contentType: 'multipart/form-data',
                })
                .then(this._onSaveResponseReceived)
                .fail(this._onError);

            return this.promise;
        }
    };


     /**
     * Adds the specified product (and configuration) to save config service before addto cart
     * cart (NO RESPONSE CACHING)
     *
     * @method saveKitConfig
     * @param {App.Models.Cart.Product} configuredProduct A product with custom configuration options set
     * @return {Promise}
     */
    proto.saveKitConfig = function(configuredProduct) {


        if(!(typeof FbtModel.fbtProducts[FbtModel.currentFbtProductIndex + 1] === 'undefined' && Settings.SVC_ADDTOCART_FLAG == 3)) {
            this.promise = new Promise(function(resolve){
                resolve({'status': true})
            })
            return this.promise;
        }
        var params = {};
        var querydata = Query.getInstance();
  
        var product = configuredProduct.kitToXML(true);

        this.setBaseUrl('');

        var endPoint;
        var querydata = Query.getInstance();
        var configId = querydata.configId || querydata.configId;
        if (configId) {
            if(querydata.fromPage === 'config') {
                endPoint = Settings.SVC_SAVE_CONFIG;
            } else {
                endPoint = Settings.SVC_SAVE_CONFIG + '?configId=' + configId;
            }
        } else {
            endPoint = Settings.SVC_SAVE_CONFIG + this.serializeParams(params);
        }
        // For FBT products
       window.onbeforeunload = null;
        this.promise = this
            .post(endPoint, product, 'text', {
                processData: false,
                contentType: 'multipart/form-data',
            })
            .then(this._onSaveResponseReceived)
            .fail(this._onError);

        return this.promise;
    };


     /**
     * Adds the specified product (and configuration) to save config service before addto cart
     * cart (NO RESPONSE CACHING)
     *
     * @method saveConfig
     * @param {App.Models.Cart.Product} configuredProduct A product with custom configuration options set
     * @return {Promise}
     */
    proto.saveConfig = function(configuredProduct) {
        var params = {};
  
        var product = configuredProduct.toXML(true, true);

        this.setBaseUrl('');

        var endPoint;
        var querydata = Query.getInstance();
        var configId = querydata.configId || querydata.configId;
        if (configId) {
            if(querydata.fromPage === 'config') {
                endPoint = Settings.SVC_SAVE_CONFIG;
            } else {
                endPoint = Settings.SVC_SAVE_CONFIG + '?configId=' + configId;
            }
        } else {
            endPoint = Settings.SVC_SAVE_CONFIG + this.serializeParams(params);
        }
        // For FBT products
        if (typeof FbtModel.fbtProducts != 'undefined' && Object.keys(FbtModel.fbtProducts).length > 0) {
            var isLast = (typeof FbtModel.fbtProducts[FbtModel.currentFbtProductIndex + 1] === 'undefined') ? true : false;
            if(isLast) {
                window.onbeforeunload = null;
            }                
        } else {
           window.onbeforeunload = null;   
        }
        this.promise = this
            .post(endPoint, product, 'text', {
                processData: false,
                contentType: 'multipart/form-data',
            })
            .then(this._onSaveResponseReceived)
            .fail(this._onError);

        return this.promise;
    };

    /**
     * Handles the response from the saveConfig  call
     *
     * @method _onSaveResponseReceived
     *
     * @param {HTMLDocument} data
     * @private
     */
    proto._onSaveResponseReceived = function(data) {
        if (!data) {
            throw new Error(data);
        }

        data = $.parseXML($.trim(data));
        var response = xmlToObject(data);

        if (response.error) {
            throw new Error(response.error);
        }
        if (response.uid) {
            var result = {};
            result.status = true;
            result.uid = response.uid;
            this.result = result;

            var querydata = Query.getInstance();
            querydata.cartConfigId = this.result.uid;
            return result;
        }
        //return response.addToCart.success === 'true' || response.addToCart.success === true;
    };

    /**
     * Handles the response from the getPrice call
     *
     * @method _onResponseReceived
     *
     * @param {HTMLDocument} data
     * @private
     */
    proto._onResponseReceived = function(data) {
        if (!data) {
            throw new Error(data);
        }

        data = $.parseXML($.trim(data));
        var response = xmlToObject(data);

        if (response.error) {
            throw new Error(response.error);
        }

        // console.log('AddToCart._onResponseReceived', this.result);

        // If result is set by config save return that data
        if (this.result) {
            return this.result;
        } else {
            return response.addToCart.success === 'true' || response.addToCart.success === true;
        }
    };

    return new AddToCartProvider();
});
