<div class="js-logo-filter js-logos-spinner spinner-container">
    <p class="logoMix-Bus-title">Add Your Business Information</p>
    <div class="line1">
        <label class="txtLarge">
            Text Line 1 (ex. Your Business Name)
            <input class="inputBox" type="text" id="line_1" value="{{line1}}" placeholder="Enter line one"
                maxlength="30">
            <span class="error js-error"></span>
        </label>
    </div>
    <div class="line2">
        <label class="txtLarge">
            Text Line 2 (ex. 25 Years of Service)
            <input class="inputBox" id="line_2" type="text" value="{{line2}}" placeholder="" maxlength="30">
            <span class="error js-error"></span>
        </label>
    </div>
    <div class="update-button">
        <button id="logoMixResults" class="button button_next button_neutral mix-results">UPDATE LOGO TEXT</button>
    </div>
    <div class="filterLogos">
        <p class="LogoMix-filter-title">Filter Logos</p>
        <div class="filter-by-style">
            <label class="txtLarge" for="style"> Filter by Style </label>
            <span class="inputBox inputBox_select">
                <select class="inputBox_select-input js-all-styles" name="quantity" id="style" placeholder="All Styles">
                    <option value="" id="QuantityCombobox-50">All Styles</option>
                    <option value="icon">Icon</option>
                    <option value="initial">Initial</option>
                    <option value="badge">Badge</option>
                    <option value="text">Text</option>
                </select>
            </span>
            <span class="error js-error"></span>
        </div>
        <div class="filter-by-layout js-all-layout showme">
            <label for="layout"> Filter by Layout </label>
            <span class="inputBox inputBox_select ">
                <select class="inputBox_select-input js-filter-layout" name="quantity" id="layout">
                    <option value="" id="QuantityCombobox-50">All Layouts</option>
                    <option value="bottom">Icon above</option>
                    <option value="arc">Curved Text</option>
                    <option value="left">Icon left</option>
                    <option value="right">Icon right</option>
                </select>
            </span>
            <span class="error js-error"></span>
        </div>
        <div class="search-logos">
            <label class="txtLarge" for="keyword"> Search Available Logos </label>
            <input class="inputBox mix-search-input" type="text" value="" placeholder="" id="keyword">
            <img class="searchicon" src="{{host_url}}/webasset/w2p_mobile/{{baseAppUrl}}assets/media/images/search.png" alt="Search" srcset="">
            <span class="error js-error"></span>
        </div>
    </div>
    <div class="js-logo-loading">
        <div class="uploading-spinner"></div>
    </div>

    <div>
        <h2 class="logo_name">Logo Selection</h2>
    </div>
    <div class="logo_border">
        <div class="selected-logo js-logo-selected logomix d-none" id="canvasparent">
            <canvas id="mix-canvas" width="165" height="165">
            </canvas>
        </div>
        <div class="no-logo-logomix">No Logo Selected</div>
    </div>
    <button class="button button_next button_neutral cancel_upload_logo_button">Cancel</button>
</div>