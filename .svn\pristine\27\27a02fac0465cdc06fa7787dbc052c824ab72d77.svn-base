{"name": "selectonic", "title": "Selectonic", "version": "0.4.2", "description": "jQuery-plugin for making any list of items selectable by mouse and keyboard.", "homepage": "https://github.com/anovi/selectonic", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "./dist/selectonic.js", "keywords": ["list", "j<PERSON>y", "selectable", "selection"], "repository": {"type": "git", "url": "https://github.com/anovi/selectonic.git"}, "bugs": "https://github.com/anovi/selectonic/issues", "licenses": [{"type": "MIT", "url": "https://github.com/anovi/selectonic/blob/master/LICENSE-MIT"}], "ignore": ["**/.*", "src", "libs", "benchmarks", "node_modules", "bower_components", "Gruntfile.js", "package.json", "test", "tests", "requirements"], "dependencies": {"jquery": ">= 1.7"}, "devDependencies": {"qunit": "~1.12.0", "jquery-1.7.0": "jquery#1.7.0", "jquery-1.9.0": "jquery#1.9.0", "jquery-1.10.2": "jquery#1.10.2", "jquery-2.0.3": "jquery#2.0.3", "benchmark": "~1.0.0", "syn": "~0.0.2"}}