define(function (require) { // jshint ignore:line
    'use strict';

    var $ = require('jquery');
    var AbstractCollection = require('../../collections/Abstract');
    var UiQuestionsCollection = require('./Questions');
    var UIStepModel = require('../Step');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var Query = require('models/Query');

    /**
     * @class App.Models.Ui.Collections.Steps
     * @extends App.Models.Collections.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} UIStepsCollection
     */
    var UIStepsCollection = function (UIStepsCollection) {
        bindAll(this,
            '_getQuestions',
            '_getPriceQuestions'
        );

        AbstractCollection.call(this, UIStepsCollection);
    };

    var proto = inherits(UIStepsCollection, AbstractCollection);

    /**
     * @property itemClass
     * @type {App.Models.Ui.Step}
     */
    proto.itemClass = UIStepModel;

    /**
     * @method getQuestions
     * @return {Array.<UiQuestionModel>}
     */
    proto.getQuestions = function() {
        var questions = this._items
            .reduce(this._getQuestions, []);

        return new UiQuestionsCollection(questions);
    };

    /**
     * @method getQuestions
     * @return {Array.<UiQuestionModel>}
     */
    proto.getPriceQuestions = function() {
        return this.getQuestions().getPriceAffected();
    };

    /**
     * @method _getQuestions
     * @return {Array.<UiQuestionModel>}
     */
    proto._getQuestions = function(list, step) {
        return list.concat(step.getQuestions()._items);
    };

    /**
     * Recursively sets product info on all steps.
     *
     * @method setInfo
     * @param {Object} info
     * @param {App.Models.FXG} fxg
     * @param {App.Models.CustomerProfile} customer
     * @chainable
     */
    proto.setInfo = function(info, fxg, customer) {
        this.each(this._setInfo.bind(this, info, fxg, customer));

        return this;
    };

    /**
     * @method _setInfo
     * @param {Object} info
     * @param {App.Models.FXG} fxg
     * @param {App.Models.CustomerProfile} customer
     * @param {App.Models.Ui.Step} step
     * @callback
     */
    proto._setInfo = function(info, fxg, customer, step) {
        step.setInfo(info, fxg, customer);
    };

    /**
     * Returns an object containing all selections made by
     * the user for all contained questions
     *
     * @method getValues
     * @returns {Object}
     */
    proto.getValues = function() {
        var results = {
            blocks: [],
            values: {},
            surcharges: []
        };

        this.each(this._getValues.bind(this, results));

        // For holiday card with no productId and quantity
        if (typeof results.values.productId === 'undefined' && typeof results.values.quantity === 'undefined' ) {
            var data = this.getUrlValues();
            results.values.productId = {};
            results.values.quantity = {};
            results.values.productId.value = data.productId;
            results.values.quantity.value = data.quantity;
        }

        return results;
    };

    /**
     * For holiday card products to get the productId and quantity
     *
     * @method setValues
     * @param {object} values
     * @chainable
     */
    proto.getUrlValues = function() {
        var queryParam = Query.getInstance();
        //console.log('jiby.Steps.queryParam', queryParam);
        var results = {};
        results.productId = queryParam.productId;
        results.quantity = queryParam.qty;
        return results;
    };

    /**
     * Sets values from incoming XML to product questions
     *
     * @method setValues
     * @param {object} values
     * @chainable
     */
    proto.setValues = function(values) {
        this.each(this._setValues.bind(this, values));
        return this;
    };

    /**
     * Appends a single question's value to an existing object
     *
     * @method _getValues
     * @param {object} results result set to append to
     * @param {App.Models.Ui.Step} step
     * @private
     */
    proto._getValues = function(results, step) {
        var values = step.getValues();
        $.extend(results.values, values.values);
        results.blocks = $.merge(results.blocks, values.blocks);
        results.surcharges = $.merge(results.surcharges, values.surcharges);
    };

    /**
     * Appends a single question's value to an existing object
     *
     * @method _setValues
     * @param {object} results
     * @param {App.Models.Ui.Step} step
     * @private
     */
    proto._setValues = function(results, step) {
        var values = step.getValues();
        $.extend(results.values, values.values);
        results.blocks = $.merge(results.blocks, values.blocks);
        results.surcharges = $.merge(results.surcharges, values.surcharges);
    };

    return UIStepsCollection;
});
