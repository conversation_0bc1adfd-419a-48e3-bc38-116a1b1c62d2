define(function(require) {
    'use strict';

    var bindAll = require('mout/object/bindAll');

    /**
     * Abstract state-stepper base class.
     *
     * @class App.Models.State.Abstract
     *
     * @constructor
     */
    function AbstractState() {
        bindAll(this,
            'add',
            'back',
            'forward',
            'pushState',
            'onChange'
        );

        /**
         * @property current
         * @type {Number}
         * @default -1
         */
        this.current = -1;

        /**
         * @property states
         * @type {Array.<Object>}
         * @default []
         */
        this.states = [];
    }

    var proto = AbstractState.prototype;

    // -- Accessors ------------------------------------------------------------

    /**
     * @method getState
     * @return {Object}
     */
    proto.getState = function() {
        return this.states[this.current];
    };

    // -- Methods --------------------------------------------------------------

    /**
     * @method add
     * @chainable
     */
    proto.add = function(state) {
        var states = this.states;

        // Handle empty states
        state = state || {};

        // Set state index
        state.index = states.length;

        // Handle unidentified states
        if (!state.id) {
            state.id = state.index + 1;
        }

        // Handle hashless states
        if (!state.hash) {
            state.hash = '#step-' + state.id;
        }

        // Add state
        states.push(state);

        return this;
    };

    /**
     * @throws {Error} Abstract error.
     *
     * @method start
     * @chainable
     */
    proto.start = function() {
        throw new Error('Abstract method not implemented.');
    };

    /**
     * @throws {Error} Abstract error.
     *
     * @method back
     * @chainable
     */
    proto.back = function() {
        throw new Error('Abstract method not implemented.');
    };

    /**
     * @throws {Error} Abstract error.
     *
     * @method forward
     * @chainable
     */
    proto.forward = function() {
        throw new Error('Abstract method not implemented.');
    };

    /**
     * @throws {Error} Abstract error.
     *
     * @method pushState
     * @chainable
     */
    proto.pushState = function() {
        throw new Error('Abstract method not implemented.');
    };

    // -- Event Handlers -------------------------------------------------------

    /**
     * @method onChange
     * @param {jQuery.Event} event
     * @throws {Error} Abstract error.
     * @callback
     */
    proto.onChange = function(event) {
        throw new Error('Abstract method not implemented.');
    };

    return AbstractState;
});
