<p class="txtLarge review_note mb5">
    <span class="hdg hdg_h2">{{mainheading}}</span>
</p>
{{#if logoCheck}}
    <span class="txtStd logoDescr">
        {{logoDesc}}
    </span>
{{/if}}

{{#if options}}
    <label id="designRadioId" for="designRadio" style="display:none;"> {{desc}}</label>
        <h2 class="hdg hdg_h2 mt10">{{desc}}
            {{#if toolTip}}
            <span id="primaryColorHelp" data-template='<div id="primary-popup" class="custpop popover" role="tooltip"><div class="arrow"></div><div class="popover-body"></div></div>' data-container="body" data-placement="bottom" data-content="" data-original-title="" title="" class="js-button-primary-action pop-space" alt="primaryColorHelp"></span>
            {{/if}}
        </h2>
        
   
    {{#if options.[0].descExtended }}
        <label  for="{{options.[0].slug}}" style="display:none;"> inkColor1 </label>
        
        <ul class="blocks colorblocks js-inkColors">
            <li>
                <input type="radio" class="inkcolorRadio inkRadioLabel" id="{{options.[0].slug}}" name="inkColor1" value="{{options.[0].id}}" checked />
                <label class="inkRadioLabel" for="{{options.[0].slug}}">
                    <span class="designBox toggle_rgb inkColorBox" style="background-color: rgb({{options.[0].rgbString}})"></span>
                    <span class="inkColorLabel">{{options.[0].desc}}</span>
                </label>
            </li>
        </ul>
        <div>
            <span class="txtStd">
                {{options.[0].descExtended}}
            </span>
        </div>
            {{!-- <div class="media-img media-img_small">
                <div class="designBox designBox_active" style="background-color: rgb({{options.[0].rgbString}})"></div>
            </div>
            <div class="media-body">
                <h3 class="hdg hdg_h4">
                    {{options.[0].desc}}
                </h3>
                <span class="txtStd">
                    {{options.[0].descExtended}}
                </span>
            </div> --}}
        {{!-- </div> --}}
        
    {{else}}
        <ul class="blocks colorblocks js-inkColors">
            {{#each options}}
                <li>
                    <input type="radio" class="{{#if ../single_flag}}hide-me {{/if}} inkcolorRadio inkRadioLabel {{#is id ../default}} isChecked{{/is}} js-question-option" id="{{slug}}" name="{{../id}}" value="{{id}}"{{#is id ../default}} checked{{/is}} />
                    <label class="inkRadioLabel" for="{{slug}}">
                        {{> question/required}}
                        <span class="designBox toggle_rgb inkColorBox" style="background-color: rgb({{rgbString}});{{#if colorContrastRgbString}} border-color: rgb({{colorContrastRgbString}}){{/if}}"></span>
                        <span class="inkColorLabel">
                            {{desc}}
                        </span>
                        
                    </label>
                </li>
            {{/each}}
        </ul>
    {{/if}}
{{/if}}
