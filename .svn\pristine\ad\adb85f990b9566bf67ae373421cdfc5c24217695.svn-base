/* ---------------------------------------------------------------------
 Tooltip Styles
------------------------------------------------------------------------ */

/* Number of Copies Popup -- starts*/
.popover{
	position:absolute;
	top:0;
	left:0;
	z-index:1010;
	display:none;
	width:432px;
	padding:1px;
	text-align:left;
	white-space:normal;
	-webkit-box-shadow:0 5px 10px rgba(0,0,0,.2);
	box-shadow:0 5px 10px rgba(0,0,0,.2);
	background-clip:padding-box
}
.popover.bottom{
	margin-top:15px;
	margin-left:-131.546875px
}
.popover-heading{
	font-weight:700;
	font-family: source sans pro;
	font-size:15px;
	color:#333;
	
}
.popovertitle{
	font-size :20px;
	font-family: source sans pro;
	color:#333;
	font-weight:bold;
	margin-bottom: 0px; 
}
.popover-info{
	font-size :15px;
	font-family: source sans pro;
	color:#333;
	font-weight:100;
	padding-top:5px;
	padding-bottom:5px;
	margin-bottom: 15px;
}
.popover-title{
	padding:8px 14px;
	margin:0;
	font-size:14px;
	font-family: source sans pro;
	font-weight:400;
	line-height:18px;
	background-color:#f7f7f7;
	border-bottom:1px solid #ebebeb;
	border-radius:5px 5px 0 0;
	padding-bottom:5px
}
.popover-body{
	padding:15px 22px 22px 22px !important;
	font-size:15px;
	line-height: 22px;
}

.popover .arrow:after {
	border-bottom-color: #3174D8 !important;
	border-top-color: #3174D8 !important;
}

.close{
	float:right;
	font-size:21px !important;
	font-weight:700;
	font-family: source sans pro;
	color:#000 !important;
	text-shadow:0 1px 0 #fff !important;
	filter:alpha(opacity=20);
	position:relative;
	top:-2px;
	right:-21px;
	line-height:20px;
	margin-top:5px;
	margin-right:-1px
}
.close:hover,.close:focus {
  color: #000000;
  text-decoration: none;
  cursor: pointer;
  filter: alpha(opacity=50);
} 
.fade{
	opacity:1;
	-webkit-transition:opacity 0.15s linear;
	-moz-transition:opacity 0.15s linear;
	-o-transition:opacity 0.15s linear;
	transition:opacity 0.15s linear;
}
.fade.in{opacity:1;}

.tdsec td{padding-top : 25px;}
/* Number of Copies Popup -- ends*/
/* Reverse number popup --starts*/
.custpop{
	position:absolute;
	top:0;
	left:0;
	z-index:1010;
	display:none;
	width:450px !important;
	padding:1px;
	text-align:left;
	white-space:normal;
	background-color:#3174D8 !important;
	border:5px solid #fff;
	-webkit-box-shadow:0 5px 10px rgba(0,0,0,.2);
	box-shadow:0 5px 10px rgba(0,0,0,.2);
	background-clip:padding-box;
	max-width: none !important;
}
.custpop.bottom{
	margin-top:15px;
	margin-left:-146px
}
.custpop .arrow1,.custpop .arrow1:after{
	position:absolute;
	display:block;
	width:0;
	height:0;
	border-color:transparent;
	border-style:solid;
}
.custpop .arrow1{border-width:11px}

.custpop .arrow1:after{
	border-width:10px;
	content:"";
}
.custpop.bottom .arrow1{
	margin-left:342px;
	border-bottom-color:#fff;
	border-top-width:0;
	top: -11px;
}
.pop-copy.bottom .arrow1{
	margin-left:345px;
}
.custpop.bottom .arrow1:after{
	top:1px;
	margin-left:-10px;
	border-bottom-color:#aedfe8;
	border-top-width:0;
	content:" ";
}
.othertxt{
	font-weight: normal;
	font-size: 15px;
	color: #ffffff;
	line-height: 22px;
	font-family: source sans pro;
	margin-bottom: 0px !important;
}
.titletxt
{
	font-weight: bold;
	font-size: 20px;
	color: #ffffff;
	font-family: source sans pro;
}
#CopiesHelp, #NumberingHelp, #routingNumberHelp, #accountNumberHelp, #routingNameHelp, #accountNameHelp, #otaccountNameHelp, #voucherHelp, #signatureTextHelp, #sideMarginHelp, #primaryColorHelp, #SignatureHelp, #bankNameHelp, #paymentTermsHelp
{
	cursor:pointer;
}

/* Reverse number popup --ends*/
#pcHelpTable{
	width: 100%;
	font-family: 'Arial Regular', 'Arial';
    font-weight: 400;
    font-style: normal;
    font-size: 13px;
    color: #000f0c;
    text-align: left;
    line-height: normal;
}
#pcHelpTable td{
	padding: 10px;
}
#pcHelpTable tr:nth-child(even) {
	padding: 15px;
	background: #E7E7E7;
}

#pcHelpTable tr:nth-child(odd) {
	padding: 15px;
	background: #FFF;
}

.boldText {
	font-weight	: bold !important;
}

.tooltip-close {
	position:absolute;
	top:0;
	right: 7px;
}

.sub-pop .tooltip-close, .popover-close {
	width: 20px;
	line-height: 20px;
}
.othertxt-bold {
	font-weight:bold !important;
}

.subtotal-title {
    font-weight: bold;
    font-size: 22px;
    color: #333333;
    font-family: source sans pro;
}

.price-summary-tooltip.sub-pop {
    background-color: #efefef !important;
    border: 10px solid #827b7b !important;
	position: fixed !important;
	transform: none !important;
	top: auto !important;
	bottom: 95px !important;
	left: 0 !important;
	max-width: 100% !important;
	width: 100% !important;
}

.subtotal-price {
	height: auto Im !important;
}

.price-summary-tooltip {
	display: none;
}

.price-summary-popover {
	margin: 22px;
}