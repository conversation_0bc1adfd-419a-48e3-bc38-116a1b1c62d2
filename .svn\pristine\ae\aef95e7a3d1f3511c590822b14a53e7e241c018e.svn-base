define(function (require) {
    'use strict';

    var $ = require('jquery');
    var AbstractModel = require('../../Abstract');
    var CartProductImprintBlocksCollection = require('./collections/ImprintBlocks');
    var RegEx = require('../../../constants/RegEx');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.Cart.Product.ImprintLocation
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} CartProductValues
     */
    var CartProductImprintLocationModel = function (CartProductValues) {
        AbstractModel.call(this, CartProductValues);
    };

    var proto = inherits(CartProductImprintLocationModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} values
     * @chainable
     */
    proto.init = function(values) {

        /**
         * @property id
         * @default {null}
         * @type {string}
         */
        this.id = null;

        /**
         * @property description
         * @default {null}
         * @type {string}
         */
        this.description = null;

        /**
         * @property imprintTypestyle
         * @default {null}
         * @type {number}
         */
        this.imprintTypestyle = null;

        /**
         * @property imprintType
         * @default {null}
         * @type {string}
         */
        this.imprintType = null;

        /**
         * @property blocks
         * @default {null}
         * @type {App.Models.Cart.Product.Collections.ImprintBlocks}
         */
        this.blocks = null;

        base.init.call(this, values);

        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param json the original JSON data.
     */
    proto.fromJSON = function(json) {
        this.id = json.locationId;
        this.description = json.locationDesc;
        this.imprintTypestyle = json.imprintTypestyle;
        this.imprintType = json.imprintType;
        this.blocks = new CartProductImprintBlocksCollection(json.imprintBlocks);

        // Deluxe productInfoLookup returns a single digit when editing an order,
        // even though we send '06' in the initial addToCart request.
        if (this.imprintTypestyle && this.imprintTypestyle.length === 1) {
            this.imprintTypestyle = '0'+this.imprintTypestyle;
        }

        this.availableValues = {
            typestyle: this.imprintTypestyle
        };
    };

    /**
     * Converts all data into an XML string for sending
     * to the API
     * @method toXML
     * @return {string}
     */
    proto.toXML = function() {
        var xml;
        var $layout = $('<imprintLocations/>')
            .append($('<locationId/>').text(this.id))
            .append($('<description/>').text(this.locationDesc))
            .append($('<imprintTypestyle/>').text(this.imprintTypestyle))
            .append($('<imprintType/>').text(this.imprintType))

            .append(this.blocks.toXML());

        xml = $layout[0].outerHTML;
        return xml;
    };

    /**
     * Returns all associated blocks so that we can pass them up
     * to the parent Cart Product model, which can then be used
     * to apply the imprint block values to associated UI Question Blocks
     * in an unconfigured product.
     *
     * @returns {Object}
     */
    proto.getBlocks = function() {
        var response = {};
        var i;
        var _getInput = function() {
            return this.userInput;
        };
        var _getValue = function() {
            return this.value;
        };

        if (!this.blocks) {
            this.availableValues = {};
            return response;
        }

        this.blocks.each(function(block) {
            var inputs;
            var input;
            var item;
            var blockCode = block._data.blockCode;

            // Pull the logo details out as a top level block element.
            if (block._data.selectedLogoDetails) {
                response['logo'] = block._data.selectedLogoDetails;
            }

            response[blockCode] = block;
            inputs = block._data.userInput;

            // imprintBlocks that contain only 1 userInput node are
            // cast to objects, so turn it into an array so they
            // are processed correctly below.
            if (inputs && typeof inputs === 'object') {
                inputs = [].concat(inputs);
            }

            if (inputs && Array.isArray(inputs)) {
                for (i = 0; i < inputs.length; i++) {
                    input = inputs[i];
                    if (input.lineCode === 'CI') {
                        input.userInput=(input.userInput).replace(/^,|,$/g,'');
                        /*var CIData = RegEx.CITY_STATE.exec(input.userInput);
                        if (CIData) {
                            input.userInput = {
                                city: CIData[1],
                                state: CIData[2],
                                zipCode: CIData[3]
                            };
                        }*/
                    }
                    // BI Block always needs to be an array, but if the user only
                    // enters a Bank Name, them it'll come back as a string from
                    // the parser, so cast it to an array so it re-populates.
                    if (input.lineCode === 'BI' && typeof input.userInput === 'string') {
                        input.userInput = [input.userInput];
                    }
                    if(!response[input.lineCode] || (response[input.lineCode] && (!response[block.id]._data || response[input.lineCode]._data && !input._data)))
                    response[input.lineCode] = input;
                    input.getValue = _getInput.bind(input);
                }
            }
            if (inputs && Array.isArray(inputs.userInput)) {
                for (i=0; i < inputs.userInput.length; i++) {
                    input = inputs.userInput[i];
                    item = {
                        getValue: _getValue,
                        value: input
                    };
                    response[inputs.lineCode + '_' + (i + 1)] = item;
                }
            }
        });

        return response;
    };

    /**
     *
     * @param {string} name
     * @returns {*}
     */
    proto.getValue = function(name) {
        return this.availableValues[name];
    };



    return CartProductImprintLocationModel;
});
