define(function(require) {
    'use strict';
    require('bootstrap');
    var AbstractQuestionController = require('./Abstract');
    var DomEvents = require('../../constants/DomEvents');
    var ProductEvents = require('../../constants/ProductEvents');
    var EventController = require('../Event');
    var Classes = require('../../constants/Classes');
    var ActionEvents = require('../../constants/ActionEvents');
    var Content = require('i18n!../../constants/nls/en-us/Content');
    var Settings = require('../../constants/Settings');
    var inherits = require('mout/lang/inheritPrototype');
    var CheckboxModel = require('../../models/Checkbox');
    var Surcharge = require('util/Surcharge');
    var currency = require('util/currencyFormat');
    var bindAll = require('mout/object/bindAll');
    var SessionStorage = require('../../providers/SessionStorage');
    var $ = require('jquery');
    var Helper = require('../../util/helper');
   
     /**
     * @class App.Controllers.Question.Checkbox
     * @extends App.Controllers.Question.Abstract
     *
     * @constructor
     * @param {Object} config
     * @param {jQuery} config.view
     * @param {Models.UI.Question} config.model
     */
    function CheckboxQuestionController(config) {
        bindAll(this,
            'onChange',
            'onButtonClick',
            'onProductChange',
            'onReloadClick'
        );
        AbstractQuestionController.call(this, config);
    }

    var proto = inherits(CheckboxQuestionController, AbstractQuestionController);
    var cv_flag = false;
    var priceFor100, unitPriceFold;
    /**
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = require('hbs!templates/question/checkbox');

    /**
     * @method addToCartTemplate
     * @param {Object} model
     * @return {String}
     */
    proto.addToCartTemplate = require('hbs!templates/question/checkbox/addToCart');

    /**
     * @method render
     * @chainable
     */
    proto.render = function() {
        var model = this.model;
        if (model.id === 'addToCart') {
            return this.renderAddToCart();
        }
        return this.renderDefault();
    };
    
    /**
     * @method getCheckboxStatus
     * @chainable
     */
    proto.getCheckboxStatus = function(){
        var model = this.model;
        var value = '';
        //console.log('fff', value);
        var valueMap = {'yes': 'Y', 'no' : 'N'};
        if (model.id === 'folding' || model.id === 'signatures') {
            var dropDown = "";
            dropDown = this.$view.find('input[type=checkbox]');
            if (dropDown.prop('checked')) {
                value = 'yes';
            } else {
                value = 'no';
            }
            if (model.id === 'folding') {
                CheckboxModel.folding = value;
                EventController.emit(ProductEvents.FOLDING_CHANGE, valueMap[value]);
            } else {
                CheckboxModel.signature = value;
                EventController.emit(ProductEvents.SIGNATURE_CHANGE, valueMap[value]);
            }
        }
        return value;
    };

    /**
     * @method cacheElements
     * @chainable
     */
    proto.cacheElements = function() {
        var $view = this.$view;
        this.$reverseNumberingCheckBox = $view.find('#reverseNumbering');
        // this.$view
        //     .off(DomEvents.CHANGE, this.onChange)
        //     .off(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick);
        return this;
    };

    /**
     * @method renderDefault
     * @chainable
     */
    proto.renderDefault = function() {
        var model = this.model;
        var info = model.info;
        if (model.desc.indexOf(Content.get('reverseNumbering')) > -1) {
            model.noc = true;
        } else {
            model.noc = false;
        }
        model.anotherSignature = (model.id === "SL") ? true : false;
        model.folding = (model.id === 'folding') ? true : false;
        model.signatures = (model.id === 'signatures') ? true : false;
        model.ENVELOPE_RETURN = (model.id === 'ENVELOPE_RETURN') ? true : false;
        model.className = (model.folding || model.signatures || model.ENVELOPE_RETURN) ? true : false;
        model.classNameN = !model.className;
        model.ENVELOPE_IMPRINT_COPY = Content.get(Settings.SITE_CORP + '_COPY_ENVELOPE_IMPRINT');
        model.ENVELOPE_IMPRINT_SHOW_GRID = Settings.ENVELOPE_IMPRINT_SHOW_GRID;
        //model.FOLDING_COPY = Content[Settings.SITE_CORP + '_COPY'].FOLDING;
        if (model.id === 'folding' && info && info.option){
             var surchargeId = info.option[1].surcharge.id;
             priceFor100 = Surcharge.getPrice(surchargeId, false, 100);
             var priceFor125 = Surcharge.getPrice(surchargeId, true, 125);
             unitPriceFold = currency(priceFor125 / 125 , 0);
        }
        var foldingCopy = Settings.SITE_CORP + '_COPY_FOLDING';
        model.FOLDING_COPY = Content.get(foldingCopy,{value: priceFor100, unitValue: unitPriceFold});
        model.baseAppUrl = baseAppUrl;
        model.host_url = host_url;
        model['default'] = model.getValue() || (info && info['default']);
        model['imprintingPrice'] = Surcharge.getPrice('ENVIM', true) ? Surcharge.getPrice('ENVIM', true) : '';
        // model['fieldValue'] = model.getValue() || (info && info['default']);
        this.$view.html(this.template(model));

        if (model.id === 'signatures') {
            var value = this.getCheckboxStatus();
            if (value == 'yes') {
                this.model.setValue('SIGN');
            } else {
                this.model.setValue(value);
            }
        } else if (model.id === 'folding') {
            this.model.setValue(this.getCheckboxStatus());
        } else if (model.id === 'ENVELOPE_RETURN') {
            setTimeout(function() {
                var eiBlock = $('li[data-id="EI"]');
                if ($('#ENVELOPE_RETURN').is(':checked')) {
                    eiBlock.addClass('selectedBorder');
                    eiBlock.addClass('marginer');

                    eiBlock.find('input:text').each(function() {
                        $(this).removeAttr("disabled");
                    });
                    eiBlock.find('select').removeAttr("disabled");
                    $(".address_container").hide();
                    $(".address_container_selected").show();
                    //Imprint fields will display when the checkbox is checked
                    eiBlock.show();
                } else {
                    $("input[name^='EI'],select[name^='EI']").prop('disabled', 'disabled');
                    $(".address_container").show();
                    $(".address_container_selected").hide();

                    eiBlock.removeClass('selectedBorder');
                    eiBlock.find('input:text').each(function() {
                        $(this).attr("disabled", "disabled");
                    });
                    eiBlock.find('select').prop('disabled', 'disabled');
                    //Imprint fields will hide when the checkbox is unchecked
                    eiBlock.hide();
                }
            }, 500);
        } else if (model.id === 'reverseNumbering') {
            var reverseNumberingValues = ['false', 'reverseNumbering', '', null ]
            if( !reverseNumberingValues.includes(model.value)) {
                $('#reverseNumbering').trigger('click');
            }
        }
        return this;
    };

    /**
     * @method renderAddToCart
     * @chainable
     */
    proto.renderAddToCart = function() {
        var model = this.model;
        var info = model.info;
        var value = model.getValue() || (info && info['default']);
        var subtotal = $('[data-id="COMMENT"]');
        var listClass = this.$view.parent()[0].className;
        var approvalCopyKey = Settings.SITE_CORP + '_COPY_REVIEW';
        // reorder outside of regular step order
        // add to cart specifically appears after subtotal
        this.$view
            .detach()
            .html(this.addToCartTemplate({
                id: model.id,
                desc: model.desc,
                isRequired: model.isRequired,
                'default': value,
                company: Settings.SITE_CORP,
                approvalCopy: Content.get(approvalCopyKey)
            }))
            .wrap('<ul class="' + listClass + '"></ul>')
            .hide()
            .parent()
            .insertAfter(subtotal);
        return this;
    };

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        this.$view
            .on(DomEvents.CHANGE, this.onChange)
            .on(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick);
        EventController
            .on(ProductEvents.CHANGE, this.onProductChange)
            .on(ActionEvents.RELOAD_ADDTOCART_REVIEW_TEMPLATE, this.onReloadClick);
        return this;
    };

    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        this.$view
            .off(DomEvents.CHANGE, this.onChange)
            .off(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick);
        return this;
    };

    proto.onReloadClick = function() {
        this.model.value = undefined;
        this.render();
    }

    /**
     * @method onChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onChange = function() {
        var model = this.model;
        var value = this.$view.find('input:checked').val();
        if (model.id === 'folding' || model.id === 'signatures') {
            value = this.getCheckboxStatus();
        }
        if (model.id === 'ENVELOPE_RETURN') {

            var eiBlock = $('li[data-id="EI"]');

            var dropDown = "";
            dropDown = this.$view.find('input[id=ENVELOPE_RETURN]');

            if (dropDown.is(':checked')) {
                value = 'GCENVELOPE';
                eiBlock.addClass('selectedBorder');
                eiBlock.addClass('marginer');

                eiBlock.find('input:text').each(function() {
                    $(this).removeAttr("disabled");
                });
                eiBlock.find('select').removeAttr("disabled");
                $(".address_container").hide();
                $(".address_container_selected").show();
                CheckboxModel.envelopeReturn = value;
                EventController.emit(ProductEvents.ENVELOPE_RETURN_CHANGE, value);
                //Imprint fields will display when the checkbox is checked.
                eiBlock.show();
            } else {

                $(".address_container").show();
                $(".address_container_selected").hide();

                value = null;
                eiBlock.removeClass('selectedBorder');
                eiBlock.find('input:text').each(function() {
                    $(this).attr("disabled", "disabled");
                });
                eiBlock.find('select').prop('disabled', 'disabled');
                CheckboxModel.envelopeReturn = value;
                EventController.emit(ProductEvents.ENVELOPE_RETURN_CHANGE, value);
                //Imprint fields will hide when the checkbox is unchecked.
                eiBlock.hide();
            }
        }
        if (model.id != 'signatures') {
            this.model.setValue(value);
        } else {
            if (value == 'yes') {
                this.model.setValue('SIGN');
            } else {
                this.model.setValue(' ');
            }
        }

        this.isValid();
    };
    
    $(window).resize(function() {
      var b = $('#NumberingHelp').offset();
      if(b){
        var tp1 = b.top;
        var lt1 = b.left;
        $('.custpop').offset({top: tp1+32 ,left: lt1-360});
        $('.arrow').offset({top: tp1+26,left:lt1-8});
      }
    });

    /**
     * @method onButtonClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onButtonClick = function(event) {
        var model = this.model;
        if(Helper.isSameTooltipClick(event)){
            if($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
        } else {
            if($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
            if (model.id === 'ENVELOPE_RETURN') {
                $('#returnHelp').popover({
                    html: true,
                    trigger: "manual",
                    placement: 'bottom',
                    content: function () {
                        return $('#returnPopup').html();
                    },
                });
                if (!cv_flag) {
                    $('#returnHelp').popover('show');
                } else {
                    $('#returnHelp').popover('hide');
                }
                cv_flag = !cv_flag;

                $("#foldingCloseIcon.close.tooltip-close").on("click", function () {
                    cv_flag = false;
                    $('#returnHelp').popover('hide');
                });
            }

            if (model.id === 'folding') {
                $('#foldingHelp').popover({
                    html: true,
                    trigger: "manual",
                    placement: 'bottom',
                    content: function () {
                        return $('#foldingPopup').html();
                    },
                });
                if (!cv_flag) {
                    $('#foldingHelp').popover('show');
                } else {
                    $('#foldingHelp').popover('hide');
                }
                cv_flag = !cv_flag;

                $("#foldingCloseIcon.close.tooltip-close").on("click", function () {
                    cv_flag = false;
                    $('#foldingHelp').popover('hide');
                });
            } else if (model.noc === false && model.id === "SL") {
                $('#SignatureHelp').popover({
                    html: true,
                    trigger: "manual",
                    placement: "bottom",
                    content: function () {
                        var message = '<div><img src="' + host_url + '/webasset/w2p_mobile/' + baseAppUrl + 'assets/media/images/tooltip-close-new.png" id="SignCloseIcon" alt="SignCloseIcon" class="close tooltip-close" style="position: absolute; top: 0; right: 7px;"/>';
                        message = message + "<div class='titletxt'>Add Another Signature</div><br/>";
                        message = message + "<p class='othertxt'>Adding a second signature line to a check indicates the company requires both signatures to agree to payment. ";
                        message = message + "Adding dual signatures can help increase confidence in payment disbursement</p>";
                        return message + "</div>";
                    }
                });
                if (!Settings.ANOTHER_SIGNATURE_FLAG) {
                    $('#SignatureHelp').popover('show');
                }
                else {
                    $('#SignatureHelp').popover('hide');
                }
                Settings.ANOTHER_SIGNATURE_FLAG = !Settings.ANOTHER_SIGNATURE_FLAG;

                $('#SignCloseIcon').click(function (e) {
                    Settings.ANOTHER_SIGNATURE_FLAG = false;
                    $('#SignatureHelp').popover('hide');
                });
            } else {
                $('#NumberingHelp').popover({
                    html: true,
                    trigger: "manual",
                    placement: 'bottom',
                    content: function () {
                        var msg0 = '<div><img src="' + host_url + '/webasset/w2p_mobile/' + baseAppUrl + 'assets/media/images/tooltip-close-new.png" id="NumberingCloseIcon" alt="NumberingCloseIcon" class="close tooltip-close" style="position: absolute; top: 0; right: 7px;"/>';
                        var msg1 = "<div class='titletxt'>Reverse Start Number Order</div><br/>";
                        var msg2 = "<div class='othertxt othertxt-bold'>What is it, and Why would I need it?</div>";
                        var msg3 = "<p class='othertxt'>The numbers following the number that you enter will be in consecutive, descending order.</p><br/>";
                        var msg4 = "<p class='othertxt'>Some printers require stock to be loaded and fed with the</p>";
                        var msg5 = "<p class='othertxt'>numbering in reverse.</p><br/>";
                        var msg6 = "<p class='othertxt'>Standard Numbering - Lowest # at the top, Face Up</p>";
                        var msg7 = "<p class='othertxt'>Reverse Numbering - Lowest # at the top, Face Down</p><br/>";
                        var msg8 = "<div class='titletxt'>Less than 5% of our customers use reverse numbering.</div>";
                        return msg0 + msg1 + msg2 + msg3 + msg4 + msg5 + msg6 + msg7 + msg8 + "</div>";
                    },
                });
                if (!Settings.REVERSE_FLAG) {
                    $('#NumberingHelp').popover('show');
                } else {
                    $('#NumberingHelp').popover('hide');
                }
                Settings.REVERSE_FLAG = !Settings.REVERSE_FLAG;

                $('#NumberingCloseIcon').click(function (e) {
                    Settings.REVERSE_FLAG = false;
                    $('#NumberingHelp').popover('hide');
                });
            }
        }
    };

    /**
     * @method onProductChange
     * @param {jQuery.Event} event
     * @param {App.Models.Product} product
     * @callback
     */
    proto.onProductChange = function(event, product) {
        //if product xml doesn't have the folding or signature question, hide & deselect.
        if (this.model.id === 'folding' || this.model.id === 'signatures') {
            var hasFolding = false;
            var hasSignatures = false;
            var questions = [].concat(product.info.productInfo.question);
            for (var i=0; i<questions.length; i++) {
                if (questions[i].id == 'folding') {
                    hasFolding = true;
                } else if (questions[i].id == 'signatures') {
                    hasSignatures = true;
                }
            }
            if (this.model.id === 'folding' && !hasFolding) {
                this.$view.hide();
            }
            if (this.model.id === 'signatures' && !hasSignatures) {
                this.$view.hide();
            }
        }
        EventController.off(ProductEvents.CHANGE, this.onProductChange);
    };

    return CheckboxQuestionController;
});
