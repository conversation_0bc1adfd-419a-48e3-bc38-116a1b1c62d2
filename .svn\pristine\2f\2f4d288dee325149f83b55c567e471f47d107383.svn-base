define(function (require) {
    'use strict';

    var AbstractCollection = require('../../collections/Abstract');
    var UIQuestionBlockModel = require('../QuestionBlock');
    var inherits = require('mout/lang/inheritPrototype');
    var toLookup = require('mout/array/toLookup');
    var isLogo = false;

    /**
     * @class App.Models.Ui.Collections.QuestionBlocks
     * @extends App.Models.Collections.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} blocks
     */
    var UIQuestionBlocksCollection = function (blocks) {
        AbstractCollection.call(this, blocks);
    };

    var proto = inherits(UIQuestionBlocksCollection, AbstractCollection);

    /**
     * @property itemClass
     * @type {App.Models.Ui.QuestionBlock}
     */
    proto.itemClass = UIQuestionBlockModel;

    /**
     * Recursively sets product info on all steps.
     *
     * @method setInfo
     * @param {Object} info
     * @param {Object} product
     * @param {App.Models.CustomerProfile} customer
     * @chainable
     */
    proto.setInfo = function(info, fxg, product, customer) {
        var option = info && info.option;
        if (option && option.location)
        {
            var blockIndex = {};
            var locations = option && [].concat(option.location);
            for (var i=0; i<locations.length; i++)
            {
                var thisLocation = locations[i];
                var blocks = [].concat(thisLocation.block);
                //console.log("QuestionBlocks.location.id: " + thisLocation.id + " : " + blocks.length);
                for (var ii=0; ii<blocks.length; ii++)
                {
                    var thisBlock = blocks[ii];
                    if (thisLocation.surcharge) {
                        thisBlock.surcharges = [].concat(thisLocation.surcharge);
                    }
                    //console.log("QuestionBlocks.block.id: " + thisBlock.id);
                    blockIndex[thisBlock.id] = thisBlock;
                }
            }
            //console.log("QuestionBlocks.blockIndex: ", blockIndex);
            this.each(this._setInfo.bind(this, blockIndex, info, fxg, product, customer));
        }
        return this;
    };

    /**
     * @method _setInfo
     * @param {Object} blockIndex
     * @param {Object} question
     * @param {Object} product
     * @param {App.Models.CustomerProfile} customer
     * @param {App.Models.Ui.QuestionBlock} block
     * @callback
     */
    proto._setInfo = function(blockIndex, question, fxg, product, customer, block) {
        var info = blockIndex[block.id];

        block.setInfo(info, question, product, fxg, customer,isLogo);
        if(block){
            isLogo =  isLogo || block.hasLogo;
        }
    };

    /**
     * Gets blocks that have info. Useful to limit what is displayed to the user
     * as blocks without info should not be displayed.
     *
     * @method getBlocksWithInfo
     * @return {Array.<App.Models.Ui.QuestionBlock>}
     */
    proto.getBlocksWithInfo = function() {
        return this._items.filter(this._getBlocksWithInfo);
    };

    /**
     * Determines whether a block has info.
     *
     * @method _getBlocksWithInfo
     * @return {Boolean}
     */
    proto._getBlocksWithInfo = function(block) {
        return !!block.info;
    };

    /**
     * Returns an object containing all selections made by
     * the user for all contained questions
     *
     * @method getValues
     * @returns {Object}
     */
    proto.getValues = function() {
        var results = [];
        this.each(this._getValues.bind(this, results));
        return results;
    };

    /**
     * Appends a single question's value to an existing object
     *
     * @method _getValue
     * @param {object} results result set to append to
     * @param {App.Models.Ui.Tab} block
     * @private
     */
    proto._getValues = function(results, block) {
        var blockValues = block.getValues();

        if (blockValues) {
            results.push(blockValues);
        }
    };

    /**
     * @method isValid
     * @return {Boolean}
     */
    proto.isValid = function () {
        var blocks = this._items;
        var length = blocks.length;
        var i = 0;

        for (; i < length; i++) {
            if (blocks[i].isValid() !== true) {
                return false;
            }
        }

        return true;
    };

    return UIQuestionBlocksCollection;
});
