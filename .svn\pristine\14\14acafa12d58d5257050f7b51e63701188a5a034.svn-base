define(function (require) {
    'use strict';

    var AbstractProvider = require('./Abstract');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Providers.RenderBackground
     * @extends App.Providers.Abstract
     *
     * @constructor
     */
    var RenderBackgroundProvider = function () {
        AbstractProvider.call(this);
    };

    var proto = inherits(RenderBackgroundProvider, AbstractProvider);

    /**
     * Get Image URL
     *
     * @method getBackgroundUrl
     * @param {string} color
     * @return {string}
     */
    proto.getBackgroundUrl = function(color) {
        var endpoint;

        if (!color || typeof color !== 'string') {
            throw new Error();
        }

        endpoint = Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE + color;

        return endpoint;
    };

    /**
     * @method getSwatchesByProduct
     * @param {App.Models.ProductInfo} product
     * @return {Array}
     */
    proto.getSwatchesByProduct = function(product) {
        var results = [];
        var i;
        for (i = 0; i < product.matrixInfo.length; i++) {
            var color = product.matrixInfo[i];
            if (color.id.indexOf(product.productId) === 0) {
                results.push(this.getBackgroundUrl(color.swatch));
            }
        }
        return results;
    };

    /**
     * @method getBaseImagesByProduct
     * @param {App.Models.ProductInfo} product
     * @return {Array}
     */
    proto.getBaseImagesByProduct = function(product) {
        var results = [];
        var i;
        for (i = 0; i < product.matrixInfo.length; i++) {
            var color = product.matrixInfo[i];
            if (color.id.indexOf(product.productId) === 0) {
                results.push(this.getBackgroundUrl(color.baseImg));
            }
        }
        return results;
    };

    return new RenderBackgroundProvider();
});
