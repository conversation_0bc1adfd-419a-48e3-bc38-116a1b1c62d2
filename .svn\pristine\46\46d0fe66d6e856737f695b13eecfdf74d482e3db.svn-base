define(function(require) {
    'use strict';

    var $ = require('jquery');
    var Classes = require('../../constants/Classes');
    var ConfigurationProvider = require('../../providers/Configuration');
    var Content = require('i18n!../../constants/nls/en-us/Content');
    var AbstractQuestionController = require('./Abstract');
    var DomEvents = require('../../constants/DomEvents');
    var SessionStorage = require('../../providers/SessionStorage');
    var EventController = require('../Event');
    var ProductEvents = require('../../constants/ProductEvents');
    var InkColorsProvider = require('../../providers/InkColors');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var Settings = require('../../constants/Settings');
    var Surcharge = require('../../util/Surcharge');
    var ActionEvents = require('../../constants/ActionEvents');
    var Helper = require('../../util/helper');

    /**
     * @class App.Controllers.Question.Rgb
     * @extends App.Controllers.Question.Abstract
     *
     * @constructor
     * @param {Object} config
     * @param {jQuery} config.view
     * @param {Models.Ui.Question} config.model
     */
    function RgbQuestionController(config) {
        bindAll(this,
            'initColors',
            'setColors',
            'onEnabledChange',
            'onRadioChange',
            'onButtonClick',
            'onPrimaryColorButtonClick',
            'changeQuantity',
            'onProdChange',
            'callAfterAll',
            'onQntyChange',
            'onCheckboxChange'
        );
        this.logoCheck = false;
        this.pdtQty = 0;
        AbstractQuestionController.call(this, config);
    }

    var proto = inherits(RgbQuestionController, AbstractQuestionController);

    /**
     * The following config call will be refactored to not
     * require an extra provider call.
     *
     * @method init
     * @return {Promise}
     */
    proto.init = function() {
        //console.log('JoeTest.Rgb.init');
        return ConfigurationProvider
            .getConfiguration()
            .then(this.initColors)
            .then(this.setColors);
    };

    proto.callAfterAll = function () {

        EventController.emit(ActionEvents.REQUEST_LOGO_STATE_REP);

    }

    /**
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = require('hbs!templates/question/rgb');

    /**
     * @method toggleTemplate
     * @param {Object} model
     * @return {String}
     */
    proto.toggleTemplate = require('hbs!templates/question/rgb/toggle');

    /**
     * @method initColors
     * @return {Promise.<App.Models.Collections.Colors>}
     */
    proto.initColors = function(config) {
        return InkColorsProvider
            .setConfig(config)
            .getColors();
    };

    /**
     * @method setColors
     * @param {App.Models.Collections.Colors} colors
     * @chainable
     */
    proto.setColors = function(colors) {
        this.colors = colors;
        this.model.options.each(this._setColor.bind(this, colors));
        return this;
    };

    /**
     * @method _setColor
     * @param {App.Models.Collections.Colors} colors
     * @param {App.Models.Ui.QuestionOption} option
     * @callback
     */
    proto._setColor = function(colors, option) {
        var color = colors.getById(option.id);
        option.rgbString = color.getRGBString();
        option.colorContrastRgbString = color.logo_rgb;
        option.type = color.type;
    };

    /**
     * @method render
     * @chainable
     */
    proto.render = function(event, data) {
        //console.log('JoeTest.Rgb.render');
        var model = this.model;
        model.baseAppUrl = baseAppUrl;
        model.host_url = host_url;
        if (model.id === 'inkColor2') {
            return this.renderToggle(data ? data : null);
        }
        return this.renderDefault();
    };
    
    proto.changeQuantity = function(event, data) {
        var model = this.model;
        model.baseAppUrl = baseAppUrl;
        model.host_url = host_url;
        if (model.id === 'inkColor2') {
            return this.renderToggle(data ? data : null);
        }
    }

    /**
     * @method renderDefault
     * @chainable
     */
    proto.renderDefault = function() {
        //console.log('JoeTest.Rgb.renderDefault');
        var model = this.model;
        var info = model.info;
        var options = model.options;
        var value = model.getValue() || (info && info['default']);
        if (!model.getValue()) {
              model.setValue(info['default']);  
        }
        // Set the first option to have an extended description.
        // In most cases this will be Black, and it will display
        // differently in the template if it has descExtended.
        if (options && options._items.length == 1) {
            Settings.DEFAULTCOLOR = options._items[0].desc;
        }

        for(var i =0;i< options._items.length;i++){
            if(options._items[i].surcharge){
                var surchargeId = options._items[i].surcharge.id;
                var surchargePrice = Surcharge.getPrice(surchargeId,true);
                if(surchargePrice instanceof Number || typeof surchargePrice === "number"){
                    surchargePrice = Number(surchargePrice).toFixed(2).toString();
                }
                options._items[i].surcharge.price = surchargePrice;
            }
        }
        let single_flag = options && options._items.length == 1 ? true : false;
        
        this.$view
            .html(this.template({
                id: model.id,
                desc: model.desc,
                mainheading: Content.get('INK_2_SUBHEADING'),
                logoCheck: this.logoCheck,
                logoDesc: 'Ink color(s) will print as displayed. Custom Logos are printed in one color.',
                isRequired: model.isRequired,
                'default': value,
                options: options && options._items,
                single_flag: single_flag,
                toolTip: model.toolTip
            }));

        this.$primaryColorRadios = this.$view.find(Classes.RADIO_SELECTOR);
        return this;
    };

    /**
     * @method renderToggle
     * @chainable
     */
    proto.renderToggle = function(quantity) {
        //console.log('JoeTest.Rgb.renderToggle', this.model);
        var model = this.model;
        var info = model.info;
        var options = model.options;
        var value = model.getValue() || (info && info['default']);
        var isInkColor2 = false;
        //console.log('JoeTest.1: ' + model.id);
        if (model.id === 'inkColor2' && options._items.length > 0) {
            isInkColor2 = true;
            //console.log('JoeTest.inkColor2.model:', model);
            //if (list already has black, do not add it)
            // let blackColorExist = options._items && options._items[0].id === 'NO_INK2' ? 'NO_INK2' : 'BLACK';
            // if (!this.hasOptionID(options, blackColorExist)) {
            //     options._items.unshift(this.createArtificialOption(options._items[0]));
            // }
        }

        for(var i =0;i< options._items.length;i++){
            if(options._items[i].surcharge){
                var surchargeId = options._items[i].surcharge.id;
                var surchargePrice = Surcharge.getPrice(surchargeId,true, quantity);
                if(surchargePrice instanceof Number || typeof surchargePrice === "number"){
                    surchargePrice = Number(surchargePrice).toFixed(2).toString();
                }
                options._items[i].surcharge.price = surchargePrice;
            }
        }
        
        var newSurchargePrice = surchargePrice;
        var prodQnty = this.pdtQty;
        if (model.id === 'inkColor2' && prodQnty > 0 && model.productInfo.priceInfo.length) {
            model.productInfo.priceInfo.forEach( function(price) {
                if(price.id === surchargeId && price.option.length){
                    price.option.forEach( function(opt) {
                        if(opt.qty === prodQnty){
                            newSurchargePrice = opt.price;
                        }
                    });
                }
            });
        }
        
        if(newSurchargePrice){
            var newHeading = 'Add an accent color to boost your brand (+$'+newSurchargePrice+')';
        }else{
            newHeading = 'Add an accent color to boost your brand';
        }
        this.$view
            .html(this.toggleTemplate({
                id: model.id,
                desc: model.desc,
                heading: newHeading,
                isRequired: model.isRequired,
                'default': value,
                options: options && options._items,
                isInkColor2: isInkColor2,
                baseAppUrl: baseAppUrl,
                host_url: host_url
            }));

        this.$secondaryColorRadios = this.$view.find(Classes.RADIO_SELECTOR);

        return this;
    };

    proto.hasOptionID = function(options, id) {
        for (var i=0; i<options._items.length; i++) {
            if (options._items[i].id == id) {
                return true;
            }
        }
        return false;
    };

    proto.createArtificialOption = function(origOption) {
        let clone = jQuery.extend(true, {}, origOption)
            clone.surcharge = null;
            clone.id = 'NO_INK2';
            clone.desc = 'Black';
            clone.rgbString = '0,0,0';
            clone.slug = 'INK_COLOR2_BLACK';
                clone.info.id = 'NO_INK2';
                clone.info.code = 'BK';
                clone.info.desc = 'NO_INK2';
                clone.info.surcharge = null;
                clone.info.chg = 'N';
        return clone;
    };

    /**
     * @method cacheElements
     * @chainable
     */
    proto.cacheElements = function() {
        this.$colorCheckbox =  this.$view.find(Classes.CHECKBOX_COLOR_SELECTOR);
        this.$secondaryColorRadios = this.$view.find(Classes.RADIO_SELECTOR);
        this.$primaryColorRadios = this.$view.find(Classes.RADIO_SELECTOR);

        return this;
    };

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        this.$view
            //.on(DomEvents.CHANGE, Classes.QUESTION_ENABLED_SELECTOR, this.onEnabledChange)
            .on(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick)            
            .on(DomEvents.CLICK, Classes.QUESTION_OPTION_SELECTOR, this.onRadioChange)
            .on(DomEvents.CLICK, Classes.CHECKBOX_COLOR_SELECTOR, this.onCheckboxChange)
            .on(DomEvents.CLICK, Classes.BUTTON_PRIMARY_COLOR_ACTION_SELECTOR, this.onPrimaryColorButtonClick);
        
        EventController
            .on(ActionEvents.CHANGE_QUANTITY_VALUE, this.changeQuantity)
            .on(ActionEvents.LOGO_STATE_REP, this.onProdChange)
            .on(ProductEvents.QNTY_CHANGE, this.onQntyChange)
            ;


        return this;
    };

    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        this.$view
            //.off(DomEvents.CHANGE, Classes.QUESTION_ENABLED_SELECTOR, this.onEnabledChange)
            .off(DomEvents.CLICK, Classes.BUTTON_ACTION_SELECTOR, this.onButtonClick)
            .off(DomEvents.CLICK, Classes.QUESTION_OPTION_SELECTOR, this.onRadioChange)
            .off(DomEvents.CLICK, Classes.CHECKBOX_COLOR_SELECTOR, this.onCheckboxChange)
            .off(DomEvents.CLICK, Classes.BUTTON_PRIMARY_COLOR_ACTION_SELECTOR, this.onPrimaryColorButtonClick);

        EventController
            .off(ActionEvents.LOGO_STATE_REP, this.onProdChange)
            .off(ProductEvents.QNTY_CHANGE, this.onQntyChange)
            ;
        return this;
    };

     
    proto.onButtonClick = function(event) {
        var model = this.model;
        var info = model.info;
        var options = model.options;
        var construction = this.construction;

        if (Helper.isSameTooltipClick(event)) {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
        } else {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }

            $('#ExtraColorHelp').popover({
                html: true,
                trigger: "manual",
                content: function () {
                    var msg = '<div><img src="' + host_url + '/webasset/w2p_mobile/' + baseAppUrl + 'assets/media/images/tooltip-close.png" id="ExtraColorCloseIcon" alt="ExtraColorCloseIcon" class="close tooltip-close" style="position: absolute; top: 0; right: 7px;"/>';
                    var msg1 = "<div class='popover-info' >Extra Color will be applied to business name and logo.</div>";
                    return msg + msg1 + "</div>";
                }
            });
            if (!Settings.COPIES_FLAG) {
                $('#ExtraColorHelp').popover('show');
            } else {
                $('#ExtraColorHelp').popover('hide');
            }
            Settings.COPIES_FLAG = !Settings.COPIES_FLAG;

            $('#ExtraColorCloseIcon').click(function (e) {
                Settings.COPIES_FLAG = false;
                $('#ExtraColorHelp').popover('hide');
            });
        }
    };


        /**
     * @method onPrimaryColorButtonClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onPrimaryColorButtonClick = function(e) {
        e.preventDefault();

        if (Helper.isSameTooltipClick(e)) {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }
        } else {
            if ($("div[class*='custpop']").is(':visible')) {
                $('.close.tooltip-close').trigger("click");
            }

            $('#primaryColorHelp').popover({
                html: true,
                trigger: "manual",
                placement: 'bottom',
                content: function () {
                    var msg0 = '<div><img src="' + host_url + '/webasset/w2p_mobile/' + baseAppUrl + 'assets/media/images/tooltip-close-new.png" id="primaryColorCloseIcon" alt="primaryColorCloseIcon" class="close tooltip-close" style="position: absolute; top: 0; right: 7px;"/>';
                    var msg1 = "<div class='titletxt'>Standard Color</div><br/>";
                    var msg2 = '<div class="othertxt">Applies to: <br/><ul class="textColorList"><li class="textColorLi">Inside of card</li><li class="textColorLi longTextColor">Envelope return address (if selected)</li></ul></div>';
                    return msg0 + msg1 + msg2 + "</div>";
                },
            });
            if (!Settings.PRIMARY_COLOR_FLAG) {
                $('#primaryColorHelp').popover('show');
            } else {
                $('#primaryColorHelp').popover('hide');
            }
            Settings.PRIMARY_COLOR_FLAG = !Settings.PRIMARY_COLOR_FLAG;

            $('#primaryColorCloseIcon').click(function (e) {
                Settings.PRIMARY_COLOR_FLAG = false;
                $('#primaryColorHelp').popover('hide');
            });
        }
 
    }

     /**
     * @method onEnabledChange
     * @param {jQuery.Event} event
     * @callback
     */
    /*
    proto.onEnabledChange = function(event) {
        console.log('JoeTest.Rgb.onEnabledChange');
        if ($(event.target).prop('checked') !== true) {
            this.$secondaryColorRadios
                .prop('checked', false)
                .removeClass(Classes.IS_CHECKED);
            this.model.setValue('');
        }
    };
    */

    /**
     * @method onCheckboxChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onCheckboxChange = function() {
        if (this.model.id == 'inkColor2') {
            if ($('.js-checkbox-color').hasClass('isChecked')){
              this.model.setValue('');  
              $('.second-colorbox').addClass('hide-second-color')
              $('.js-checkbox-color').trigger('click')
              $('.js-checkbox-color').removeClass('isChecked').prop('checked', false)             
            } else {
                if(typeof this.$secondaryColorRadios.filter(':checked').val() !== 'undefined' ) {               
                    this.onRadioChange(); 
                    $('.js-checkbox-color').trigger('click')
                }
                $('.second-colorbox').removeClass('hide-second-color')
                $('.js-checkbox-color').addClass('isChecked').prop('checked', true)
            }
        }
        EventController.emit(ProductEvents.COLOR_CHANGE);
      
     }



    /**
     * @method onRadioChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onRadioChange = function() {
        
        if (this.model.id == 'inkColor2') {
            var value = this.$secondaryColorRadios.filter(':checked').val();
            if (this.model.value === value) {
                this.$secondaryColorRadios
                    .prop('checked', false)
                    .removeClass(Classes.IS_CHECKED);

                this.model.setValue('');
                //$('#inkColor2_enabled').prop('checked', false);
                //$('#inkColor2_enabled').removeClass('isChecked');
            } else {
                if(value.localeCompare('NO_INK2') == 0) {
                   if(this.$secondaryColorRadios.filter(':checked').hasClass(Classes.IS_CHECKED)) {
                        this.$secondaryColorRadios.prop('checked', false).removeClass(Classes.IS_CHECKED);
                    }
                    this.model.setValue('');
                } else {
                    Settings.EXTRACOLOR = value;
                    this.model.setValue(value);
                    this.isValid();
                }
                //$('#inkColor2_enabled').prop('checked', true);
                //$('#inkColor2_enabled').addClass('isChecked');
            }
            // if(SessionStorage.getValue('logo') == 'LOGOMX') {
            //     EventController.emit(ProductEvents.UPDATELOGOMIXPREVIEW, SessionStorage.getValue('logo_LOGOMX'));
            // }
        } else {
            var value = this.$primaryColorRadios.filter(':checked').val();
            Settings.EXTRACOLOR = value;
            this.model.setValue(value);
            this.isValid();
        }
        EventController.emit(ProductEvents.COLOR_CHANGE);
    };

        /**
     * @method onProdChange
     * @param {jQuery.Event} event
     * @param {App.Models.State} state
     * @callback
     */
        proto.onProdChange = function (event, state) {
            var logoIndex;
            if( state.states != null) {
                logoIndex = state.states.findIndex(function (obj) { return obj.tlId === "Logo"; });
            }
            if(logoIndex > 0 ){
                this.logoCheck = true;
                this.render();
            }else{
                this.logoCheck = false;
                this.render();
            }
        };
        
    /**
     * @method onQntyChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onQntyChange = function(event, data) {
        var model = this.model;
        this.pdtQty = data.value;
        if (model.id === 'inkColor2') {
            return this.renderToggle(data ? data : null);
        }
    };

    return RgbQuestionController;
});
