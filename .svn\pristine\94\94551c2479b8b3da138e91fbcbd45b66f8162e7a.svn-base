define(function(require) {
    'use strict';

    var ActionEvents = require('../constants/ActionEvents');
    var Classes = require('../constants/Classes');
    var ClipArtProvider = require('../providers/ClipArt');
    var LogoMixProvider = require('../providers/LogoMix');
    var ConfigurationProvider = require('../providers/Configuration');
    var InkColorsProvider = require('../providers/InkColors');
    var Content = require('i18n!../constants/nls/en-us/Content');
    var Errors = require('i18n!../constants/nls/en-us/Errors');
    var Controller = require('./Controller');
    var DomEvents = require('../constants/DomEvents');
    var EventController = require('./Event');
    var ProductEvents = require('../constants/ProductEvents');
    var ProductModel = require('../models/ProductInfo');
    var SessionStorage = require('../providers/SessionStorage');
    var Settings = require('../constants/Settings');
    var StateEvents = require('../constants/StateEvents');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var Query = require('models/Query');
    var q = require('q');
    var Helper = require('util/helper');
    var Registry = require('util/Registry');
    var fabric =  require('fabric');
    var Notes = require('i18n!../constants/nls/en-us/Notes');    
    

    /**
     * @class App.Controllers.LogoBrowse
     * @extends App.Controllers.Controller
     *
     * @constructor
     * @param {Object} config
     */
    function LogoMixController(config) {
        bindAll(this,
            'initClipArt',
            'setCategories',
            'updateCategories',
            'updateLogos',
            'updateTitle',
            'onHideAction',
            'onShowAction',
            'onLogoChange',
            'onClickResults',
            'onNextClick',
            'onStateChange',
            'getPageLoadMixLogos',
            'getMixLogos',
            'renderLogos',
            'notifyWhenImagesLoaded',
            'onClickPages',
            'uploadDataResponse',
            'onProductChange',
            'update',
            'initColors',
            'setColors',
            'renderCanvas',
            'updateNext',
            'updateSelected',
            'onLogoLineFocusout',
            'renderPaginationInfo',
            'drawCanvas',
            'triggerSetLogo',
            'runFabric'
        );

        /**
         * @property categories
         * @type {App.Models.ClipArt.Collections.Categories}
         * @default null
         */
        this.categories = null;
        this.colors = null;
        this.pdtModel = null;
        this.setLogo(SessionStorage.getValue('logo_LOGOMX'));

        /**
         * @property query
         * @type {App.Models.Query}
         */
        this.query = Query.getInstance();

       

        Controller.call(this, config);

        // Start hidden
        this.$view.hide();
        
        /**
         * Omniture site prefix
         *
         * @type {String}
         */
        this.site_prefix = Settings.OMNITURE_PREFIX;

        this.productModel;
        this.productModelInkColor1Obj;
        this.productModelInkColor2Obj;
        
        /**
         * @property ready
         * @type {Promise}
         */
        this.ready = q
            .when(this.ready)
            .then(this.updateCategories);
    }

    var proto = inherits(LogoMixController, Controller);

    var filterForm;

    /**
     * @method init
     * @return {Promise}
     */
    proto.init = function() {
        return ConfigurationProvider
            .getConfiguration()
            .then(this.initColors)
            .then(this.setColors);
    };

    /**
     * @method initColors
     * @return {Promise.<App.Models.Collections.Colors>}
     */
    proto.initColors = function(config) {
        return InkColorsProvider
            .setConfig(config)
            .getColors();
    };

    /**
     * @method setColors
     * @param {App.Models.Collections.Colors} colors
     * @chainable
     */
    proto.setColors = function(colors) {
        this.colors = colors;
        return this;
    };

    // -- Accessors ------------------------------------------------------------

    /**
     * @method setCategories
     * @param {App.Models.ClipArt.Collections.Categories}
     * @chainable
     */
    proto.setCategories = function(categories) {
        this.categories = categories;
        this._selectCategories();
        return this;
    };

    /**
     * @method getCategories
     * @return {App.Models.ClipArt.Collections.Categories}
     */
    proto.getCategories = function() {
        return this.categories;
    };

    /**
     * @method getCategory
     * @return {App.Models.ClipArt.Category}
     */
    proto.getCategory = function() {
        var id = this.$categories.find('select').val();
        var categories = this.getCategories();
        return categories && categories.getById(id);
    };

    /**
     * @method setLogo
     * @param {Object} data
     * @param {Boolean} [silent=false] If true, category dropdown will not be updated
     * @chainable
     */
    proto.setLogo = function(data, silent) {
        this.logo = data;
        this.logourl = data && data.logoUrl;
        if (silent !== true && this.categories) {
            this._selectCategories();
        }
        return this;
    };

    /**
     * @method _selectCategories
     * @private
     */
    proto._selectCategories = function() {
        if (!this.logo || !this.logo.logoCode) {
            return;
        }

        var id = this.logo.logoCode;
        var data = Registry.get(Registry.LOGO, id);

        this.selectedCategory = data.category;
        this.selectedSubcategory = data.subcategory;

        if (this.$categories) {
            this.updateCategories();
        }
    };

    // -- Methods --------------------------------------------------------------

    /**
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = require('hbs!templates/logoMix');

    /**
     * @method designLogoTemplate
     * @param {Object} model
     * @return {String}
     */
    proto.designLogoTemplate = require('hbs!templates/form/designLogo');

    /**
     * @method inputBoxTemplate
     * @param {Object} model
     * @return {String}
     */
    proto.inputBoxTemplate = require('hbs!templates/form/inputBox');

    /**
     * @method titleTemplate
     * @param {Object} model
     * @return {String}
     */
    proto.titleTemplate = require('hbs!templates/product/title');

    /**
	 * @method template
	 * @param {Object} model
	 * @return {String}
	 */
	proto.noteTemplate = require('hbs!templates/question/note');

    var uploadResponse = null;

    /**
     * TODO: refactor this
     *
     * @method render
     * @chainable
     */
    proto.render = function() {
        this.$view
            .html(this.template({
                next: Content.get('Next Step'),
                prev: Content.get('Previous Step'),
                brandLogoUrl: Settings.BRAND_LOGO_URL,
                siteCss: Settings.SITE_CSS,
                brandLogo: Settings.BRAND_LOGO,
                host_url: host_url,
                baseAppUrl: baseAppUrl,
            }));
        return this;
    };

    /**
     * @method cacheElements
     * @chainable
     */
    proto.cacheElements = function() {
        var $view = this.$view;

        this.$title = $view.find(Classes.PRODUCT_TITLE_SELECTOR);
        this.$categories = $view.find(Classes.LOGO_CATEGORIES_SELECTOR);
        this.$filter = $view.find(Classes.MIX_LOGO_FILTER_SELECTOR);
        this.$logoscontainer = $view.find(Classes.LOGOS_CONTAINER_SELECTOR);
        this.$logosspinnercontainer = $view.find(Classes.MIX_LOGOS_SPINNER_SELECTOR);
        this.$logos = $view.find(Classes.LOGOS_SELECTOR);
        this.$pages = $view.find(Classes.LOGOS_PAGES_SELECTOR);
        this.$prev = $view.find(Classes.PREV_SELECTOR);
        this.$next = $view.find(Classes.NEXT_SELECTOR);
        this.$nextLabel = $view.find(Classes.NEXT_LABEL_SELECTOR);
        this.$selected = $view.find(Classes.LOGO_SELECTED_SELECTOR);
        this.$subcategories = $view.find(Classes.LOGO_SUBCATEGORIES_SELECTOR);
        this.$results = $view.find(Classes.LOGO_MIX_SELECTOR);

        return this;
    };

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        this.$view
            .on(DomEvents.CHANGE, Classes.LOGO_CATEGORIES_SELECTOR, this.onCategoryChange)
            .on(DomEvents.CHANGE, Classes.LOGOS_SELECTOR, this.onClickPages)
            .on(DomEvents.CHANGE, Classes.ALLSTYLES_SELECTOR, this.onStyleChange)
            .on(DomEvents.CLICK, Classes.NEXT_SELECTOR, this.onNextClick)
            .on(DomEvents.CLICK, Classes.INPUT_BOX_SELECTOR, this.onLogoLineFocusout);

        EventController.on(ActionEvents.HIDE_ALL, this.onHideAction)
            .on(ActionEvents.LOGO_LOGOMX, this.onShowAction)
            .on(StateEvents.CHANGE, this.onStateChange)
            .on(ActionEvents.MIX_LOGO_UPLOAD_COMPLETE, this.uploadDataResponse)
            .on(ActionEvents.UPDATELOGORESULTS, this.onClickResults)
            .on(ProductEvents.CHANGE, this.onProductChange)
            .on(ProductEvents.SET_LOGOMX, this.triggerSetLogo);

    return this;
  };

    /**
     * @method detachEvents
     * @chainabledesignRadio 
     */
    proto.detachEvents = function() {
        this.$view
            .off( DomEvents.CHANGE, Classes.LOGO_CATEGORIES_SELECTOR, this.onCategoryChange)
            .off(DomEvents.CHANGE, Classes.LOGOS_SELECTOR, this.onLogoChange)
            .off(DomEvents.CHANGE, Classes.ALLSTYLES_SELECTOR, this.onStyleChange)
            .off(DomEvents.CLICK, Classes.NEXT_SELECTOR, this.onNextClick)
            .off( DomEvents.CLICK, Classes.INPUT_BOX_SELECTOR, this.onLogoLineFocusout);

        EventController.off(ActionEvents.HIDE_ALL, this.onHideAction)
            .off(ActionEvents.LOGO_LOGOMX, this.onShowAction)
            .off(StateEvents.CHANGE, this.onStateChange)
            .off(ActionEvents.MIX_LOGO_UPLOAD_COMPLETE, this.uploadDataResponse)
            .off(ActionEvents.UPDATELOGORESULTS, this.onClickResults)
            .off(ProductEvents.CHANGE, this.onProductChange)
            .off(ProductEvents.SET_LOGOMX, this.triggerSetLogo);

        return this;
  };

     /**
     * @method onClickPages
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onStyleChange = function(event) {
        var style = $('.js-all-styles').val();
        if (style == '' || style == 'icon') {
            $('.js-all-layout').addClass('showme');
        } else {
            $('.js-all-layout').removeClass('showme');
        }
    };

    /**
     * @method update
     * @chainable
     */
    proto.updateCategories = function() {
        var categories = this.getCategories();

        this.$categories.html(this.inputBoxTemplate({
            selected: this.selectedCategory,
            items: categories && categories._items,
            tlId: 'StandardLogoMajorCategory'
        }));
    };

    /**
     * @method updateLogos
     * @chainable
     */
    proto.updateLogos = function() {
        SessionStorage.storeValue('logo_mix_page', 1);
        this.getPageLoadMixLogos(1).then(this.renderLogos);
        return this;
    };

    function extractHostname(url) {
        var hostname;
        //find & remove protocol (http, ftp, etc.) and get hostname

        if (url.indexOf("//") > -1) {
            hostname = url.split('/')[2];
        } else {
            hostname = url.split('/')[0];
        }

        //find & remove port number
        hostname = hostname.split(':')[0];
        //find & remove "?"
        hostname = hostname.split('?')[0];

        return hostname;
    }

    /**
     * @method updateSelected
     * @chainable
     */
    proto.updateSelected = function() {
        $('.logo-selection-section').show();
        var selected = this.logourl;
        if (!selected) {
            // $selected.css('background-image', '');
            SessionStorage.storeValue('logo', '');
            return this;
        }

        var image = selected + '/png/165';
        var host = extractHostname(image);

        var domain = host.split('.');
        if(domain[1] == 'deluxe') {
            var imagehash = this.logo && this.logo.logoHash;
            if (typeof imagehash == 'undefined') {
                if(this.logo.data) {
                    for(let i = 0; i<this.logo.data.item.length; i++) {
                        if(this.logo.data.item[i].key === 'logoMixRef') {
                            imagehash = this.logo.data.item[i].value;
                        }
                    }
                }
            }
            if (!imagehash) {
                // $('.designBoxPreview').empty();
                return this;
            }
            // image = Settings.SVC_LOGOMIX_PATH + imagehash + '/png/165';
            var imageSize = 165;
            var logocode = this.logo.logoCode;
            var imageHeightWidth = 'hei:'+ imageSize + ',wid:' + imageSize + ',fit:constrain';
            image = Settings.SVC_UPLOAD_DOMAIN + '?UID=' + Helper.isAlphaNumeric(logocode) ? logocode : "" + '&Custom=' + imageHeightWidth + '';
        }
        $('.designBoxPreview-panel').empty();
        $('.logopreview-panel').removeClass('show-logopreview logomix-preview');

        // $('#mix-canvas').show();
        // $('.canvas-overlay').show();

        // if (typeof this.canvas == 'undefined') {
        //     this.canvas = new fabric.Canvas('mix-canvas');
        //     // this.canvas.setHeight(500);
        //     // this.canvas.setWidth(800);
        // }

        // this.canvas = new fabric.Canvas('mix-canvas');
        // this.canvas.clear();
        var logoColor = this.defaultLogoColor;
        EventController.emit(ActionEvents.LOGO_COLOR, logoColor);

        // canvas.listenToEvents( false);
        // var canvas = this.canvas;

        this.drawCanvas(image);
        return this;
  };

    /**
     * @method updateNext
     * @chainable
     */
    proto.updateNext = function(logourl) {
        this.logourl = logourl;
        if (this.logourl) {
            var host = extractHostname(this.logourl);

            var domain = host.split('.');
            if(domain[1] != 'deluxe') {
                this.uploadlogourl = this.logourl + '/png/800';
            }
            this.$nextLabel.html(Content.get('Select'));
            this.$next
                .removeClass(Classes.BUTTON_GREY)
                .removeClass(Classes.BUTTON_NEUTRAL);
            this.$prev.html('<span class="js-prev-label">' + Content.get('Proceed without Logo') + '</span>');
        } else {
            this.$nextLabel.html(Content.get('Proceed without Logo'));
            this.$next.addClass(Classes.BUTTON_NEUTRAL);
            this.$prev.attr('id', 'Header-Previous-BrowseLogo');
        }
        return this;
    };

    /**
     * @method updateTitle
     * @chainable
     */
    proto.updateTitle = function() {
        var state = this.state;

        if (!state) {
            return this;
        }

        this.$title.html(this.titleTemplate({
            id: state.id,
            description: state.description
        }));

        return this;
    };

    // -- Event Handlers -------------------------------------------------------

    /**
     * @method onChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onCategoryChange = function() {
        this.updateSubcategories();
    };

    /**
     * @method onLogoChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onLogoChange = function() {
        this
            .updateNext()
            .updateSelected();
    };

    /**
     * @method onHideAction
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onHideAction = function() {
        this.$view.hide();
    };

    /**
     * @method onShowAction
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onShowAction = function(event, done) {
        this.onHideAction();
        var state = this.state;
        var controller = state && state.controller;
        var model = (controller && controller.model) || state;
        var trackEvent = model && model.trackEvent;
        var omniturePageName = model && model.description;
        
        var product = this.pdtModel;
        var productQuestion = product.info.productInfo.question
        var inkColor1Obj;
        var inkColor2Obj;
        productQuestion.forEach( function(e) {
            if(e.id === 'inkColor1') {
                inkColor1Obj = e;
            }
            if(e.id === 'inkColor2') {
                inkColor2Obj = e;
            }
        });
        var color1;
        var color2;
        
        if(product.steps){
            var values = product.steps.getValues().values;
            if(values.inkColor1) {
                color1 = values.inkColor1.value;
                color2 = values.inkColor2.value || color1;
            }
            else {
                color1 = 'BLACK';
                color2 = 'BLACK';
            }
        }
        if(!inkColor2Obj) {
            color2 = color1;
        }
        var color = this.colors.getById(color2);
        // var a2 = color.getRGBString();

        // var color = this.colors.getById(this.defaultInkColor);
        if(color) {
            var a = color.getRGBString() || '0,0,0';
        } else {
            var a = '0,0,0';
        }

        a = a.split(',');
        var b = a.map(function(x){                //For each array element
            x = parseInt(x).toString(16);         //Convert to a base16 string
            return x.length == 1 ? '0' + x : x;   //Add zero if we get only one character
        });
        b = '#' + b.join('');
        this.defaultLogoColor = b;

        // On edit/re-order of a product this controller
        // is instantiated before the logo data is fetched
        // from the order and save to session.
        if (!this.logourl) {
            //this.setLogo(SessionStorage.getValue('logo_Mix'));
            this.setLogo(SessionStorage.getValue('logo_LOGOMX'));
        }

        this.callback = done;
        if ( omniturePageName && omniturePageName.indexOf('Inside of Card - Logo') > -1) {
            omniturePageName = omniturePageName.replace('Inside of Card - Logo', 'Inside of Card:Logo');
        }
        this.track({
            pageName: this.site_prefix + ': W2P :' + omniturePageName,
            eventName: 'event115',
        });

        this
        .updateNext(this.logourl)
        .updateSelected()
        .$view
        .show();

    };

    /**
     * @method onClickResults
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onClickResults = function(event, data) {
        $('.mix-results').trigger('blur');
        //Get all the filter information
        let dataObj = {
            line1: $('#line_1').val(),
            line2: $('#line_2').val(),
            category: $('#category').val(),
            layout: $('#layout').val(),
            style: $('#style').val(),
            keyword: $('#keyword').val(),
        };
        if (!this.logo) {
            $('.logo-selection-section').hide();
        }
        filterForm = dataObj;
        SessionStorage.storeValue('logo_mix_page', 1);
        setTimeout(function() {
            this.getMixLogos(1, data).then(this.renderLogos);
        }.bind(this), 10);
    };

    /**
     * @method onClickPages
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onClickPages = function(event) {

        // pagination change
        var pages = this.$logos.find(Classes.LOGOS_PAGES_SELECTOR);
        var p_page = SessionStorage.getValue('logo_mix_page');
        var id = pages.find('input:checked').val();

        if ((typeof p_page != 'undefined') && (p_page != id)) {
            SessionStorage.storeValue('logo_mix_page', id);
            this.getMixLogos(id).then(this.renderLogos);
            return;
        }

        var logourl = this.$logos.find('input:checked').val();
        if (this.logourl != logourl) {
            this.logourl = logourl;
            this.uploadlogourl = this.logourl + '/png/800';
            EventController.emit(ActionEvents.LOGOMIX_SELECTION, logourl);
            // auto select previously selected logo            
            if( this.logo && SessionStorage.getValue('logo') === 'LOGOMX' ) {
            }
            this.updateNext(logourl).updateSelected();
        }
    };

    /**
     * @method uploadDataReady
     * @param {jQuery.Event} event
     * @callback
     */

    proto.uploadDataResponse = function(event, uploadLogoMixReponse) {

        if (uploadLogoMixReponse) {
            uploadResponse = uploadLogoMixReponse;
        }

        // var xmlDoc = $.parseXML(uploadResponse);
        // var $xml = $(xmlDoc);
        // var uid = $xml.find('UID').text();
        // this.message = $xml.find('message').text();

        // var status = $xml.find('status').text();
        // var URL = $xml.find('URL').text() + '&Custom=hei:100,wid:100,fit:constrain';
        // var displayUrl = $xml.find('URL').text() + '&Custom=hei:142,wid:142,fit:constrain';
        // var code = $xml.find('error_code').text();

        var uid = uploadResponse.UID;
        var message = uploadResponse.message;
        this.message = message;
        var status = uploadResponse.status;
        var URL = uploadResponse.URL + '&Custom=hei:100,wid:100,fit:constrain';
        var displayUrl = uploadResponse.URL + '&Custom=hei:142,wid:142,fit:constrain';
        var code = uploadResponse.error_code;

        var line1 = this.$filter.find('#line_1').val();
        var line2 = this.$filter.find('#line_2').val();
        var layout = this.$filter.find('#layout').val();
        var style = this.$filter.find('#style').val();
        var keyword = this.$filter.find('#keyword').val();
        if (status == 'TRUE') {
            var logoHash = '';
            if (this.logourl) {
                logoHash = this.logourl.substring(this.logourl.lastIndexOf('/') + 1);
            }
            var logo = {
                logoType: 'LOGOMX',
                logoCode: uid,
                logoImageName: URL,
                logoUrl: displayUrl,
                logoHash: logoHash,
                line1: line1,
                line2: line2,
                filter: keyword,
                style: style,
                layout: layout,
                // logoHeight: 555,
                //logoWidth: 500
            };
            var form_params = [
                { key: 'line1', value: line1 },
                { key: 'line2', value: line2 },
                { key: 'filter', value: keyword },
                { key: 'style', value: style },
                { key: 'layout', value: layout },
            ];
            logo.data = {};
            logo.data.item = [];
            logo.data.item = form_params;

            this.logo = logo;
            var logo = this.logo || { logoType: 'NO LOGO' };
            var callback = this.callback;
            if (typeof callback === 'function') {
                callback(logo);
            }
            this.current = logo;
            delete this.callback;
            this.$logosspinnercontainer.removeClass('loading');
            EventController.emit(ProductEvents.LOGO_CHANGE, logo)
              // .emit(ActionEvents.HIDE_ALL)
              .emit(StateEvents.CHANGE)
              .emit(ActionEvents.PRODUCT_STEP);
        } else {
            return;
        }
    };

    proto.getImageFormUrl = function(url, callback) {
        var img = new Image();
        img.width = 150;
        img.height = 150;
        img.setAttribute('crossOrigin', 'anonymous');
        let $logosspinnercontainer = this.$logosspinnercontainer;
        img.onload = function(a) {
            var canvas = document.createElement('canvas');
            canvas.width = 150;
            canvas.height = 150;
            var ctx = canvas.getContext('2d');
            ctx.drawImage(this, 0, 0);

            var dataURI = canvas.toDataURL('image/jpg');

            // convert base64/URLEncoded data component to raw binary data held in a string
            var byteString;
            if (dataURI.split(',')[0].indexOf('base64') >= 0)
                byteString = atob(dataURI.split(',')[1]);
            else 
                byteString = unescape(dataURI.split(',')[1]);

            // separate out the mime component
            var mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];

            // write the bytes of the string to a typed array
            var ia = new Uint8Array(byteString.length);
            for (var i = 0; i < byteString.length; i++) {
                ia[i] = byteString.charCodeAt(i);
            }
        
            return callback(new Blob([ia], { type: mimeString }));
        };
        img.onerror = function(error) {
            $logosspinnercontainer.removeClass('loading');

            var logo = { logoType: 'NO LOGO' };
            EventController.emit(ProductEvents.LOGO_CHANGE, null).emit(
              StateEvents.CHANGE
            );
            alert('Our image service is taking an unusually long time to respond. Please reload this page and try again.');
        };
        img.src = url;
    };

    /**
     * @method getPageLoadMixLogos
     * @param {logos}
     * @chainable
     */
    proto.getPageLoadMixLogos = function(page) {

        const per_page = 20;
        this.$logoscontainer.addClass('loading');
        // event.preventDefault();
        var line1 = this.$filter.find('#line_1').val();
        var line2 = this.$filter.find('#line_2').val();
        var category = this.$filter.find('#category').val();
        var layout = this.$filter.find('#layout').val();
        var style = this.$filter.find('#style').val();
        var keyword = this.$filter.find('#keyword').val();

        if (line1 == '') {
            line1 = SessionStorage.getValue('CI_ML') ? SessionStorage.getValue('CI_ML') : 'Enter Line One';

            if (line1.length > 30) {
                this.$filter
                    .find('.logo_text_error')
                    .html(Errors.get('TRIM_LOGO_TEXT'));
            }

            line1 = line1.substring(0, 30);
            this.$filter.find('#line_1').val(line1);
        }

        if (!SessionStorage.getValue('logo_mix_line2') && line2 == '') {
            line2 = SessionStorage.getValue('CI_DE') ? SessionStorage.getValue('CI_DE') : '';
            SessionStorage.storeValue('logo_mix_line2', line2);

            if (line2.length > 30) {
                this.$filter
                    .find('.logo_text_error')
                    .html(Errors.get('TRIM_LOGO_TEXT'));
            }

            line2 = line2.substring(0, 30);
            this.$filter.find('#line_2').val(line2);
        }

        if (typeof SessionStorage.getValue('logo_LOGOMX') != 'undefined') {

            line1 = (SessionStorage.getValue('logo_LOGOMX').line1 != '') ?  SessionStorage.getValue('logo_LOGOMX').line1 : line1;
            line2 = (SessionStorage.getValue('logo_LOGOMX').line2 != '') ?  SessionStorage.getValue('logo_LOGOMX').line2 : line2;
            keyword = (SessionStorage.getValue('logo_LOGOMX').filter != '') ?  SessionStorage.getValue('logo_LOGOMX').filter : keyword;
            style = (SessionStorage.getValue('logo_LOGOMX').style != '') ?  SessionStorage.getValue('logo_LOGOMX').style : style;
            layout = (layout == 'bottom' && SessionStorage.getValue('logo_LOGOMX').layout != '') ?  SessionStorage.getValue('logo_LOGOMX').layout : layout;

            this.$filter.find('#line_1').val(line1);
            this.$filter.find('#line_2').val(line2);
            this.$filter.find('#keyword').val(keyword);
            this.$filter.find('#style').val(style);
            this.$filter.find('#layout').val(layout);

            $('.logo_text_error').hide();
        }
        if (typeof line1 == 'undefined') {
            line1 = 'Enter Line One';
        }

        var filterParams = {
            cat: category,
            l1: line1,
            l2: line2,
            tag: keyword,
            'layouts[]': layout,
            'types[]': style,
            'logodownload[]': 'preview',
            num: per_page,
            p: page,
            lang: 'en',
            w: 198,
            mod: 'step2',
            ptrn_id: 1,
            pk: 'dek001Jee0a',
        };
        var letters = /[A-Z]/g;
        if(line1 && line1.match(letters) && line1.match(letters).length == line1.length) {
            // console.log('just letting you know few logos doesnt have print clear when letters are in caps');
        }
        return LogoMixProvider.getLogos(filterParams);
  };

    /**
     * @method getMixLogos
     * @param {logos}
     * @chainable
     */
    proto.getMixLogos = function(page, data) {
        const per_page = 20;
        this.$logoscontainer.addClass('loading');
        // event.preventDefault();

        var line1 = filterForm.line1;
        var line2 = filterForm.line2;
        var category = filterForm.category;
        var layout = filterForm.layout;
        var style = filterForm.style;
        var keyword = filterForm.keyword;

        if (line1 == '') {
            line1 = SessionStorage.getValue('CI_ML') ? SessionStorage.getValue('CI_ML') : "Enter Line One";   
            this.$filter.find('#line_1').val(line1);
        }

        if (!SessionStorage.getValue('logo_mix_line2') && line2 == '') {
            line2 = SessionStorage.getValue('CI_DE') ? SessionStorage.getValue('CI_DE') : ""; 
            SessionStorage.storeValue('logo_mix_line2', line2);
            this.$filter.find('#line_2').val(line2);
        }

        var filterParams = {
            cat: category,
            l1: line1,
            l2: line2,
            tag: keyword,
            'layouts[]': layout,
            'types[]': style,
            'logodownload[]': 'preview',
            num: per_page,
            p: page,
            lang: 'en',
            w: 198,
            mod: 'step2',
            ptrn_id: 1,
            pk: 'dek001Jee0a',
        };
        var letters = /[A-Z]/g;
        return LogoMixProvider.getLogos(filterParams);
    };

    /**
     * @method notifyWhenImagesLoaded
     * @param selector , callback
     * @callback
     */
    proto.notifyWhenImagesLoaded = function(rootSelector, callback) {
        // function notifyWhenImagesLoaded(rootSelector, callback) {
        var imageList = $(rootSelector + ' img');
        var imagesRemaining = imageList.length;

        function checkDone() {
            if (imagesRemaining === 0) {
                callback();
            }
        }
    
        imageList.each(function() {
            // if the image is already loaded, just count it
            if (this.complete) {
                --imagesRemaining;
            } else {
                // not loaded yet, add an event handler so we get notified
                // when it finishes loading
                $(this).load(function() {
                    --imagesRemaining;
                    checkDone();
                });
            }
        });
        checkDone();
    };

    /**
     * @method renderLogos
     * @param promise logos
     * @callback
     */
    proto.renderLogos = function(data) {
        var results = $.parseJSON(data);
        const res_total_pages = results.data.total_results;
        const total_logos = res_total_pages < 140 ? res_total_pages : 140;
        const per_page = 20;
        var total_pages = Math.ceil(total_logos / per_page);
        var pages = [];
        for (var index = 1; index <= total_pages; index++) {
            var t = { id: index };
            pages.push(t);
        }
        var logos = results.data.logos;
        var current_page = results.data.p;
        this.$logos.html(this.designLogoTemplate({
            items: logos,
            current_page: current_page,
            pages: pages,
        }));
        this.total_dis_logos = logos.length;
        this.logo_index = 0;
        this.display_logos = logos;
        var renderCanvas = this.renderCanvas;
        renderCanvas(this.logo_index);
        var last_page = current_page - 1;
        var startCount = last_page * per_page + 1;
        var endCount = last_page * per_page + this.total_dis_logos;
        this.renderPaginationInfo(startCount, endCount, total_logos);

        this.notifyWhenImagesLoaded(Classes.LOGOS_SELECTOR, function() {
            setTimeout(function() {
                $(Classes.LOGOS_CONTAINER_SELECTOR).removeClass('loading');
            }, 2000);
        });
    };

    /**
     * @method renderCanvas
     * @param {jQuery.Event} event
     * @callback
     */
    proto.renderCanvas = function(i) {

        var renderCanvas = this.renderCanvas;
        var logos = this.display_logos;
        // if(!this.productModelInkColor2Obj) {
            // this.defaultLogoColor = '#000000';
            // EventController.emit(ActionEvents.LOGO_COLOR, this.defaultLogoColor);
        // }
        var logoColor = this.defaultLogoColor;

        var canvas = new fabric.Canvas('mix-listing-canvas-' + i);

        canvas.setHeight(132);
        canvas.setWidth(132);

        canvas.hoverCursor = 'pointer';
        canvas.selection = false;

        var left = 0;
        var top = 0;

        // for (var i = 0; i < logos.length; i++) {
        if (i < this.total_dis_logos) {
            fabric.Image.fromURL( logos[i].url, function(img) {
                canvas.clear();

                if (logoColor == '#000000') {
                    var grayscale = new fabric.Image.filters.Grayscale();
                    img.filters.push(grayscale);
                } else {
                    var removeWhite = new fabric.Image.filters.RemoveColor({
                        color: '#ffffff',
                        distance: 0.1,
                    });

                    // Add Red color
                    var addColor = new fabric.Image.filters.BlendColor({
                        color: logoColor,
                        mode: 'tint',
                        opacity: 0.5,
                        alpha: 0.9,
                    });
                    img.filters.push(removeWhite);
                    img.filters.push(addColor);
                }

                img.lockRotation = true;
                img.lockMovementX = true;
                img.lockMovementY = true;
                img.lockScalingX = true;
                img.lockScalingY = true;
                img.lockUniScaling = true;
                img.hasControls = false;
                // img.borderScaleFactor = 4;
                // img.set({borderColor: '#fd9600', LineWidth: 18});
                img.hoverCursor = 'pointer';

                img.selectable = false;
                img.top = top;
                img.left = left;
                img.scaleX = 0.65;
                img.scaleY = 0.65;

                // apply filters and re-render canvas when done
                img.applyFilters();
                // add image onto canvas (it also re-render the canvas)
                canvas.add(img);

                canvas.renderAll();
                i++;
                renderCanvas(i);
            }, 
            { crossOrigin: 'Anonymous' });
        }
    };

    /**
     * @method onNextClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onNextClick = function(event) {

        event.preventDefault();
        var logo = this.logo || { logoType: 'NO LOGO' };

        if ( event.target.textContent.indexOf('Proceed without Logo') >= 0 || typeof this.uploadlogourl == 'undefined') {
            this.track({
                pageName: this.site_prefix + ': W2P: Logo No Logo',
            });
            this.current = logo;
            delete this.callback;

            EventController.emit(ProductEvents.LOGO_CHANGE, logo)
                .emit(StateEvents.CHANGE)
                .emit(ActionEvents.PRODUCT_STEP);
        } else {
            this.$logosspinnercontainer.addClass('loading');

            let $logosspinnercontainer = this.$logosspinnercontainer;
            this.getImageFormUrl(this.uploadlogourl, function(blobImage) {
                var file = blobImage;

                var myarr = file.type.split('/');
                var filename = 'filename.' + myarr[myarr.length - 1];
                var formData = new FormData();
                formData.append('source', '');
                formData.append('noScale', 'false');
                formData.append('file', file, filename);

                $.ajax({
                    url: Settings.SVC_UPLOAD,
                    type: 'POST',
                    data: formData,
                    cache: false,
                    dataType: 'json',
                    processData: false, // Don't process the files
                    contentType: false, // Set content type to false as jQuery will tell the server its a query string request
                    headers: { 'Content-Type': undefined },
                    xhrFields: {
                        withCredentials: false,
                    },
                    success: function(data, textStatus, jqXHR) {
                        uploadResponse = data;
                        EventController.emit(ActionEvents.MIX_LOGO_UPLOAD_COMPLETE);
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        $logosspinnercontainer.removeClass('loading');

                        var logo = { logoType: 'NO LOGO' };
                        EventController.emit(ProductEvents.LOGO_CHANGE, null).emit(StateEvents.CHANGE);
                        alert('Our image service is taking an unusually long time to respond. Please reload this page and try again.');
                    },
                });
            });
        }
    };

    /**
     * @method onStateChange
     * @callback
     */
    proto.onStateChange = function(event, state) {
        this.state = state;
        this.updateTitle();
    };

    /**
     * @method onProductChange
     * @param {jQuery.Event} event
     * @param {App.Models.Product} product
     * @callback
     */
    proto.onProductChange = function(event, product) {
        this.update(product, event);
    };

    /**
     * @method update
     * @param {App.Models.Product} product
     * @chainable
     */
    proto.update = function(product, event) {
        this.productModel = product;
        this.pdtModel = product;
        var productQuestion = product.info.productInfo.question

        var inkColor1Obj;
        var inkColor2Obj;

        productQuestion.forEach( function(e) {
            if(e.id === 'inkColor1') {
                inkColor1Obj = e;
            }
            if(e.id === 'inkColor2') {
                inkColor2Obj = e;
            }
        });

        this.productModelInkColor1Obj = inkColor1Obj;
        this.productModelInkColor2Obj = inkColor2Obj;

        // var ink2 = SessionStorage.getValue('inkColor2');
        // if (typeof ink2 == 'undefined' || ink2 == '' || ink2 == null) {
        //     ink2 = SessionStorage.getValue('inkColor1');
        // }

        // this.defaultInkColor = ink2;
        
        if(!this.productModelInkColor2Obj) {
            this.defaultInkColor = '#000000';
        }
            this.defaultInkColor = '#000000';

        if (product.steps) {
            var values = product.steps.getValues().values;
            this.color = values.inkColor2 || {};

            if (!this.color.value) {
                this.defaultInkColor = SessionStorage.getValue('inkColor1');
            }
        }

        var logoType = SessionStorage.getValue('logo');
        var logo = null;
        if (logoType) {
            logo = SessionStorage.getValue('logo_' + logoType);
        }

        if (logo) {
            this.model.logo = logo;
        }

        // auto select previously selected logo
        if( this.logo && logoType === 'LOGOMX' ) {
            EventController.emit(ProductEvents.LOGO_CHANGE, this.logo);
        }

        // Preview the logo at last step
        var my_logo;
        var plogo = SessionStorage.getValue('logo_CUSTOM');
        if (typeof plogo != 'undefined') {
            my_logo = plogo.logoImageName;
            if (plogo.logoUrl) {
                my_logo = plogo.logoUrl;
            }
        }
        var mix_logo;
        var mix_plogo = SessionStorage.getValue('logo_LOGOMX') || this.model.logo;
        if (typeof mix_plogo != 'undefined') {
            // mix_logo = mix_plogo.logoCode;
            if (mix_plogo.logoHash) {
                mix_logo = mix_plogo.logoHash;
            }            
            if (typeof mix_logo == 'undefined') {
                // Edit from cart   
                if(mix_plogo.data) {
                    for(let i = 0; i<mix_plogo.data.item.length; i++) {
                        if(mix_plogo.data.item[i].key === 'logoMixRef') {
                            mix_logo = mix_plogo.data.item[i].value;
                        }
                    }
                }
            }
        }
        var base = Settings.SCENE7_HOST + Settings.SCENE7_API_IMAGE;
        var imageExt = "?fmt=webp&hei=142&wid=142&qlt=85";
        var image1 = SessionStorage.getValue('logo_STANDARD');
        var image, clipart_logo;
        var customLogoStyle = '<img class="custom_logo" src="' + my_logo + '" alt="custom_logo"/>';
        var summaryCustomLogoStyle = '<img  id="summary-custom-logo" class="custom_logo" src="' + my_logo + '" alt="custom_logo"/>';
        if (typeof plogo != 'undefined') {
            if(plogo.logoCode && !plogo.logoCode.startsWith('U'))
            {
                customLogoStyle = '<img class="custom_logo_mset" src="' + my_logo + '" alt="custom_logo"/>';
                summaryCustomLogoStyle = '<img  id="summary-custom-logo" class="custom_logo_mset" src="' + my_logo + '" alt="custom_logo"/>'
            }
        }
        if (typeof image1 != 'undefined') {
            image = image1.logoCode;
            clipart_logo= base + image + imageExt;
        }
        var editLogoUrl = host_url + '/webasset/w2p_mobile/' + baseAppUrl + 'assets/media/images/edit.png';

        if (my_logo && logoType == 'CUSTOM') {
            var log_image = '<div class="logo-preview"><h2 class="logo_name logo_left">Logo</h3><div class="logo_right"><img id="summary-edit-logo" class="edit_logo" src="'+ editLogoUrl +'"></img><i class="edit_text">Edit</i></div></div><div class="logo_border">' + customLogoStyle + '</div> <div class="my_logo_message">Logo Applied Successfully!</div>';
            $('.my_logo_summary').html(log_image);
            $('.my_logo_summary').parent().show();
            $('[data-id="HIDDEN_LOGO"]').show();

            let logo_image = '<div class="logo_border">' + customLogoStyle + '</div> <div class="my_logo_message">Logo Applied Successfully!</div> <div class="logo_message">' +
            this.noteTemplate({
                show: true,
                note: Notes["HIDDEN_LOGO"],
                link: false,
                my_logo: false,
                messageCheck: true
            })+'</div>';
            $('#js-logo-summary').html(logo_image);
            
        } else if (mix_logo && logoType == 'LOGOMX') {
            var log_image = '<div class="logo-preview"><h2 class="logo_name logo_left">Logo</h3><div class="logo_right"><img id="summary-edit-logo" class="edit_logo" src="'+ editLogoUrl +'"></img><i class="edit_text">Edit</i></div></div><div  id="summary-custom-logo" class="logo_border"><canvas id="review-canvas-summary" width="150" height="150"></canvas></div>';
            $('.my_logo_summary').html(log_image);
            $('.my_logo_summary').parent().show();
            var canvas = new fabric.Canvas('review-canvas-summary');

            var log_image = '<div class="logo_border"><canvas id="reorder-canvas-summary" width="150" height="150"></canvas></div>';
            $('#js-logo-summary').html(log_image);
            // $('.my_logo_summary').parent().show();
            var reorderCanvas = new fabric.Canvas('reorder-canvas-summary');

            // var ink2 = SessionStorage.getValue('inkColor2');
            // if (typeof ink2 == 'undefined') {
            //     ink2 = SessionStorage.getValue('inkColor1');
            // }

            var color2;
            var color1;

            // if (product.steps) {
            //     var values = product.steps.getValues().values;
            //     var color = values.inkColor2 || {};

            //     color1 = values2.inkColor1.value;
            //     color2 = values2.inkColor2.value || color1;

            //     // if (!color.value) {
            //     //     ink2 = SessionStorage.getValue('inkColor1');
            //     // }
            // }

            if (product.steps) {
                var values = product.steps.getValues().values;
                if(values.inkColor1) {
                    color1 = values.inkColor1.value;
                    color2 = values.inkColor2.value || color1;
                }
                else {
                    color1 = 'BLACK';
                    color2 = 'BLACK';
                }
            }

            
            if(!inkColor2Obj) {
                color2 = color1;
            }

            // var color = this.colors.getById(ink2);
            var color = this.colors.getById(color2);
            var a = color.getRGBString();
            // console.log(a)
            var a1=a;
            a = a.split(',');
            // console.log(a)

            var b = a.map(function(x) {
                //For each array element
                x = parseInt(x).toString(16); //Convert to a base16 string
                return x.length == 1 ? '0' + x : x; //Add zero if we get only one character
            });

            // console.log(b)
            b = '#' + b.join('');
            // console.log(b)
            var logoColor = b;
            // sizeremoved
            // var image = Settings.SVC_LOGOMIX_PATH + mix_logo;
            // var image = Settings.SVC_LOGOMIX_PATH + mix_logo + '/png/150';
            var logocode = this.logo.logoCode;
            var imageSize = 150;
            var imageHeightWidth = 'hei:'+ imageSize + ',wid:' + imageSize + ',fit:constrain';
            var image = Settings.SVC_UPLOAD_DOMAIN + '?UID=' + Helper.isAlphaNumeric(logocode)? logocode : "" + '&Custom=' + imageHeightWidth + '';
            this.runFabric(image, canvas, logoColor);
            this.runFabric(image, reorderCanvas, logoColor);
        } else if(clipart_logo && logoType == 'STANDARD') {
            // var ink22 = SessionStorage.getValue('inkColor2');
            // if (typeof ink22 == 'undefined') {
            //     ink22 = SessionStorage.getValue('inkColor1');
            // }
            var color2;
            var color1;

            if (product.steps) {
                var values = product.steps.getValues().values;
                if(values.inkColor1) {
                    color1 = values.inkColor1.value;
                    color2 = values.inkColor2.value || color1;
                }
                else {
                    color1 = 'BLACK';
                    color2 = 'BLACK';
                }
            }
            if(!inkColor2Obj) {
                color2 = color1;
            }
            var requiredInkColor = this.pdtModel.getRequiredInkForBlock('LO');
            if (requiredInkColor) {
                color2 = requiredInkColor;
            }
            var color = this.colors.getById(color2);
            var a2 = color.logo_rgb || color.getRGBString();

            // a2 = a2.split(',');

            clipart_logo= base + image1.logoCode + imageExt + '&op_colorize=' + a2 + '&layer=1';
            var logo_image1 = '<div class="logo-preview"><h2 class="logo_name logo_left">Logo</h3><div class="logo_right"><img id="summary-edit-logo" class="edit_logo" src="'+ editLogoUrl +'"></img><i class="edit_text">Edit</i></div></div><div class="logo_border"><img  id="summary-custom-logo" class="custom_logo" src="' + clipart_logo + '" alt="custom_logo"/></div> <div class="my_logo_message">Logo Applied Successfully!</div>';
            $('.my_logo_summary').html(logo_image1);
            $('.my_logo_summary').parent().show();
            $('[data-id="HIDDEN_LOGO"]').show();

            var reorder_logo_image = '<div class="logo_border"><img class="custom_logo" src="' + clipart_logo + '" alt="clipart_logo"/></div> ';
            $('#js-logo-summary').html(reorder_logo_image);
        } else {
            $('[data-id="HIDDEN_LOGO"]').hide();
            $('.my_logo_summary').html('');
            $('.my_logo_summary').parent().hide();
        }
    };

     /**
     * @method onLogoLineFocusout
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onLogoLineFocusout = function(event) {
        $('#line_1,#line_2').focusout(function() {
            $('.logo_text_error').css('display', 'none');
        });
    };

    /**
     * @method renderPaginationInfo
     * @param {String} startCount
     * @param {String} endCount
     * @param {String} totalCount
     */
    proto.renderPaginationInfo = function(startCount, endCount, totalCount) {
        var subTitle = "Showing "+ startCount +" - " + endCount + " of " + totalCount;
        ($('.logo-mix-subTitle')[0]).innerHTML = subTitle;
    };

    proto.drawCanvas = function(image) {
        $('.no-logo-logomix').hide();
        $('.js-logo-selected.logomix').removeClass('d-none');
        $('#canvasparent').empty().html('<canvas id="mix-canvas" width="165" height="165"></canvas>');
        $('#mix-canvas').show();
        var canvas = new fabric.Canvas('mix-canvas');
        var logoColor = this.defaultLogoColor;
        this.runFabric(image, canvas, logoColor);
    };

    proto.runFabric = function(image, canvas, logoColor) {
        fabric.Image.fromURL( image, function(img) {
            canvas.clear();
            // Gray scale
            if (logoColor == '#000000') {
                var grayscale = new fabric.Image.filters.Grayscale();
                img.filters.push(grayscale);
            } else {
                var removeWhite = new fabric.Image.filters.RemoveColor({
                    color: '#ffffff',
                    distance: 0.1,
                });
                // Add color
                var addColor = new fabric.Image.filters.BlendColor({
                    color: logoColor,
                    mode: 'tint',
                    opacity: 0.5,
                    alpha: 0.9,
                });
                img.filters.push(removeWhite);
                img.filters.push(addColor);
            }

            img.lockRotation = true;
            img.lockMovementX = true;
            img.lockMovementY = true;
            img.lockScalingX = true;
            img.lockScalingY = true;
            img.lockUniScaling = true;

            img.selectable = false;
            // apply filters and re-render canvas when done
            img.applyFilters();
            // add image onto canvas (it also re-render the canvas)
            canvas.add(img);

            canvas.renderAll();
        },{ crossOrigin: 'Anonymous' });
    };

    proto.triggerSetLogo = function() {
        $('.logo-selection-section').show();
        setTimeout(function() {
            this.updateSelected();
        }.bind(this), 1000);
    };

    return LogoMixController;
});