define(function(require){

//automatically generated, do not edit!
//run `node build` instead
return {
    'contains' : require('./collection/contains'),
    'every' : require('./collection/every'),
    'filter' : require('./collection/filter'),
    'find' : require('./collection/find'),
    'forEach' : require('./collection/forEach'),
    'make_' : require('./collection/make_'),
    'map' : require('./collection/map'),
    'max' : require('./collection/max'),
    'min' : require('./collection/min'),
    'pluck' : require('./collection/pluck'),
    'reduce' : require('./collection/reduce'),
    'reject' : require('./collection/reject'),
    'size' : require('./collection/size'),
    'some' : require('./collection/some')
};

});
