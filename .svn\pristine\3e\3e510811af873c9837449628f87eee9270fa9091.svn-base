define(function(require) {
    'use strict';

    var ActionEvents = require('../constants/ActionEvents');
    var Classes = require('../constants/Classes');
    var ConfigurationProvider = require('../providers/Configuration');
    var Content = require('i18n!../constants/nls/en-us/Content');
    var Controller = require('./Controller');
    var DomEvents = require('../constants/DomEvents');
    var EventController = require('./Event');
    var ProductEvents = require('../constants/ProductEvents');
    var Settings = require('../constants/Settings');
    var StateEvents = require('../constants/StateEvents');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var Query = require('models/Query');
    var q = require('q');
   

    /**
     * @class App.Controllers.StandardVerse
     * @extends App.Controllers.Controller
     *
     * @constructor
     * @param {Object} config
     */
    function RMBlockController(config) {
        bindAll(this,
            'initClipArt',
            'updateTitle',
            'onHideAction',
            'onShowAction',
            'onRMBlockChange',
            'onNextClick',
            'onPrevClick',
            'onStateChange',
            'onProductChange'
        );


        this.product = null;
        
        /**
         * @property query
         * @type {App.Models.Query}
         */
        this.query = Query.getInstance();

        Controller.call(this, config);

        // Start hidden
        this.$view.hide();
        
        /**
         * Omniture site prefix
         *
         * @type {String}
         */
        this.site_prefix = Settings.OMNITURE_PREFIX;
    }

    var proto = inherits(RMBlockController, Controller);

    /**
     * @method init
     * @return {Promise}
     */
    proto.init = function() {
        
    };

    

    // -- Methods --------------------------------------------------------------

    /**
     * @method template
     * @param {Object} model
     * @return {String}
     */
    proto.template = require('hbs!templates/SelectRMBlock');

    

    /**
     * @method titleTemplate
     * @param {Object} model
     * @return {String}
     */
    proto.titleTemplate = require('hbs!templates/product/title');

    /**
     * TODO: refactor this
     *
     * @method render
     * @chainable
     */
    proto.render = function() {
        this.$view
            .html(this.template({
                next: Content.get('Choose Selected'),
                prev: Content.get('Previous Step'),
                brandLogoUrl: Settings.BRAND_LOGO_URL,
                siteCss: Settings.SITE_CSS,
                brandLogo: Settings.BRAND_LOGO
            }));
            
        return this;
    };

    /**
     * @method cacheElements
     * @chainable
     */
    proto.cacheElements = function() {
        var $view = this.$view;
        
        this.$title = $view.find(Classes.PRODUCT_TITLE_SELECTOR);
        this.$prev = $view.find(Classes.PREV_SELECTOR);
        this.$next = $view.find(Classes.NEXT_SELECTOR);
        this.$nextLabel = $view.find(Classes.NEXT_LABEL_SELECTOR);
        // this.$selected = $view.find(Classes.VERSE_SELECTED_SELECTOR);
        //this.$selected = $view.find(Classes.IS_CHECKED);
        
        return this;
    };

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        this.$view
            .on(DomEvents.CHANGE, Classes.RM_SELECTOR, this.onRMBlockChange)
            .on(DomEvents.CLICK, Classes.NEXT_SELECTOR, this.onNextClick)
            .on(DomEvents.CLICK, Classes.PREV_SELECTOR, this.onPrevClick);
            
        EventController
            .on(ActionEvents.HIDE_ALL, this.onHideAction)
            .on(ActionEvents.CHANGE_RM, this.onShowAction)
            .on(StateEvents.CHANGE, this.onStateChange)
            .on(ProductEvents.CHANGE, this.onProductChange);

        return this;
    };

    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        this.$view
            .off(DomEvents.CHANGE, Classes.RM_SELECTOR, this.onRMBlockChange)
            .off(DomEvents.CLICK, Classes.NEXT_SELECTOR, this.onNextClick)
            .off(DomEvents.CLICK, Classes.PREV_SELECTOR, this.onPrevClick);

        EventController
            .off(ActionEvents.HIDE_ALL, this.onHideAction)
            .off(ActionEvents.CHANGE_RM, this.onShowAction)
            .off(StateEvents.CHANGE, this.onStateChange)
            .off(ProductEvents.CHANGE, this.onProductChange);

        return this;
    };

    

  

    /**
     * @method updateTitle
     * @chainable
     */
    proto.updateTitle = function() {
        var state = this.state;
        if (!state) {
            return this;
        }

        this.$title.html(this.titleTemplate({
            id: state.id,
            description: state.description
        }));

        return this;
    };

    // -- Event Handlers -------------------------------------------------------

    

    proto.onRMBlockChange = function() {        

        $('.button_next_header').css('display', 'inline-block');      

    };

    /**
     * @method onHideAction
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onHideAction = function() {
        this.$view.hide();
        $('.button_next_header').css('display', 'none');
    };

    /**
     * @method onShowAction
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onShowAction = function(event) {
        
        this.getRMOptions(this.product)
        this
            .$view
            .show();
    };

    /**
     * @method onNextClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onNextClick = function(event) {
        event.preventDefault();
        let selectedRMBlock = {value:this.$view.find('input:checked').val()} ;
        EventController.emit(ProductEvents.SAVE_RM, selectedRMBlock);
        EventController.emit(ProductEvents.CHANGE, this.product);
        EventController
            .emit(ActionEvents.HIDE_ALL)
            .emit(ActionEvents.PRODUCT_STEP);
    };

    /**
     * @method onPrevClick
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onPrevClick = function(event) {
        event.preventDefault();        
        EventController
            .emit(ActionEvents.HIDE_ALL)
            .emit(ActionEvents.PRODUCT_STEP);
    };

    /**
     * @method onStateChange
     * @callback
     */
    proto.onStateChange = function(event, state) {
        this.state = state;
        this.updateTitle();
    };

    proto.getRMOptions = function(product) {

        this.rmOpts = product.getRMOptions();
        if(!this.rmOpts.length) {
            return;
        }
        var rmDefault = product.getDefaultRM();
        var isMobile = window.innerWidth < 768;
        if(this.rmOpts) {
            this.rmOpts = this.rmOpts.map(function(option) {
                option.imageUrl = Settings.SCENE7_HOST + Settings.SCENE7_RM_IMAGE + option.id.replace("+", "_").toLowerCase() + "?wid=300&fmt=jpg&qlt=85";
                return option
            })
            this.$view.html(
                this.template(
                    {
                        rmOpts: this.rmOpts,
                        next: Content.get('Choose selected'),
                        prev: Content.get('Cancel & Return'),
                        isMobile: isMobile ?  'notMobile' :'Mobile', 
                        brandLogoUrl: Settings.BRAND_LOGO_URL,
                        siteCss: Settings.SITE_CSS,
                        brandLogo: Settings.BRAND_LOGO,
                        id: this.state ? this.state.id : '',
                        description: this.state ? this.state.description : '',
                        'default': rmDefault
                    }
                )
            );


        }

    };

    /**
     * @method onProductChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onProductChange = function(event, product) {
        this.product = product;
        ConfigurationProvider
            .getConfiguration()
            .then(this.getRMOptions(product))
            .fail(this.onError);
    };
    
    return RMBlockController;
});
