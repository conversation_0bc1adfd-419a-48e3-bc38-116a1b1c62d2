define(function (require) {
    'use strict';

    var $ = require('jquery');
    var AbstractModel = require('../../Abstract');
    var CartProductLayoutModel = require('./Layout');
    var CartProductMatrixModel = require('./Matrix');
    var CartProductSurchargesCollection = require('./collections/Surcharges');
    var SessionStorage = require('../../../providers/SessionStorage');
    var Query = require('models/Query');

    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.Cart.Product.Options
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} CartProductValues
     */
    var CartProductOptionsModel = function (CartProductValues) {
        AbstractModel.call(this, CartProductValues);
    };

    var proto = inherits(CartProductOptionsModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} values
     * @chainable
     */
    proto.init = function(values) {

        /**
         * @property selectedProductId
         * @default {null}
         * @type {string}
         */
        this.selectedProductId = null;

        /**
         * @property selectedProductDescription
         * @default {null}
         * @type {string}
         */
        this.selectedProductDescription = null;

        /**
         * @property productType
         * @default {null}
         * @type {string}
         */
        this.productType = null;

        /**
         * @property quantity
         * @default {null}
         * @type {number}
         */
        this.quantity = null;

        /**
         * @property selectedInks
         * @default {null}
         * @type {Object}
         */
        this.selectedInks = null;

        /**
         * @property routingNumber
         * @default {null}
         * @type {string}
         */
        this.routingNumber = null;

        /**
         * @property accountNumber
         * @default {null}
         * @type {string}
         */
        this.accountNumber = null;

        /**
         * @property comment
         * @default {null}
         * @type {string}
         */
        this.comment = null;

        /**
         * @property selectedMatrix
         * @default {null}
         * @type {App.Models.Cart.Product.Matrix}
         */
        this.selectedMatrix = null;

        /**
         * @property appliedSurcharges
         * @default {null}
         * @type {App.Models.Cart.Product.Collections.Surcharges}
         */
        this.appliedSurcharges = null;

        /**
         * @property selectedLayout
         * @default {null}
         * @type {App.Models.Cart.Product.Layout}
         */
        this.selectedLayout = null;

        // run the parent init method to parse determine the data type
        base.init.call(this, values);

        return this;
    };

    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param json the original JSON data.
     */
    proto.fromJSON = function(json) {
        if (Array.isArray(json)) {
             var querydata = Query.getInstance();
             json = json.find(function(item) {
                return item.selectedProductId == querydata.skuId
             })
        }
        this.selectedProductId = json.selectedProductId;
        this.selectedProductDescription = json.selectedProductIdDesc;
        this.productType = json.productType;

        this.routingNumber = json.routingNumber;
        this.accountNumber = json.accountNumber;
        this.comment = json.lineItemComments;
        this.quantity = parseInt(json.quantity, 10);

        this.selectedMatrix = new CartProductMatrixModel(json.selectedMatrix);
        this.appliedSurcharges = new CartProductSurchargesCollection(json.appliedSurcharges);
        this.selectedLayout = new CartProductLayoutModel(json.selectedLayout);

        this.selectedInks = json.selectedInks;
        
        if(this.selectedInks.secondInkId._ === 'BLACK'){
            this.selectedInks.secondInkId._ = this.selectedInks.firstInkId._ ;
            this.selectedInks.firstInkId._ = 'BLACK';
        }

        this.availableValues = {
            inkColor1: this.selectedInks.firstInkId._ || SessionStorage.getValue('inkColor1'),
            inkColor2: this.selectedInks.secondInkId._,
            accountNumber: this.accountNumber,
            routingNumber: this.routingNumber,
            comment: this.comment
        };

    };

    /**
     * Converts all data into an XML string for sending
     * to the API
     *
     * @method toXML
     * @return {string}
     */
    proto.toXML = function() {
        var $options = $('<customizedOptions/>')
            .append($('<selectedProductId/>').text(this.selectedProductId))
            .append($('<selectedProductDescription/>').text(this.selectedProductIdDesc))
            .append($('<productType/>').text(this.productType))
            .append($('<selectedProductDesc/>').text(this.selectedProductDescription))
            .append($('<routingNumber/>').text(this.routingNumber))
            .append($('<accountNumber/>').text(this.accountNumber))
            .append($('<quantity/>').text(this.quantity))
            .append(this.selectedMatrix.toXML());
        if (Settings.LINEITEM_COMMENT) {
            $options.append($('<lineItemComments/>').text(this.comment));
        }
        $options.append(this.appliedSurcharges.toXML());
        $options.append(this.selectedLayout.toXML());
        var xml = $options[0].outerHTML;
        return xml;
    };

    /**
     * Returns all associated blocks so that we can pass them up
     * to the parent Cart Product model, which can then be used
     * to apply the imprint block values to associated UI Question Blocks
     * in an unconfigured product.
     *
     * @returns {App.Models.Cart.Product.Collections.ImprintBlocks}
     */
    proto.getBlocks = function() {
        return this.selectedLayout.getBlocks();
    };

    proto.getValue = function(name) {
        return this.availableValues[name] || this.selectedLayout.getValue(name);
    };

    return CartProductOptionsModel;
});
