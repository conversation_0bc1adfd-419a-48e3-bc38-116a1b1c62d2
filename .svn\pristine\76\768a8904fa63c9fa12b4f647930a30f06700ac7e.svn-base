define(function (require) {
    'use strict';

    var AbstractModel = require('./Abstract');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Models.Verse
     * @extends App.Models.Abstract
     *
     * @constructor
     * @param {Document|Object|undefined} VerseValues
     */
    var VerseModel = function (VerseValues) {
        AbstractModel.call(this, VerseValues);
    };

    var proto = inherits(VerseModel, AbstractModel);
    var base = AbstractModel.prototype;

    /**
     * Initializes the model
     *
     * @method init
     * @param {Document|Object|undefined} VerseValues
     * @chainable
     */
    proto.init = function(VerseValues) {

        /**
         * @property verseType
         * @default {null}
         * @type {string}
         */
        this.verseType = 'STANDARD';

        /**
         * @property verseCode
         * @default {null}
         * @type {string}
         */
        this.verseCode = null;
        this.selectedVerseImg = '';
        // run the parent init method to parse determine the data type
        base.init.call(this, VerseValues);

        return this;
    };


    /**
     * Parses JSON data into our model, with deep copy to avoid copy by
     * reference of the original children
     *
     * @method fromJSON
     * @param json the original JSON data.
     */
    proto.fromJSON = function(json) {
        this.verseType = json.verseType;
        this.verseCode = json.verseCode;
        this.selectedVerseImg = json.selectedVerseImg;
        
    };

    return VerseModel;
});
