<label class="txtLarge">
    {{> question/required}}
    {{cityLabel}}
    <input class="inputBox {{piiClass}}" type="text"
        name="{{#if blockId}}{{blockId}}_{{/if}}{{id}}_city"
        id="{{#if blockId}}{{blockId}}_{{/if}}{{id}}_city"
        maxlength="50" value="{{cityDefault}}" />
    <span class="error js-error"></span>
</label>
<label class="txtLarge">
    {{> question/required}}
    {{stateLabel}}
    <span class="inputBox inputBox_select">
        <select class="inputBox_select-input"
            name="{{#if blockId}}{{blockId}}_{{/if}}{{id}}_state"
            id="{{#if blockId}}{{blockId}}_{{/if}}{{id}}_state">
            {{#each stateOptions}}
                <option value="{{value}}"
                    id="YourInfoState-{{abbreviation}}"
                    {{#is value ../stateDefault}} selected{{/is}}>{{abbreviation}}</option>
            {{/each}}
        </select>
    </span>
    <span class="error js-error"></span>
</label>
<label class="txtLarge">
    {{> question/required}}
    {{zipLabel}}
    <input class="inputBox {{piiClass}}" type="text"
        name="{{#if blockId}}{{blockId}}_{{/if}}{{id}}_zipCode"
        id="{{#if blockId}}{{blockId}}_{{/if}}{{id}}_zipCode"
        maxlength="50" value="{{zipDefault}}" />
    <span class="error js-error"></span>
</label>
