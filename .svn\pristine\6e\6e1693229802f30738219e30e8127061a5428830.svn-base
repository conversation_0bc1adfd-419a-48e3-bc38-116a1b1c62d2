/* shiftzoom.js 3.9 (13-Dec-2011) (c) by <PERSON>. All Rights Reserved. Source: shiftzoom.netzgesta.de. Distributed under Netzgestade Software License Agreement. Read more at... http://www.netzgesta.de/cvi/LICENSE.txt */
var cvi_szclback, cvi_sztimer, cvi_szactive, cvi_szimage = null, shiftzoom = {
		
		_shiftzoom : null,
		defaultOverview : true,
		defaultIcons : null,
		defaultBicubic : false,
		defaultForcetouchui : false,
		defaultLowres : '',
		defaultMillisec : 40,
		defaultOpacity : 90,
		defaultOvsfact : 20,
		defaultOvaopac : 75,
		defaultOvacolor : 'red',
		defaultOvbcolor : 'white',
		defaultOvborder : '',
		defaultZoom : 200,
		defaultXpos : 0,
		defaultYpos : 0,
		
		add : function(ele, opts) {
		
			function roundTo(val, dig) {
				var num = val;
				if (val > 8191 && val < 10485) {
					val = val - 5000;
					num = Math.round(val * <PERSON>pow(10, dig)) / Math.pow(10, dig);
					num = num + 5000
				} else {
					num = Math.round(val * Math.pow(10, dig)) / Math.pow(10, dig)
				}
				return num
			}

			function uniqueID() {
				var val = Math.floor(Math.random() * 100000000000);
				return val.toString(16);
			}

			function boxShadow() {
				var bs = false, mbs = false, kbs = false, wbs = false;
				try {
					bs = (document.body.style.boxShadow !== undefined)
				} catch (e) {
				}
				try {
					mbs = (document.body.style.MozBoxShadow !== undefined)
				} catch (e) {
				}
				try {
					kbs = (document.body.style.KhtmlBoxShadow !== undefined)
				} catch (e) {
				}
				try {
					wbs = (document.body.style.WebkitBoxShadow !== undefined)
				} catch (e) {
				}
				return (bs || mbs || kbs || wbs ? true : false)
			}
			if (!ele.active) {
				ele.style.visibility = "hidden";
				var defopts = {
						"opacity" : shiftzoom.defaultOpacity,
						"millisec" : shiftzoom.defaultMillisec,
						"ovsfact" : shiftzoom.defaultOvsfact,
						"ovaopac" : shiftzoom.defaultOvaopac,
						"ovacolor" : shiftzoom.defaultOvacolor,
						"ovbcolor" : shiftzoom.defaultOvbcolor,
						"zoom" : shiftzoom.defaultZoom,
						"xpos" : shiftzoom.defaultXpos,
						"ypos" : shiftzoom.defaultYpos,
						"lowres" : shiftzoom.defaultLowres,
						"icons" : shiftzoom.defaultIcons,
						"bicubic" : shiftzoom.defaultBicubic,
						"forcetouchui" : shiftzoom.defaultForcetouchui
				};
				if (opts) {
					for ( var i in defopts) {
						if (!opts[i]) {
							opts[i] = defopts[i]
						}
					}
				} else {
					opts = defopts
				}
				if (document.images && document.createElement
						&& document.getElementById && document.getElementsByTagName) {
					var st, tmp, over, view, div = ele.parentNode, img = shiftzoom
					.E('div'), xref = shiftzoom.E('img'), outer = shiftzoom
					.E('div'), map, mpn = ele.useMap.split("#");
					img.xid = (ele.id != "" ? ele.id : ele.id = uniqueID());
					div.appendChild(outer);
					outer.id = img.xid + '_wrap';
					outer.appendChild(xref);
					outer.appendChild(img);
					img.wrapid = outer.id;
					img.opts = defopts;
					img.highres = ele.src;
					if (ele.naturalWidth && ele.naturalHeight) {
						img.xfactor = roundTo(ele.naturalWidth / ele.width, 4);
						img.yfactor = roundTo(ele.naturalHeight / ele.height, 4);

						img.maxwidth = ele.naturalWidth * 4;
						img.maxheight = ele.naturalHeight * 4;
					} else {
						tmp = new Image;
						tmp.src = ele.src;
						img.xfactor = roundTo(tmp.width / ele.width, 4);
						img.yfactor = roundTo(tmp.height / ele.height, 4);
						img.maxwidth = tmp.width;
						img.maxheight = tmp.height;

					}
					if (ele.width >= 100 && ele.width <= img.maxwidth
							&& ele.height >= 100 && ele.height <= img.maxheight) {
						
						img.bicubic = (typeof opts['bicubic'] === 'boolean' ? opts['bicubic']
						: shiftzoom.defaultBicubic);
						img.overview = (typeof opts['overview'] === 'boolean' ? opts['overview']
						: shiftzoom.defaultOverview);
						img.forcetouchui = (typeof opts['forcetouchui'] === 'boolean' ? opts['forcetouchui']
						: shiftzoom.defaultForcetouchui);
						img.lowres = (typeof opts['lowres'] === 'string' ? opts['lowres'] : img.opts['lowres']);
						img.icons = (typeof opts['icons'] === 'object' ? opts['icons'] : img.opts['icons']);
						img.bicubic = (img.bicubic ? "bicubic" : "nearest-neighbor");
						img.ovacolor = (typeof opts['ovacolor'] === 'string' ? opts['ovacolor'] : img.opts['ovacolor']);
						img.ovbcolor = (typeof opts['ovbcolor'] === 'string' ? opts['ovbcolor'] : img.opts['ovbcolor']);
						img.ovsfact = (typeof opts['ovsfact'] === 'number' ? parseInt(Math
								.min(Math.max(10, opts['ovsfact']), 50)) : img.opts['ovsfact']) / 100;
						img.millisec = (typeof opts['millisec'] === 'number' ? parseInt(Math
								.min(Math.max(5, opts['millisec']), 100)) : img.opts['millisec']);
						img.ovaopac = (typeof opts['ovaopac'] === 'number' ? parseInt(Math
								.min(Math.max(0, opts['ovaopac']), 100)) : img.opts['ovaopac']);
						img.opacity = (typeof opts['opacity'] === 'number' ? parseInt(Math
								.min(Math.max(0, opts['opacity']), 100)) : img.opts['opacity']);
						img.ovborder = (typeof opts['ovborder'] === 'number' ? parseInt(Math
								.min(Math.max(0, opts['ovborder']), 20))
								: Math.min(Math.round(ele.width / 100), Math.round(ele.height / 100)));
						img.zoom = (typeof opts['zoom'] === 'number' ? parseFloat(Math
								.min(Math.max(0, opts['zoom']), 400)) : img.opts['zoom']);
						img.zoom = 100;
						img.xpos = (typeof opts['xpos'] === 'number' ? parseFloat(Math
								.min(Math.max(0, opts['xpos']), 100)) : img.opts['xpos']);
						img.ypos = (typeof opts['ypos'] === 'number' ? parseFloat(Math
								.min(Math.max(0, opts['ypos']), 100)) : img.opts['ypos']);
						img.opts = null;
						defopts = null;
						img.bc = "1px white solid";
						img.dc = "1px gray solid";
						img.automode = false;
						img.autoloop = false;
						img.autowait = 0;
						st = ele.parentNode.style;
						st.position = (st.position == 'static' || st.position == '' ? 'relative' : st.position);
						st.height = ele.height + 'px';
						st.width = ele.width + 'px';
						st.padding = '0px';
						st.overflow = 'hidden';
						st.MozUserSelect = st.KhtmlUserSelect = "none";
						ele.parentNode.unselectable = "on";
						outer.left = 0;
						outer.top = 0;
						outer.width = ele.width;
						outer.height = ele.height;
						outer.unselectable = "on";
						st = outer.style;
						st.MozUserSelect = st.KhtmlUserSelect = "none";
						st.visibility = "hidden";
						st.display = "block";
						st.position = "absolute";
						st.left = '0px';
						st.top = '0px';
						st.width = ele.width + 'px';
						st.height = ele.height + 'px';
						xref.id = img.xid + '_img';
						xref.src = window.loc;
						st = xref.style;
						st.msInterpolationMode = img.bicubic;
						st.position = "absolute";
						st.left = '-20px';
						st.top = '-20px';
						//st.width = '105%';
						//st.height = '90%';
						img.xrefid = xref.id;
						img.unselectable = "on";
						st = img.style;
						st.MozUserSelect = st.KhtmlUserSelect = st.WebkitTouchCallout = "none";
						st.display = "block";
						st.position = "relative";
						st.left = '-20px';
						st.top = '-20px';
						//st.width = '105%';
						//st.height = '90%';
						st.cursor = "grab";
						img.pointer = st.cursor;
						img.minwidth = outer.width;

						img.hasmoved = false;
						img.lasttap = 0;
						img.minheight = outer.height;
						img.maxleft = img.maxwidth - $("#mainImg").width();
						img.maxtop = img.maxheight - $("#mainImg").height();

						ele.id = "";
						outer.parentNode.removeChild(ele);
						img.id = img.xid;
						img.aos = navigator.userAgent.match(/android/i);
						img.ios = navigator.platform.match(/iPad|iPhone|iPod/i);
						img.stg = typeof (document.ontouchstart) != "undefined" ? true
								: false;
						img.mtg = typeof (document.ongesturestart) != "undefined" ? true
								: false;
						img.tod = img.stg
						&& (img.ios || img.aos || img.forcetouchui) ? true
								: false;
						img.trident = document.all
						&& !window.opera
						&& (!document.documentMode || document.documentMode < 9) ? 1
								: 0;
						
						img.isIE = navigator.userAgent.match(/trident/i) ? true : false;
						img.chakra = document.all && document.documentMode
						&& document.documentMode >= 9 ? 1 : 0;
						img.notrans = img.trident && !window.XMLHttpRequest ? 1 : 0;
						
						outer.style.visibility = 'visible';
						img.webkit = window.atob != undefined
						&& !window.updateCommands && !window.opera ? 1 : 0;
						img.divbug = !img.webkit
						&& navigator.userAgent.indexOf('WebKit') > -1 ? 1
								: 0;
						img.gecko = navigator.userAgent.indexOf('Gecko') > -1
						&& window.updateCommands && !window.opera ? 1 : 0;
						img.presto = window.opera ? 1 : 0;
						img.bshadow = boxShadow();
						img.bmode = (img.trident
								&& (document.compatMode == 'BackCompat' || document.compatMode == 'QuirksMode') ? true
										: false);
						img.active = true;
						over = shiftzoom.E('div');
						over.id = img.id + '_over';
						st = over.style;
						st.height = ($("#mainImg").height() * img.ovsfact) + 'px';
						st.width = ($("#mainImg").width() * img.ovsfact) + 'px';
						st.display = "block";
						st.position = 'absolute';
						st.bottom = '0px';
						st.right = '0px';
						st.borderLeft = img.ovborder + 'px solid ' + img.ovbcolor;
						st.borderTop = img.ovborder + 'px solid ' + img.ovbcolor;
						if (img.webkit || img.bshadow) {
							st.borderLeft = '0px solid ' + img.ovbcolor;
							st.borderTop = '0px solid ' + img.ovbcolor;
							st.WebkitBoxShadow = st.MozBoxShadow = st.KhtmlBoxShadow = st.boxShadow = "0px 0px 8px black"
						}
						st.MozUserSelect = st.KhtmlUserSelect = "none";
						st.visibility = "hidden";
						over.unselectable = "on";
						div.appendChild(over);
						view = shiftzoom.E('img');
						view.id = img.id + '_tumb';
						view.src = xref.src;
						st = view.style;
						st.height = ($("#mainImg").height() * img.ovsfact) + 'px';
						st.width = ($("#mainImg").width() * img.ovsfact) + 'px';
						st.display = "block";
						st.position = 'absolute';
						st.bottom = '0px';
						st.right = '0px';
						st.backgroundColor = 'white';
						st.msInterpolationMode = img.bicubic;
						over.appendChild(view);
						img.tumbid = view.id;
						if (!img.tod) {
							view.onmousedown = shiftzoom._catchDrag
						}
						view = shiftzoom.E('div');
						view.id = img.id + '_view';
						view.maxleft = 0;
						view.maxtop = 0;
						st = view.style;
						st.lineHeight = '1px';
						st.fontSize = '1px';
						st.display = "block";
						st.position = 'absolute';
						st.left = '0px';
						st.top = '0px';
						st.border = '1px solid ' + img.ovacolor;
						st.height = parseInt(($("#mainImg").height() * img.ovsfact)
								- (img.bmode ? 0 : 2))
								+ 'px';
						st.width = parseInt(($("#mainImg").width() * img.ovsfact)
								- (img.bmode ? 0 : 2))
								+ 'px';

						over.appendChild(view);
						img.overid = over.id;
						img.viewid = view.id;
						cvi_szclback = function() {
							var img = shiftzoom.G(cvi_szactive);
							if (img) {
								shiftzoom.G(img.textid).innerHTML = parseInt((img.parentNode.width / $(
								"#mainImg").width()) * 100)
								+ " / "
								+ parseInt(img.xfactor * 100)
								+ " %"
							}
						};
						if (img.tod) {
							img.ontouchstart = shiftzoom._catchTouch;
							if (img.mtg) {
								img.ongesturestart = shiftzoom._catchGesture
							}
						} else {
							view.onmousedown = shiftzoom._startMove;
							img.ondblclick = function() {
								shiftzoom._setCursor(this, 1, img.id, true)
							};
							img.oncontextmenu = function() {};
							img.onmousedown = shiftzoom._catchKey;
							img.onmouseover = shiftzoom._catchOver;
							img.onmouseout = shiftzoom._catchOut;
						}
						
						if (img.zoom > 0) {
							shiftzoom.zooming(img, img.zoom)
						}
						if (img.xpos != 50 || img.ypos != 50) {
							shiftzoom.moveto(img, img.xpos + '%', img.ypos + '%')
						}
						if (img.icons) {
							shiftzoom.construct(img, img.icons)
						}
						
					} else {
						ele.parentNode.removeChild(outer);
						ele.style.visibility = 'visible'
					}
				} else {
					ele.style.visibility = 'visible'
				}
			}
			return false
		},
		remove : function(img, v) {
			if (img) {
				var ele, obj = img.parentNode.parentNode;
				img.onmousedown = null;
				img.onmousemove = null;
				document.onmousemove = null;
				document.onmouseup = null;
				ele = shiftzoom.G(img.overid);
				if (ele) {
					obj.removeChild(ele)
				}
				ele = shiftzoom.E('img');
				ele.id = img.id;
				img.id = "";
				ele.width = (v ? img.maxwidth : $("#mainImg").width());
				ele.height = (v ? img.maxheight : $("#mainImg").height());
				ele.style.width = (v ? img.maxwidth : $("#mainImg").width()) + 'px';
				ele.style.height = (v ? img.maxheight : $("#mainImg").height())
				+ 'px';
				/* ele.style.border = "0px none"; */
				ele.style.cursor = "default";
				ele.src = img.highres;
				obj.style.width = (v ? img.maxwidth : $("#mainImg").width()) + 'px';
				obj.style.height = (v ? img.maxheight : $("#mainImg").height())
				+ 'px';
				obj.removeChild(img.parentNode);
				obj.appendChild(ele)
			}
			return false
		},
		construct : function(img, v) {
			if (img && typeof (v) === "object") {
				var i, d, x, y, w, h, p, q, r, t, g, s, z, m, n, oe, oes, ie, ies, ele, l = v.length;
				for (i = 0; i < l; i++) {
					w = v[i].w || 0;
					h = v[i].h || 0;
					s = v[i].src || 0;
					q = v[i].noscale || 0;
					d = v[i].id || 0;
					if (d) {
						ele = shiftzoom.G(d)
					} else {
						ele = false
					}
					if (!ele && w >= 8 && h >= 8 && s != '') {
						x = Math.abs(v[i].x) || 0;
						y = Math.abs(v[i].y) || 0;
						p = Math.max(Math.min(Math.abs(v[i].pos), 9), 0) || 0;
						z = v[i].src2 || 0;
						r = v[i].href || 0;
						t = v[i].title || 0;
						g = v[i].target || 0;
						oe = shiftzoom.E('a');
						oes = oe.style;
						if (d) {
							oe.id = d
						}
						if (r) {
							oe.href = r
						}
						if (g) {
							oe.target = g
						}
						oe.unselectable = "on";
						/* oes.border = "0px none"; */
						oes.fontSize = "0px";
						oes.lineHeight = "0px";
						oes.margin = "0px";
						oes.padding = "0px";
						oes.textDecoration = "none";
						oes.mozUserSelect = oes.khtmlUserSelect = oes.webkitUserSelect = "none";
						img.appendChild(oe);
						ie = shiftzoom.E('img');
						ies = ie.style;
						ie.width = w;
						ie.height = h;
						if (t) {
							ie.title = t
						}
						ie.unselectable = "on";
						ies.position = "absolute";
						ies.margin = "0px";
						ies.padding = "0px";
						/* ies.border = "0px none"; */
						ies.width = q ? w + 'px' : (w / (img.maxwidth / 100)) + '%';
						ies.height = q ? h + 'px' : (h / (img.maxheight / 100))
								+ '%';
						n = (img.maxheight / 100);
						m = (img.maxwidth / 100);
						if (q) {
							ies.top = (y ? y / n : 0) + '%';
							ies.left = (x ? x / m : 0) + '%'
						} else {
							if (!p || p == 4 || p == 5 || p == 6) {
								ies.top = ((y ? y / n : 0) - (parseFloat(ies.height) / 2))
								+ '%'
							} else if (p == 7 || p == 8 || p == 9) {
								ies.top = ((y ? y / n : 0) - parseFloat(ies.height))
								+ '%'
							} else if (p == 1 || p == 2 || p == 3) {
								ies.top = (y ? y / n : 0) + '%'
							}
							if (!p || p == 2 || p == 5 || p == 8) {
								ies.left = ((x ? x / m : 0) - (parseFloat(ies.width) / 2))
								+ '%'
							} else if (p == 3 || p == 6 || p == 9) {
								ies.left = ((x ? x / m : 0) - parseFloat(ies.width))
								+ '%'
							} else if (p == 1 || p == 4 || p == 7) {
								ies.left = (x ? x / m : 0) + '%'
							}
						}
						ies.mozUserSelect = ies.khtmlUserSelect = ies.webkitUserSelect = "none";
						ies.msInterpolationMode = img.bicubic;
						if (z) {
							ie.first = s;
							ie.secnd = z;
							if (!img.trident) {
								ie.setAttribute("onmouseover",
								"this.src=this.secnd;");
								ie.setAttribute("onmouseout",
								"this.src=this.first;")
							}
						}
						oe.appendChild(ie);
						if (z && img.trident) {
							oe.onmouseover = shiftzoom._switchOver;
							oe.onmouseout = shiftzoom._switchOut
						}
					}
				}
			}
			return false
		},
		destruct : function(img, v) {
			if (img && v) {
				if (typeof (v) === "string") {
					var ele = shiftzoom.G(v);
					if (ele) {
						img.removeChild(ele)
					}
				}
				if (typeof (v) === "boolean") {
					img.innerHTML = ""
				}
			}
			return false
		},
		moveto : function(img, x, y) {
			if (img) {
				if (img.parentNode.width > $("#mainImg").width()
						|| img.parentNode.height > $("#mainImg").height()) {
					function LFL(m, i, n) {
						var d = parseFloat(m);
						if (d > n) {
							d = n
						} else if (d < i) {
							d = i
						}
						return d
					}
					var f, h, v, q = parseFloat((img.parentNode.width - $(
					"#mainImg").width())
					/ (img.maxwidth - $("#mainImg").width()));
					if (typeof (x) == "string") {
						if (x.match(/^([+-])?\d*([\.])?\d*$/)) {
							f = (LFL(x, -180, 180) * -1) + 180;
							x = f
							* (((img.maxwidth - $("#mainImg").width()) * q) / 360);
							h = (0.5 - (f / 360)) * $("#mainImg").width();
							x = x - h
						} else if (x.match(/^\d*([\.])?\d*([%]){1,1}$/)) {
							x = ((img.maxwidth - $("#mainImg").width()) * q)
							* (parseFloat(x) / 100)
						} else {
							x = 0
						}
					} else {
						x = (x - ($("#mainImg").width() / 2)) * q
					}
					if (typeof (y) == "string") {
						if (y.match(/^([+-])?\d*([\.])?\d*$/)) {
							f = (LFL(y, -90, 90) * -1) + 90;
							y = f
							* (((img.maxheight - $("#mainImg").height()) * q) / 180);
							v = (0.5 - (f / 180)) * $("#mainImg").height();
							y = y - v
						} else if (y.match(/^\d*([\.])?\d*([%]){1,1}$/)) {
							y = ((img.maxheight - $("#mainImg").height()) * q)
							* (parseFloat(y) / 100)
						} else {
							y = 0
						}
					} else {
						y = (y - ($("#mainImg").height() / 2)) * q
					}
					h = Math.max(0, Math.min(img.maxleft, x || 0));
					v = Math.max(0, Math.min(img.maxtop, y || 0));
					img.parentNode.style.left = (h * -1) + 'px';
					img.parentNode.style.top = (v * -1) + 'px';
					img.parentNode.left = (h * -1);
					img.parentNode.top = (v * -1);
					if (window.desc.indexOf("Top") > -1) {
						img.parentNode.style.left = (h * -1) + 'px';
						img.parentNode.style.top = (v * -1) + 'px';
						img.parentNode.left = (h * -1);
						img.parentNode.top = (v * -1);
					} else if (window.desc.indexOf("Middle") > -1) {
						img.parentNode.style.left = (h * -1) + 'px';
						img.parentNode.style.top = "-800px";
						img.parentNode.left = (h * -1);
						img.parentNode.top = "-800";
					} else if (window.desc.indexOf("Bottom") > -1) {
						img.parentNode.style.left = (h * -1) + 'px';
						img.parentNode.style.top = "-1800px";
						img.parentNode.left = (h * -1);
						img.parentNode.top = "-1800";
					}

					if (img.overview) {
						var view = shiftzoom.G(img.viewid).style;
						view.left = Math
						.round((Math.abs(parseInt(
								img.parentNode.style.left, 10)) / (img.parentNode.width / $(
								"#mainImg").width()))
								* img.ovsfact)
								- (img.bmode ? 2 : 0) + 'px';
						view.top = Math
						.round((Math.abs(parseInt(img.parentNode.style.top,
								10)) / (img.parentNode.height / $(
										"#mainImg").height()))
										* img.ovsfact)
										- (img.bmode ? 2 : 0) + 'px'
					}
				}
			}
			return false
		},
		zooming : function(img, v) {
			if (img) {
				if (typeof (v) === "number") {
					var mw, mh, mx, my, f;
					v = Math.max(0, Math.min(1000, parseFloat(v)));
					f = v > 0 ? v / 100 : 0;
					mw = Math.round(f * (img.maxwidth - $("#mainImg").width()))
					+ $("#mainImg").width();
					mh = Math.round(f * (img.maxheight - $("#mainImg").height()))
					+ $("#mainImg").height();
					mx = Math.round((mw / 2) - ($("#mainImg").width() / 2)) * -1;
					my = Math.round((mh / 2) - ($("#mainImg").height() / 2)) * -1;
					img.parentNode.style.width = mw + 'px';
					img.parentNode.style.height = mh + 'px';
					img.parentNode.style.left = mx + 'px';
					img.parentNode.style.top = my + 'px';
					img.parentNode.width = mw;
					img.parentNode.height = mh;
					img.parentNode.left = mx;
					img.parentNode.top = my;
					img.maxleft = img.maxwidth - $("#mainImg").width();
					img.maxtop = img.maxheight - $("#mainImg").height();
					if (img.parentNode.width > $("#mainImg").width()
							|| img.parentNode.height > $("#mainImg").height()) {
						if (img.isIE) {
							img.style.cursor = "url('/webasset/w2p_mobile/assets/media/images/grab.cur'),move";
						} else {
							img.style.cursor = "-webkit-grab"
						}
						if (img.overview) {
							shiftzoom._setOverview(img);
							shiftzoom.G(img.overid).style.visibility = "visible"
						}
					} else {
						img.style.cursor = "-webkit-grab";
						if (img.overview) {
							shiftzoom.G(img.overid).style.visibility = "hidden"
						}
					}
				}
			}
			return false
		},
		kenburns : function(img, obj) {
		},
		play : function(img, d, l, obj, c) {
			if (img) {
				if (!img.automode && typeof (d) === "number"
					&& typeof (l) === "boolean" && typeof (obj) === "object") {
					if (obj.length >= 1) {
						var n = 0, mx = 3, t = obj.length, i;
						for (i = 0; i < t; ++i) {
							n = Math.max(0, obj[i].length);
							mx = n < mx ? n : mx
						}
						img.callback = (c && typeof (c) === "string"
							&& eval('typeof ' + c) == "function" ? c : 0);
						if (mx >= 3) {
							img.step = obj;
							img.cpos = 0;
							img.automode = true;
							img.autoloop = l;
							img.autowait = Math.abs(d);
							img.nozoom = true;
							if (img.overview) {
								shiftzoom.set(img, 'overview', false)
							}
							shiftzoom.kenburns(shiftzoom.G(img.id),
									img.step[img.cpos])
						}
					}
				}
			}
			return false
		},
		stop : function(img) {
			if (img) {
				if (img.automode) {
					img.automode = false;
					if (img.timer) {
						window.clearInterval(img.timer)
					}
					img.autoloop = false;
					img.cpos = 0;
					img.maxleft = img.maxwidth - $("#mainImg").width();
					img.maxtop = img.maxheight - $("#mainImg").height();
					if (img.lowres && img.highres) {
						shiftzoom.source(img, img.highres, false, true)
					}
					if (img.parentNode.width > $("#mainImg").width()
							|| img.parentNode.height > $("#mainImg").height()) {
						if (img.isIE) {
							img.style.cursor = "url('/webasset/w2p_mobile/assets/media/images/grab.cur'),move";
						} else {
							img.style.cursor = "-webkit-grab"
						}
						if (img.overview) {
							shiftzoom._setOverview(img);
							shiftzoom.G(img.overid).style.visibility = "visible"
						}
					} else {
						img.style.cursor = "-webkit-grab";
						if (img.overview) {
							shiftzoom.G(img.overid).style.visibility = "hidden"
						}
					}
					if (img.callback) {
						window.setTimeout("window['" + img.callback + "']()", 200)
					}
				}
			}
			return false
		},
		source : function(img, src, v, z) {
			if (img) {
				if (typeof (src) === "string" && typeof (v) === "boolean") {
					var tmp = new Image();
					shiftzoom.G(img.xrefid);
					tmp.onload = function() {
						shiftzoom.G(img.overid).style.visibility = "hidden";
						if (v == true) {
							shiftzoom.G(img.isrcid).src = tmp.src;
							if (!z) {
								if (img.trident) {
									tmp.onload = '';
									tmp = null
								}
								delete tmp
							}
							
						} else if (v == false) {
							var obj = shiftzoom.G(img.xrefid);
							obj.src = tmp.src;
							obj.style.msInterpolationMode = img.bicubic;
							if (!z) {
								shiftzoom.G(img.tumbid).src = obj.src;
								if (img.highres != obj.src) {
									img.highres = obj.src
								}
								if (img.trident) {
									tmp.onload = '';
									tmp = null
								}
								delete tmp
							}
							if (img.overview
									&& (img.parentNode.width > $("#mainImg")
											.width() || img.parentNode.height > $(
											"#mainImg").height())) {
								shiftzoom.G(img.overid).style.visibility = "visible"
							}
						}
					};
					tmp.src = src
				}
			}
			return false
		},
		lowsource : function(img, src) {
			if (img) {
				if (typeof (src) === "string") {
					var low = new Image();
					low.onload = function() {
						img.lowres = low.src;
						if (img.trident) {
							low.onload = '';
							low = null
						}
						delete low
					};
					low.src = src
				}
			}
			return false
		},
		info : function(img, v) {},
		set : function(img, d, v) {
			if (img) {
				if (d && typeof (v) === "boolean") {
					switch (d.toLowerCase()) {
					case 'overview':
						if (v == false && img.overview == true) {
							shiftzoom.G(img.overid).style.visibility = "hidden"
						} else if (v == true && img.overview == false) {
							if (img.parentNode.width > $("#mainImg").width()
									|| img.parentNode.height > $("#mainImg")
									.height()) {
								shiftzoom._setOverview(img);
								shiftzoom.G(img.overid).style.visibility = "visible"
							} else {
								shiftzoom.G(img.overid).style.visibility = "hidden"
							}
						}
						img.overview = v;
						break;
					}
				}
			}
			return false
		},
		get : function(img, d) {
			if (img && d && typeof (img.ctrlid) === "string") {
				if (d.toLowerCase() == "maxzoomx") {
					return img.xfactor
				} else if (d.toLowerCase() == "maxzoomy") {
					return img.yfactor
				} else if (d.toLowerCase() == "maxwidth") {
					return img.maxwidth
				} else if (d.toLowerCase() == "maxheight") {
					return img.maxheight
				} else if (d.toLowerCase() == "playing") {
					return img.automode
				} else if (d.toLowerCase() == "currentxyz") {
					var q = parseFloat((img.parentNode.width - $("#mainImg")
							.width())
							/ (img.maxwidth - $("#mainImg").width())), z = Math
							.min(Math.max(q * 100, 0), 100) || 0;
					var x = Math
					.min(
							Math
							.max(
									((Math
											.abs(parseFloat(img.parentNode.style.left)) / ((img.maxwidth - $(
													"#mainImg").width()) * q)) * 100),
													0), 100) || 0;
					var y = Math
					.min(
							Math
							.max(
									((Math
											.abs(parseFloat(img.parentNode.style.top)) / ((img.maxheight - $(
													"#mainImg").height()) * q)) * 100),
													0), 100) || 0;
					return {
						x : x,
						y : y,
						z : z
					}
				}
			}
			return false
		},
		G : function(v) {
			return (document.getElementById(v))
		},
		E : function(v) {
			return (document.createElement(v))
		},
		L : function(s, v) {
			s = s != "" ? s.toLowerCase() : 'log';
			if (window.opera && window.opera.postError) {
				window.opera.postError(s.toUpperCase() + ': ' + v)
			} else if (window.console) {
				if (!window.console[s]) {
					window.console.log(s + ': ' + v)
				} else {
					window.console[s](v)
				}
			} else {
				window.document.title = s.toUpperCase() + ': ' + v
			}
			return false
		},
		_next : function(img) {
			
			if (img && typeof (img.ctrlid) === "string") {
				if (img.automode) {
					if (img.autoloop && img.cpos < (img.step.length - 1)) {
						img.cpos = img.cpos + 1
					} else if (img.autoloop && img.cpos >= (img.step.length - 1)) {
						img.cpos = 0
					} else if (!img.autoloop && img.cpos < (img.step.length - 1)) {
						img.cpos = img.cpos + 1
					} else {
						img.cpos = img.step.length
					}
					if (img.cpos < img.step.length) {
						shiftzoom.kenburns(shiftzoom.G(img.id), img.step[img.cpos])
					} else {
						shiftzoom.stop(img)
					}
				}
			}
			return false
		},
		_setOverview : function(img) {
			
			var mHei = $( window ).height()*0.8;
			var mWid = $( window ).width()*0.8;
			var view = shiftzoom.G(img.viewid);
			view.style.width = (Math.round((img.ovsfact * $("#mainImg").width())/(img.parentNode.width / mWid)) - (img.bmode ? 0: 2))+ 'px';
			view.style.height = (Math.round((img.ovsfact * 1.2 * $("#mainImg").height())/(img.parentNode.height / mHei)) - (img.bmode ? 0: 2))+ 'px';
			view.style.left = Math.round((Math.abs(img.parentNode.left) / (img.parentNode.width / mWid))* img.ovsfact) + 30- (img.bmode ? 2 : 0) + 'px';
			view.style.top = Math.round((Math.abs(img.parentNode.top) / (img.parentNode.height / mHei))* img.ovsfact) + 15- (img.bmode ? 2 : 0) + 'px';
			view.maxleft = ($("#mainImg").width() * img.ovsfact)- (img.bmode ? 0 : 2) - parseInt(view.style.width, 10)+30;
			view.maxtop = ($("#mainImg").height() * img.ovsfact)- (img.bmode ? 0 : 2) - parseInt(view.style.height, 10)+15;
			return false
		},
		_findPosXY : function(ele) {
			
			var t, d = {
					x : ele.offsetLeft,
					y : ele.offsetTop
			};
			if (ele.offsetParent) {
				t = shiftzoom._findPosXY(ele.offsetParent);
				d.x += t.x;
				d.y += t.y
			}
			return d
		},
		_getMousePos : function(ex, ey, px, py) {
			var ox, oy, k = {
					ox : 0,
					oy : 0,
					ex : ex,
					ey : ey
			};
			if (self.pageXOffset || self.pageYOffset) {
				ox = self.pageXOffset;
				if (ox > 0 && px == ex) {
					ex -= ox
				}
				oy = self.pageYOffset;
				if (oy > 0 && py == ey) {
					ey -= oy
				}
			} else if (document.documentElement) {
				ox = document.documentElement.scrollLeft;
				oy = document.documentElement.scrollTop
			} else if (document.body) {
				ox = document.body.scrollLeft;
				oy = document.body.scrollTop
			}
			k.ox = ox;
			k.oy = oy;
			k.ex = ex;
			k.ey = ey;
			return k
		},
		_showCoords : function(e) {
		},
		_showPixel : function(e) {
		},
		_showPercent : function(e) {
		},
		_showLatLon : function(e) {
		},
		_killTooltip : function(id) {
		},
		_showTooltip : function(id, xx, yy) {
		},
		_setCursor : function(ele, d, id, nop) {
		},
		_zoomIn : function(id, ct, st, sw, ew, sh, eh, sx, ex, sy, ey, nz) {},
		_zoomOut : function(id, rm, ct, st, sw, ew, sh, eh, sx, ex, sy, ey, nz) {
		},
		_stopZoom : function() {
			var view, butt, img = shiftzoom._shiftzoom;
			document.onmouseup = null;
			clearInterval(cvi_sztimer);
			img.parentNode.left = parseInt(img.parentNode.style.left, 10);
			img.parentNode.top = parseInt(img.parentNode.style.top, 10);
			img.parentNode.width = parseInt(img.parentNode.style.width, 10);
			img.parentNode.height = parseInt(img.parentNode.style.height, 10);
			img.maxleft = img.parentNode.width - $("#mainImg").width();
			img.maxtop = img.parentNode.height - $("#mainImg").height();
			if (img.parentNode.width > $("#mainImg").width()
					|| img.parentNode.height > $("#mainImg").height()) {
				if (img.isIE) {
					img.style.cursor = "url('/webasset/w2p_mobile/assets/media/images/grab.cur'),move";
				} else if(img.gecko){
					img.style.cursor = "-moz-grab";
				} else {
					img.style.cursor = "-webkit-grab"
				}
				if (img.overview) {
					shiftzoom._setOverview(img);
					shiftzoom.G(img.overid).style.visibility = "visible"
				}
			} else {
				img.style.cursor = "-webkit-grab";
				if (img.overview) {
					shiftzoom.G(img.overid).style.visibility = "hidden"
				}
			}
			butt = shiftzoom.G(img.zoinid).style;
			butt.border = img.bc;
			butt.borderBottom = img.dc;
			butt.borderRight = img.dc;
			butt = shiftzoom.G(img.zoutid).style;
			butt.border = img.bc;
			butt.borderBottom = img.dc;
			butt.borderRight = img.dc;
			img.pointer = img.style.cursor;
			shiftzoom.G(img.textid).innerHTML = parseInt((img.parentNode.width / $(
			"#mainImg").width()) * 100)
			+ " / " + parseInt(img.xfactor * 100) + " %";
			if (img.lowres && img.highres) {
				shiftzoom.source(img, img.highres, false, true)
			}
			
			shiftzoom._shiftzoom = null;
			return false
		},
		_catchDrag : function(e) {
			return false
		},
		_catchWheel : function(e) {},
		
		_switchOver : function(e) {
			if (window.XMLHttpRequest) {
				this.firstChild.src = this.firstChild.secnd;
				return false
			} else {
				this.firstChild.style.filter = "progid:DXImageTransform.Microsoft.AlphaImageLoader(src='"
					+ this.firstChild.secnd + "',sizingMethod='scale')"
			}
			return false
		},
		_switchOut : function(e) {
			if (window.XMLHttpRequest) {
				this.firstChild.src = this.firstChild.first;
				return false
			} else {
				this.firstChild.style.filter = "progid:DXImageTransform.Microsoft.AlphaImageLoader(src='"
					+ this.firstChild.first + "',sizingMethod='scale')"
			}
			return false
		},
		_catchKey : function(e) {
			$('.text-on-image').css('display','none');
			clearInterval(cvi_sztimer);
			var img = shiftzoom._shiftzoom = this;
			var rm = false, mm = false, k, t, ex, ey, px = 0, py = 0;
			e = e ? e : window.event;
			if (e.which) {
				mm = (e.which == 2);
				rm = (e.which == 3)
			} else if (e.button) {
				mm = (e.button == 4);
				rm = (e.button == 2)
			}
			ex = e.clientX;
			ey = e.clientY;
			if (e.pageX || e.pageY) {
				px = e.pageX;
				py = e.pageY
			}
			k = shiftzoom._getMousePos(ex, ey, px, py);
			t = shiftzoom._findPosXY(img.parentNode.parentNode);
			img.mouseX = Math.min(Math.max(k.ex + k.ox - t.x, 0), $("#mainImg")
					.width());
			img.mouseY = Math.min(Math.max(k.ey + k.oy - t.y, 0), $("#mainImg")
					.height());
			if (((e.altKey && !e.shiftKey) || rm || img.zoomout)
					&& !img.automode
					&& !img.nozoom
					&& (img.parentNode.width > $("#mainImg").width() || img.parentNode.height > $(
					"#mainImg").height())) {
				var butt, sw, ew, sh, eh, sx, ex, sy, ey, st;
				img.pointer = img.style.cursor;
				if (!img.zoomout) {
					butt = shiftzoom.G(img.zoutid).style;
					butt.border = img.bc;
					butt.borderLeft = img.dc;
					butt.borderTop = img.dc;
					img.zoomout = true
				}
				sw = img.parentNode.width;
				ew = (img.parentNode.width - $("#mainImg").width()) * -1;
				sh = img.parentNode.height;
				eh = (img.parentNode.height - $("#mainImg").height()) * -1;
				sx = parseInt(img.parentNode.style.left, 10);
				ex = sx * -1;
				sy = parseInt(img.parentNode.style.top, 10);
				ey = sy * -1;
				st = Math.max(1, Math.round((img.parentNode.width / $("#mainImg")
						.width()) * 3));
				document.onmouseup = shiftzoom._stopZoom;
				if (img.lowres && img.highres) {
					shiftzoom.source(img, img.lowres, false, true)
				}
				cvi_sztimer = setInterval("shiftzoom._zoomOut('" + img.id + "',"
						+ rm + "," + (img.webkit && rm ? 1 : 0) + "," + st + ","
						+ sw + "," + ew + "," + sh + "," + eh + "," + sx + "," + ex
						+ "," + sy + "," + ey + "," + img.nozoom + ")",
						img.millisec)
			} else if (((!e.altKey && e.shiftKey) || mm)
					&& !img.automode
					&& (img.parentNode.width < img.maxwidth || img.parentNode.height < img.maxheight)) {
				var butt, sw, ew, sh, eh, sx, ex, sy, ey, st;
				if (img.gecko) {
					img.style.cursor = "-moz-zoom-in"
				}  else if (img.isIE) {
					img.style.cursor = "url('/webasset/w2p_mobile/assets/media/images/zoomin.ico'),move";
				} else if (img.webkit) {
					img.style.cursor = "-webkit-zoom-in"
				} else {
					img.style.cursor = "crosshair"
				}
				img.pointer = img.style.cursor;
				sw = img.parentNode.width;
				ew = img.maxwidth - img.parentNode.width;
				sh = img.parentNode.height;
				eh = img.maxheight - img.parentNode.height;
				sx = parseInt(img.parentNode.style.left, 10);
				sy = parseInt(img.parentNode.style.top, 10);
				ex = Math
				.max(
						0,
						Math
						.min(
								ew,
								Math
								.round(((img.mouseX - sx) * (img.maxwidth / img.parentNode.width))
										- ($("#mainImg")
												.width() * 0.5)
												+ sx)))
												* -1;
				ey = Math
				.max(
						0,
						Math
						.min(
								eh,
								Math
								.round(((img.mouseY - sy) * (img.maxheight / img.parentNode.height))
										- ($("#mainImg")
												.height() * 0.5)
												+ sy)))
												* -1;
				st = Math.max(1, Math
						.round((img.maxwidth / img.parentNode.width) * 3));
				document.onmouseup = shiftzoom._stopZoom;
				if (img.lowres && img.highres) {
					shiftzoom.source(img, img.lowres, false, true)
				}
			} else if (img.parentNode.width > $("#mainImg").width()
					|| img.parentNode.height > $("#mainImg").height()) {
				if (img.automode) {
					shiftzoom.stop(img)
				}
				if (img.gecko) {
					img.style.cursor = "-moz-grabbing"
				} else if (img.isIE) {
					img.style.cursor = "url('/webasset/w2p_mobile/assets/media/images/grabbing.cur'),move";
				} else {
					img.style.cursor = "-webkit-grabbing"
				}
				var x = parseInt(img.parentNode.style.left, 10), y = parseInt(
						img.parentNode.style.top, 10);
				img.mouseX = e.clientX;
				img.mouseY = e.clientY;
				document.onmousemove = shiftzoom._whilePan;
				document.onmouseup = shiftzoom._stopPan
			}
			return false
		},
		_catchTouch : function(e) {
			if (e.touches.length == 1) {
				e.preventDefault();
				clearInterval(cvi_sztimer);
				var img = shiftzoom._shiftzoom = e.target;
				if (img) {
					cvi_sztimer = new Date().getTime();
					cvi_szactive = img.id;
					cvi_szimage = true;
					var delay = cvi_sztimer - img.lasttap;
					if (delay < 500 && delay > 0) {
						img.lasttap = 0;
						var p = shiftzoom.get(img, 'currentxyz');
						if (p.z <= 0) {
							p.x = 50;
							p.y = 50
						}
						shiftzoom.kenburns(img, [ p.x, p.y, (p.z < 100 ? 100 : 0),
						                          3 ])
					} else {
						var k, t, ex, ey, px = 0, py = 0;
						img.lasttap = cvi_sztimer;
						ex = e.touches[0].clientX;
						ey = e.touches[0].clientY;
						if (e.touches[0].pageX || e.touches[0].pageY) {
							px = e.touches[0].pageX;
							py = e.touches[0].pageY
						}
						k = shiftzoom._getMousePos(ex, ey, px, py);
						t = shiftzoom._findPosXY(img.parentNode.parentNode);
						img.mouseX = Math.min(Math.max(k.ex + k.ox - t.x, 0), $(
						"#mainImg").width());
						img.mouseY = Math.min(Math.max(k.ey + k.oy - t.y, 0), $(
						"#mainImg").height());
						if (img.parentNode.width > $("#mainImg").width()
								|| img.parentNode.height > $("#mainImg").height()) {
							if (img.automode) {
								shiftzoom.stop(img)
							}
							img.hasmoved = false;
							img.mouseX = e.touches[0].clientX;
							img.mouseY = e.touches[0].clientY;
							img.ontouchmove = shiftzoom._touchMove;
							img.ontouchend = shiftzoom._touchEnd
						}
					}
				}
			}
			return false
		},
		_touchMove : function(e) {
			if (e.touches.length == 1) {
				var img = shiftzoom._shiftzoom;
				if (img) {
					var x = Math.max(0, Math.min(img.maxleft, Math.abs(parseInt(
							img.parentNode.style.left, 10))
							- (e.touches[0].clientX - img.mouseX)));
					var y = Math.max(0, Math.min(img.maxtop, Math.abs(parseInt(
							img.parentNode.style.top, 10))
							- (e.touches[0].clientY - img.mouseY)));
					img.parentNode.style.left = (x * -1) + 'px';
					img.parentNode.style.top = (y * -1) + 'px';
					img.parentNode.left = (x * -1);
					img.parentNode.top = (y * -1);
					img.mouseX = e.touches[0].clientX;
					img.mouseY = e.touches[0].clientY;
					img.hasmoved = true
				}
			}
			return false
		},
		_touchEnd : function() {
			var img = shiftzoom._shiftzoom;
			if (img) {
				clearInterval(cvi_sztimer);
				img.ontouchmove = null;
				img.ontouchend = null;
				if (img.overview) {
					var view = shiftzoom.G(img.viewid).style;
					view.left = Math
					.round((Math
							.abs(parseInt(img.parentNode.style.left, 10)) / (img.parentNode.width / $(
							"#mainImg").width()))
							* img.ovsfact)
							- (img.bmode ? 2 : 0) + 'px';
					view.top = Math
					.round((Math
							.abs(parseInt(img.parentNode.style.top, 10)) / (img.parentNode.height / $(
									"#mainImg").height()))
									* img.ovsfact)
									- (img.bmode ? 2 : 0) + 'px'
				}
			}
			cvi_szactive = null;
			cvi_szimage = false;
			shiftzoom._shiftzoom = null;
			return false
		},
		_catchGesture : function(e) {
			e.preventDefault();
			clearInterval(cvi_sztimer);
			var img = shiftzoom._shiftzoom = e.target;
			if (img && typeof (img.ctrlid) === "string") {
				cvi_szactive = img.id;
				cvi_szimage = true;
				shiftzoom.G(img.textid).innerHTML = parseInt((img.parentNode.width / $(
				"#mainImg").width()) * 100)
				+ " / " + parseInt(img.xfactor * 100) + " %";
				img.ongesturechange = shiftzoom._gestureChange;
				img.ongestureend = shiftzoom._gestureEnd
			}
			return false
		},
		_gestureChange : function(e) {
			e.preventDefault();
			var img = shiftzoom._shiftzoom;
			if (img && typeof (img.ctrlid) === "string") {
				var n, p = shiftzoom.get(img,
				'currentxyz');
				if (p.z <= 0) {
					p.x = 50;
					p.y = 50
				}
				shiftzoom.G(img.textid).innerHTML = parseInt((img.parentNode.width / $(
				"#mainImg").width()) * 100)
				+ " / " + parseInt(img.xfactor * 100) + " %";
				n = e.scale < 1 ? Math.max(p.z - 33.333333, 0) : Math.min(
						p.z + 33.333333, 100);
				shiftzoom.kenburns(img, [ p.x, p.y, n, 3 ]);
				shiftzoom.G(img.textid).innerHTML = parseInt((img.parentNode.width / $(
				"#mainImg").width()) * 100)
				+ " / " + parseInt(img.xfactor * 100) + " %"
			}
			return false
		},
		_gestureEnd : function() {
			var img = shiftzoom._shiftzoom;
			if (img && typeof (img.ctrlid) === "string") {
				img.ongesturechange = null;
				img.ongestureend = null;
				clearInterval(cvi_sztimer);
				img.parentNode.left = parseInt(img.parentNode.style.left, 10);
				img.parentNode.top = parseInt(img.parentNode.style.top, 10);
				img.parentNode.width = parseInt(img.parentNode.style.width, 10);
				img.parentNode.height = parseInt(img.parentNode.style.height, 10);
				img.maxleft = img.parentNode.width - $("#mainImg").width();
				img.maxtop = img.parentNode.height - $("#mainImg").height();
				shiftzoom.G(img.textid).innerHTML = parseInt((img.parentNode.width / $(
				"#mainImg").width()) * 100)
				+ " / " + parseInt(img.xfactor * 100) + " %";
				if (img.lowres && img.highres) {
					shiftzoom.source(img, img.highres, false, true)
				}
				
			}
			cvi_szactive = null;
			cvi_szimage = false;
			shiftzoom._shiftzoom = null;
			return false
		},
		_downKey : function(e) {
			if (cvi_szactive != null) {
				e = e ? e : window.event;
				var k = (e.keyCode ? e.keyCode : e.which), s = e.shiftKey, a = e.altKey, w = false, AL = 37, AU = 38, AR = 39, AD = 40, HO = 36, EN = 35, PD = 34, PU = 33, PL = 187, MN = 189;
				switch (k) {
				case AL:
					cvi_szimage = true;
					shiftzoom._panKey(8, 0, s, a);
					break;
				case AR:
					cvi_szimage = true;
					shiftzoom._panKey(-8, 0, s, a);
					break;
				case AU:
					cvi_szimage = true;
					shiftzoom._panKey(0, 8, s, a);
					break;
				case AD:
					cvi_szimage = true;
					shiftzoom._panKey(0, -8, s, a);
					break;
				case HO:
					if (cvi_szimage == null) {
						cvi_szimage = true;
						shiftzoom._initZoom(0, 1, w)
					}
					break;
				case EN:
					if (cvi_szimage == null) {
						cvi_szimage = true;
						shiftzoom._initZoom(1, 1, w)
					}
					break;
				case MN:
				case PU:
					if (cvi_szimage == null) {
						cvi_szimage = true;
						shiftzoom._initZoom(0, 4, w)
					}
					break;
				case PL:
				case PD:
					if (cvi_szimage == null) {
						cvi_szimage = true;
						shiftzoom._initZoom(1, 4, w)
					}
					break
				}
			}
			return false
		},
		_pressKey : function(e) {
			return false
		},
		_upKey : function() {
			if (cvi_szactive != null) {
				cvi_szimage = null
			}
			return false
		},
		_initZoom : function(d, v, w) {
			var sw, ew, sh, eh, sx, ex, sy, ey, st, img = shiftzoom.G(cvi_szactive);
			if (img.automode) {
				shiftzoom.stop(img)
			}
			if (d == 0
					&& !img.nozoom
					&& (parseInt(img.parentNode.style.width, 10) > $("#mainImg")
							.width() || parseInt(img.parentNode.style.height, 10) > $(
							"#mainImg").height())) {
				img.pointer = img.style.cursor;
				sw = img.parentNode.width;
				ew = (img.parentNode.width - $("#mainImg").width()) * -1;
				sh = img.parentNode.height;
				eh = (img.parentNode.height - $("#mainImg").height()) * -1;
				sx = parseInt(img.parentNode.style.left, 10);
				ex = sx * -1;
				sy = parseInt(img.parentNode.style.top, 10);
				ey = sy * -1;
				st = Math.max(1, Math.round((img.parentNode.width / $("#mainImg")
						.width())
						* v));
				if (img.lowres && img.highres) {
					shiftzoom.source(img, img.lowres, false, true)
				}
				shiftzoom._zoomKey(d, (w ? 1 : 0), w, st, sw, ew, sh, eh, sx, ex,
						sy, ey, img.nozoom)
			} else if (d == 1
					&& !img.nozoom
					&& (parseInt(img.parentNode.style.width, 10) < img.maxwidth || parseInt(
							img.parentNode.style.height, 10) < img.maxheight)) {
				if (img.gecko) {
					img.style.cursor = "-moz-zoom-in"
				}  else if (img.isIE) {
					img.style.cursor = "url('/webasset/w2p_mobile/assets/media/images/zoomin.ico'),move";
				} else if (img.webkit) {
					img.style.cursor = "-webkit-zoom-in"
				}  else {
					img.style.cursor = "-webkit-grab"
				}
				img.pointer = img.style.cursor;
				sw = img.parentNode.width;
				ew = img.maxwidth - img.parentNode.width;
				sh = img.parentNode.height;
				eh = img.maxheight - img.parentNode.height;
				sx = parseInt(img.parentNode.style.left, 10);
				sy = parseInt(img.parentNode.style.top, 10);
				ex = Math
				.max(
						0,
						Math
						.min(
								ew,
								Math
								.round(((($("#mainImg")
										.width() / 2) - sx) * (img.maxwidth / img.parentNode.width))
										- ($("#mainImg")
												.width() * 0.5)
												+ sx)))
												* -1;
				ey = Math
				.max(
						0,
						Math
						.min(
								eh,
								Math
								.round(((($("#mainImg")
										.height() / 2) - sy) * (img.maxheight / img.parentNode.height))
										- ($("#mainImg")
												.height() * 0.5)
												+ sy)))
												* -1;
				st = Math.max(1, Math.round((img.maxwidth / img.parentNode.width)
						* v));
				if (img.lowres && img.highres) {
					shiftzoom.source(img, img.lowres, false, true)
				}
				shiftzoom._zoomKey(d, (w ? 1 : 0), w, st, sw, ew, sh, eh, sx, ex,
						sy, ey, img.nozoom)
			}
			return false
		},
		_zoomKey : function(d, ct, ww, st, sw, ew, sh, eh, sx, ex, sy, ey, nz) {
			if (cvi_szactive != null && !nz) {
				var view, mw, mh, mx, my, img = shiftzoom.G(cvi_szactive);
					function setoverview() {
						if (img.lowres && img.highres) {
							shiftzoom.source(img, img.highres, false, true)
						}
						if (img.parentNode.width > $("#mainImg").width()
								|| img.parentNode.height > $("#mainImg").height()) {
							if (img.isIE) {
								img.style.cursor = "url('/webasset/w2p_mobile/assets/media/images/grabbing.cur'),move";
							} else {
								img.style.cursor = "-webkit-grabbing"
							}
							if (img.overview) {
								shiftzoom._setOverview(img);
								shiftzoom.G(img.overid).style.visibility = "visible"
							}
						} else {
							img.style.cursor = "-webkit-grab";
							if (img.overview) {
								shiftzoom.G(img.overid).style.visibility = "hidden"
							}
						}
						img.pointer = img.style.cursor;
					}
					if (d == 0
							&& (parseInt(img.parentNode.style.width, 10) > $(
							"#mainImg").width() || parseInt(
									img.parentNode.style.height, 10) > $("#mainImg")
									.height())) {
						mw = Math.max($("#mainImg").width(), Math.min(img.maxwidth,
								Math.round(ew * ct / st + sw)));
						mx = Math.round(ex * ct / st + sx);
						mh = Math.max($("#mainImg").height(), Math.min(
								img.maxheight, Math.round(eh * ct / st + sh)));
						my = Math.round(ey * ct / st + sy);
						img.parentNode.style.width = mw + 'px';
						img.parentNode.style.height = mh + 'px';
						img.parentNode.style.left = mx + 'px';
						img.parentNode.style.top = my + 'px';
						shiftzoom.G(img.textid).innerHTML = parseInt((mw / $(
						"#mainImg").width()) * 100)
						+ " / " + parseInt(img.xfactor * 100) + " %";
						img.parentNode.width = mw;
						img.parentNode.height = mh;
						img.parentNode.left = mx;
						img.parentNode.top = my;
						img.maxleft = img.parentNode.width - $("#mainImg").width();
						img.maxtop = img.parentNode.height - $("#mainImg").height();
						ct++;
						if (img.divbug) {
							img.parentNode.firstChild.style.width = mw + 'px';
							img.parentNode.firstChild.style.height = mh + 'px'
						}
						if ((cvi_szimage || ww)
								&& (img.parentNode.width > $("#mainImg").width() || img.parentNode.height > $(
								"#mainImg").height())) {
							if (!ww) {
								setTimeout("shiftzoom._zoomKey(" + d + "," + ct
										+ "," + ww + "," + st + "," + sw + "," + ew
										+ "," + sh + "," + eh + "," + sx + "," + ex
										+ "," + sy + "," + ey + "," + nz + ")", 50)
							} else {
								setoverview();
								if (cvi_szactive != null) {
									cvi_szimage = null
								}
							}
						} else {
							setoverview()
						}
					} else if (d == 1
							&& (parseInt(img.parentNode.style.width, 10) < img.maxwidth || parseInt(
									img.parentNode.style.height, 10) < img.maxheight)) {
						mw = Math.max($("#mainImg").width(), Math.min(img.maxwidth,
								Math.round(ew * ct / st + sw)));
						mx = Math.round(ex * ct / st + sx);
						mh = Math.max($("#mainImg").height(), Math.min(
								img.maxheight, Math.round(eh * ct / st + sh)));
						my = Math.round(ey * ct / st + sy);
						img.parentNode.style.width = mw + 'px';
						img.parentNode.style.height = mh + 'px';
						img.parentNode.style.left = mx + 'px';
						img.parentNode.style.top = my + 'px';
						shiftzoom.G(img.textid).innerHTML = parseInt((mw / $(
						"#mainImg").width()) * 100)
						+ " / " + parseInt(img.xfactor * 100) + " %";
						img.parentNode.width = mw;
						img.parentNode.height = mh;
						img.parentNode.left = mx;
						img.parentNode.top = my;
						img.maxleft = img.parentNode.width - $("#mainImg").width();
						img.maxtop = img.parentNode.height - $("#mainImg").height();
						ct++;
						if (img.divbug) {
							img.parentNode.firstChild.style.width = mw + 'px';
							img.parentNode.firstChild.style.height = mh + 'px'
						}
						if ((cvi_szimage || ww)
								&& (img.parentNode.width < img.maxwidth || img.parentNode.height < img.maxheight)) {
							if (!ww) {
								setTimeout("shiftzoom._zoomKey(" + d + "," + ct
										+ "," + ww + "," + st + "," + sw + "," + ew
										+ "," + sh + "," + eh + "," + sx + "," + ex
										+ "," + sy + "," + ey + "," + nz + ")", 50)
							} else {
								setoverview();
								if (cvi_szactive != null) {
									cvi_szimage = null
								}
							}
						} else {
							setoverview()
						}
					} else {
						setoverview()
					}
			}
			return false
		},
		_panKey : function(h, v, s, a) {
			if (cvi_szactive != null) {
				var img = shiftzoom.G(cvi_szactive);
				if (img.automode) {
					shiftzoom.stop(img)
				}
				if (!img.automode
						&& (img.parentNode.width > $("#mainImg").width() || img.parentNode.height > $(
						"#mainImg").height())) {
					var x = Math.max(0, Math.min(img.maxleft, Math
							.abs(parseInt(img.parentNode.style.left))
							- (s ? 4 * h : a ? h / 4 : h)));
					var y = Math.max(0, Math.min(img.maxtop, Math
							.abs(parseInt(img.parentNode.style.top))
							- (s ? 4 * v : a ? v / 4 : v)));
					img.parentNode.style.left = (x * -1) + 'px';
					img.parentNode.style.top = (y * -1) + 'px';
					img.parentNode.left = (x * -1);
					img.parentNode.top = (y * -1);
					if (img.overview) {
						var view = shiftzoom.G(img.viewid).style;
						view.left = Math
						.round((Math
								.abs(parseInt(img.parentNode.style.left)) / (img.parentNode.width / $(
								"#mainImg").width()))
								* img.ovsfact)
								- (img.bmode ? 2 : 0) + 'px';
						view.top = Math
						.round((Math
								.abs(parseInt(img.parentNode.style.top)) / (img.parentNode.height / $(
										"#mainImg").height()))
										* img.ovsfact)
										- (img.bmode ? 2 : 0) + 'px'
					}
					if (cvi_szimage) {
						setTimeout("shiftzoom._panKey(" + h + "," + v + "," + s
								+ "," + a + ")", 50)
					}
				}
			}
			return false
		},
		
		_whilePan : function(e) {
			var img = shiftzoom._shiftzoom;
			e = e ? e : window.event;
			var x = Math.max(0, Math.min(img.maxleft, Math
					.abs(parseInt(img.parentNode.style.left))
					- (e.clientX - img.mouseX)));
			var y = Math.max(0, Math.min(img.maxtop, Math
					.abs(parseInt(img.parentNode.style.top))
					- (e.clientY - img.mouseY)));
			img.parentNode.style.left = (x * -1) + 'px';
			img.parentNode.style.top = (y * -1) + 'px';
			img.parentNode.left = (x * -1);
			img.parentNode.top = (y * -1);
			img.mouseX = e.clientX;
			img.mouseY = e.clientY;
			return false
		},
		_stopPan : function() {
			var view, butt, img = shiftzoom._shiftzoom;
			document.onmousemove = null;
			document.onmouseup = null;
			
			if (img.gecko || img.presto) {
				img.style.cursor = "-moz-grab";
			} else if(img.isIE) {
				img.style.cursor = "url('/webasset/w2p_mobile/assets/media/images/grab.cur'),move";
			} else {
				img.style.cursor = '-webkit-grab';
			}
			if (img.overview) {
				view = shiftzoom.G(img.viewid).style;
				view.left = Math
				.round((Math.abs(parseInt(img.parentNode.style.left)) / (img.parentNode.width / $("#mainImg").width()))
				* img.ovsfact) - (img.bmode ? 2 : 0) +30+ 'px';
				view.top = Math
				.round((Math.abs(parseInt(img.parentNode.style.top)) / (img.parentNode.height / $(
						"#mainImg").height())) * img.ovsfact) - (img.bmode ? 2 : 0) +15+ 'px'
			}
			shiftzoom._shiftzoom = null;
			return false
		},
		_startMove : function(e) {
			if (!e) {
				e = window.event;
				var view = e.srcElement
			} else {
				var view = e.target
			}
			var l = parseInt(view.style.left), t = parseInt(view.style.top);
			cvi_szimage = view.id.substring(0, view.id.indexOf("_"));
			view.style.cursor = "default";
			view.mouseX = e.clientX;
			view.mouseY = e.clientY;
			document.onmousemove = shiftzoom._whileMove;
			document.onmouseup = shiftzoom._stopMove;
			return false
		},
		_whileMove : function(e) {
			if (!e) {
				e = window.event;
				var view = e.srcElement
			} else {
				var view = e.target
			}
			var cen = view.id.split("_"), img = shiftzoom.G(cvi_szimage);
			if (view && cen[cen.length - 1] == 'view' && view.maxleft
					&& view.maxtop) {
				var l = Math.max(30, Math.min(view.maxleft,parseInt(view.style.left)
						+ (e.clientX - view.mouseX)));
				var t = Math.max(15, Math.min(view.maxtop,parseInt(view.style.top)
						+ (e.clientY - view.mouseY)));
				view.style.left = (l - (img.bmode ? 2 : 0)) + 'px';
				view.style.top = (t - (img.bmode ? 2 : 0)) + 'px';
				view.mouseX = e.clientX;
				view.mouseY = e.clientY;
				var x = Math.max(0, Math.min(img.maxleft, (l-30) * (img.parentNode.width / $("#mainImg").width()) * (1 / img.ovsfact)));
				var y = Math.max(0, Math.min(img.maxtop, (t-15) * (img.parentNode.height / $("#mainImg").height()) * (1 / img.ovsfact)));
				img.parentNode.style.left = (x * -1) + 'px';
				img.parentNode.style.top = (y * -1) + 'px';
				img.parentNode.left = (x * -1);
				img.parentNode.top = (y * -1)
			} else {
				document.onmousemove = null;
				document.onmouseup = null;
				if (img) {
					img.onmousedown = shiftzoom._catchKey
				}
				cvi_szimage = null
			}
			return false
		},
		_stopMove : function() {
			document.onmousemove = null;
			document.onmouseup = null;
			var img = shiftzoom._shiftzoom;
			img.style.cursor = "-moz-grab";
			if(img.isIE) img.style.cursor = "url('/webasset/w2p_mobile/assets/media/images/grab.cur'),move";
			shiftzoom.G(cvi_szimage).onmousedown = shiftzoom._catchKey;
			cvi_szimage = null;
			return false
		}
}