/* ---------------------------------------------------------------------
Target Browsers: All
Media Type: Screen, Projection
Width: All Sizes
------------------------------------------------------------------------ */
/*csslint
*/

/* ---------------------------------------------------------------------
 File
------------------------------------------------------------------------ */

.file {
    display: inline-block;
    position: relative;
}

.file-control {
    position: absolute;
    opacity: 0;
    top: 0;
    left: 0;
    z-index: 1;
    border: none;
    cursor: pointer;
    font-size: 42px;
    display: none;
}

form#file-browse-logo-upload, #file-browse {
    width: 100% !important;
    margin-top: 10px !important;
}

label#UploadLogo-BrowseForFile {
    width: 100% !important;
    padding: 10px 16px !important;
}