define(function(require) {
    'use strict';

    var $ = require('jquery');
    var Classes = require('../constants/Classes');
    var Controller = require('./Controller');
    var DomEvents = require('../constants/DomEvents');
    var EventController = require('./Event');
    var OmnitureController = require('./Omniture');
    var ProductEvents = require('../constants/ProductEvents');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');
    var Settings = require('../constants/Settings');

    //>>excludeStart("isProd", pragmas.isProd);
    var q = require('q');
    q.longStackSupport = true;
    //>>excludeEnd("isProd");

    /**
     * @class App
     * @extends App.Controllers.Controller
     *
     * @constructor
     * @param {Object} config
     */
    function AppController(config) {
        bindAll(this,
            'onCheckableChange',
            '_onCheckableChange',
            'onProductChange',
            'onNewProduct'
        );

        /**
         * @property $window
         * @type {jQuery}
         */
        this.$window = $(window);

        /**
         * @property omniture
         * @type {App.Controllers.Omniture}
         */
        this.omniture = new OmnitureController();

        Controller.call(this, config);
    }

    var proto = inherits(AppController, Controller);

    /**
     * @property registry
     * @type {Object.<String,App.Controllers.Controller>}
     */
    proto.registry = {
        // LogoBrowseController: require('./LogoBrowse'),
        // LogoMixController: require('./LogoMix'),
        LogoUploadController: require('./LogoUpload'),
        ProductController: require('./Product'),
        StandardVerseController: require('./StandardVerse'),
        RMBlockController: require('./RMBlock'),
        FbtIndexController: require('./FbtIndex'),
        FbtSummaryController: require('./FbtSummary'),
        FbtNextController: require('./FbtNext'),
    };

    /**
     * @method attachEvents
     * @chainable
     */
    proto.attachEvents = function() {
        var $view = this.$view;
        var $window = this.$window;

        $view
            .on(DomEvents.BLUR, Classes.CHECKABLE_SELECTOR, this.onCheckableChange)
            .on(DomEvents.CLICK, Classes.CHECKABLE_SELECTOR, this.onCheckableChange)
            .on(DomEvents.CHANGE, Classes.CHECKABLE_SELECTOR, this.onCheckableChange);

        EventController
            .tie(DomEvents.CLICK, $view)
            .tie(DomEvents.HASH_CHANGE, $window)
            .tie(DomEvents.STATE_POP, $window)
            .on(ProductEvents.PREV_CLICK, this.onProductChange)
            .on(ProductEvents.NEXT_CLICK, this.onProductChange)
            .on(ProductEvents.FBT_NEXT_FLOW, this.onNewProduct);

        return this;
    };

    /**
     * @method detachEvents
     * @chainable
     */
    proto.detachEvents = function() {
        var $view = this.$view;
        var $window = this.$window;

        $view
            .off(DomEvents.BLUR, Classes.CHECKABLE_SELECTOR, this.onCheckableChange)
            .off(DomEvents.CLICK, Classes.CHECKABLE_SELECTOR, this.onCheckableChange)
            .off(DomEvents.CHANGE, Classes.CHECKABLE_SELECTOR, this.onCheckableChange);

        EventController
            .off(DomEvents.CLICK, $view)
            .off(DomEvents.HASH_CHANGE, $window)
            .off(DomEvents.STATE_POP, $window)
            .off(ProductEvents.PREV_CLICK, this.onProductChange)
            .off(ProductEvents.NEXT_CLICK, this.onProductChange)
            .off(ProductEvents.FBT_NEXT_FLOW, this.onNewProduct);;

        return this;
    };

    /**
     * @method scrollToTop
     * @chainable
     */
    proto.scrollToTop = function() {
        this.$window.scrollTop(0);

        return this;
    };

    // -- Event Handlers -------------------------------------------------------

    /**
     * This updates the `isChecked` class on all checkable related to the
     * changed input. This dirty hack gets around IE8's lack of support for
     * the `:checked` pseudo selector.
     *
     * @method onCheckableChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onCheckableChange = function(event) {
        var $target = $(event.target);
        var name = $target.attr('name');

        if (!name) {
            return;
        }

        var $parent = $target.closest(Classes.CONTROLLER_SELECTOR);

        // Special handling needed for radios
        if ($target.is(':radio')) {
            // Find all related fields in the parent controller's scope
            $target = $parent.find('input[name="' + name + '"]');
        }

        // Update checked state
        $target.each(this._onCheckableChange);

        // Force repaint in IE
        $parent
            .addClass('redraw')
            .removeClass('redraw');
    };

    /**
     * @method _onCheckableChange
     * @param {Number} i
     * @param {HTMLElement} input
     * @callback
     */
    proto._onCheckableChange = function(i, input) {
        var $input = $(input);
        var checked = $input.prop('checked') === true;
        $input.toggleClass(Classes.IS_CHECKED, checked);

        // FIX FOR KINDLE FIRE BUG
        var colors = $input.siblings().filter('.colorToggle');
        if (colors.length) {
            colors.toggleClass('colorToggle_checked', checked);
        }

        // FIX FOR KINDLE FIRE BUG
        var toggles = $input.siblings().filter('.btnToggle');
        if (toggles.length) {
            toggles.toggleClass('btnToggle_checked', checked);
        }
    };

    /**
     * Reset the view to the top of the window when Next is clicked.
     *
     * @method onProductChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onProductChange = function() {
        this.scrollToTop();
    };

     /**
     * Rest product controller for next FBT product
     *
     * @method onProductChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onNewProduct = function() {
       this.fbt_start();
    };

    return AppController;
});
