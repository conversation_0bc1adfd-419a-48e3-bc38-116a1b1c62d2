define(function (require) {
    'use strict';

    var $ = require('jquery');
    var AbstractProvider = require('./Abstract');
    var ContentCollection = require('../models/collections/Content');
    var Settings = require ('../constants/Settings');
    var bindAll = require('mout/object/bindAll');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * @class App.Providers.Content
     * @extends App.Providers.Abstract
     *
     * @constructor
     */
    var ContentProvider = function () {
        bindAll(this, '_onResponseReceived');

        AbstractProvider.apply(this, arguments);
    };

    var proto = inherits(ContentProvider, AbstractProvider);

    /**
     * Makes a call for the all available external content
     *
     * @method getContent
     * @param {bool} flushCache
     * @return {Promise}
     */
    proto.getContent = function(flushCache) {
        if (this.promise && flushCache !== true) {
            // data will be cached after the first call
            return this.promise;
        }

        this.promise = this
            .setBaseUrl(Settings.SITE_HOST + Settings.SVC_API_BASE)
            //.get(Settings.SVC_CONTENT, { v: Settings.ENDPOINT_VERSION })
            .get(Settings.SVC_CONTENT)
            .then(this._onResponseReceived)
            .fail(this._onError);

        return this.promise;
    };

    /**
     * Handles the response from the ajax call
     *
     * @method _onResponseReceived
     * @param {HTMLDocument} data
     * @private
     */
    proto._onResponseReceived = function(data) {
        // success! we have the data, store and resolve the promise;
        var response = $.xml2json(data);
        var model = new ContentCollection(data);

        // So you can call content.getById('logo');
        model.setLookupId('target');

        if (response.error) {
            throw new Error(response.error);
        }

        return model;
    };

    return new ContentProvider();
});
