<div class="grid">

    <div class="grid-col grid-col_7of10">

        <div class="design">

            <div class="design-bd"></div>

        </div>

    </div>

    <div class="grid-col grid-col_3of10">

        <div class="options">

            <div class="vList vList_loose">

                <div>

                    <h2 class="hdg hdg_h2">Copies:</h2>

                    <ul class="vList vList_std">

                        <li>
                            {{! js - remove any extension classes from design-bd }}
                            <input type="radio" id="single" name="copies" value="single" class="radio" />
                            <label for="single">
                                Single Copy
                            </label>
                        </li>

                        <li>
                            {{! js - add design-bd_duplicate extension class to design-bd }}
                            <input type="radio" id="duplicate" name="copies" value="duplicate" class="radio" />
                            <label for="duplicate">
                                Duplicate Copy
                            </label>
                        </li>

                        <li>
                            {{! js - add design-bd_triplicate extension class to design-bd }}
                            <input type="radio" id="triplicate" name="copies" value="triplicate" class="radio" />
                            <label for="triplicate">
                                Triplicate Copy
                            </label>
                        </li>

                    </ul>

                </div> {{! /vList-child }}

                <div>

                    <h2 class="hdg hdg_h2">Quantity:</h2>

                    <label class="txtLarge">
                        <span class="isHidden">Quantity</span>
                        <span class="inputBox inputBox_select">
                            <select class="inputBox_select-input">
                                <option>500 Checks</option>
                                <option>1000 Checks</option>
                                <option>5000 Checks</option>
                                <option>10000 Checks</option>
                            </select>
                        </span>
                    </label>

                </div> {{! /vList-child }}

            </div> {{! /vList }}

        </div> {{! /options }}

        {{> subtotal}}

    </div> {{! /grid-col }}

</div> {{! /grid }}
