define(function(require) {
    'use strict';

    var AbstractStateModel = require('./Abstract');
    var DomEvents = require('../../constants/DomEvents');
    var EventController = require('../../controllers/Event');
    var StateEvents = require('../../constants/StateEvents');
    var inherits = require('mout/lang/inheritPrototype');

    /**
     * HTML5 history state stepper
     *
     * @class App.Models.State.History
     * @extends App.Models.State.Abstract
     *
     * @constructor
     */
    function HistoryStateModel() {
        AbstractStateModel.apply(this, arguments);

        EventController.on(DomEvents.STATE_POP, this.onChange);
    }

    var proto = inherits(HistoryStateModel, AbstractStateModel);

    // -- Methods --------------------------------------------------------------

    /**
     * @method start
     * @chainable
     */
    proto.start = function() {
        // Set initial state
        if (history.state === null) {
            this.current = -1;
            this.forward();

            return this;
        }

        // Set existing state
        this.current = history.state;
        EventController.emit(StateEvents.CHANGE, this.getState());

        return this;
    };

    /**
     * @method back
     * @chainable
     */
    proto.back = function() {
        // Restrict to lower bound
        if (this.current - 1 < 0) {
            history.back();
            return this;
        }

        // Go back
        this.current--;
        history.back();

        return this;
    };

    /**
     * @method forward
     * @chainable
     */
    proto.forward = function() {
        // Restrict to upper bound
        if (this.current + 1 >= this.states.length) {
            return this;
        }

        // Go forward
        this.current++;
        history.forward();

        setTimeout(this.pushState, 0);

        return this;
    };

    /**
     * @method pushState
     * @chainable
     */
    proto.pushState = function() {
        var current = this.current;

        // Only update state if the browser hasn't already
        if (history.state !== current) {
            if (history.state === null) {
                // Set state (doesn't break the back button)
                history.replaceState(current, '', this.getState().hash);
            } else {
                // Change state
                history.pushState(current, '', this.getState().hash);
            }

            // Trigger change
            setTimeout(this.onChange, 0);
        }

        return this;
    };

    // -- Event Handlers -------------------------------------------------------

    /**
     * @fires {Constants.StateEvents.CHANGE}
     *
     * @method onChange
     * @param {jQuery.Event} event
     * @callback
     */
    proto.onChange = function() {
        var state = history.state;

        // Reset state
        if (state === null) {
            this.start();
            return this;
        }

        // Update current on change
        this.current = state;

        // Notify application
        EventController.emit(StateEvents.CHANGE, this.getState());

        return this;
    };

    return HistoryStateModel;
});
