<div class="grid">

    <div class="grid-col grid-col_7of10">

        <div class="design">

            <div class="design-bd"></div>

        </div>

    </div>

    <div class="grid-col grid-col_3of10">

        <div class="options">

            <h2 class="hdg hdg_h2">Text Style Options:</h2>

            <div class="vList vList_loose">

                <div>
                    <span class="txtLarge">
                        Font:
                    </span>
                    <div class="inputBox inputBox_select inputBox_select_hasDropdown">
                        <div class="inputBox_select-input">
                            --Please Select--
                        </div>
                        <div class="inputBox_select-dropdown">
                            <ul class="vList vList_std js-fontList">
                                <li><a href="#">Arial</a></li>
                                <li><a href="#">Times New Roman</a></li>
                                <li><a href="#">Comic Sans</a></li>
                                <li><a href="#">Verdana</a></li>
                                <li><a href="#">Arial</a></li>
                                <li><a href="#">Times New Roman</a></li>
                                <li><a href="#">Comic Sans</a></li>
                                <li><a href="#">Verdana</a></li>
                                <li><a href="#">Arial</a></li>
                                <li><a href="#">Times New Roman</a></li>
                                <li><a href="#">Comic Sans</a></li>
                                <li><a href="#">Verdana</a></li>
                                <li><a href="#">Arial</a></li>
                                <li><a href="#">Times New Roman</a></li>
                                <li><a href="#">Comic Sans</a></li>
                                <li><a href="#">Verdana</a></li>
                                <li><a href="#">Arial</a></li>
                                <li><a href="#">Times New Roman</a></li>
                                <li><a href="#">Comic Sans</a></li>
                                <li><a href="#">Verdana</a></li>
                                <li><a href="#">Arial</a></li>
                                <li><a href="#">Times New Roman</a></li>
                                <li><a href="#">Comic Sans</a></li>
                                <li><a href="#">Verdana</a></li>
                            </ul>
                        </div>
                    </div>
                    <input type="hidden" value="selected-font" />
                </div> {{! /vList-child }}

                <div>
                    <h2 class="hdg hdg_h2">Primary Text Color:</h2>

                    <div class="media">
                        <div class="media-img media-img_small">
                            <div class="designBox designBox_active"></div>
                        </div>
                        <div class="media-body">
                            <h3 class="hdg hdg_h4">
                                Black
                            </h3>
                            <span class="txtStd">
                                Most text on your check will appear black by default.
                            </span>
                        </div>
                    </div>
                </div> {{! /vList-child }}

                <div>
                    <input type="checkbox" id="extraColor" class="checkbox" />
                    <label for="extraColor">
                        <span class="hdg hdg_h2 mix-hdg_blue mix-hdg_tight">
                            Extra Text Color (+$30)
                        </span>
                    </label>

                    <div class="colorToggle">

                        <ul class="blocks blocks_3up">

                            <li>
                                <input type="radio" name="textColor" id="textPowder" value="textPowder" class="designRadio" />
                                <label for="textPowder">
                                    <span class="designBox"></span>
                                    Powder
                                </label>
                            </li>

                            <li>
                                <input type="radio" name="textColor" id="textNavy" value="textNavy" class="designRadio" />
                                <label for="textNavy">
                                    <span class="designBox"></span>
                                    Navy
                                </label>
                            </li>

                            <li>
                                <input type="radio" name="textColor" id="textLime" value="textLime" class="designRadio" />
                                <label for="textLime">
                                    <span class="designBox"></span>
                                    Lime
                                </label>
                            </li>

                            <li>
                                <input type="radio" name="textColor" id="textRed" value="textRed" class="designRadio" />
                                <label for="textRed">
                                    <span class="designBox"></span>
                                    Red
                                </label>
                            </li>

                            <li>
                                <input type="radio" name="textColor" id="textWhite" value="textWhite" class="designRadio" />
                                <label for="textWhite">
                                    <span class="designBox"></span>
                                    White
                                </label>
                            </li>

                            <li>
                                <input type="radio" name="textColor" id="textBrown" value="textBrown" class="designRadio" />
                                <label for="textBrown">
                                    <span class="designBox"></span>
                                    Brown
                                </label>
                            </li>

                            <li>
                                <input type="radio" name="textColor" id="textRoyal" value="textRoyal" class="designRadio" />
                                <label for="textRoyal">
                                    <span class="designBox"></span>
                                    Royal
                                </label>
                            </li>

                            <li>
                                <input type="radio" name="textColor" id="textGold" value="textGold" class="designRadio" />
                                <label for="textGold">
                                    <span class="designBox"></span>
                                    Gold
                                </label>
                            </li>

                            <li>
                                <input type="radio" name="textColor" id="textSage" value="textSage" class="designRadio" />
                                <label for="textSage">
                                    <span class="designBox"></span>
                                    Sage
                                </label>
                            </li>

                        </ul>

                    </div> {{! /colorToggle }}

                </div> {{! /vList-child }}

            </div> {{! /vList }}
            
        </div> {{! /options }}

        {{> subtotal}}

    </div> {{! /grid-col }}

</div> {{! /grid }}
