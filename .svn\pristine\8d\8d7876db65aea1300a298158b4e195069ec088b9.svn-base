define(function() {
    'use strict';

    /**
     * Registry namespace
     *
     * @class Util.Registry.Namespace
     */
    var RegistryNamespace = function(type) {

        /**
         * @property type
         * @type String
         */
        this.type = type;

        this.reset();
    };

    var proto = RegistryNamespace.prototype;

    /**
     * Set key/value pair.
     * If key is array, each value in array will be added as a separate item.
     *
     * @method set
     * @param {String|Array} key
     * @param {*} value
     * @chainable
     */
    proto.set = function(key, value) {
        if (!(key instanceof Array)) {
            key = [key];
        }

        var i = 0;
        var length = key.length;

        for (; i < length; i++) {
            this.items[key[i]] = value;
        }

        return this;
    };

    /**
     * Get value by key
     *
     * @method get
     * @param {String} key
     * @return {*}
     */
    proto.get = function(key) {
        return this.items[key];
    };

    /**
     * Remove value by key
     *
     * @method remove
     * @param {String} key
     * @chainable
     */
    proto.remove = function(key) {
        delete this.items[key];

        return this;
    };

    /**
     * Itterate through all registered items
     *
     * @method forEach
     * @param {Function} callback
     * @chainable
     */
    proto.forEach = function(callback) {
        if (typeof callback !== 'function') {
            throw new TypeError('Invalid argument type `callback`: expecting a function');
        }

        var name;

        for (name in this.items) {
            if (!this.items.hasOwnProperty(name)) {
                continue;
            }
            callback(this.items[name], name);
        }

        return this;
    };

    /**
     * Remove all values
     *
     * @method reset
     * @chainable
     */
    proto.reset = function() {

        /**
         * @property items
         * @type Object
         */
        this.items = {};

        return this;
    };

    return RegistryNamespace;

});